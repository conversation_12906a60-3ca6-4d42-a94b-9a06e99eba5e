# Skeleton Loader Implementation Guide

## Overview
We have implemented enterprise-grade skeleton loaders throughout the application to provide smooth loading experiences. All skeleton components follow Material Design patterns and are consistent with the Unizo brand.

## Skeleton Components

### 1. **TableSkeleton** (`/app/components/skeletons/TableSkeleton.tsx`)
- Used for table views with pagination
- Props: `rows`, `columns`, `showHeader`, `title`, `showActions`, `showPagination`
- Usage: API logs, Event logs, Integrations list

### 2. **CardGridSkeleton** (`/app/components/skeletons/CardGridSkeleton.tsx`)
- Used for grid layouts with cards
- Props: `count`, `columns`, `cardHeight`, `showActions`
- Usage: Environments page

### 3. **DashboardSkeleton** (`/app/components/skeletons/DashboardSkeleton.tsx`)
- Used for dashboard with stats and charts
- Layout matches actual dashboard structure:
  - 4 stat cards at the top (Categories, Services, Integrations, Webhooks)
  - 2 large chart cards (API Requests, Event Requests)
  - Bottom row with smaller stats (Integration stats, Service stats, Error stats)
  - Full-width traffic provider stats table
- Responsive design adapts to different screen sizes
- Usage: Main dashboard page

### 4. **NavigationSkeleton** (`/app/components/skeletons/NavigationSkeleton.tsx`)
- Used for navigation menus
- Props: `variant` ('drawer' | 'horizontal')
- Usage: Side navigation, header navigation

### 5. **TabContentSkeleton** (`/app/components/skeletons/TabContentSkeleton.tsx`)
- Used for tabbed interfaces
- Props: `tabCount`, `contentHeight`, `showActions`
- Usage: Logs page, Settings page, Security page

### 6. **FormSkeleton** (`/app/components/skeletons/FormSkeleton.tsx`)
- Used for form layouts
- Props: `sections`, `fieldsPerSection`, `showActions`
- Usage: Settings forms, configuration pages

### 7. **DetailPageSkeleton** (`/app/components/skeletons/DetailPageSkeleton.tsx`)
- Used for detail views with header and stats
- Shows skeleton for page header, stats cards, and content sections
- Usage: Integration details page

### 8. **PageWithNavigationSkeleton** (`/app/components/skeletons/PageWithNavigationSkeleton.tsx`)
- Used for full page loading with navigation
- Shows app bar, side navigation, and content area
- Usage: Console layout initial load

### 9. **ServiceCardSkeleton** (`/app/components/skeletons/ServiceCardSkeleton.tsx`)
- Used for service/connector card grids
- Props: `count`
- Usage: Service setup pages

## Implementation Examples

### Route Level Implementation
```tsx
import { TableSkeleton } from 'components/skeletons';

export default function Integrations() {
  return (
    <ClientOnly fallback={<TableSkeleton rows={10} columns={8} title="Integrations" showActions />}>
      {() => <IntegrationsContent />}
    </ClientOnly>
  );
}
```

### Component Level Implementation
```tsx
import { CardGridSkeleton } from 'components/skeletons';

const Environments = () => {
  const { isLoading, data } = useQuery();
  
  if (isLoading) {
    return <CardGridSkeleton count={4} columns={{ xs: 12, sm: 6, md: 4, lg: 3 }} showActions />;
  }
  
  return <EnvironmentCards data={data} />;
};
```

## Pages with Skeleton Loaders

1. **Dashboard** (`/app/routes/console/dashboard/index.tsx`)
   - Uses: `DashboardSkeleton`
   
2. **Integrations** (`/app/routes/console/integrations/index.tsx`)
   - Uses: `TableSkeleton`
   
3. **Environments** (`/app/routes/console/environments/index.tsx`)
   - Uses: `CardGridSkeleton`
   
4. **Logs** (`/app/routes/console/logs.tsx`)
   - Uses: `TabContentSkeleton`
   
5. **Settings** (`/app/routes/console/settings/index.tsx`)
   - Uses: `TabContentSkeleton`
   
6. **Security** (`/app/routes/console/security/index.tsx`)
   - Uses: `TabContentSkeleton`
   
7. **Integration Details** (`/app/routes/console/integrations/$id.$spId.tsx`)
   - Uses: `DetailPageSkeleton`
   
8. **Console Layout** (`/app/routes/console/index.tsx`)
   - Uses: `PageWithNavigationSkeleton`

## Section Components with Skeletons

1. **API Logs Table** (`/app/sections/logs/api/data-table.tsx`)
   - Uses: `TableSkeleton` when `isLoading`
   
2. **Event Logs Table** (`/app/sections/logs/events/data-table.tsx`)
   - Uses: `TableSkeleton` when `isLoading`
   
3. **Environments Section** (`/app/sections/environments/index.tsx`)
   - Uses: `CardGridSkeleton` when `isLoading || isFetching`

## Best Practices

1. **Use ClientOnly with fallback**: For route-level components, use `ClientOnly` with skeleton as fallback
2. **Check loading states**: For data fetching components, check `isLoading` or `isFetching` states
3. **Match content structure**: Skeleton should match the actual content layout
4. **Consistent animations**: All skeletons use Material-UI's wave animation
5. **Proper sizing**: Use realistic dimensions that match actual content
6. **Accessibility**: Skeletons include proper ARIA labels for screen readers

## Styling Notes

- All skeletons follow sharp design (no rounded corners)
- Use Unizo brand colors where applicable
- Consistent spacing and padding with actual components
- Wave animation for better visual feedback
- Proper contrast for both light and dark modes