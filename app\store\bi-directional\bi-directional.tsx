import { State } from "hooks/useStatus";
import { Service } from "types/service";
import { create } from "zustand";
import { persist } from 'zustand/middleware';

type SelectedEvent = {
   key: string;
   name: string;
 };

 


type SegmentValues = {
   service: Service | null
   bidirectionalDetails: any
   selectAccount: Record<string, any>
   selectedCategoryDetails: {
      key:string,
      value:string,
      label:string
   }
   selectedEvents?: SelectedEvent[];
   expandedEventGroup?: string | null;
   endpointDetails: {
      url: string;
      description?: string;
    };
}

type ValueType = 'service' | 'bidirectionalDetails' | 'selectAccount'|'selectedCategoryDetails' |'selectedEvents'| 'expandedEventGroup' | 'endpointDetails'; 
type Step = number;

interface BidirectionalStore {
   // modal
   openCreate: false,
   setOpenCreate: (openCreate: boolean) => void,
   
   values: SegmentValues
   setValues: (type: ValueType, value: Record<string, any> | Record<string, any>[] | string | number | boolean | null) => void;


   windowStates: {
      bidirectionalDetails: State,
   }
   setWindowStates: (type: 'bidirectionalDetails', value: State) => void
   resetWindowStates: () => void

   mode?: Mode
   setMode: (mode: Mode) => void

   step: Step,
   move: (type: 'next' | 'prev') => void
   jump: (step: Step) => void

   done: boolean,
   setDone: (isDone: boolean) => void,

   reset: () => void;
}

const STORE_KEY: string = 'bi-directional';

const DEFAULT_VALUES: any = {
   openCreate: false,
   values: {
      service: null,
      bidirectionalDetails: null,
      selectAccount: null,
      selectedCategoryDetails:{
         key:'',
         label:'',
         value:'',
      },
      selectedEvents: [],
      expandedEventGroup: null,
      endpointDetails: {
         url: '',
         description: ''
       },
   },
   mode: 'create',
   step: 0,
   acceptedWelcome: false,
   done: false,
   windowStates: {
      bidirectionalDetails: false,
   }
}

const useBidirectional = create(
   persist<BidirectionalStore>(
      (set, get) => ({
         // open create
         openCreate: DEFAULT_VALUES.openCreate,
         setOpenCreate: (openCreate: any) => {
            set((prevState) => ({ ...prevState, openCreate }))
         },

         // segment values
         values: DEFAULT_VALUES.values,
         setValues: (type, value) => {
            const { values, ...rest } = get();
            set({
               ...rest,
               values: {
                  ...values,
                  [type]: value
               }
            })
         },

         windowStates: DEFAULT_VALUES?.windowStates,
         setWindowStates: (type, value) => {
            set((prevState) => ({ ...prevState, windowStates: { [type]: value } }))
         },

         resetWindowStates: () => {
            set({
               ...get(),
               windowStates: DEFAULT_VALUES?.windowStates
            })
         },

         mode: DEFAULT_VALUES.mode,
         setMode: (mode: Mode) => {
            set((prevState) => ({ ...prevState, mode }))
         },

         // step values
         step: DEFAULT_VALUES.step as Step,
         move: (type) => {
            const { step, ...rest } = get();
            set({
               ...rest,
               step: (() => {
                  return (type === 'next' ? step + 1 : step - 1)
               })()
            })
         },
         jump: (step) => {
            set((prevState) => (
               // hard move, setting the index what you are passing
               { ...prevState, step }
            ))
         },

         // conclusion message, container for agree the conclusion
         done: DEFAULT_VALUES.done,
         setDone: (done) => {

            set((prev) => {
               return { ...prev, done }
            });

            // rest the state
            get().reset();
         },

         reset: () => {
            set(DEFAULT_VALUES as Partial<BidirectionalStore>)
         }
      }),
      {
         name: `${STORE_KEY}_${(window as any).authUserOrgId}`,
      }
   )
);

export default useBidirectional;