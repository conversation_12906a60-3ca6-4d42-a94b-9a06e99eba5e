import {
  CalendarFilled,
  DeleteOutlined,
  EditOutlined,
  LoadingOutlined,
  PlusOutlined,
  StarFilled,
  StarOutlined,
  SafetyCertificateOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import {
  Box,
  Button,
  Card,
  CardContent,
  Stack,
  Tooltip,
  Typography,
  Grid,
  styled,
} from "@mui/material";
import { useInfiniteQuery, useMutation } from "@tanstack/react-query";
import Dot from "components/@extended/Dot";
import IconButton from "components/@extended/IconButton";
import MainCard from "components/MainCard";
import useEnvironment from "hooks/api/useEnvironment";
import { useEffect, useMemo, useRef, useState } from "react";
import CreateEnv from "./create-env";
import Environment from "types/environment";
import { getIsEnvDefault } from "./utils";
import { AxiosResponse } from "axios";
import { EnvironmentSwitchRequestState } from "constants/environment";
import { toast } from "sonner";
import { parseError } from "lib/utils";
import SkeletonLoader from "components/@extended/Skeleton";
import { formatTimeAgo } from "utils/date";
import { ResponseModel } from "types/common";
import DeleteConfirmation from "./delete-confirmation";
import useUserDetails from "store/user";
import WarningAmberOutlinedIcon from "@mui/icons-material/WarningAmberOutlined";
import LockOutlinedIcon from "@mui/icons-material/LockOutlined";
import { format } from "date-fns";
import InfoIcon from "@mui/icons-material/Info";
import { getSubscriptionFlags } from "utils/subscription";
import Environment_set from "./constants";
import { alpha, useTheme } from "@mui/material/styles";

// Styled Components
const StyledEnvironmentCard = styled(Card)(({ theme, color }) => ({
  width: "100%",
  height: 200,
  borderRadius: theme.spacing(2),
  borderLeft: `4px solid ${color}`,
  padding: theme.spacing(3),
  display: "flex",
  flexDirection: "column",
  justifyContent: "space-between",
  position: "relative",
  boxShadow: `0 4px 16px 0 ${color}33`,
  transition: "box-shadow 0.2s, border-color 0.2s",
  "&:hover": {
    boxShadow: `0 6px 32px 4px ${color}55`,
    borderColor: color,
  },
}));

const StyledTemplateCard = styled(Card)<{ color: string; locked: boolean }>(
  ({ theme, color, locked }) => ({
    width: "100%",
    height: 200,
    textAlign: "center",
    borderStyle: "dotted",
    borderColor: color,
    borderWidth: 2,
    borderRadius: theme.spacing(3),
    boxShadow: "0 4px 16px 0 rgba(25, 118, 210, 0.1)",
    padding: theme.spacing(3),
    cursor: locked ? "not-allowed" : "pointer",
    opacity: locked ? 0.6 : 1,
    transition: "all 0.3s cubic-bezier(.4,0,.2,1)",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "space-between",
    "&:hover": locked
      ? {}
      : {
          boxShadow: `0 6px 32px 4px ${color}44`,
          borderColor: color,
          background: `linear-gradient(135deg, #fff 0%, ${color}10 100%)`,
          transform: "scale(1.05)",
        },
  })
);

const StyledPlanCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.spacing(3),
  border: "none",
  boxShadow: `0 2px 12px 0 ${theme.palette.primary.main}14`,
  marginBottom: theme.spacing(1.5),
  padding: 0,
}));

const StyledIconContainer = styled(Box)(({ theme, color }) => ({
  border: `0.5px dotted ${color}`,
  borderRadius: "50%",
  width: 48,
  height: 48,
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  margin: "0 auto",
  backgroundColor: `${color}15`,
  flexShrink: 0,
}));

const StyledPlanBadge = styled(Box)(({ theme }) => ({
  paddingLeft: theme.spacing(1.5),
  paddingRight: theme.spacing(1.5),
  paddingTop: theme.spacing(0.2),
  paddingBottom: theme.spacing(0.2),
  borderRadius: theme.spacing(2),
  fontSize: 13,
  fontWeight: 700,
  background: alpha(theme.palette.primary.light, 0.3),
  color: theme.palette.primary.main,
  letterSpacing: 0.5,
  marginLeft: theme.spacing(1),
}));

// Theme-based styles
const cardHeaderStyles = {
  display: "flex",
  alignItems: "center",
  gap: 1.5,
  mb: 1,
};

const cardContentStyles = {
  flex: 1,
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  py: 0.5,
};

const actionButtonsStyles = {
  display: "flex",
  gap: 0.5,
};

const titleStyles = {
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  fontSize: { xs: 16, md: 18 },
  letterSpacing: 0.1,
};

const descriptionStyles = {
  overflow: "hidden",
  textOverflow: "ellipsis",
  display: "-webkit-box",
  WebkitLineClamp: 2,
  WebkitBoxOrient: "vertical",
  lineHeight: 1.5,
  fontSize: 14,
  mb: 0.5,
};

const templateCardTitleStyles = {
  overflow: "hidden",
  textOverflow: "ellipsis",
  display: "-webkit-box",
  WebkitLineClamp: 1,
  WebkitBoxOrient: "vertical",
  fontSize: { xs: 14, md: 14 },
  letterSpacing: 0.1,
};

const templateCardDescriptionStyles = {
  overflow: "hidden",
  textOverflow: "ellipsis",
  display: "-webkit-box",
  WebkitLineClamp: 2,
  WebkitBoxOrient: "vertical",
  lineHeight: 1.5,
  fontSize: 14,
};

const planInfoStyles = {
  display: "flex",
  alignItems: "center",
  gap: 2,
};

const planTitleStyles = {
  color: "#222",
  fontSize: 16,
};

const planDescriptionStyles = {
  color: "#555",
  fontSize: 14,
};

const iconStyles = {
  fontSize: 28,
};

const getPlanEnvironments = (plan: string) => {
  const planKey = plan?.toUpperCase();
  return Environment_set?.plans[planKey]?.environments || [];
};

function getEnvLimitMessage(isLaunch: boolean, isEnterprise: boolean) {
  if (isLaunch) {
    return "You can create up to 1 environment with your current subscription.";
  }
  if (isEnterprise) {
    return "You can create up to 5 environments with your current subscription.";
  }
  return "Your subscription doesn't support creating environments. Upgrade to create and manage environments.";
}

function EnvironmentCard({
  env,
  onEdit,
  onDelete,
  onStar,
  isDefault,
}: {
  env: any;
  onEdit: (env: any) => void;
  onDelete: (env: any) => void;
  onStar: (env: any) => void;
  isDefault: boolean;
}) {
  const theme = useTheme();
  
  return (
    <StyledEnvironmentCard variant="outlined" color={env.color}>
      <Box sx={cardHeaderStyles}>
        <Box display="flex" alignItems="center" gap={1} flex={1} minWidth={0}>
          <Dot color={env.color} />
          <Typography variant="subtitle1" fontWeight={600} sx={titleStyles}>
            {env.name}
          </Typography>
        </Box>
        <Box sx={actionButtonsStyles}>
             {/* <Tooltip title={isDefault ? "Default Environment" : "Mark as Default"}>
            <IconButton size="small" onClick={() => onStar(env)}>
              <StarOutlined style={{ color: isDefault ? "#FFD600" : "#bdbdbd" }} />
            </IconButton>
          </Tooltip> */}
          <Tooltip title="Edit">
            <IconButton
              size="small"
              onClick={() => onEdit(env)}
              aria-label="Edit environment"
            >
              <EditOutlined style={{ ...iconStyles, fontSize: 18, color: env.color }} />
            </IconButton>
          </Tooltip>
            {/* <Tooltip title="Delete">
            <IconButton size="small" onClick={() => onDelete(env)}>
              <DeleteOutlined />
            </IconButton>
          </Tooltip> */}
        </Box>
      </Box>
      
      <Box sx={cardContentStyles}>
        <Typography variant="body2" color="text.secondary" sx={descriptionStyles}>
          {env.description || "No description provided"}
        </Typography>
      </Box>
    </StyledEnvironmentCard>
  );
}

const getIconComponent = (iconName: string, color: string) => {
  const iconStyle = { ...iconStyles, color };
  const iconComponents = {
    plus: <PlusOutlined style={iconStyle} />,
    secure: <SafetyCertificateOutlined style={iconStyle} />,
    warning: <ExclamationCircleOutlined style={iconStyle} />,
    check: <CheckCircleOutlined style={iconStyle} />,
    calendar: <CalendarFilled style={iconStyle} />,
    info: <InfoCircleOutlined style={iconStyle} />,
    "test-tube": <InfoCircleOutlined style={iconStyle} />,
    code: <InfoCircleOutlined style={iconStyle} />,
    puzzle: <InfoCircleOutlined style={iconStyle} />,
  };

  return iconComponents[iconName] || <InfoCircleOutlined style={iconStyle} />;
};

function LaunchStyleEnvironmentCard({
  card,
  onClick,
}: {
  card: any;
  onClick: (key: string, name: string) => void;
}) {
  return (
    <StyledTemplateCard
      variant="outlined"
      color={card.colorPicker}
      locked={card.locked}
      onClick={() => (card.locked ? undefined : onClick(card.key, card.name))}
    >
      <StyledIconContainer color={card.colorPicker}>
        {card.locked ? (
          <LockOutlinedIcon sx={{ ...iconStyles, color: card.colorPicker }} />
        ) : (
          getIconComponent(card.icon, card.colorPicker)
        )}
      </StyledIconContainer>
      
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          gap: 0.5,
        }}
      >
        <Typography variant="subtitle1" fontWeight={700} sx={templateCardTitleStyles}>
          {card.name}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={templateCardDescriptionStyles}>
          {card.description}
        </Typography>
      </Box>
    </StyledTemplateCard>
  );
}

const Environments = () => {
  const theme = useTheme();
  const { subscriptions } = useUserDetails();

  const { actionEnvironmentQueryConfig, getAllEnvironmentInfinateQueryConfig } =
    useEnvironment();

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selected, setSelected] = useState<Environment.Root | null>(null);
  const [selectedKey, setSelectedKey] = useState<string | null>(null);
  const [currenttype, setCurrentType] = useState<string | null>(null);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState<boolean>(false);
  const [defailtEnv, setDefaultEnv] = useState<Environment.Root | null>(null);
  const [mode, setMode] = useState<"create" | "edit">("create");

  const loadMoreRef = useRef<HTMLDivElement>(null);

  const {
    isLoading,
    isFetching,
    data,
    fetchNextPage: loadMore,
    refetch: refetchEnvironments,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    ...getAllEnvironmentInfinateQueryConfig(),
  });

  const { mutateAsync: attemptToSwitchEnvironment, isPending: isUpdating } =
    useMutation({
      ...actionEnvironmentQueryConfig(),
      onSettled(resp) {
        setDefaultEnv((resp as AxiosResponse<Environment.Root>).data);
        refetchEnvironments();
      },
    });

  const environments = (data?.pages?.reduce(
    (
      acc: (Environment.Root & { color: string })[],
      page: AxiosResponse<ResponseModel<Environment.Root>>
    ) => {
      const mapped =
        page?.data?.data?.map((env) => ({
          ...env,
          color: (env as any).colorPicker || (env as any).color || "#1976d2",
        })) || [];
      return [...acc, ...mapped];
    },
    []
  ) ?? []) as (Environment.Root & { color: string })[];

  const highestTier = getSubscriptionFlags(subscriptions);
  const isEnterprise = highestTier === "Enterprise";
  const isLaunch = highestTier === "Launch";
  const isFreePlan = highestTier === "Free";

  const onEdit = (requestedEnv: Environment.Root) => {
    setSelected(requestedEnv);
    setMode("edit");
    onModalOpen();
  };

  const onModalOpen = (key?: string, name?: string) => {
    setIsOpen(true);
    setSelectedKey(key || null);
    setCurrentType(name ?? "TEST");
  };

  const onModalClose = () => {
    setIsOpen(false);
    setMode("create");
    setSelected(null);
  };

  const onConfirmationModalOpen = (requestedEnv: Environment.Root) => {
    setIsConfirmationModalOpen(true);
    setSelected(requestedEnv);
  };

  const onConfirmationModalClose = () => {
    setIsConfirmationModalOpen(false);
    setSelected(null);
  };

  const onActiveEnvironment = (environment: Environment.Root) => {
    setSelected(environment);
    try {
      toast.promise(
        attemptToSwitchEnvironment({
          environmentId: environment.id,
          payload: { type: EnvironmentSwitchRequestState.DefaultRequest },
        }),
        {
          loading: "Switching...",
          success: (resp) => {
            return `Switched to ${(resp as AxiosResponse<Environment.Root>)?.data?.name}`;
          },
          error: (err) => {
            return parseError(err?.response?.data)?.message;
          },
        }
      );
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (environments.length && !isFetching) {
      setDefaultEnv(
        environments.find((e) => getIsEnvDefault(e)) as Environment.Root
      );
    }
  }, [isFetching]);

  const planEnvironments = useMemo(() => {
    const planEnvs = getPlanEnvironments(highestTier || "Free") || [];

    return planEnvs.filter((planEnv : any) => {
      if (isFreePlan && planEnv.locked === true) {
        return true;
      }

      const existingEnv = environments.find((env) => env.key === planEnv.key);
      return !existingEnv;
    });
  }, [highestTier, environments, isFreePlan]);

  const renderEnvironmentUI = () => {
    return (
      <>
        {/* Plan Information Card */}
        <StyledPlanCard variant="outlined">
          <CardContent sx={{ p: 3 }}>
            <Box sx={planInfoStyles}>
              <InfoIcon
                sx={{
                  fontSize: 24,
                  color: theme.palette.primary.main,
                  flexShrink: 0,
                }}
              />
              <Box>
                <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                  <Typography variant="subtitle1" fontWeight={700} sx={planTitleStyles}>
                    Current Plan
                  </Typography>
                  <StyledPlanBadge component="span">
                    {highestTier}
                  </StyledPlanBadge>
                </Box>
                <Typography variant="body2" sx={planDescriptionStyles}>
                  {getEnvLimitMessage(isLaunch, isEnterprise)}
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </StyledPlanCard>

        {/* Combined Grid for Template Cards and Created Environments */}
        <Grid container spacing={3}>
          {/* Template Cards */}
          {planEnvironments.map((card :any, idx : number) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={`plan-card-${idx}`}>
              <LaunchStyleEnvironmentCard
                card={card}
                onClick={onModalOpen}
              />
            </Grid>
          ))}

          {/* Created Environment Cards */}
          {environments.map((env) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={`env-${env.id}`}>
              <EnvironmentCard
                env={env}
                onEdit={onEdit}
                onDelete={onConfirmationModalOpen}
                onStar={onActiveEnvironment}
                isDefault={getIsEnvDefault(env)}
              />
            </Grid>
          ))}
        </Grid>
      </>
    );
  };

  return (
    <Stack gap={2} ref={loadMoreRef}>
      <Stack spacing={2}>{renderEnvironmentUI()}</Stack>

      <CreateEnv
        refetchEnvironments={refetchEnvironments}
        isOpen={isOpen}
        onClose={onModalClose}
        mode={mode}
        slelectedKey={selectedKey}
        selectedType={currenttype}
        selected={selected}
        existingEnvironments={environments}
      />
      <DeleteConfirmation
        isOpen={isConfirmationModalOpen}
        onClose={onConfirmationModalClose}
        selected={selected}
        onSuccess={() => {
          onConfirmationModalClose();
          refetchEnvironments();
        }}
      />
    </Stack>
  );
};

export default Environments;