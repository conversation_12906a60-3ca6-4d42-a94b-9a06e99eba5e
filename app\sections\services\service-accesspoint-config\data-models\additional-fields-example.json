[{"name": "customer_profile", "displayName": "Customer Profile", "type": "object", "description": "Complete customer profile information", "required": true, "children": [{"name": "customer_id", "displayName": "Customer ID", "type": "string", "description": "Unique identifier for the customer", "required": true, "format": "uuid"}, {"name": "email", "displayName": "Email Address", "type": "string", "description": "Customer's primary email address", "required": true, "format": "email", "validation": {"pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"}}, {"name": "personal_info", "displayName": "Personal Information", "type": "object", "description": "Customer's personal details", "children": [{"name": "first_name", "displayName": "First Name", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 50}}, {"name": "last_name", "displayName": "Last Name", "type": "string", "required": true, "validation": {"minLength": 1, "maxLength": 50}}, {"name": "date_of_birth", "displayName": "Date of Birth", "type": "date", "format": "date"}, {"name": "phone_numbers", "displayName": "Phone Numbers", "type": "array", "arrayItemType": "string", "description": "List of customer phone numbers", "validation": {"pattern": "^\\+?[1-9]\\d{1,14}$"}}]}, {"name": "address", "displayName": "Address", "type": "object", "description": "Customer's physical address", "children": [{"name": "street", "displayName": "Street Address", "type": "string", "required": true}, {"name": "city", "displayName": "City", "type": "string", "required": true}, {"name": "state", "displayName": "State/Province", "type": "string", "validation": {"maxLength": 2}}, {"name": "postal_code", "displayName": "Postal Code", "type": "string", "validation": {"pattern": "^[0-9]{5}(-[0-9]{4})?$"}}, {"name": "country", "displayName": "Country", "type": "string", "required": true, "enum": ["US", "CA", "UK", "AU", "DE", "FR", "JP", "IN"]}]}]}, {"name": "order_details", "displayName": "Order Details", "type": "object", "description": "E-commerce order information", "children": [{"name": "order_id", "displayName": "Order ID", "type": "string", "required": true, "format": "uuid"}, {"name": "order_date", "displayName": "Order Date", "type": "date", "required": true, "format": "date-time"}, {"name": "status", "displayName": "Order Status", "type": "string", "required": true, "enum": ["pending", "processing", "shipped", "delivered", "cancelled", "refunded"], "defaultValue": "pending"}, {"name": "total_amount", "displayName": "Total Amount", "type": "number", "required": true, "format": "decimal", "validation": {"min": 0, "max": 999999.99}}, {"name": "items", "displayName": "Order Items", "type": "array", "arrayItemType": "object", "description": "List of items in the order", "children": [{"name": "product_id", "displayName": "Product ID", "type": "string", "required": true}, {"name": "quantity", "displayName": "Quantity", "type": "number", "required": true, "format": "integer", "validation": {"min": 1, "max": 999}}, {"name": "unit_price", "displayName": "Unit Price", "type": "number", "required": true, "format": "decimal", "validation": {"min": 0}}, {"name": "discount_percentage", "displayName": "Discount %", "type": "number", "format": "integer", "defaultValue": 0, "validation": {"min": 0, "max": 100}}]}, {"name": "shipping_info", "displayName": "Shipping Information", "type": "object", "children": [{"name": "carrier", "displayName": "Shipping Carrier", "type": "string", "enum": ["USPS", "UPS", "FedEx", "DHL", "Amazon"]}, {"name": "tracking_number", "displayName": "Tracking Number", "type": "string"}, {"name": "estimated_delivery", "displayName": "Estimated Delivery", "type": "date", "format": "date"}]}]}, {"name": "product_catalog", "displayName": "Product Catalog", "type": "object", "description": "Product information for catalog", "children": [{"name": "sku", "displayName": "SKU", "type": "string", "required": true, "validation": {"pattern": "^[A-Z0-9-]+$"}}, {"name": "name", "displayName": "Product Name", "type": "string", "required": true, "validation": {"minLength": 3, "maxLength": 200}}, {"name": "category", "displayName": "Category", "type": "string", "required": true, "enum": ["Electronics", "Clothing", "Home & Garden", "Sports", "Books", "Toys", "Other"]}, {"name": "price", "displayName": "Price", "type": "number", "required": true, "format": "decimal", "validation": {"min": 0.01, "max": 99999.99}}, {"name": "in_stock", "displayName": "In Stock", "type": "boolean", "required": true, "defaultValue": true}, {"name": "tags", "displayName": "Product Tags", "type": "array", "arrayItemType": "string", "description": "Tags for product categorization and search"}, {"name": "specifications", "displayName": "Product Specifications", "type": "object", "description": "Technical specifications and attributes", "children": [{"name": "weight", "displayName": "Weight (kg)", "type": "number", "format": "decimal"}, {"name": "dimensions", "displayName": "Dimensions", "type": "object", "children": [{"name": "length", "displayName": "Length (cm)", "type": "number", "format": "decimal"}, {"name": "width", "displayName": "Width (cm)", "type": "number", "format": "decimal"}, {"name": "height", "displayName": "Height (cm)", "type": "number", "format": "decimal"}]}]}]}, {"name": "user_preferences", "displayName": "User Preferences", "type": "object", "description": "User settings and preferences", "children": [{"name": "language", "displayName": "Language", "type": "string", "required": true, "enum": ["en", "es", "fr", "de", "it", "pt", "ja", "zh"], "defaultValue": "en"}, {"name": "timezone", "displayName": "Timezone", "type": "string", "required": true, "defaultValue": "UTC"}, {"name": "notifications", "displayName": "Notification Settings", "type": "object", "children": [{"name": "email_notifications", "displayName": "Email Notifications", "type": "boolean", "defaultValue": true}, {"name": "sms_notifications", "displayName": "SMS Notifications", "type": "boolean", "defaultValue": false}, {"name": "push_notifications", "displayName": "Push Notifications", "type": "boolean", "defaultValue": true}, {"name": "notification_frequency", "displayName": "Notification Frequency", "type": "string", "enum": ["realtime", "daily", "weekly", "monthly"], "defaultValue": "daily"}]}]}]