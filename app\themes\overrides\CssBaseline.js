// ==============================|| OVERRIDES - CSS BASELINE ||============================== //

export default function CssBaseline(theme) {
  return {
    MuiCssBaseline: {
      styleOverrides: {
        '@global': {
          html: {
            WebkitFontSmoothing: 'antialiased',
            MozOsxFontSmoothing: 'grayscale',
            fontSmooth: 'always',
            textRendering: 'optimizeLegibility',
            fontVariantLigatures: 'normal',
            fontFeatureSettings: '"kern" 1, "liga" 1, "calt" 1',
          },
          body: {
            WebkitFontSmoothing: 'antialiased',
            MozOsxFontSmoothing: 'grayscale',
            fontSmooth: 'always',
            textRendering: 'optimizeLegibility',
          },
          // Ensure all text elements have proper font smoothing
          '*, *::before, *::after': {
            WebkitFontSmoothing: 'antialiased',
            MozOsxFontSmoothing: 'grayscale',
          },
          // Improve font rendering for specific elements
          'h1, h2, h3, h4, h5, h6': {
            fontVariantLigatures: 'normal',
            fontFeatureSettings: '"kern" 1, "liga" 1, "calt" 1',
            letterSpacing: 'normal',
          },
          // Smoother rendering for body text
          'p, li, td, th, div, span': {
            WebkitTextSizeAdjust: '100%',
            textRendering: 'optimizeLegibility',
          },
          // Better rendering for form elements
          'input, textarea, select, button': {
            WebkitFontSmoothing: 'antialiased',
            MozOsxFontSmoothing: 'grayscale',
            fontSmooth: 'always',
          },
          // Improve rendering on high-DPI displays
          '@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi)': {
            body: {
              WebkitFontSmoothing: 'antialiased',
              MozOsxFontSmoothing: 'grayscale',
            }
          }
        }
      }
    }
  };
}