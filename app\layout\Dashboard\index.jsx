import { useEffect } from 'react';

import { useLocation } from "@remix-run/react";
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Toolbar from '@mui/material/Toolbar';

// project import
import Drawer from './Drawer';
import Header from './Header';
import Footer from './Footer';
import HorizontalBar from './Drawer/HorizontalBar';
import Loader from '../../components/Loader';
import Breadcrumbs from '../../components/@extended/Breadcrumbs';
import { DashboardContainer, MainContent } from './Dashboard.styles';
import OnboardingDialog from 'sections/onboarding/OnboardingDialog';
// import { SessionProvider } from '../../contexts/SessionProvider';

import { MenuOrientation } from 'config';
import useConfig from 'hooks/useConfig';
import { Grid, Stack, Typography } from '@mui/material';

import { handlerDrawerOpen, useGetMenuMaster } from 'api/menu';
import { useBreadcrumbs } from 'hooks/useBreadcrumbs';
import ErrorBoundary from 'hoc/error-boundaries/global';
import { useSessionTimeout } from 'components/sessionTimeout';
import { getSubscriptionFlags } from "utils/subscription";
import useUserDetails from "store/user";
import { useTrialExpiration } from 'hooks/api/useTrialExpiration';
import { TrialExpiredDialog } from 'components/dialogs';


// ==============================|| MAIN LAYOUT ||============================== //

export default function DashboardLayout({ children }) {
  const { item } = useBreadcrumbs();
  const timeOut = useSessionTimeout();
  const { subscriptions } = useUserDetails();
  const highestTier = getSubscriptionFlags(subscriptions);
  const { isTrialExpired, latestExpirationDate } = useTrialExpiration();
  
  // Calculate days used (assuming 15-day trial)
  const calculateDaysUsed = () => {
    if (!latestExpirationDate) return 15;
    const expirationDate = new Date(latestExpirationDate);
    const trialStartDate = new Date(expirationDate);
    trialStartDate.setDate(trialStartDate.getDate() - 15); // 15-day trial
    const today = new Date();
    const daysUsed = Math.floor((today - trialStartDate) / (1000 * 60 * 60 * 24));
    return Math.min(Math.max(daysUsed, 1), 15); // Clamp between 1 and 15
  };

  const location = useLocation();
  const isHomePage = location.pathname === "/console/dashboard";
  const isQuickStartPage = location.pathname === "/console/quick-start" || location.pathname === "/console/quick-start_";
  const { menuMasterLoading } = useGetMenuMaster();
  const downXL = useMediaQuery((theme) => theme.breakpoints.down('xl'));
  const downLG = useMediaQuery((theme) => theme.breakpoints.down('lg'));

  const { container, miniDrawer, menuOrientation } = useConfig();

  const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG;

  // set media wise responsive drawer
  useEffect(() => {
    if (!miniDrawer) {
      handlerDrawerOpen(!downXL);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [downXL]);

  if (menuMasterLoading) return <Loader />;

  return (
    // TODO: Uncomment SessionProvider when backend endpoints are ready
    // <SessionProvider enabled={true}>
    <>
      <DashboardContainer>
        {timeOut}
        <Header breadcrumbs={<Breadcrumbs />} />
        {!isHorizontal ? <Drawer /> : <HorizontalBar />}

        <MainContent component="main">
          <Container
            maxWidth={container ? 'xl' : false}
            sx={{
              ...(container ? { px: { xs: 0, sm: 2 } } : { px: { xs: 1 } }),
              position: 'relative',
              minHeight: 'calc(100vh - 110px)',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            <Stack gap={2}>

              {/* breadcrumbs block */}
              {(item?.title || item?.description) && item?.id !== 'getting-started' && !isQuickStartPage && (
                <Grid container>
                  <Grid item xs={12}>
                    {item?.title && (
                      <Typography variant="h4" mb={1}>{item?.title}</Typography>
                    )}
                  </Grid>
                  <Grid item xs={12} lg={8}>
                    {item?.description && (
                      <Typography className='font-normal' variant={'body1'} color={'secondary.600'} mb={1}>{item?.description}</Typography>
                    )}
                  </Grid>
                </Grid>
              )}
              {/* breadcrumbs block */}
              {children}
            </Stack>
            <Footer />
          </Container>
        </MainContent>
      </DashboardContainer>
      <OnboardingDialog />
      <TrialExpiredDialog 
        open={isTrialExpired} 
        daysUsed={calculateDaysUsed()}
        onSignOut={() => {
          // Add sign out logic here
          window.location.href = '/logout';
        }}
      />
    </>
    // </SessionProvider>
  );
}
