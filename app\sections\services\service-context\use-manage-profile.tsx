import { createContext, useContext, useEffect, useMemo, useState } from "react";

import { useGetServiceProfile } from "hooks/api/service-profile/useGetServiceProfile";
import { useGetAccessPoint } from "hooks/api/use-accesspoint-config/useGetAccessPoint";
import useDebounce from "hooks/use-debounce";


import useServiceConfig from "store/setup-service";
import useUserDetails from "store/user";
import { ServiceProfile } from "types/service-profile"
import { Pagination } from "types/utils";

import { useSearchParams, } from '@remix-run/react';

const ServiceContext = createContext<any>(null);

export const ServiceProvider = ({ children }: any) => {

   // pagination query
   const [pagination] = useState<Pagination<any>>({ limit: 33, offset: 0, total: 0 });

   // search query
   const [search, setSearch] = useState('');
   const debouncedValue = useDebounce(search, 500);

   const [searchParams, setSearchParams] = useSearchParams({ type: '', connectorId: '' });
   const domain = searchParams.get('type');
   const selectedServiceId = searchParams.get('connectorId');

   const { categories } = useUserDetails();

   const {
      selectedService,
      setSelectedService,
      resetSelectedService,
   } = useServiceConfig();

   const {
      serviceProfileClient,
      createService: createServiceProp,
      updateService: updateServiceProp,
      updateVersions,
   } = useGetServiceProfile({
      searchOptions: {
         search: {
            type: domain as string,
            ...pagination,
            search: debouncedValue
         }
      }
   });

   const createService = (arg: any) => {
      createServiceProp(arg, (service: any) => {
         setSelectedService({ ...selectedService, service } as any)
      })
   };
   const updateService = (arg: any) => {
      updateServiceProp(arg, (service: any) => {
         setSelectedService({ ...selectedService, service } as any)
      })
   };

   const {
      serviceProfileAccessPoints,
      isLoading: isLoadingAccessPoints,
      attemptUpdateAccessPointsSelection: updateAccessPointsSelection,
      clearClient
   } = useGetAccessPoint({
      serviceProfileId: selectedService?.id,
      serviceId: selectedService?.service?.id
   });

   const clearSelectedService = () => {
      resetSelectedService();
   }

   const menu = useMemo(() => {
      return categories?.map(({ label: title, key: id, disabled = false, released, isNew }) => (
         { title, id, disabled, released, isNew }
      ))
   }, [categories])

   const isCategoryDisabled = useMemo(() => {
      return menu?.find((i) => i?.id === domain)?.disabled ?? false
   }, [domain, menu])

   const filteredProfiles = useMemo(() => {
      if (!serviceProfileClient?.data) return [];

      return (serviceProfileClient?.data as any)
         .sort((a: ServiceProfile, b: ServiceProfile) => a?.name.localeCompare(b?.name));
   }, [serviceProfileClient?.data]);

   const selectedIndex = useMemo(() => {
      const index = filteredProfiles?.findIndex((i: ServiceProfile) => i?.id === selectedServiceId);
      return index >= 0 ? index : null
   }, [filteredProfiles, selectedServiceId])

   const setDomain = (selectedCategory: string) => {
      setSearchParams((prev) => {
         prev.set('type', selectedCategory);
         prev.delete('connectorId')
         return prev;
      })
   }

   const onSelectService = (_selectedServiceIndex: string, requestServiceProfile: ServiceProfile) => {
      setSearchParams((prev) => {
         prev.set('connectorId', requestServiceProfile?.id)
         return prev;
      }, { replace: true })
   }

   useEffect(() => {
      clearSelectedService();
   }, [domain])

   useEffect(() => {
      if (!domain) {
         setDomain(menu?.length ? menu?.[0]?.id : '')
      }
   }, [menu, domain]);

   useEffect(() => {
      if (filteredProfiles && filteredProfiles?.length) {
         setSelectedService(filteredProfiles?.find(
            (i: ServiceProfile) => i?.id === selectedServiceId))
      }
   }, [selectedServiceId, filteredProfiles])

   return (
      <ServiceContext.Provider
         value={{
            domain,
            setDomain,
            isCategoryDisabled,

            // menu
            menu,

            profiles: filteredProfiles,
            isProfileLoading: serviceProfileClient?.isFetching,

            selectedIndex,
            selectedService,

            // function for create service
            createService,
            // function for update service
            updateService,

            // flush
            refetchAll: () => {
               clearSelectedService();
               // make sure to clear the accessPoint client queries
               clearClient()
               serviceProfileClient.refetch();
            },

            // search event
            onSearch: (query: string) => void setSearch(query),

            // event for select service
            onSelectService,

            // managing version,
            updateVersions,


            // <-------------- ACCESS POINTS -------------->
            serviceProfileAccessPoints,
            isLoadingAccessPoints,
            // manage accessPoint selection
            updateAccessPointsSelection
         }}
      >
         {children}
      </ServiceContext.Provider>
   )
}

export default () => {
   try {
      return useContext(ServiceContext)
   } catch (error) {
      throw new Error('hook should be wrapped within the serviceProvider context')
   }
}