import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Paper,
  Stack,
  Chip,
  useTheme,
  alpha,
  Tooltip,
} from '@mui/material';
import InputAdornment  from '@mui/material/InputAdornment';
import IconButton from '@mui/material/IconButton';
import { Globe, Copy, ExternalLink, Info, Webhook, Sparkles } from 'lucide-react';
import useOnboardingStore from 'store/onboarding';
import { useServiceProfile } from 'hooks/useServiceProfile';
import { WebhookData } from 'store/onboarding';
import { getDomainByValue } from 'data/domains';

export default function WebhookConfiguration() {
  const theme = useTheme();
  const { selectedCategories, webhookConfigs, setWebhookConfigs } = useOnboardingStore();
  const { getAllDomains } = useServiceProfile();
  const [localWebhooks, setLocalWebhooks] = useState<WebhookData[]>([]);

  const allDomains = getAllDomains();

  useEffect(() => {
    // Initialize webhook data for selected categories + platform webhook
    const platformWebhook = webhookConfigs.find(wc => wc.category === 'PLATFORM') || 
      { category: 'PLATFORM', url: '', isValid: true };
    
    const categoryWebhooks = selectedCategories.map(category => {
      const existing = webhookConfigs.find(wc => wc.category === category);
      return existing || { category, url: '', isValid: true };
    });
    
    setLocalWebhooks([platformWebhook, ...categoryWebhooks]);
  }, [selectedCategories, webhookConfigs]);

  const validateUrl = (url: string): boolean => {
    if (!url) return true; // Empty is valid (optional)
    try {
      new URL(url);
      return url.startsWith('https://') || url.startsWith('http://');
    } catch {
      return false;
    }
  };

  const handleUrlChange = (category: string, value: string) => {
    const isValid = validateUrl(value);
    const updatedWebhooks = localWebhooks.map(webhook => 
      webhook.category === category 
        ? { ...webhook, url: value, isValid } 
        : webhook
    );
    setLocalWebhooks(updatedWebhooks);
    setWebhookConfigs(updatedWebhooks);
  };

  const getWebhookData = (category: string) => {
    return localWebhooks.find(wc => wc.category === category) || { category, url: '', isValid: true };
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const getDomainHookType = (category: string) => {
    const domain = allDomains.find(d => d.key === category);
    return domain?.hook || null;
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom fontWeight={600}>
        Configure Webhooks
      </Typography>
      <Typography variant="body2" color="text.secondary" mb={3}>
        Set up webhook endpoints to receive real-time updates
      </Typography>


      <Stack spacing={2}>
        {/* Platform Webhook - Always Required */}
        <Paper
          variant="outlined"
          sx={{
            p: 3,
            borderRadius: 2,
            borderColor: theme.palette.divider,
            backgroundColor: theme.palette.background.paper,
            '&:hover': {
              borderColor: alpha(theme.palette.primary.main, 0.3),
              backgroundColor: alpha(theme.palette.action.hover, 0.02),
            },
          }}
        >
          <Stack spacing={2}>
            <Stack direction="row" spacing={1} alignItems="center">
              <Webhook size={20} color={theme.palette.primary.main} />
              <Typography variant="body1" fontWeight={600} color="primary">
                Platform Event Listener
              </Typography>
            </Stack>

            <TextField
              fullWidth
              placeholder="https://your-domain.com/platform/webhook"
              value={getWebhookData('PLATFORM').url}
              onChange={(e) => handleUrlChange('PLATFORM', e.target.value)}
              error={!getWebhookData('PLATFORM').isValid}
              helperText={!getWebhookData('PLATFORM').isValid ? 'Please enter a valid URL' : ''}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Webhook size={18} color={theme.palette.primary.main} />
                  </InputAdornment>
                ),
                endAdornment: getWebhookData('PLATFORM').url && (
                  <InputAdornment position="end">
                    <Stack direction="row">
                      <Tooltip title="Copy URL">
                        <IconButton
                          size="small"
                          onClick={() => copyToClipboard(getWebhookData('PLATFORM').url)}
                          sx={{
                            color: theme.palette.text.secondary,
                            '&:hover': {
                              backgroundColor: alpha(theme.palette.action.active, 0.04),
                              color: theme.palette.text.primary,
                            },
                          }}
                        >
                          <Copy size={16} />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Test endpoint">
                        <IconButton
                          size="small"
                          onClick={() => window.open(getWebhookData('PLATFORM').url, '_blank')}
                          sx={{
                            color: theme.palette.text.secondary,
                            '&:hover': {
                              backgroundColor: alpha(theme.palette.action.active, 0.04),
                              color: theme.palette.text.primary,
                            },
                          }}
                        >
                          <ExternalLink size={16} />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </InputAdornment>
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1.5,
                },
              }}
            />

            <Stack spacing={1}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Sparkles size={14} color={theme.palette.primary.main} />
                <Typography variant="caption" color="text.secondary" fontWeight={500}>
                  This webhook enables key features
                </Typography>
              </Stack>
              <Typography variant="caption" color="text.secondary">
                The platform webhook is essential for features like Connect UI and Connect Agent. 
                It receives critical system events and enables real-time synchronization across your integrations.
              </Typography>
            </Stack>
          </Stack>
        </Paper>


        {/* Category Webhooks */}
        {selectedCategories.map((category) => {
          const webhookData = getWebhookData(category);
          const hookType = getDomainHookType(category);
          
          if (!hookType) {
            // Skip categories that don't support webhooks
            return null;
          }

          return (
            <Paper
              key={category}
              variant="outlined"
              sx={{
                p: 3,
                borderRadius: 2,
                '&:hover': {
                  borderColor: theme.palette.primary.main,
                },
              }}
            >
              <Stack spacing={2}>
                <Typography variant="body1" fontWeight={500}>
                  {getDomainByValue(category)?.label || category}
                </Typography>

                <TextField
                  fullWidth
                  placeholder="https://your-domain.com/webhook/endpoint"
                  value={webhookData.url}
                  onChange={(e) => handleUrlChange(category, e.target.value)}
                  error={!webhookData.isValid}
                  helperText={!webhookData.isValid ? 'Please enter a valid URL' : ''}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Globe size={18} color={theme.palette.text.disabled} />
                      </InputAdornment>
                    ),
                    endAdornment: webhookData.url && (
                      <InputAdornment position="end">
                        <Stack direction="row">
                          <Tooltip title="Copy URL">
                            <IconButton
                              size="small"
                              onClick={() => copyToClipboard(webhookData.url)}
                              sx={{
                                color: theme.palette.text.secondary,
                                '&:hover': {
                                  backgroundColor: alpha(theme.palette.action.active, 0.04),
                                  color: theme.palette.text.primary,
                                },
                              }}
                            >
                              <Copy size={16} />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Test endpoint">
                            <IconButton
                              size="small"
                              onClick={() => window.open(webhookData.url, '_blank')}
                              sx={{
                                color: theme.palette.text.secondary,
                                '&:hover': {
                                  backgroundColor: alpha(theme.palette.action.active, 0.04),
                                  color: theme.palette.text.primary,
                                },
                              }}
                            >
                              <ExternalLink size={16} />
                            </IconButton>
                          </Tooltip>
                        </Stack>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 1.5,
                    },
                  }}
                />
              </Stack>
            </Paper>
          );
        })}
      </Stack>

    </Box>
  );
}