import useMediaQuery from '@mui/material/useMediaQuery';

// project import
import NavUser from './NavUser';
import NavCard from './NavCard';
import Navigation from './Navigation';
import GetStartedProgressEnhanced from './GetStartedProgressEnhanced';
import SimpleBar from 'components/third-party/SimpleBar';
import { useGetMenuMaster } from 'api/menu';
import useUserDetails from 'store/user';
import { Box, Skeleton, Stack } from '@mui/material';
import { MenuSkeleton } from 'components/@extended/Menu';

// ==============================|| DRAWER CONTENT ||============================== //



export default function DrawerContent() {

  const { isLoading } = useUserDetails();

  const { menuMaster } = useGetMenuMaster();
  const drawerOpen = menuMaster.isDashboardDrawerOpened;
  const downLG = useMediaQuery((theme) => theme.breakpoints.down('lg'));

  return (
    <Box sx={{ 
      height: '100%',
      // Hide native scrollbars for cross-browser compatibility
      overflow: 'hidden',
      '&::-webkit-scrollbar': {
        display: 'none'
      },
      scrollbarWidth: 'none', // Firefox
      msOverflowStyle: 'none', // IE and Edge
    }}>
      <SimpleBar 
        sx={{ 
          height: '100%',
          '& .simplebar-scrollbar': {
            '&:before': {
              opacity: 0,
              transition: 'opacity 0.2s ease'
            }
          },
          // Show scrollbar on hover of the entire drawer content
          '& .simplebar-track': {
            opacity: 0,
            transition: 'opacity 0.2s ease'
          },
          '&:hover .simplebar-track': {
            opacity: 1
          },
          '&:hover .simplebar-scrollbar:before': {
            opacity: 0.3
          },
          '& .simplebar-scrollbar.simplebar-visible:before': {
            opacity: 0
          },
          '&:hover .simplebar-scrollbar.simplebar-visible:before': {
            opacity: 0.5
          },
          // For dragging state
          '& .simplebar-scrollbar.simplebar-dragging:before': {
            opacity: 0.8
          },
          '& .simplebar-content': { 
            display: 'flex', 
            flexDirection: 'column',
            paddingBottom: 2, // Add some bottom padding
            ...(!drawerOpen && {
              alignItems: 'center'
            })
          } 
        }}
      >
      {isLoading ? (
        <Stack gap={2}>
          {new Array(2).fill({}).map((_, index) => (
            <MenuSkeleton key={index} />
          ))}
        </Stack>
      ) : (
        <>
          {/* <GetStartedProgressEnhanced drawerOpen={drawerOpen} orgId={window.authUserOrgId} /> */}
          <Navigation />
          
          {/* Documentation Card as last nav item */}
          {drawerOpen && !downLG && <NavCard />}
        </>
      )}
      </SimpleBar>
    </Box>
  );
}
