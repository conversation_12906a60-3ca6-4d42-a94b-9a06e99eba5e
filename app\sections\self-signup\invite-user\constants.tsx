/* eslint-disable @typescript-eslint/no-explicit-any */
import { UserRoleEnum } from "hooks/api/permission/usePermission";
import { UserOutlined, CrownOutlined } from '@ant-design/icons';

export const ROLE_ICONS: any = {
   [UserRoleEnum.ORG_USER]: <UserOutlined style={{ fontSize: '14px' }} />,
   [UserRoleEnum.ORG_ADMIN]: <CrownOutlined style={{ fontSize: '14px' }} />,
   [UserRoleEnum.ORG_OBSERVER]: <UserOutlined style={{ fontSize: '14px' }} />,
};