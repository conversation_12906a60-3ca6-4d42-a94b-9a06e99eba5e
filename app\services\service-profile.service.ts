import { API_ENDPOINTS } from "utils/api/api-endpoints";
import fetchInstance from "utils/api/fetchinstance";
import platformfetchInstance from "utils/api/fetchinstance/platform-fetch-Instance";

export const serviceProfileClient = {
  searchProfiles: (payload: any) => {
    return platformfetchInstance.post(
      `${API_ENDPOINTS.SERVICE_PROFILE}s/search`,
      payload
    );
  },
  getServiceProfileSpecifications: (id: string, params: object) => {
    return platformfetchInstance.get(
      `${API_ENDPOINTS.SERVICE_PROFILE}s/${id}/specifications`,
      { params }
    );
  },
  getServiceProfileDataTypes: (id: string) => {
    return platformfetchInstance.get(
      `${API_ENDPOINTS.SERVICE_PROFILE}s/${id}/dataTypes`
    );
  },
  searchServiceProfileDataTypes: (id: string, payload: object | object[]) => {
    return platformfetchInstance.post(
      `${API_ENDPOINTS.SERVICE_PROFILE}s/${id}/dataTypes/search`,
      payload
    );
  },
  searchServices: (payload: any) => {
    return fetchInstance.post(`${API_ENDPOINTS.SERVICE}s/search`, payload);
  },
  updateService: (id: string, payload: any) => {
    return fetchInstance.post(
      `${API_ENDPOINTS.SERVICE}s/${id}/actions`,
      payload
    );
  },
  createService: (payload: any) => {
    return fetchInstance.post(`${API_ENDPOINTS.SERVICE}s`, payload);
  },
  getService: (id: string) => {
    return fetchInstance.get(`${API_ENDPOINTS.SERVICE}s/${id}`);
  },
  getVersions: (id: string) => {
    return platformfetchInstance.get(
      `${API_ENDPOINTS.SERVICE_PROFILE}s/${id}/versions`
    );
  },
  updateSandboxConfig: (id: string, payload: any) => {
    return fetchInstance.patch(`${API_ENDPOINTS.SERVICE}s/${id}`, payload);
  },
  enableUpdateSandboxConfig: (id: string) => {
    return platformfetchInstance.get(
      `${API_ENDPOINTS.SERVICE_PROFILE}s/${id}/sandboxTypeConfigs`
    );
  },
  getServiceProfileAccessPoints: (id: string) => {
    return platformfetchInstance.get(
      `${API_ENDPOINTS.SERVICE_PROFILE}s/${id}/accessPointTypeConfigs`
    );
  },
  getServiceAccessPoints: (id: string) => {
    return fetchInstance.get(`${API_ENDPOINTS.SERVICE}s/${id}/accessPoints`);
  },
  updateAccessPointsSelection: (id: string, payload: Record<string, any>) => {
    return fetchInstance.put(
      `${API_ENDPOINTS.SERVICE}s/${id}/accessPoints/bulk`,
      payload
    );
  },
  updateAccessPoints: (
    id: string,
    accessPointId: string,
    payload: Record<string, any>
  ) => {
    return fetchInstance.patch(
      `${API_ENDPOINTS.SERVICE}s/${id}/accessPoints/${accessPointId}`,
      payload,
      {
        headers: {
          "Content-Type": "multipart/form-data", // Important header for file upload
        },
      }
    );
  },
  createServicesBulk: (payload: any) => {
    return fetchInstance.post(`${API_ENDPOINTS.SERVICE}s/bulk`, payload);
  },
};
