import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';

// material-ui
import OutlinedInput from '@mui/material/OutlinedInput';
import { Select, MenuItem, FormControl, InputLabel } from '@mui/material';

import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';

import { selectProps } from 'utils/date-field'

import { FormItemType } from 'constants/form';

import { DateTimePickerAdornment, TextFieldAdornment } from './adornments';
import { parseDateString, getDateValue } from './helper';

const SX = {
  minWidth: '100%'
};

export default function DebouncedInput({
  value: initialValue,
  onFilterChange,
  debounce = 500,
  size,
  startAdornment = null,
  inputType,
  options = [],
  ...props
}: any) {
  const [value, setValue] = useState(initialValue);

  const handleInputChange = (event: any) => {

    if (inputType === FormItemType.DateTime) {
      // treat date field as object datatype (moment, day js)
      setValue(
        parseDateString(event)
      )
    } else {
      setValue(event.target.value as string)
    }
  };

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      onFilterChange(value);
    }, debounce);

    return () => clearTimeout(timeout);
  }, [value]);
  const label = props?.placeholder ?? null;

  if (inputType === FormItemType.Select) {
    return (
      <FormControl sx={SX}>
        <InputLabel
          {...(size && { size })}
          id={`table-filter-select:${label}`}
          sx={{
            mt: .4
          }}
        >
          {label}
        </InputLabel>

        <Select
          labelId={`table-filter-select:${label}`}
          sx={SX}
          {...props}
          value={value ?? ''}
          onChange={handleInputChange}
          {...(startAdornment && { startAdornment })}
          {...(size && { size })}
          endAdornment={(
            <TextFieldAdornment
              value={value}
              onClear={handleInputChange}
            />
          )}
        >
          {(options as any[]).map(({ label, value }, key) => {
            return (
              <MenuItem key={key} value={value}>{label}</MenuItem>
            )
          })}
        </Select>

      </FormControl>
    )
  } else if (inputType === FormItemType.DateTime) {
    return (
      <DateTimePicker
        slotProps={{
          textField: {
            placeholder: label,
            ...(size && { size }),
            InputProps: {
              endAdornment: (
                <DateTimePickerAdornment
                  onClear={handleInputChange}
                  value={value}
                />
              ),
            },
          },
          inputAdornment: {
            position: 'start',
          },
        }}
        {...selectProps(props)}
        value={getDateValue(value)}
        onAccept={handleInputChange}
        sx={SX}
      />
    )
  }

  return (
    <OutlinedInput
      {...props}
      value={value}
      onChange={handleInputChange}
      sx={SX}
      {...(startAdornment && { startAdornment })}
      {...(size && { size })}
      endAdornment={(
        <TextFieldAdornment
          value={value}
          onClear={handleInputChange}
        />
      )}
    />
  );
}

DebouncedInput.propTypes = {
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  onFilterChange: PropTypes.func,
  debounce: PropTypes.number,
  size: PropTypes.any,
  startAdornment: PropTypes.any,
  SearchOutlined: PropTypes.any,
  props: PropTypes.any
};
