/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { createContext, useContext, ReactNode } from 'react';
import { TenantMetadata } from 'types/tenant-metadata';
import useSelfSignUp from '../hooks/useSelfSignUp';
import { DialogProps } from '@mui/material';

interface TenantMetadataContextType {
   tenantMetadata: TenantMetadata | undefined;
   isLoading: boolean;
   error: unknown;
   onClose: DialogProps['onClose']
}

const TenantMetadataContext = createContext<TenantMetadataContextType | undefined>(undefined);

interface TenantMetadataProviderProps {
   children: ReactNode;
   value?: any
}

export const TenantMetadataProvider: React.FC<TenantMetadataProviderProps> = ({ children, value: valueProp }: any) => {
   const { tenantMetaDataClient } = useSelfSignUp();

   const value: TenantMetadataContextType = {
      tenantMetadata: tenantMetaDataClient.data,
      isLoading: tenantMetaDataClient.isLoading,
      error: tenantMetaDataClient.error,
      ...valueProp
   };

   return (
      <TenantMetadataContext.Provider value={value}>
         {children}
      </TenantMetadataContext.Provider>
   );
};

export const useTenantMetadata = () => {
   const context = useContext(TenantMetadataContext);
   if (context === undefined) {
      throw new Error('useTenantMetadata must be used within a TenantMetadataProvider');
   }
   return context;
}