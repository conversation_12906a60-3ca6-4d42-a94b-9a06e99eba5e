
import { Stack, Box, Typography, BoxProps, ListItem, ListItemProps } from "@mui/material";

type ListEmptyProps = {
   title?: string
   description?: string
   action?: React.ReactElement | null

   // props fot the action
   actionProps?: BoxProps
} & ListItemProps

export default ({
   title,
   description,
   action = null,
   actionProps = {},
   ...rest
}: ListEmptyProps) => {
   return (
      <ListItem
         {...rest}
         sx={{
            textAlign: 'center',
            justifyContent: 'center',
            py: 2
         }}
      >
         <Box
            sx={{
               pointerEvents: 'none'
            }}
         >
            <Stack gap={1} alignItems={'center'}>
               <Typography variant='subtitle1' color={'secondary.800'} >
                  {title ?? `No Record Found`}
               </Typography>
               <Typography variant='body1' color={'secondary.600'} >
                  {description ?? `When you have records available, they will show up here.`}
               </Typography>
            </Stack>

            {/* action block */}
            {action && (
               <Box {...actionProps}>{action}</Box>
            )}
         </Box>
      </ListItem>

   )
}