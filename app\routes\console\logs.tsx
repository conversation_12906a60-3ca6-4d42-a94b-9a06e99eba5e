import { type MetaFunction } from "@remix-run/node";
import { ClientOnly } from 'remix-utils/client-only';

import { Stack } from "@mui/material";
import { TabContentSkeleton } from 'components/skeletons';


import _ from "lodash";

import Tabs from "components/@extended/tab";
import LogTable from "sections/logs/api/data-table";
import EventTable from "sections/logs/events/data-table";

export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
   ];
};


export default function APIlogs() {

   return (
      <ClientOnly fallback={<TabContentSkeleton tabCount={2} contentHeight={500} />}>
         {() => {
            return (
               <Tabs
                  classNames={{
                     tab: 'max-w-[150px]'
                  }}
                  items={[
                     {
                        title: 'API Request',
                        key: 0,
                        children: (
                           <APILogs />
                        )
                     },
                     {
                        title: 'Webhook Events',
                        key: 1,
                        children: <EventLogs />
                     },
                  ]}
               />
            )
         }}
      </ClientOnly>
   );
}

const APILogs = () => {

   return (
      <Stack gap={2}>
         <LogTable />
      </Stack>
   )
}

function EventLogs() {

   return (
      <Stack
         gap={2}
      >
         <EventTable />
      </Stack>
   );
}
