import { Stack, TableCell, TableRow, Typography } from "@mui/material";

type TableEmptyCellProps = {
   colSpan?: number
}

export default ({ colSpan }: TableEmptyCellProps) => {
   return (
      <TableRow
        sx={{
           pointerEvents: 'none'
        }}
      >
         <TableCell
            {...{ colSpan }}
            sx={{
               py: 5,
            }}
         >
            <Stack gap={1} alignItems={'center'}>
               <Typography variant='subtitle1' color={'secondary.800'} >No Record Found</Typography>
               <Typography variant='body1' color={'secondary.600'} >When you have records available, they will show up here.</Typography>
            </Stack>
         </TableCell>
      </TableRow>
   )
}