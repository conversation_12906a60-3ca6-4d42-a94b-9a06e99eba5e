import { LoadingOutlined, SyncOutlined } from "@ant-design/icons"
import { <PERSON><PERSON>, <PERSON><PERSON>, Stack, Typography } from "@mui/material"
import MainCard from "components/MainCard"
import { useAccesskey } from "hooks/api/useAccesskey"
import useMediaQuery from '@mui/material/useMediaQuery';
import CopyableText from "components/@extended/Copyable-text";

export default ({ showDescription = false }: Record<string, any>) => {

   const { generate, isPending, accessKey, createdAt, expiresAt } = useAccesskey()


   const downMd = useMediaQuery((theme: any) => theme.breakpoints.down('md'));

   return (
      <Stack gap={2}>
         {showDescription && (
            <Typography variant='button' sx={{ textTransform: 'none', color: 'secondary.600' }}>
               Utilize the following API Key to interact with our Unizo APIs, enabling seamless integration and enhanced functionality for your applications.
            </Typography>
         )}
         {accessKey && (
            <Alert
               severity='info'
            >
               Make sure to copy API key now. You won't be able to see it again!
            </Alert>
         )}
         <MainCard >
            <Stack gap={2}>
               <Stack
                  direction={downMd ? 'column' : 'row'}
                  alignItems={downMd ? 'start' : 'center'}
                  justifyContent={'space-between'}
                  gap={downMd ? 2 : 10}
               >
                  <Stack
                     direction={downMd ? 'column' : 'row'}
                     alignItems={downMd ? 'start' : 'center'}
                     justifyContent={'space-between'}
                     gap={downMd ? 2 : 10}
                  >
                     <Stack direction={'column'}>
                        <Typography variant='h6' sx={{ fontWeight: "bold" }}>
                           {'API key'}
                        </Typography >

                        <CopyableText text={accessKey as string}>
                           <Typography variant="caption">
                              {accessKey ?? '-'}
                           </Typography>
                        </CopyableText>

                     </Stack>
                     <Stack direction={'column'}>
                        <Typography variant='h6' sx={{ fontWeight: "bold" }}>
                           {'Created date'}
                        </Typography>
                        <Typography variant='caption'>
                           {createdAt ?? "-"}
                        </Typography>
                     </Stack>
                     <Stack direction={'column'}>
                        <Typography variant='h6' sx={{ fontWeight: "bold" }}>
                           {'Expiry date'}
                        </Typography>
                        <Typography variant='caption'>
                           {expiresAt ?? "-"}
                        </Typography>
                     </Stack>
                  </Stack>
                  <Button
                     variant='contained'
                     color='primary'
                     startIcon={!isPending ? <SyncOutlined /> : <LoadingOutlined />}
                     onClick={generate}
                     sx={{
                        textTransform: 'none'
                     }}
                  >
                        Generate API key
                  </Button>
               </Stack>
            </Stack>
         </MainCard>
      </Stack>
   )
}