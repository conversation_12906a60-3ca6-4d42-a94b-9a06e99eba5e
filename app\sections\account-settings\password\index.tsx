import { useMemo, useRef, useState } from "react";

import { CheckOutlined, EyeInvisibleOutlined, EyeOutlined, LineOutlined } from "@ant-design/icons"
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from 'react-hook-form';

import {
   Box,
   Grid,
   List,
   Stack,
   Typography,
   ListItem,
   ListItemText,
   ListItemIcon,
   FormControl,
   TextField,
   InputAdornment,
   OutlinedInput,
   Button
} from "@mui/material"
import { Form, FormField, FormItem } from "components/@extended/Form";
import { isNumber, isLowercaseChar, isUppercaseChar, isSpecialChar, minLength } from 'utils/password-validation';
import { z } from "zod";
import IconButton from "components/@extended/IconButton";
import Password<PERSON>ield from "components/@extended/password-field";
import { useGetUser } from "hooks/api/user/useUser";
import useUserDetails from "store/user";
import { clearEnvironmentId } from "utils/environment-id";

const FormSchema = z.object({
   password: z
      .string()
      .min(8, "Password must be at least 8 characters long")
      .max(20, "Password must be at most 20 characters long")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/\d/, "Password must contain at least one number")
      .regex(/[!@#$%^&*(),.?":{}|<>]/, "Password must contain at least one special character")
      .nonempty("Password is required"),
   confirmPassword: z
      .string()
      .nonempty("Confirm Password is required")
}).superRefine(({ confirmPassword, password }, ctx) => {
   if (confirmPassword !== password) {
      ctx.addIssue({
         code: "custom",
         message: "The passwords did not match",
         path: ['confirmPassword']
      });
   }
})

type FormValues = z.infer<typeof FormSchema>;

const defaultValues: Partial<FormValues> = {
   password: '',
   confirmPassword: '',
}

const type = "PASSWORD_RESET_REQUEST"

export default () => {

   const form = useForm<FormValues>({
      resolver: zodResolver(FormSchema),
      defaultValues,
      mode: "onChange",
   }),
      { watch, formState } = form;
   const formRef = useRef<HTMLFormElement>(null);
   const password = watch('password');

   const { user } = useUserDetails(),
      { attemptUpdateUserPassword, attemptUserLogout } = useGetUser({});




   const onSubmit = ({ password }: FormValues) => {

      const payload = {
         data: { password },
         type
      }
      if (user) {
         attemptUpdateUserPassword(user?.id, payload, () => { 
            clearEnvironmentId();
           attemptUserLogout(() => {}); 
         })
      }
   }

   return (
      <Stack>
         <Grid container spacing={6}>
            <Grid item xs={12} sm={6}>
               <Stack>
                  <Form {...form}>
                     <Stack
                        component={'form'}
                        onSubmit={(...args) => (
                           void form.handleSubmit(onSubmit)(...args)
                        )}
                        ref={formRef}
                        gap={3}
                     >
                        <FormField
                           control={form.control}
                           name='password'
                           render={({ field }) => (
                              <FormItem label='Password'>
                                 <FormControl>
                                    <PasswordField placeholder="Enter Password" {...field} />
                                 </FormControl>
                              </FormItem>
                           )}
                        />
                        <FormField
                           control={form.control}
                           name='confirmPassword'
                           render={({ field }) => (
                              <FormItem label='Confirm Password'>
                                 <FormControl>
                                    <PasswordField placeholder="Enter Confirm Password" {...field} />
                                 </FormControl>
                              </FormItem>
                           )}
                        />
                     </Stack>
                  </Form>
               </Stack>
            </Grid>
            <Grid item xs={12} sm={6}>
               <PasswordState password={password} />
            </Grid>
            <Grid item xs={12}>
               <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
                  <Button variant="outlined" color="secondary">
                     Cancel
                  </Button>
                  <Button
                     type="submit"
                     variant="contained"
                     onClick={() => {
                        formRef.current?.requestSubmit()
                     }}
                     disabled={!formState.isValid}
                  >
                     Save
                  </Button>
               </Stack>
            </Grid>
         </Grid>
      </Stack>
   )
}

type PasswordStateProps = {
   password?: string
}

function PasswordState({ password }: PasswordStateProps) {

   return (
      <Box >
         <Typography variant="h5">New password must contain:</Typography>
         <List sx={{ p: 0, mt: 1 }}>
            <ListItem divider>
               <ListItemIcon sx={{ color: minLength(password) ? 'success.main' : 'inherit' }}>
                  {minLength(password) ? <CheckOutlined /> : <LineOutlined />}
               </ListItemIcon>
               <ListItemText primary="At least 8 characters" />
            </ListItem>
            <ListItem divider>
               <ListItemIcon sx={{ color: isLowercaseChar(password) ? 'success.main' : 'inherit' }}>
                  {isLowercaseChar(password) ? <CheckOutlined /> : <LineOutlined />}
               </ListItemIcon>
               <ListItemText primary="At least 1 lower letter (a-z)" />
            </ListItem>
            <ListItem divider>
               <ListItemIcon sx={{ color: isUppercaseChar(password) ? 'success.main' : 'inherit' }}>
                  {isUppercaseChar(password) ? <CheckOutlined /> : <LineOutlined />}
               </ListItemIcon>
               <ListItemText primary="At least 1 uppercase letter (A-Z)" />
            </ListItem>
            <ListItem divider>
               <ListItemIcon sx={{ color: isNumber(password) ? 'success.main' : 'inherit' }}>
                  {isNumber(password) ? <CheckOutlined /> : <LineOutlined />}
               </ListItemIcon>
               <ListItemText primary="At least 1 number (0-9)" />
            </ListItem>
            <ListItem>
               <ListItemIcon sx={{ color: isSpecialChar(password) ? 'success.main' : 'inherit' }}>
                  {isSpecialChar(password) ? <CheckOutlined /> : <LineOutlined />}
               </ListItemIcon>
               <ListItemText primary="At least 1 special characters" />
            </ListItem>
         </List>
      </Box>
   )
}
