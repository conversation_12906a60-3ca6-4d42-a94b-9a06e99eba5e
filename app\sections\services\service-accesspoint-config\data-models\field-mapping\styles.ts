import { alpha, Theme } from "@mui/material"
import { ModeTypes } from "./type"

export const styles = {
   toggleGroupStyles: ({ theme, viewMode }: { viewMode: ModeTypes, theme: Theme }) => {
      return {
         display: "flex",
         gap: 1,
         "& .MuiToggleButton-root": {
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: 1,
            py: 0.75,
            px: 2,
            minWidth: 120,
            textTransform: "none",
            fontSize: "0.875rem",
            whiteSpace: "nowrap",
            border: `1px solid ${theme.palette.divider}`,
            "&.Mui-selected": {
               bgcolor:
                  viewMode === "visual"
                     ? alpha(theme.palette.primary.main, 0.1)
                     : alpha(theme.palette.grey[500], 0.1),
               borderColor:
                  viewMode === "visual"
                     ? theme.palette.primary.main
                     : theme.palette.grey[500],
            },
         },
      }
   },
   mappingsRoot: ({ theme }: { theme: Theme }) => {
      return {
         p: 3,
         bgcolor:
            theme.palette.mode === "dark"
               ? alpha(theme.palette.background.paper, 0.3)
               : "#fafafa",
      }
   },
   mappingItemRoot: ({ theme }: { theme: Theme }) => {
      return {
         bgcolor: "background.paper",
         border: `1px solid ${theme.palette.divider}`,
         borderRadius: 1,
         overflow: "hidden",
      }
   },
   toggleButtonRoot: ({ isExpanded }: { isExpanded: boolean }) => {
      return {
         p: 0.5,
         visibility: isExpanded ? "visible" : "hidden",
         alignSelf: "center",
      }
   },
   toggleButtonIcon: ({ isExpanded }: { isExpanded: boolean }) => {
      return {
         transform: isExpanded ? "rotate(0deg)" : "rotate(-90deg)",
         transition: "transform 0.2s",
      }
   },
   seperationArrowRoot: ({ theme, isChild }: { theme: Theme, isChild: boolean }) => {
      return {
         display: "flex",
         alignItems: "center",
         alignSelf: "center",
         color: alpha(theme.palette.text.secondary, 0.4),
         transform: isChild ? "translateY(-0px)" : "translateY(9px)",
      }
   },
   mappingRowDeleteButtonRoot: ({ canRemove, theme, isChild }: { theme: Theme, canRemove: boolean, isChild: boolean }) => {
      return {
         p: 0.5,
         alignSelf: "center",
         transform: isChild ? "translateY(-0px)" : "translateY(9px)",
         "&:hover": {
            bgcolor: !canRemove
               ? "transparent"
               : alpha(theme.palette.error.main, 0.08),
            color: !canRemove ? "inherit" : theme.palette.error.main,
         },
         "&.Mui-disabled": {
            color: alpha(theme.palette.text.secondary, 0.3),
         },
      }
   },
   addMappingButtonRoot: ({ theme }: { theme: Theme }) => {
      return {
         alignSelf: "flex-start",
         textTransform: "none",
         fontSize: "0.875rem",
         fontWeight: 500,
         borderColor: theme.palette.primary.main,
         color: theme.palette.primary.main,
         borderRadius: 1.5,
         px: 2,
         py: 1,
         "&:hover": {
            bgcolor: alpha(theme.palette.primary.main, 0.04),
            borderColor: theme.palette.primary.main,
         },
      }
   },
   childFieldMappingRow: ({ theme, index}: { theme: Theme, index: number }) => {
      return {
         borderTop: index  === 1 ? `1px solid ${theme.palette.divider}` : '',
         pt: 2,
         pb: 2,
         position: "relative",
      }
   },
   hierarchyStraightLine: ({ theme }: { theme: Theme }) => {
      return {
         position: "absolute",
         left: -24,
         top: -8,
         bottom: -8,
         width: 2,
         bgcolor: theme.palette.divider,
         borderRadius: "1px",
      }
   },
   hierarchyCurveLine: ({ theme }: { theme: Theme }) => {
      return {
         position: "absolute",
         left: -24,
         top: "50%",
         transform: "translateY(-50%)",
         width: 24,
         height: 2,
         bgcolor: theme.palette.divider,
         "&::before": {
            content: '""',
            position: "absolute",
            left: 0,
            top: -8,
            width: 2,
            height: 8,
            bgcolor: theme.palette.divider,
         },
      }
   }
}