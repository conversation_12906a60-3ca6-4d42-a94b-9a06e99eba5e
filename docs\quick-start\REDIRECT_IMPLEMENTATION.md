# Quick Start Redirect Implementation

## Overview

Similar to the `useOnboarding` hook, we've implemented a quick-start redirect system that checks tenant data and automatically redirects users to `/quick-start` if their setup is incomplete.

## Implementation Details

### 1. `useQuickStartRedirect` Hook

Located in: `/app/hooks/use-quick-start-redirect.tsx`

This hook checks tenant metadata and redirects if:
- No tenant metadata exists (404 error)
- Onboarding is not completed
- No categories or services are selected

```typescript
const { isChecking, needsQuickStart } = useQuickStartRedirect({
  enabled: true,
  redirectPath: '/quick-start'
});
```

### 2. `useQuickStartRedirectSimple` Hook

Located in: `/app/hooks/use-quick-start-redirect-simple.tsx`

A simplified version that checks:
- If user has no subscriptions → redirect to quick-start
- If localStorage flag `quickStartCompleted` is not set → redirect

This is similar to how `useOnboarding` checks for empty tenant data.

### 3. Integration in Console Route

The console route now uses the redirect hook:

```typescript
// In /app/routes/console/index.tsx
const { isChecking } = useQuickStartRedirect({
  enabled: !!user && user?.organization?.state !== State.SUBMITTED
});

if (isChecking) {
  return <PageWithNavigationSkeleton />;
}
```

## How It Works

1. **User accesses `/console`**
2. **Hook checks tenant data**:
   - Fetches tenant metadata from API
   - Checks subscription count
   - Verifies onboarding completion status
3. **If data is empty/incomplete**:
   - Redirects to `/console/quick-start`
4. **On quick-start completion**:
   - Sets localStorage flag
   - Updates tenant metadata (when API is available)
   - Redirects back to dashboard

## Testing

### Method 1: Clear localStorage and Reload
```javascript
localStorage.removeItem('quickStartCompleted');
window.location.reload();
```

### Method 2: Use Test Dashboard
Navigate to: `/console/dashboard-test`

This page:
- Shows current user/subscription data
- Has a button to clear the quick-start flag
- Will auto-redirect if quick-start is needed

### Method 3: Direct Console Access
1. Clear browser data
2. Navigate to `/console`
3. Should redirect to `/console/quick-start` if no tenant data

## API Endpoints Used

1. **Check Status**: `GET /tenantMetadata/{organizationId}`
   - Returns 404 if no metadata → triggers redirect
   - Returns data with onboarding status

2. **Check Tenants**: `GET /tenants`
   - If empty array → triggers quick-start
   - Similar to `useOnboarding` logic

## Key Differences from useOnboarding

1. **Target**: Redirects to `/quick-start` instead of self-signup modal
2. **Checks**: Looks for onboarding completion, not just tenant existence
3. **Completion**: Sets localStorage flag to prevent redirect loops

## Configuration Options

```typescript
useQuickStartRedirect({
  enabled: boolean,        // Enable/disable redirect check
  redirectPath: string,    // Where to redirect (default: '/quick-start')
});
```

## Future Improvements

1. **Backend Integration**: Store completion status in database
2. **Progressive Onboarding**: Track partial completion
3. **Skip Option**: Allow users to skip and come back later
4. **Analytics**: Track drop-off points in the flow