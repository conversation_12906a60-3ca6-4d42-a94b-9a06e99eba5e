import { Stack, Typography } from "@mui/material"
import LearnMoreLink from 'components/@extended/LearnMoreLink';
import RefreshIcon from '@mui/icons-material/Refresh';
import IconButton from "components/@extended/IconButton";
import { useServiceProfile } from "hooks/useServiceProfile";
import { useGetServiceProfile } from "hooks/api/service-profile/useGetServiceProfile";



export default function ContentHeader({ domain, onReload }: Record<string, any>) {
   const { getDomainName } = useServiceProfile();

   return (
      <Stack
         direction={'row'}
         justifyContent={'space-between'}
         alignItems={'center'}
      >

         <Stack gap={1}>
            {/* title */}
            <Typography variant='h5'>
               Configure {getDomainName(domain)} integrations
            </Typography>
            <Typography variant='body2' sx={{ color: 'text.secondary' }}>
               View supported fields and compare integration coverage. <LearnMoreLink href="https://docs.unizo.ai/integrations"
                  target="_blank">Learn more</LearnMoreLink>
            </Typography>
         </Stack>

         <Stack>
            <IconButton onClick={onReload}>
               <RefreshIcon />
            </IconButton>
         </Stack>
      </Stack>
   )
}