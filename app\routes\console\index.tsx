import { type MetaFunction } from "@remix-run/node";
import { ClientOnly } from 'remix-utils/client-only';
import {
   Outlet,
} from "@remix-run/react";
import DashboardLayout from "layout/Dashboard";
import { InitialSetUp } from "sections/initial-setup";
import { State } from "hooks/useStatus";
import useUserDetails from "store/user";
import { getEnvironmentId } from "utils/environment-id";
import { PageWithNavigationSkeleton } from "components/skeletons";

export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
      { name: "description", content: "Welcome to Remix!" },
   ];
};

export default function Index() {
   return (
      <ClientOnly fallback={<PageWithNavigationSkeleton />}>
         {() => {
            const { user, isLoadingEnvironments } = useUserDetails();
            return (
               <>
                  <DashboardLayout>
                     {!isLoadingEnvironments ? <Outlet /> : <PageWithNavigationSkeleton />}
                  </DashboardLayout>
               </>
            )
         }}
      </ClientOnly>
   );
}
