// import { EDGE_STYLES, STATES } from './flow-diagram';

export const EDGE_STYLES: React.CSSProperties = {
   strokeWidth: 2
};
export const STATES = {
   SUCCESS: '#237804',
   ERROR: '#cf1322',
   WARNING: 'orange'
};

const COMMON_STYLES = {
   stroke: STATES?.ERROR,
   strokeWidth: EDGE_STYLES?.strokeWidth,
};
const LINE_STYLES: any = {
   strokeLinecap: "round",
   strokeLinejoin: "round"
}

export const ErrorEdges = ({
   id,
   sourceX,
   sourceY,
   targetX,
   targetY,
   style = {},
   markerEnd,
}: Record<string, any>) => {


   // Calculate the mid-point and offsets for creating the gap and positioning the diagonal lines
//    const midX = (sourceX + targetX) / 2;
//    const midY = (sourceY + targetY) / 2;
//    const gapSize = 10; // The gap size in the middle, matching the diagonal line spacing
//    const offset = gapSize / 2; // Matching the gap size with the offset for the diagonal lines

//    // Split the horizontal line into two segments, leaving a gap in the middle
//    const leftSegment = `
//     M${sourceX - 2},${sourceY}
//     L268,20
//   `;

//    const rightSegment = `
//     M279,20
//     L${targetX + 2},${targetY}
//   `;

//    // Create diagonal lines with a border radius using quadratic Bézier curves
//    const line1 = `
//     M${midX - offset - 8},${midY - 8}
//     Q269,20 275,28
//   `;

//    const line2 = `
//     M${midX + offset - 8},${midY - 8}
//     Q${midX + offset - 2},${midY} ${midX + offset + 5},${midY + 8}
//   `;

   return (
      <>
         {/* <path
            id={id}
            style={style}
            d={leftSegment}
            {...COMMON_STYLES}
            {...LINE_STYLES}
         />
         <path
            id={id}
            style={style}
            d={rightSegment}
            {...COMMON_STYLES}
            {...LINE_STYLES}
            markerEnd={markerEnd}
         />
         <path
            d={line1}
            {...COMMON_STYLES}
            {...LINE_STYLES}
            fill="none"
         />
         <path
            d={line2}
            {...COMMON_STYLES}
            {...LINE_STYLES}
            fill="none"
         /> */}
      </>
   );
};
