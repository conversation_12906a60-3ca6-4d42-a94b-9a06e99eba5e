import { type MetaFunction } from "@remix-run/node";
import { ClientOnly } from 'remix-utils/client-only';

import { Button, TextField, Stack, ModalProps, Typography, Select, MenuItem, InputAdornment, DialogTitle, DialogContent, Grid, DialogActions, Badge, Chip } from "@mui/material";
import { useTable } from "hooks/table/useTable";
import { useGetUser } from "hooks/api/user/useUser";
import { UserRoleEnum, useGetPermission } from "hooks/api/permission/usePermission";
import { useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod"
import Alert from '@mui/material/Alert';

import Avatar from "components/@extended/Avatar";
import { Form, FormControl, FormField, FormItem } from "components/@extended/Form";
import { UserOutlined } from "@ant-design/icons";
import useUserDetails from "store/user";

import Table from "components/@extended/Table";
import { DialogClose, Dialog } from "components/@extended/dialog";


export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
   ];
};

const HEADER_NAMES = {
   NAME: 'Name',
   EMAIL: 'Email',
   ROLE: 'Role',
   ACTIONS: 'Actions',
}

export default function Settings() {


   const { deriveRoleLabel } = useGetPermission(),
      { user } = useUserDetails()

   const { users, getAvatarLabel } = useGetUser({ orgId: user?.organization?.id });
   const [open, setOpen] = useState<boolean>(false);
   const [editMode, setEditMode] = useState<boolean>(false);
   const [selected, setSelected] = useState<Record<string, any> | null | undefined>(null);

   const [openPendingModal, setSetOpenPendingModal] = useState<boolean>(false);

   const {
      extendedProps,
      paginationModal: { pagination, onPaginationChange }
   } = useTable();

   const onOpen = (
      isOpen: boolean,
      isEditMode: boolean = false,
      selectedRecord?: any
   ) => {
      setOpen(isOpen);
      setEditMode(isEditMode)
      setSelected(selectedRecord)
   }

   const onClose = () => {
      setOpen(false);
   }

   const columns: any = useMemo(
      () => [
         {
            accessorKey: 'name',
            header: HEADER_NAMES.NAME,
            cell({ row: { original: row } }: any) {
               return (
                  <Stack direction={'row'} gap={1} alignItems={'center'} className='mt-1'>
                     <Avatar size='small' className='bg-orange-500 text-white' alt="Remy Sharp" type={undefined}>
                        {getAvatarLabel(row?.firstName)}
                     </Avatar>
                     <Typography variant='button'>
                        {row?.firstName ?? row?.email}
                     </Typography>
                  </Stack>
               )
            },
         },
         {
            accessorKey: 'email', //normal accessorKey
            header: HEADER_NAMES.EMAIL,
         },
         {
            accessorKey: 'role',
            header: HEADER_NAMES.ROLE,
            cell({ row: { original: row } }: any) {
               return deriveRoleLabel(row?.role?.type)
            },
         },
         {
            accessorKey: 'startDate',
            header: HEADER_NAMES.ACTIONS,
            cell({ row: { original: row } }: any) {
               return (
                  <Button
                     onClick={() => {
                        onOpen(true, true, row)
                     }}
                     size="small"
                  >
                     Edit
                  </Button>
               )
            },
         },

      ],
      [],
   );

   const onOpenPending = (isOpen: boolean) => {
      setSetOpenPendingModal(isOpen);
   }

   const onClosePending = () => {
      setSetOpenPendingModal(false);
   }
   return (
      <ClientOnly fallback={null}>
         {() => (
            <>
               <Stack gap={2}>
                  <Stack gap={2}>
                     <Stack direction={'row'} alignItems={'center'} justifyContent={'flex-end'}>
                        <Stack gap={1} direction={'row'}>
                           <Button variant='outlined' color='primary' onClick={() => onOpenPending(true,)}>
                              View pending users
                           </Button>
                           {
                              UserRoleEnum.ORG_ADMIN === user?.role?.type && (
                                 <Button variant='contained' color='primary' onClick={() => onOpen(true,)}>
                                    Invite New Users
                                 </Button>
                              )
                           }
                        </Stack>
                     </Stack>
                     <Table
                        data={users}
                        columns={columns}
                        disablePagination
                     />
                  </Stack>
               </Stack>
               <InviteUserForm
                  open={open}
                  onClose={onClose}
                  isEditMode={editMode}
                  selected={selected}
               />
               <PendingUserModal open={openPendingModal} onClose={onClosePending} />
            </>
         )}
      </ClientOnly>
   );
}

const PendingUserModal = ({ onClose, ...props }: Omit<ModalProps, 'children'>) => {
   const { } = useTable(),
      { deriveRoleLabel } = useGetPermission(),
      { user } = useUserDetails()

   const {
      pendingUserResp,
      getAvatarLabel
   } = useGetUser({ orgId: user?.organization?.id });

   const columns: any = useMemo(
      () => [
         {
            accessorKey: 'name',
            header: 'Email',
            cell({ row: { original: row } }: any) {
               return (
                  <Stack direction={'row'} gap={1} alignItems={'center'} className='mt-1'>
                     <Avatar size='small' className='bg-orange-500 text-white' alt="Remy Sharp" type={undefined}>
                        {getAvatarLabel(row?.firstName)}
                     </Avatar>
                     <Typography variant='button'>
                        {row?.firstName ?? row?.email}
                     </Typography>
                  </Stack>
               )
            },
         },
         // {
         //    accessorKey: 'email', //normal accessorKey
         //    header: HEADER_NAMES.EMAIL,
         // },
         {
            accessorKey: 'role',
            header: HEADER_NAMES.ROLE,
            cell({ row: { original: row } }: any) {
               return deriveRoleLabel(row?.role?.type)
            },
         },
      ],
      [],
   );

   const data = pendingUserResp?.data ?? [],
      total = pendingUserResp?.pagination?.total ?? 0

   return (
      <Dialog onClose={onClose} {...props}>
         <DialogTitle >
            <Stack gap={2} direction={'row'}>
               <Typography variant='h5'>
                  {`Pending invites in ${user?.organization?.name}`}
               </Typography>
            </Stack>
            <Stack gap={2} direction={'row'}>
               <Typography variant='h6'>
                  Invited by email

               </Typography>
               <Chip color="error" label={total} size="small" variant='filled' />
            </Stack>
         </DialogTitle>
         <DialogClose onClose={onClose} />
         <DialogContent dividers sx={{ padding: 0 }}>
            <Table data={data} columns={columns} />
         </DialogContent>
      </Dialog>
   )
}

const FormScheme = z.object({
   email: z.string().nonempty('Email is required').email({ message: "Invalid email address" }),
   role: z.enum([UserRoleEnum.ORG_USER, UserRoleEnum.ORG_ADMIN, UserRoleEnum.ORG_OBSERVER]) // Add the desired role options in the enum array
});

type InviteUserFormProps = {
   isEditMode: boolean
   selected: Record<string, any> | null | undefined
} & Omit<ModalProps, 'children'>;

type FormValues = z.infer<typeof FormScheme>

const defaultValues: Partial<FormValues> = {
   email: '',
   role: UserRoleEnum.ORG_ADMIN
}

const InviteUserForm = ({ open,
   isEditMode,
   onClose: onCloseProp,
   selected
}: InviteUserFormProps) => {

   const formRef = useRef<HTMLFormElement>(null);

   const { getAllRoles } = useGetPermission(),
      { user } = useUserDetails(),
      { attemptBulkUserInvite, attemptBulkUserRoleEdit } = useGetUser({ orgId: user?.organization?.id })
   const roles = getAllRoles();

   const onClose: ModalProps['onClose'] = (e, reason) => {
      form.reset({});
      onCloseProp && onCloseProp(e, reason);
   }

   const form = useForm<FormValues>({
      resolver: zodResolver(FormScheme),
      defaultValues,
      mode: "onSubmit",
   })

   const onSubmit = (e: FormValues) => {
      if (!isEditMode && !selected) {
         attemptBulkUserInvite({ data: [{ ...e, role: { type: e?.role } }] }, () => {
            onClose({}, 'backdropClick')
         });
      } else {
         attemptBulkUserRoleEdit({
            id: selected?.id,
            payload: [
               {
                  "op": "replace",
                  "path": "/role/type",
                  "value": e.role
               }
            ]
         }, () => {
            onClose({}, 'backdropClick')
         });
      }
   }

   useEffect(() => {
      if (isEditMode) {
         form.reset({
            email: selected?.email,
            role: selected?.role?.type as UserRoleEnum
         })
      } else {
         form.reset(defaultValues)
      }
   }, [isEditMode, open, selected?.id])

   return (
      <Dialog open={open} onClose={onClose}
         sx={{
            "& .MuiDialog-paper": {
               maxWidth: 500,
               width: "100%",
            }
         }}
      >
         <DialogTitle variant='h5'>
            {!isEditMode ? `Add users to ${user?.organization?.name}` : `Re-assign role to ${selected?.firstName}`}
         </DialogTitle>
         <DialogClose onClose={onClose} />
         {
            user?.id === selected?.id ?
               <DialogContent dividers sx={{ minWidth: 500, minHeight: 100, margin: 'auto' }}> <Alert severity="warning" sx={{ textAlign: "center", margin: "auto" }}>
                  You can't reassign a role to yourself.
               </Alert></DialogContent> : <Stack>
                  <DialogContent dividers sx={{ minWidth: 500 }}>
                     <Stack gap={2}>
                        <Form {...form}>
                           <form
                              ref={formRef}
                              onSubmit={(...args) => void form.handleSubmit(onSubmit)(...args)}
                              className="flex flex-col gap-5"
                           >
                              <Stack direction={'column'} gap={2} className="w-full">
                                 <FormField
                                    control={form.control}
                                    name='email'
                                    render={({ field }) => (
                                       <FormItem className="w-full" description="Invite new users to this organization" >
                                          <FormControl
                                          >
                                             <TextField
                                                disabled={isEditMode}
                                                InputProps={{
                                                   startAdornment: (
                                                      <>
                                                         <InputAdornment position="start">
                                                            <UserOutlined />
                                                         </InputAdornment>
                                                      </>
                                                   ),
                                                }}
                                                fullWidth placeholder="Enter Email"{...field} />
                                          </FormControl>
                                       </FormItem>
                                    )}
                                 />

                                 <Grid container>
                                    <Grid item xs={12} lg={8}>
                                       <FormField
                                          control={form.control}
                                          name='role'
                                          render={({ field }) => (
                                             <FormItem description="New users will join as:" >
                                                <FormControl>
                                                   <Select label="role" {...field} >
                                                      {roles?.map((role) => (
                                                         <MenuItem
                                                            disabled={role?.value === selected?.role?.type}
                                                            value={role?.value}
                                                            key={role?.value}
                                                         >
                                                            {role?.label}
                                                         </MenuItem>
                                                      ))}
                                                   </Select>
                                                </FormControl>
                                             </FormItem>
                                          )}
                                       />
                                    </Grid>
                                 </Grid>
                              </Stack>

                           </form>
                        </Form>
                     </Stack>
                  </DialogContent>
                  <DialogActions>
                     <Button
                        onClick={() => (
                           void formRef.current?.requestSubmit()
                        )}
                        variant='contained'
                        color='primary' type="submit"
                     >Send invite</Button>
                  </DialogActions>
               </Stack>

         }
      </Dialog >
   )
}
