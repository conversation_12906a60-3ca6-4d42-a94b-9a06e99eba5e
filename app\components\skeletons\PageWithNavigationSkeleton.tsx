import React from 'react';
import { Box, AppBar, Toolbar, Skeleton, useMediaQuery, useTheme } from '@mui/material';
import NavigationSkeleton from './NavigationSkeleton';

const PageWithNavigationSkeleton: React.FC = () => {
  const theme = useTheme();
  const matchDownMd = useMediaQuery(theme.breakpoints.down('md'));
  const drawerWidth = 260;

  return (
    <Box sx={{ display: 'flex' }}>
      {/* App Bar */}
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Skeleton variant="rectangular" width={40} height={40} />
              <Skeleton width={100} height={24} />
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Skeleton variant="rectangular" width={120} height={36} />
              <Skeleton variant="circular" width={40} height={40} />
            </Box>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Side Navigation */}
      {!matchDownMd && (
        <Box
          sx={{
            width: drawerWidth,
            flexShrink: 0,
            '& .MuiDrawer-paper': {
              width: drawerWidth,
              boxSizing: 'border-box',
            },
          }}
        >
          <Toolbar />
          <Box sx={{ overflow: 'auto', mt: 2 }}>
            <NavigationSkeleton variant="drawer" />
          </Box>
        </Box>
      )}

      {/* Main Content */}
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />
        <Box sx={{ mt: 2 }}>
          <Skeleton width={200} height={32} />
          <Skeleton width={400} height={20} sx={{ mt: 1, mb: 3 }} />
          <Skeleton variant="rectangular" width="100%" height={400} />
        </Box>
      </Box>
    </Box>
  );
};

export default PageWithNavigationSkeleton;