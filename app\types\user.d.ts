import { Organization } from "./organization"

export interface User {
   href: string
   type: string
   id: string
   username: string
   state: string
   firstName: string
   lastName: string
   phoneNumber: string
   email: string
   role: Role
   notifications: any[]
   settings: Settings
   tags: any[]
   attributes: any[]
   organization: Organization
   changeLog: ChangeLog
   links: any[]
 }

 export interface Role {
   type: string
 }

 export interface Settings {}


 export interface ChangeLog {
   createdDateTime: string
   lastUpdatedDateTime: string
 }