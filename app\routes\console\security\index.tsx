import { useEffect, useState } from "react";
import { ClientOnly } from "remix-utils/client-only"

import { Outlet, useNavigate, useLocation } from "@remix-run/react";
import { TabContentSkeleton } from 'components/skeletons';

import Tabs, { TabItemsTypes } from "components/@extended/tab";
import useCustomBreadcrumbs from "store/custom-breadcrumbs";

const tabItems: (TabItemsTypes)[] = [
   {
      title: 'Logging​',
      key: 0,
      children: <Outlet />,
   },
   {
      title: 'Key Management​',
      key: 1,
      children: <Outlet />,
   }
];

const tabMap: { [key: number]: { url: string, index: number } } = {
   0: {
      url: 'log-protection',
      index: 0,
   },
   1: {
      url: 'key-protection',
      index: 1,
   }
}

const getSelectedTabByIndex = (pathname: string): number => {
   return Object.values(tabMap).find(({ url }) => pathname.endsWith(url))?.index ?? 0;
}


export default () => {
   const navigate = useNavigate();
   const location = useLocation();

   const { update, reset } = useCustomBreadcrumbs()

   const [value, setValue] = useState<number>();

   const index = getSelectedTabByIndex(location.pathname);

   useEffect(() => {
      setValue(index)
   }, [index])

   useEffect(() => {

      
      update({
         title: 'Logging and Key Management​',
         description: 'Select how your data is handled with Unizo. Use our built-in logging and key management tools or bring your own (BYOL / BYOK) for full control and flexibility​.',
         links: [
            { title: 'Security', to: '/console/profile-settings' },
         ]
      });

      return () => {
         reset()
      }
   }, [])

   useEffect(() => {
      if (location.pathname.endsWith('security')) {
         navigate('log-protection')
      }
   }, []);

   return (
      <ClientOnly fallback={<TabContentSkeleton tabCount={2} contentHeight={400} />}>
         {() => (
            <Tabs
               classNames={{
                  tab: 'max-w-[150px]'
               }}
               value={value}
               items={tabItems}
               onTabChange={(_, selected) => {
                  navigate(tabMap[selected].url)
               }}
            />
         )}
      </ClientOnly>
   )
}