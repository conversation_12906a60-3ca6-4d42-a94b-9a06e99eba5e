import { API_ENDPOINTS } from "utils/api/api-endpoints";
import fetchInstance from "utils/api/fetchinstance";

export const logsClient = {
  searchLogs: (payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.LOGS}/search`, payload);
  },
  searchEvenLogs: (payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.EVENT_LOGS}/search`, payload);
  },
  searchAPIActivities: (payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.ACTIVITIES}/search`, payload);
  },
  searchEventActivities: (payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.ACTIVITY_EVENTS}/search`, payload);
  }
};
