import type { MetaFunction } from '@remix-run/node';
import { ClientOnly } from 'remix-utils/client-only';

import FieldMappings from 'sections/field-mappings';
import { FieldMappingsSkeleton } from 'components/skeletons';

export const meta: MetaFunction = () => {
  return [
    { title: 'Unizo' },
    { name: 'description', content: 'Define how data fields map between your system and external integrations' }
  ];
};

export default function FieldMappingsRoute() {
  return (
    <ClientOnly fallback={<FieldMappingsSkeleton />}>
      {() => <FieldMappings />}
    </ClientOnly>
  );
}