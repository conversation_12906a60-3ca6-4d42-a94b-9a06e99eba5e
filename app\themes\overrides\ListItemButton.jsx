// ==============================|| OVERRIDES - LIST ITEM ICON ||============================== //

export default function ListItemButton(theme) {
  return {
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 0,
          marginBottom: '2px',
          marginLeft: '8px',
          marginRight: '8px',
          '&.Mui-selected': {
            color: theme.palette.primary.main,
            fontWeight: 600,
            '& .MuiListItemIcon-root': {
              color: theme.palette.primary.main
            },
            '& .MuiTypography-root': {
              fontWeight: 600
            }
          }
        }
      }
    }
  };
}
