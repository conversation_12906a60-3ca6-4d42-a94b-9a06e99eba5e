import { MetaFunction } from "@remix-run/node";
import { useEffect, useState } from "react";
import { ClientOnly } from "remix-utils/client-only";
import { useNavigate, useLocation, Outlet } from "@remix-run/react";

import Tabs, { TabItemsTypes } from "components/@extended/tab";
import { UserRoleEnum } from "hooks/api/permission/usePermission";
import useUserDetails from "store/user";

import Watch from "sections/services/setup/bi-directional";
import HealthCheck from "sections/services/setup/health-check";
import { SetupServices } from "./service";
import useCustomBreadcrumbs from "store/custom-breadcrumbs";

export const meta: MetaFunction = () => {
   return [{ title: "Unizo" }];
};

export default function Setup() {
   const { user } = useUserDetails();
   const navigate = useNavigate();
   const location = useLocation();
   const { update, reset } = useCustomBreadcrumbs()

   const tabItems: TabItemsTypes[] = [];

   const tabMap: { [key: number]: { url: string; index: number } } = {};

   let tabIndex = 0;

   if (UserRoleEnum.ORG_USER !== user?.role?.type) {
      tabMap[tabIndex] = { url: "connectors", index: tabIndex };
      tabItems.push({
         title: "Connectors",
         key: tabIndex,
         children: <SetupServices />,
      });
      tabIndex++;
   }

   if (UserRoleEnum.ORG_ADMIN === user?.role?.type) {
      // tabMap[tabIndex] = { url: "webhooks", index: tabIndex };
      // tabItems.push({
      //    title: "Webhooks",
      //    key: tabIndex,
      //    children: <Watch />,
      // });
      // tabIndex++;

      tabMap[tabIndex] = { url: "health-checks", index: tabIndex };
      tabItems.push({
         title: "Health Checks",
         key: tabIndex,
         children: <HealthCheck />,
      });
   }

   const getSelectedTabByIndex = (pathname: string): number => {
      return Object.values(tabMap).find(({ url }) => pathname.endsWith(url))?.index ?? 0;
   };

   const [value, setValue] = useState<number>();

   const index = getSelectedTabByIndex(location.pathname);

   useEffect(() => {
      setValue(index);
   }, [index]);

   useEffect(() => {


      update({
         title: 'Setup Integrations',
         description: 'Manage and supervise the integrations you offer to your users.',
         links: [
            { title: 'Setup Integrations', to: '/console/integrations-setup' },
         ]
      });

      return () => {
         reset()
      }
   }, [])

   useEffect(() => {
      if (location.pathname.endsWith("setup-integrations")) {
         // Navigate to the first visible tab
         navigate(tabMap[0]?.url);
      }
   }, []);

   return (
      <ClientOnly fallback={<p>Loading...</p>}>
         {() => (
            <Tabs
               classNames={{ tab: "max-w-[150px] normal-case" }}
               value={value}
               items={tabItems}
               onTabChange={(_, selected) => {
                  navigate(tabMap[selected].url);
               }}
            />
         )}
      </ClientOnly>
   );
}
