import React from 'react';
import {
  Box,
  Typography,
  useTheme,
  alpha,
  Stepper,
  Step,
  Step<PERSON><PERSON><PERSON>,
  StepConnector,
  stepConnectorClasses,
  styled,
} from '@mui/material';
import { Check } from 'lucide-react';

export interface TabItem {
  id: number;
  number: string;
  title: string;
  helpText: string;
  status: 'completed' | 'active' | 'pending';
}

interface MobileCustomTabsProps {
  tabs: TabItem[];
  activeTab: number;
  onTabChange: (tabId: number) => void;
  children: React.ReactNode;
}

// Custom connector for vertical stepper
const ColorlibConnector = styled(StepConnector)(({ theme }) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 22,
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: theme.palette.primary.main,
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: theme.palette.primary.main,
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    borderColor: theme.palette.divider,
    borderLeftWidth: 3,
    borderRadius: 1,
    marginLeft: -1,
  },
}));

// Custom step icon
const ColorlibStepIconRoot = styled('div')<{
  ownerState: { completed?: boolean; active?: boolean };
}>(({ theme, ownerState }) => ({
  backgroundColor: theme.palette.background.paper,
  zIndex: 1,
  color: theme.palette.grey[400],
  width: 32,
  height: 32,
  display: 'flex',
  borderRadius: '50%',
  justifyContent: 'center',
  alignItems: 'center',
  border: `2px solid ${
    ownerState.active || ownerState.completed
      ? theme.palette.primary.main
      : theme.palette.grey[400]
  }`,
  fontSize: '0.875rem',
  fontWeight: 500,
  ...(ownerState.active && {
    color: theme.palette.primary.main,
  }),
  ...(ownerState.completed && {
    color: theme.palette.primary.main,
  }),
}));

function ColorlibStepIcon(props: {
  active?: boolean;
  completed?: boolean;
  className?: string;
  icon: React.ReactNode;
}) {
  const { active, completed, className, icon } = props;

  return (
    <ColorlibStepIconRoot ownerState={{ completed, active }} className={className}>
      {completed ? <Check size={16} strokeWidth={3} /> : icon}
    </ColorlibStepIconRoot>
  );
}

export default function MobileCustomTabs({ tabs, activeTab, onTabChange, children }: MobileCustomTabsProps) {
  const theme = useTheme();

  return (
    <Box sx={{ width: '100%' }}>
      {/* Mobile Tab Selector */}
      <Box 
        sx={{ 
          mb: 3,
          backgroundColor: theme.palette.background.paper,
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          p: 2,
        }}
      >
        <Typography 
          variant="caption" 
          sx={{ 
            color: theme.palette.text.secondary,
            textTransform: 'uppercase',
            letterSpacing: 0.5,
            fontWeight: 600,
            mb: 1,
            display: 'block',
          }}
        >
          Current Step
        </Typography>
        
        {/* Active Tab Display */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1.5,
            mb: 2,
          }}
        >
          <Box
            sx={{
              width: 36,
              height: 36,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: theme.palette.primary.main,
              color: theme.palette.common.white,
              fontWeight: 600,
              fontSize: '1rem',
            }}
          >
            {tabs[activeTab].number}
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.25 }}>
              {tabs[activeTab].title}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {tabs[activeTab].helpText}
            </Typography>
          </Box>
        </Box>

        {/* Progress Steps */}
        <Box 
          sx={{ 
            overflowX: 'auto',
            overflowY: 'hidden',
            mx: -2,
            px: 2,
            pb: 1,
            '&::-webkit-scrollbar': { display: 'none' },
            scrollbarWidth: 'none',
          }}
        >
          <Stepper 
            activeStep={activeTab} 
            connector={<ColorlibConnector />}
            orientation="horizontal"
            sx={{ minWidth: 'max-content' }}
          >
            {tabs.map((tab) => (
              <Step 
                key={tab.id} 
                completed={tab.status === 'completed'}
                onClick={() => onTabChange(tab.id)}
                sx={{ 
                  cursor: 'pointer',
                  minWidth: 80,
                  '& .MuiStepLabel-root': {
                    '& .MuiStepLabel-labelContainer': {
                      display: 'none', // Hide labels on mobile
                    },
                  },
                }}
              >
                <StepLabel
                  StepIconComponent={ColorlibStepIcon}
                  StepIconProps={{ icon: tab.number }}
                />
              </Step>
            ))}
          </Stepper>
        </Box>

        {/* Navigation Dots */}
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            gap: 0.5,
            mt: 2,
          }}
        >
          {tabs.map((tab) => (
            <Box
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: activeTab === tab.id 
                  ? theme.palette.primary.main 
                  : alpha(theme.palette.primary.main, 0.3),
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  transform: 'scale(1.2)',
                },
              }}
            />
          ))}
        </Box>
      </Box>

      {/* Tab Content */}
      <Box>
        {children}
      </Box>
    </Box>
  );
}