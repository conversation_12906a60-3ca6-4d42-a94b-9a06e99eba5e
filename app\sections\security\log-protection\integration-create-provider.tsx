import { LogProtectionConnectorType } from "constants/log-protection";
import { useGetSecurity } from "hooks/api/security/useGetLogProtection";
import React, { RefObject, createContext, useCallback, useContext, useMemo } from "react";
import { LogProtectionNamespace } from "types/log-protection";
import { Service } from "types/service";
import customFields from "./integration-list/create/step-contents/integration-details/standard/custom-fields";
import _ from "lodash";
import useUserDetails from "store/user";
import { AccessPointConfigType } from "hooks/api/use-accesspoint-config/useGetAccessPoint";
import { hasTiers } from "./helper";
import { AVAILABLE_SUBSCRIPTION_TIERS } from "./constant";

const defaultValues: Partial<Values> = {
   actionRefs: {
      integrationDetails: React.createRef()
   }
}

const IntegrationCreateContext = createContext<Partial<Values>>(defaultValues);

interface Props {
   children: React.ReactNode,
   value: { hasEnterpriseTier: boolean }
};

type CreateIntegrationConfig = {
   selectedService: Service,
   updated: LogProtectionNamespace.IntegrationConnectorConfig
}

type Values = {
   actionRefs: {
      integrationDetails: RefObject<HTMLFormElement>
   }
   getIsIntegrationExist: (serviceId: string) => Record<string, any>
   getIsWebhookConfigExist: () => Record<string, any>
   getIsIntegrationConfigExist: (serviceId: string) => Record<string, any>
   getIsWebhookConfigIndexExist: () => number

   updateWebhookConfig: (updated: LogProtectionNamespace.WebhookConfig, onSuccess: VoidFunction) => void
   createIntegrationConfig: (params: CreateIntegrationConfig, onSuccess: VoidFunction) => void
   getIsServiceHaveIntegration: (id: Service['id']) => Record<string, any> | undefined

   isWebhookExist: boolean
   isIntegrationExist: boolean
   isIntegrationConfigExceed: boolean
   hasEnterpriseTier: boolean

} & ReturnType<typeof useGetSecurity>

export default ({ value, ...props }: Props) => {
   const { subscriptions = [] } = useUserDetails();

   const hasEnterpriseTier = hasTiers(subscriptions, AVAILABLE_SUBSCRIPTION_TIERS);

   const security = useGetSecurity({
      hasEnterpriseTier: hasEnterpriseTier
   });

   const { user } = useUserDetails()

   const { attemptUpdateLogProtection, attemptCreateIntegration, attemptUpdateIntegration } = security;

   const { data: logProtections } = security?.searchLogProtections();

   const getIsWebhookConfigExist = useCallback(() => {
      return logProtections?.connectorTypeConfigs?.find((i: any) => i?.type === LogProtectionConnectorType.Webhook)
   }, [logProtections]);

   const getIsWebhookConfigIndexExist = useCallback(() => {
      return logProtections?.connectorTypeConfigs?.findIndex((i: any) => i?.type === LogProtectionConnectorType.Webhook)
   }, [logProtections]);

   const getIsIntegrationConfigExist = useCallback((integrationId: string) => {
      return integrationId ? (
         logProtections?.connectorTypeConfigs?.find((i: any) => (
            i?.integration?.id === integrationId
         ))
      ) : undefined
   }, [logProtections]);

   const getIsServiceHaveIntegration = useCallback((serviceId: Service['id']): Record<string, any> => {
      return logProtections?.connectorTypeConfigs?.find(
         (i: LogProtectionNamespace.IntegrationConfig) => i?.service?.id === serviceId)
   }, [logProtections])

   const isWebhookExist = useMemo(() => {
      return logProtections?.connectorTypeConfigs?.map((i: any) => i?.type)?.includes(LogProtectionConnectorType.Webhook)
   }, [logProtections]);

   const isIntegrationExist = useMemo(() => {
      return logProtections?.connectorTypeConfigs?.map((i: any) => i?.type)?.includes(LogProtectionConnectorType.Integration)
   }, [logProtections]);

   const getOnlyIntegrationTypeConfigs = useMemo(() => {
      return logProtections?.connectorTypeConfigs?.filter((i: any) => i?.type === LogProtectionConnectorType.Integration) ?? []
   }, [logProtections?.connectorTypeConfigs]);

   const isIntegrationConfigExceed = useMemo(() => {
      return !!getOnlyIntegrationTypeConfigs?.length
   }, [logProtections?.connectorTypeConfigs, getOnlyIntegrationTypeConfigs])

   const updateWebhookConfig = (
      updated: LogProtectionNamespace.WebhookConfig,
      onSuccess: VoidFunction
   ) => {

      const ROOT_PATH = `/connectorTypeConfigs${isWebhookExist ? `/${getIsWebhookConfigIndexExist()}` : '/-'}`
      const CONTAINER_PATH = 'webhookConfig';

      let value = {
         type: LogProtectionConnectorType.Webhook,
         [CONTAINER_PATH]: updated
      }


      attemptUpdateLogProtection([{
         op: isWebhookExist ? 'replace' : 'add',
         path: ROOT_PATH,
         value
      }], logProtections?.id, () => {
         typeof onSuccess === 'function' && onSuccess()
      })
   }

   const createIntegrationConfig = (
      params: CreateIntegrationConfig,
      onSuccess: VoidFunction
   ) => {

      const { updated, selectedService } = params as any;

      const existingIntegration = getIsServiceHaveIntegration(selectedService.id);

      if (!existingIntegration) {
         const customPaths = _.map(customFields, 'property');
         const customValues: Record<string, any> = {}

         const result = Object.keys(updated).reduce((acc, jsonPath) => {
            // Clean the path by removing leading slash if present
            const cleanPath = jsonPath.startsWith("/") ? jsonPath.slice(1) : jsonPath;

            if (customPaths.includes(jsonPath)) {
               customValues[cleanPath] = updated[jsonPath] as any
               return acc;
            }

            const dotNotationPath = cleanPath.split("/").join(".");

            _.set(acc, dotNotationPath, updated[jsonPath] as any);

            return acc;
         }, {});

         const payload = {
            name: customValues?.name,
            type: "MONITORING",
            subOrganization: {
               name: user?.organization?.name,
               externalKey: user?.organization?.id
            },
            target: {
               accessPoint: {
                  type: "SP",
                  service: {
                     id: selectedService?.id
                  },
                  accessPointTypeConfig: {
                     type: AccessPointConfigType.APIKeyFlow
                  },
                  ...result,
               }
            }
         };

         attemptCreateIntegration(logProtections?.id, payload, ({ data: { id } }: Record<string, any>) => {

            const ROOT_PATH = '/connectorTypeConfigs/-';
            const payload = [
               {
                  "op": "add",
                  "path": ROOT_PATH,
                  "value": {
                     "type": LogProtectionConnectorType.Integration,
                     "integration": {
                        id
                     },
                     "service": {
                        "id": selectedService?.id
                     },
                     "serviceProfile": {
                        "id": selectedService?.serviceProfile?.id
                     }
                  }
               }
            ]

            // After successfully creating the integration, update log protection
            attemptUpdateLogProtection(
               payload,
               logProtections?.id,
               () => {
                  typeof onSuccess === 'function' && (
                     onSuccess()
                  )
               }
            );
         })
      } else {
         const ROOT_PATH = '/target'
         const CONTAINER_PATH = 'accessPoint';
         const CUSTOM_NAME_PATH = '/name';

         let payload: Record<string, any> = {}

         Object.entries(updated)?.forEach(([key, value]) => {
            const keys = key.split('/').filter(Boolean);
            const result = {};
            let current: any = result;

            if (CUSTOM_NAME_PATH !== key) {
               keys.forEach((key, index) => {
                  current[key] = index === keys.length - 1 ? value : {};
                  current = current[key];
               });
            }
            payload = { ...payload, ...result }
         })

         payload = [
            {
               op: "replace",
               path: CUSTOM_NAME_PATH,
               value: updated?.[CUSTOM_NAME_PATH]
            },
            {
               op: "replace",
               path: ROOT_PATH,
               value: {
                  [CONTAINER_PATH]: {
                     type: "SP",
                     service: {
                        id: selectedService?.id
                     },
                     accessPointTypeConfig: {
                        type: AccessPointConfigType.APIKeyFlow
                     },
                     ...payload
                  }
               }
            }
         ];

         attemptUpdateIntegration(
            logProtections?.id,
            existingIntegration?.integration?.id as any,
            payload,
            () => {
               typeof onSuccess === 'function' && (
                  onSuccess()
               )
            }
         )
      }

   }

   return (
      <IntegrationCreateContext.Provider
         value={{
            ...defaultValues,
            ...security,
            ...value,

            getIsWebhookConfigExist,
            getIsIntegrationConfigExist,
            getIsWebhookConfigIndexExist,

            // function for updated the webhook config
            updateWebhookConfig,
            // function for updated the integration config
            createIntegrationConfig,

            getIsServiceHaveIntegration,

            isWebhookExist,
            isIntegrationExist,
            isIntegrationConfigExceed
         }}
         {...props}
      />
   )
}

export const useIntegrationCreateProvider = (): Values => {
   try {
      return useContext(IntegrationCreateContext) as any;
   } catch (error) {
      console.error(`should be use with <IntegrationCreationCreateProvider />`)
      return null as any
   }
}