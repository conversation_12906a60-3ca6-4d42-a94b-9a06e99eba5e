{
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/.server/**/*.ts",
    "**/.server/**/*.tsx",
    "**/.client/**/*.ts",
    "**/.client/**/*.tsx",
    "app/types/**/*.d.ts",
    "app/declaration/**/*.d.ts"
, "app/components/third-party/react-table/Filter.jsx", "app/sections/dashboard-v2/layout/helper.js", "app/sections/security/log-protection/integration-list/create/step-contents/integration-details/webhook", "app/sections/security/log-protection/integration-list/create/step-contents/integration-details/standard/helper.js"  ],
  "compilerOptions": {
    // "paths": {
    //   "components/*": [
    //     "app/components/*"
    //   ],
    // },
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ES2022"
    ],
    "types": [
      "@remix-run/node",
      "vite/client"
    ],
    "isolatedModules": true,
    "esModuleInterop": true,
    "jsx": "react-jsx",
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "target": "ES2022",
    "strict": true,
    "allowJs": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "*": [
        "./app/*",
      ],
      "components/*": [
        "./app/components/*"
      ],
      "utils/*": [
        "./app/utils/*"
      ],
      "styles/*": [
        "./app/styles/*"
      ],
      "hooks/*": [
        "./app/hooks/*"
      ],
      "events/*": [
        "./app/events/*"
      ]
    },
    // Vite takes care of building everything, not tsc.
    "noEmit": true
  }
}