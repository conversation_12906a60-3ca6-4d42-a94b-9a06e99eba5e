
import { useEffect } from "react";
import { AxiosResponse } from "axios";
import {
  Box,
  Stack,
  Typography,
  CircularProgress,
  Paper,
  useTheme,
  alpha,
} from "@mui/material";

import { tenantsClient } from "services/tenants.service";

import { ResponseModel } from "types/common";
import Tenant from "types/tenant";
import { State } from "hooks/useStatus";
import { Routes } from "constants/route";

const TenantOnboardingState = () => {
   const theme = useTheme();

   useEffect(() => {
      const interval = setInterval(async () => {
         try {
            const response: AxiosResponse<ResponseModel<Tenant>> = await tenantsClient.getAllTenants();

            const tenant = response.data.data.at(0);

            // Commented out for testing - uncomment in production
            if (tenant?.state === State.PROVISIONED) {
               /**
                * redirect to dashboard page
                */
               window.location.href = Routes.QuickStart
            }
         } catch (error) {
            console.error(error);
         }
      }, 5000);

      return () => clearInterval(interval);
   }, [])

   return (
      <Box
         sx={{
            height: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: theme.palette.background.default,
            position: 'relative',
            overflow: 'hidden',
         }}
      >
         {/* Background Pattern */}
         <Box
            sx={{
               position: 'absolute',
               top: 0,
               left: 0,
               right: 0,
               bottom: 0,
               opacity: 0.03,
               backgroundImage: `radial-gradient(circle at 1px 1px, ${theme.palette.primary.main} 1px, transparent 1px)`,
               backgroundSize: '50px 50px',
            }}
         />

         {/* Main Content */}
         <Paper
            elevation={0}
            sx={{
               position: 'relative',
               p: 6,
               borderRadius: 3,
               backgroundColor: theme.palette.background.paper,
               border: `1px solid ${theme.palette.divider}`,
               maxWidth: 480,
               width: '100%',
               mx: 3,
            }}
         >
            <Stack spacing={4} alignItems="center">
               {/* Loading Animation */}
               <Box sx={{ position: 'relative' }}>
                  <CircularProgress
                     size={80}
                     thickness={2}
                     sx={{
                        color: theme.palette.primary.main,
                     }}
                  />
                  <Box
                     sx={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        width: 60,
                        height: 60,
                        borderRadius: '50%',
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                     }}
                  >
                     <Box
                        sx={{
                           width: 40,
                           height: 40,
                           borderRadius: '50%',
                           backgroundColor: alpha(theme.palette.primary.main, 0.2),
                        }}
                     />
                  </Box>
               </Box>

               {/* Text Content */}
               <Stack spacing={2} alignItems="center" textAlign="center">
                  <Typography
                     variant="h4"
                     fontWeight={600}
                     sx={{
                        color: theme.palette.text.primary,
                        mb: 1,
                     }}
                  >
                     Creating your account
                  </Typography>
                  <Typography
                     variant="body1"
                     sx={{
                        color: theme.palette.text.secondary,
                        maxWidth: 360,
                     }}
                  >
                     Please wait while we set up your workspace. This usually takes a few moments.
                  </Typography>
               </Stack>

               {/* Help Text */}
               <Typography
                  variant="caption"
                  sx={{
                     color: theme.palette.text.secondary,
                     mt: 2,
                     textAlign: 'center',
                  }}
               >
                  If this takes longer than expected, please{' '}
                  <Box
                     component="a"
                     href="#"
                     onClick={(e) => {
                        e.preventDefault();
                        window.location.reload();
                     }}
                     sx={{
                        color: theme.palette.primary.main,
                        textDecoration: 'none',
                        fontWeight: 500,
                        '&:hover': {
                           textDecoration: 'underline',
                        },
                     }}
                  >
                     refresh the page
                  </Box>
               </Typography>
            </Stack>
         </Paper>
      </Box>
   );
};

export default TenantOnboardingState
