import { BarChartProps } from "@mui/x-charts";
import { CSSProperties } from "react";

export const BREADCRUMB_S = { title: '', links: [{}] };

export const GRID_LAYOUT_ROOT_PROPS = {
   rowHeight: 30,
   verticalCompact: true,
   compactType: 'vertical',
}

export const LAYOUT_BREAK_POINTS = {
   xl: 1400,
   lg: 1200,
   md: 960,
   sm: 768,
   xs: 576
};

export const CONTAINER_STYLE_S: CSSProperties = {
   height: '100%'
}

export const GRAPH_CONTAINER_MARGIN_S: BarChartProps['margin'] = { top: 40, bottom: 20, right: 20 }