import { styled } from '@mui/material/styles';
import {
  Box,
  Button,
  Card,
  Paper,
  TextField,
  Chip,
  IconButton,
  Typography,
  AppBar,
} from '@mui/material';

// Modern Card with subtle shadow and hover effect
export const ModernCard = styled(Card)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.spacing(2),
  border: 'none',
  boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    transform: 'translateY(-2px)',
  },
}));

// Primary button with Unizo brand colors
export const PrimaryButton = styled(Button)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  textTransform: 'none',
  fontWeight: 500,
  padding: theme.spacing(1.25, 3),
  borderRadius: theme.spacing(1),
  boxShadow: 'none',
  '&:hover': {
    backgroundColor: theme.palette.primary.dark,
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  },
  '&:active': {
    backgroundColor: theme.palette.primary.darker,
  },
  '&.Mui-disabled': {
    backgroundColor: theme.palette.primary.lighter,
    color: theme.palette.primary.light,
  },
}));

// Secondary button with orange accent
export const SecondaryButton = styled(Button)(({ theme }) => ({
  backgroundColor: '#ea580c',
  color: '#ffffff',
  textTransform: 'none',
  fontWeight: 500,
  padding: theme.spacing(1.25, 3),
  borderRadius: theme.spacing(1),
  boxShadow: 'none',
  '&:hover': {
    backgroundColor: '#db3f0a',
    boxShadow: '0 4px 6px -1px rgba(234, 88, 12, 0.3)',
  },
  '&:active': {
    backgroundColor: '#b52e0a',
  },
  '&.Mui-disabled': {
    backgroundColor: '#fde8d6',
    color: '#f6a372',
  },
}));

// Outline button
export const OutlineButton = styled(Button)(({ theme }) => ({
  color: '#21384f',
  border: '1px solid #d4e0ed',
  backgroundColor: 'transparent',
  textTransform: 'none',
  fontWeight: 500,
  padding: theme.spacing(1, 2.5),
  borderRadius: theme.spacing(1),
  '&:hover': {
    backgroundColor: '#f8fafc',
    borderColor: '#21384f',
  },
}));

// Modern text field
export const ModernTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    backgroundColor: '#ffffff',
    borderRadius: theme.spacing(1),
    transition: 'all 0.2s ease-in-out',
    '& fieldset': {
      borderColor: '#d4e0ed',
      transition: 'border-color 0.2s ease-in-out',
    },
    '&:hover fieldset': {
      borderColor: '#6f99c3',
    },
    '&.Mui-focused fieldset': {
      borderColor: '#21384f',
      borderWidth: 2,
    },
  },
  '& .MuiInputLabel-root': {
    color: '#6b7280',
    '&.Mui-focused': {
      color: '#21384f',
    },
  },
}));

// Status chip variants
export const StatusChip = styled(Chip)<{ 
  status?: 'active' | 'inactive' | 'pending' | 'error' | 'success' 
}>(({ theme, status = 'active' }) => {
  const statusStyles = {
    active: {
      backgroundColor: theme.palette.primary.lighter,
      color: theme.palette.primary.main,
      borderColor: theme.palette.primary.light,
    },
    inactive: {
      backgroundColor: theme.palette.grey[100],
      color: theme.palette.text.secondary,
      borderColor: theme.palette.grey[300],
    },
    pending: {
      backgroundColor: theme.palette.warning.lighter,
      color: theme.palette.warning.main,
      borderColor: theme.palette.warning.light,
    },
    error: {
      backgroundColor: theme.palette.error.lighter,
      color: theme.palette.error.main,
      borderColor: theme.palette.error.light,
    },
    success: {
      backgroundColor: theme.palette.success.lighter,
      color: theme.palette.success.main,
      borderColor: theme.palette.success.light,
    },
  };

  return {
    ...statusStyles[status],
    fontWeight: 500,
    fontSize: '0.875rem',
    border: '1px solid',
    borderRadius: theme.spacing(1),
  };
});

// Modern navigation bar
export const ModernAppBar = styled(AppBar)(({ theme }) => ({
  backgroundColor: '#ffffff',
  color: '#1f2937',
  boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  borderBottom: '1px solid #e5e7eb',
}));

// Dashboard stat card
export const StatCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: theme.spacing(2),
  backgroundColor: '#ffffff',
  border: '1px solid #edf2f7',
  boxShadow: 'none',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    borderColor: '#d4e0ed',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  },
}));

// Section container with subtle background
export const SectionContainer = styled(Box)(({ theme }) => ({
  backgroundColor: '#f9fafb',
  borderRadius: theme.spacing(2),
  padding: theme.spacing(3),
  marginBottom: theme.spacing(3),
}));

// Modern icon button
export const ModernIconButton = styled(IconButton)(({ theme }) => ({
  color: '#6b7280',
  padding: theme.spacing(1),
  '&:hover': {
    backgroundColor: '#f3f4f6',
    color: '#21384f',
  },
  '&.active': {
    color: '#ea580c',
    backgroundColor: '#fef5ee',
  },
}));

// Page header typography
export const PageHeader = styled(Typography)(({ theme }) => ({
  fontSize: '2rem',
  fontWeight: 700,
  color: '#1f2937',
  marginBottom: theme.spacing(1),
}));

// Subheader typography
export const SubHeader = styled(Typography)(({ theme }) => ({
  fontSize: '1rem',
  color: '#6b7280',
  marginBottom: theme.spacing(3),
}));

// Feature card for landing pages
export const FeatureCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(4),
  height: '100%',
  backgroundColor: '#ffffff',
  border: '1px solid #edf2f7',
  borderRadius: theme.spacing(2),
  boxShadow: 'none',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    borderColor: '#ea580c',
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    transform: 'translateY(-4px)',
  },
}));

// Gradient background box
export const GradientBox = styled(Box)(({ theme }) => ({
  background: 'linear-gradient(135deg, #21384f 0%, #2a4765 50%, #35597e 100%)',
  color: '#ffffff',
  padding: theme.spacing(6),
  borderRadius: theme.spacing(2),
}));

// Modern divider
export const ModernDivider = styled(Box)(({ theme }) => ({
  height: 1,
  backgroundColor: '#e5e7eb',
  margin: theme.spacing(3, 0),
}));