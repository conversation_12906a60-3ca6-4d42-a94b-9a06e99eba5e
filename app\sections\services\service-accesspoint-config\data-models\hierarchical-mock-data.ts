export interface HierarchicalField {
  id: string;
  name: string;
  displayName?: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  required?: boolean;
  description?: string;
  path?: string;
  children?: HierarchicalField[];
}

// Unified Data Models (Unizo Common Data Model)
export const UNIFIED_DATA_MODELS: Record<string, HierarchicalField> = {
  organization: {
    id: 'organization',
    name: 'organization',
    displayName: 'Organization',
    type: 'object',
    children: [
      {
        id: 'org_id',
        name: 'org_id',
        displayName: 'Organization ID',
        type: 'string',
        required: true,
        description: 'Unique identifier for the organization',
      },
      {
        id: 'org_name',
        name: 'org_name',
        displayName: 'Organization Name',
        type: 'string',
        required: true,
        description: 'Display name of the organization',
      },
      {
        id: 'org_url',
        name: 'org_url',
        displayName: 'Organization URL',
        type: 'string',
        required: false,
        description: 'Website URL of the organization',
      },
      {
        id: 'created_at',
        name: 'created_at',
        displayName: 'Created Date',
        type: 'date',
        required: true,
        description: 'Date when the organization was created',
      },
      {
        id: 'member_count',
        name: 'member_count',
        displayName: 'Member Count',
        type: 'number',
        required: false,
        description: 'Total number of members',
      },
      {
        id: 'metadata',
        name: 'metadata',
        displayName: 'Metadata',
        type: 'object',
        children: [
          {
            id: 'metadata.tags',
            name: 'tags',
            displayName: 'Tags',
            type: 'array',
            description: 'Associated tags',
          },
          {
            id: 'metadata.custom_fields',
            name: 'custom_fields',
            displayName: 'Custom Fields',
            type: 'object',
            description: 'User-defined custom fields',
          },
        ],
      },
    ],
  },
  repository: {
    id: 'repository',
    name: 'repository',
    displayName: 'Repository',
    type: 'object',
    children: [
      {
        id: 'repo_id',
        name: 'repo_id',
        displayName: 'Repository ID',
        type: 'string',
        required: true,
        description: 'Unique identifier for the repository',
      },
      {
        id: 'repo_name',
        name: 'repo_name',
        displayName: 'Repository Name',
        type: 'string',
        required: true,
        description: 'Name of the repository',
      },
      {
        id: 'description',
        name: 'description',
        displayName: 'Description',
        type: 'string',
        required: false,
        description: 'Repository description',
      },
      {
        id: 'private',
        name: 'private',
        displayName: 'Is Private',
        type: 'boolean',
        required: true,
        description: 'Whether the repository is private',
      },
      {
        id: 'default_branch',
        name: 'default_branch',
        displayName: 'Default Branch',
        type: 'string',
        required: true,
        description: 'Default branch name',
      },
      {
        id: 'language',
        name: 'language',
        displayName: 'Primary Language',
        type: 'string',
        required: false,
        description: 'Primary programming language',
      },
      {
        id: 'size',
        name: 'size',
        displayName: 'Repository Size',
        type: 'number',
        required: false,
        description: 'Size of the repository in KB',
      },
      {
        id: 'owner_info',
        name: 'owner_info',
        displayName: 'Owner Information',
        type: 'object',
        children: [
          {
            id: 'owner_info.id',
            name: 'id',
            displayName: 'Owner ID',
            type: 'string',
            description: 'Owner unique identifier',
          },
          {
            id: 'owner_info.name',
            name: 'name',
            displayName: 'Owner Name',
            type: 'string',
            description: 'Owner display name',
          },
          {
            id: 'owner_info.type',
            name: 'type',
            displayName: 'Owner Type',
            type: 'string',
            description: 'Type of owner (user/organization)',
          },
          {
            id: 'owner_info.contact',
            name: 'contact',
            displayName: 'Contact Info',
            type: 'object',
            children: [
              {
                id: 'owner_info.contact.email',
                name: 'email',
                displayName: 'Email',
                type: 'string',
                description: 'Contact email address',
              },
              {
                id: 'owner_info.contact.phone',
                name: 'phone',
                displayName: 'Phone',
                type: 'string',
                description: 'Contact phone number',
              },
              {
                id: 'owner_info.contact.address',
                name: 'address',
                displayName: 'Address',
                type: 'object',
                children: [
                  {
                    id: 'owner_info.contact.address.street',
                    name: 'street',
                    displayName: 'Street',
                    type: 'string',
                    description: 'Street address',
                  },
                  {
                    id: 'owner_info.contact.address.city',
                    name: 'city',
                    displayName: 'City',
                    type: 'string',
                    description: 'City name',
                  },
                  {
                    id: 'owner_info.contact.address.country',
                    name: 'country',
                    displayName: 'Country',
                    type: 'string',
                    description: 'Country name',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'permissions',
        name: 'permissions',
        displayName: 'Permissions',
        type: 'object',
        children: [
          {
            id: 'permissions.admin',
            name: 'admin',
            displayName: 'Admin Access',
            type: 'boolean',
            description: 'Administrative access permission',
          },
          {
            id: 'permissions.push',
            name: 'push',
            displayName: 'Push Access',
            type: 'boolean',
            description: 'Push/write access permission',
          },
          {
            id: 'permissions.pull',
            name: 'pull',
            displayName: 'Pull Access',
            type: 'boolean',
            description: 'Pull/read access permission',
          },
        ],
      },
      {
        id: 'contributors',
        name: 'contributors',
        displayName: 'Contributors',
        type: 'array',
        description: 'List of repository contributors',
        children: [
          {
            id: 'contributors.item',
            name: 'item',
            displayName: 'Contributor',
            type: 'object',
            children: [
              {
                id: 'contributors.item.user_id',
                name: 'user_id',
                displayName: 'User ID',
                type: 'string',
                description: 'Contributor user ID',
              },
              {
                id: 'contributors.item.username',
                name: 'username',
                displayName: 'Username',
                type: 'string',
                description: 'Contributor username',
              },
              {
                id: 'contributors.item.contributions',
                name: 'contributions',
                displayName: 'Contributions',
                type: 'number',
                description: 'Number of contributions',
              },
              {
                id: 'contributors.item.profile',
                name: 'profile',
                displayName: 'Profile',
                type: 'object',
                children: [
                  {
                    id: 'contributors.item.profile.avatar_url',
                    name: 'avatar_url',
                    displayName: 'Avatar URL',
                    type: 'string',
                    description: 'Profile avatar URL',
                  },
                  {
                    id: 'contributors.item.profile.bio',
                    name: 'bio',
                    displayName: 'Bio',
                    type: 'string',
                    description: 'User biography',
                  },
                  {
                    id: 'contributors.item.profile.location',
                    name: 'location',
                    displayName: 'Location',
                    type: 'string',
                    description: 'User location',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'tags',
        name: 'tags',
        displayName: 'Tags',
        type: 'array',
        description: 'Repository tags/topics',
        children: [
          {
            id: 'tags.item',
            name: 'item',
            displayName: 'Tag',
            type: 'string',
            description: 'Tag name',
          },
        ],
      },
      {
        id: 'webhooks',
        name: 'webhooks',
        displayName: 'Webhooks',
        type: 'array',
        description: 'Configured webhooks',
        children: [
          {
            id: 'webhooks.item',
            name: 'item',
            displayName: 'Webhook',
            type: 'object',
            children: [
              {
                id: 'webhooks.item.id',
                name: 'id',
                displayName: 'Webhook ID',
                type: 'string',
                description: 'Webhook identifier',
              },
              {
                id: 'webhooks.item.url',
                name: 'url',
                displayName: 'URL',
                type: 'string',
                description: 'Webhook endpoint URL',
              },
              {
                id: 'webhooks.item.events',
                name: 'events',
                displayName: 'Events',
                type: 'array',
                description: 'Subscribed events',
                children: [
                  {
                    id: 'webhooks.item.events.item',
                    name: 'item',
                    displayName: 'Event',
                    type: 'string',
                    description: 'Event name',
                  },
                ],
              },
              {
                id: 'webhooks.item.config',
                name: 'config',
                displayName: 'Configuration',
                type: 'object',
                children: [
                  {
                    id: 'webhooks.item.config.content_type',
                    name: 'content_type',
                    displayName: 'Content Type',
                    type: 'string',
                    description: 'Request content type',
                  },
                  {
                    id: 'webhooks.item.config.secret',
                    name: 'secret',
                    displayName: 'Secret',
                    type: 'string',
                    description: 'Webhook secret',
                  },
                  {
                    id: 'webhooks.item.config.insecure_ssl',
                    name: 'insecure_ssl',
                    displayName: 'Insecure SSL',
                    type: 'boolean',
                    description: 'Allow insecure SSL',
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        id: 'settings',
        name: 'settings',
        displayName: 'Repository Settings',
        type: 'object',
        children: [
          {
            id: 'settings.has_issues',
            name: 'has_issues',
            displayName: 'Has Issues',
            type: 'boolean',
            description: 'Issues feature enabled',
          },
          {
            id: 'settings.has_wiki',
            name: 'has_wiki',
            displayName: 'Has Wiki',
            type: 'boolean',
            description: 'Wiki feature enabled',
          },
          {
            id: 'settings.has_pages',
            name: 'has_pages',
            displayName: 'Has Pages',
            type: 'boolean',
            description: 'GitHub Pages enabled',
          },
          {
            id: 'settings.branch_protection',
            name: 'branch_protection',
            displayName: 'Branch Protection',
            type: 'object',
            children: [
              {
                id: 'settings.branch_protection.required_reviews',
                name: 'required_reviews',
                displayName: 'Required Reviews',
                type: 'number',
                description: 'Number of required reviews',
              },
              {
                id: 'settings.branch_protection.dismiss_stale_reviews',
                name: 'dismiss_stale_reviews',
                displayName: 'Dismiss Stale Reviews',
                type: 'boolean',
                description: 'Dismiss stale PR reviews',
              },
              {
                id: 'settings.branch_protection.require_code_owner_reviews',
                name: 'require_code_owner_reviews',
                displayName: 'Require Code Owner Reviews',
                type: 'boolean',
                description: 'Require code owner reviews',
              },
              {
                id: 'settings.branch_protection.restrictions',
                name: 'restrictions',
                displayName: 'Restrictions',
                type: 'object',
                children: [
                  {
                    id: 'settings.branch_protection.restrictions.users',
                    name: 'users',
                    displayName: 'Users',
                    type: 'array',
                    description: 'Restricted users',
                    children: [
                      {
                        id: 'settings.branch_protection.restrictions.users.item',
                        name: 'item',
                        displayName: 'User',
                        type: 'string',
                        description: 'Username',
                      },
                    ],
                  },
                  {
                    id: 'settings.branch_protection.restrictions.teams',
                    name: 'teams',
                    displayName: 'Teams',
                    type: 'array',
                    description: 'Restricted teams',
                    children: [
                      {
                        id: 'settings.branch_protection.restrictions.teams.item',
                        name: 'item',
                        displayName: 'Team',
                        type: 'string',
                        description: 'Team name',
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  pull_request: {
    id: 'pull_request',
    name: 'pull_request',
    displayName: 'Pull Request',
    type: 'object',
    children: [
      {
        id: 'pr_number',
        name: 'pr_number',
        displayName: 'PR Number',
        type: 'number',
        required: true,
        description: 'Pull request number',
      },
      {
        id: 'title',
        name: 'title',
        displayName: 'Title',
        type: 'string',
        required: true,
        description: 'Pull request title',
      },
      {
        id: 'description',
        name: 'description',
        displayName: 'Description',
        type: 'string',
        required: false,
        description: 'Pull request description',
      },
      {
        id: 'state',
        name: 'state',
        displayName: 'State',
        type: 'string',
        required: true,
        description: 'Current state (open, closed, merged)',
      },
      {
        id: 'source_branch',
        name: 'source_branch',
        displayName: 'Source Branch',
        type: 'string',
        required: true,
        description: 'Branch to merge from',
      },
      {
        id: 'target_branch',
        name: 'target_branch',
        displayName: 'Target Branch',
        type: 'string',
        required: true,
        description: 'Branch to merge into',
      },
      {
        id: 'created_by',
        name: 'created_by',
        displayName: 'Created By',
        type: 'string',
        required: true,
        description: 'User who created the PR',
      },
      {
        id: 'reviewers',
        name: 'reviewers',
        displayName: 'Reviewers',
        type: 'array',
        required: false,
        description: 'List of reviewers',
      },
      {
        id: 'labels',
        name: 'labels',
        displayName: 'Labels',
        type: 'array',
        required: false,
        description: 'Associated labels',
      },
      {
        id: 'author_details',
        name: 'author_details',
        displayName: 'Author Details',
        type: 'object',
        children: [
          {
            id: 'author_details.id',
            name: 'id',
            displayName: 'Author ID',
            type: 'string',
            description: 'Author unique identifier',
          },
          {
            id: 'author_details.username',
            name: 'username',
            displayName: 'Username',
            type: 'string',
            description: 'Author username',
          },
          {
            id: 'author_details.email',
            name: 'email',
            displayName: 'Email',
            type: 'string',
            description: 'Author email address',
          },
        ],
      },
      {
        id: 'merge_info',
        name: 'merge_info',
        displayName: 'Merge Information',
        type: 'object',
        children: [
          {
            id: 'merge_info.merged',
            name: 'merged',
            displayName: 'Is Merged',
            type: 'boolean',
            description: 'Whether the PR has been merged',
          },
          {
            id: 'merge_info.merged_at',
            name: 'merged_at',
            displayName: 'Merged At',
            type: 'date',
            description: 'Date when PR was merged',
          },
          {
            id: 'merge_info.merged_by',
            name: 'merged_by',
            displayName: 'Merged By',
            type: 'string',
            description: 'User who merged the PR',
          },
        ],
      },
    ],
  },
  commit: {
    id: 'commit',
    name: 'commit',
    displayName: 'Commit',
    type: 'object',
    children: [
      {
        id: 'commit_sha',
        name: 'commit_sha',
        displayName: 'Commit SHA',
        type: 'string',
        required: true,
        description: 'Unique commit identifier',
      },
      {
        id: 'message',
        name: 'message',
        displayName: 'Commit Message',
        type: 'string',
        required: true,
        description: 'Commit message',
      },
      {
        id: 'author_name',
        name: 'author_name',
        displayName: 'Author Name',
        type: 'string',
        required: true,
        description: 'Name of the commit author',
      },
      {
        id: 'author_email',
        name: 'author_email',
        displayName: 'Author Email',
        type: 'string',
        required: true,
        description: 'Email of the commit author',
      },
      {
        id: 'committed_date',
        name: 'committed_date',
        displayName: 'Commit Date',
        type: 'date',
        required: true,
        description: 'Date of the commit',
      },
      {
        id: 'files_changed',
        name: 'files_changed',
        displayName: 'Files Changed',
        type: 'number',
        required: false,
        description: 'Number of files changed',
      },
      {
        id: 'additions',
        name: 'additions',
        displayName: 'Lines Added',
        type: 'number',
        required: false,
        description: 'Number of lines added',
      },
      {
        id: 'deletions',
        name: 'deletions',
        displayName: 'Lines Deleted',
        type: 'number',
        required: false,
        description: 'Number of lines deleted',
      },
      {
        id: 'commit_details',
        name: 'commit_details',
        displayName: 'Commit Details',
        type: 'object',
        children: [
          {
            id: 'commit_details.tree_sha',
            name: 'tree_sha',
            displayName: 'Tree SHA',
            type: 'string',
            description: 'Git tree SHA',
          },
          {
            id: 'commit_details.parent_shas',
            name: 'parent_shas',
            displayName: 'Parent SHAs',
            type: 'array',
            description: 'Parent commit SHAs',
          },
          {
            id: 'commit_details.verified',
            name: 'verified',
            displayName: 'Verified',
            type: 'boolean',
            description: 'Whether commit is verified',
          },
        ],
      },
    ],
  },
  branch: {
    id: 'branch',
    name: 'branch',
    displayName: 'Branch',
    type: 'object',
    children: [
      {
        id: 'branch_name',
        name: 'branch_name',
        displayName: 'Branch Name',
        type: 'string',
        required: true,
        description: 'Name of the branch',
      },
      {
        id: 'commit_sha',
        name: 'commit_sha',
        displayName: 'Latest Commit SHA',
        type: 'string',
        required: true,
        description: 'SHA of the latest commit',
      },
      {
        id: 'protected',
        name: 'protected',
        displayName: 'Is Protected',
        type: 'boolean',
        required: true,
        description: 'Whether the branch is protected',
      },
      {
        id: 'ahead_by',
        name: 'ahead_by',
        displayName: 'Commits Ahead',
        type: 'number',
        required: false,
        description: 'Number of commits ahead of base',
      },
      {
        id: 'behind_by',
        name: 'behind_by',
        displayName: 'Commits Behind',
        type: 'number',
        required: false,
        description: 'Number of commits behind base',
      },
      {
        id: 'protection_rules',
        name: 'protection_rules',
        displayName: 'Protection Rules',
        type: 'object',
        children: [
          {
            id: 'protection_rules.require_reviews',
            name: 'require_reviews',
            displayName: 'Require Reviews',
            type: 'boolean',
            description: 'Whether reviews are required',
          },
          {
            id: 'protection_rules.dismiss_stale_reviews',
            name: 'dismiss_stale_reviews',
            displayName: 'Dismiss Stale Reviews',
            type: 'boolean',
            description: 'Whether to dismiss stale reviews',
          },
          {
            id: 'protection_rules.require_status_checks',
            name: 'require_status_checks',
            displayName: 'Require Status Checks',
            type: 'boolean',
            description: 'Whether status checks are required',
          },
        ],
      },
    ],
  },
};

// Provider Data Models (Service Provider Fields)
export const PROVIDER_DATA_MODELS: Record<string, Record<string, HierarchicalField>> = {
  github: {
    organization: {
      id: 'github_org',
      name: 'github_organization',
      displayName: 'GitHub Organization',
      type: 'object',
      children: [
        {
          id: 'id',
          name: 'id',
          displayName: 'ID',
          type: 'number',
          description: 'GitHub organization ID',
        },
        {
          id: 'login',
          name: 'login',
          displayName: 'Login',
          type: 'string',
          description: 'Organization login name',
        },
        {
          id: 'name',
          name: 'name',
          displayName: 'Name',
          type: 'string',
          description: 'Organization display name',
        },
        {
          id: 'blog',
          name: 'blog',
          displayName: 'Blog',
          type: 'string',
          description: 'Organization blog URL',
        },
        {
          id: 'created_at',
          name: 'created_at',
          displayName: 'Created At',
          type: 'string',
          description: 'ISO 8601 creation date',
        },
        {
          id: 'public_repos',
          name: 'public_repos',
          displayName: 'Public Repos',
          type: 'number',
          description: 'Number of public repositories',
        },
        {
          id: 'owner',
          name: 'owner',
          displayName: 'Owner',
          type: 'object',
          children: [
            {
              id: 'owner.login',
              name: 'login',
              displayName: 'Login',
              type: 'string',
              description: 'Owner login name',
            },
            {
              id: 'owner.id',
              name: 'id',
              displayName: 'ID',
              type: 'number',
              description: 'Owner ID',
            },
            {
              id: 'owner.type',
              name: 'type',
              displayName: 'Type',
              type: 'string',
              description: 'Owner type (User/Organization)',
            },
            {
              id: 'owner.site_admin',
              name: 'site_admin',
              displayName: 'Site Admin',
              type: 'boolean',
              description: 'Whether owner is site admin',
            },
          ],
        },
        {
          id: 'plan',
          name: 'plan',
          displayName: 'Plan',
          type: 'object',
          children: [
            {
              id: 'plan.name',
              name: 'name',
              displayName: 'Plan Name',
              type: 'string',
              description: 'Billing plan name',
            },
            {
              id: 'plan.space',
              name: 'space',
              displayName: 'Space',
              type: 'number',
              description: 'Storage space in plan',
            },
            {
              id: 'plan.private_repos',
              name: 'private_repos',
              displayName: 'Private Repos',
              type: 'number',
              description: 'Number of private repos allowed',
            },
          ],
        },
      ],
    },
    repository: {
      id: 'github_repo',
      name: 'github_repository',
      displayName: 'GitHub Repository',
      type: 'object',
      children: [
        {
          id: 'id',
          name: 'id',
          displayName: 'ID',
          type: 'number',
          description: 'Repository ID',
        },
        {
          id: 'name',
          name: 'name',
          displayName: 'Name',
          type: 'string',
          description: 'Repository name',
        },
        {
          id: 'full_name',
          name: 'full_name',
          displayName: 'Full Name',
          type: 'string',
          description: 'Full repository name (owner/repo)',
        },
        {
          id: 'description',
          name: 'description',
          displayName: 'Description',
          type: 'string',
          description: 'Repository description',
        },
        {
          id: 'private',
          name: 'private',
          displayName: 'Private',
          type: 'boolean',
          description: 'Whether repository is private',
        },
        {
          id: 'default_branch',
          name: 'default_branch',
          displayName: 'Default Branch',
          type: 'string',
          description: 'Default branch name',
        },
        {
          id: 'language',
          name: 'language',
          displayName: 'Language',
          type: 'string',
          description: 'Primary programming language',
        },
        {
          id: 'size',
          name: 'size',
          displayName: 'Size',
          type: 'number',
          description: 'Repository size in KB',
        },
        {
          id: 'owner',
          name: 'owner',
          displayName: 'Owner',
          type: 'object',
          children: [
            {
              id: 'owner.login',
              name: 'login',
              displayName: 'Login',
              type: 'string',
              description: 'Owner login name',
            },
            {
              id: 'owner.id',
              name: 'id',
              displayName: 'ID',
              type: 'number',
              description: 'Owner ID',
            },
            {
              id: 'owner.avatar_url',
              name: 'avatar_url',
              displayName: 'Avatar URL',
              type: 'string',
              description: 'Owner avatar image URL',
            },
            {
              id: 'owner.type',
              name: 'type',
              displayName: 'Type',
              type: 'string',
              description: 'Owner type',
            },
            {
              id: 'owner.site_admin',
              name: 'site_admin',
              displayName: 'Site Admin',
              type: 'boolean',
              description: 'Is site administrator',
            },
            {
              id: 'owner.company',
              name: 'company',
              displayName: 'Company',
              type: 'string',
              description: 'Company name',
            },
            {
              id: 'owner.location',
              name: 'location',
              displayName: 'Location',
              type: 'string',
              description: 'Geographic location',
            },
          ],
        },
        {
          id: 'permissions',
          name: 'permissions',
          displayName: 'Permissions',
          type: 'object',
          children: [
            {
              id: 'permissions.admin',
              name: 'admin',
              displayName: 'Admin',
              type: 'boolean',
              description: 'Admin permission',
            },
            {
              id: 'permissions.push',
              name: 'push',
              displayName: 'Push',
              type: 'boolean',
              description: 'Push permission',
            },
            {
              id: 'permissions.pull',
              name: 'pull',
              displayName: 'Pull',
              type: 'boolean',
              description: 'Pull permission',
            },
            {
              id: 'permissions.maintain',
              name: 'maintain',
              displayName: 'Maintain',
              type: 'boolean',
              description: 'Maintain permission',
            },
            {
              id: 'permissions.triage',
              name: 'triage',
              displayName: 'Triage',
              type: 'boolean',
              description: 'Triage permission',
            },
          ],
        },
        {
          id: 'license',
          name: 'license',
          displayName: 'License',
          type: 'object',
          children: [
            {
              id: 'license.key',
              name: 'key',
              displayName: 'Key',
              type: 'string',
              description: 'License key identifier',
            },
            {
              id: 'license.name',
              name: 'name',
              displayName: 'Name',
              type: 'string',
              description: 'License full name',
            },
            {
              id: 'license.spdx_id',
              name: 'spdx_id',
              displayName: 'SPDX ID',
              type: 'string',
              description: 'SPDX license identifier',
            },
            {
              id: 'license.url',
              name: 'url',
              displayName: 'URL',
              type: 'string',
              description: 'License URL',
            },
          ],
        },
        {
          id: 'topics',
          name: 'topics',
          displayName: 'Topics',
          type: 'array',
          description: 'Repository topics/tags',
          children: [
            {
              id: 'topics.item',
              name: 'item',
              displayName: 'Topic',
              type: 'string',
              description: 'Topic name',
            },
          ],
        },
        {
          id: 'contributors',
          name: 'contributors',
          displayName: 'Contributors',
          type: 'array',
          description: 'Repository contributors',
          children: [
            {
              id: 'contributors.item',
              name: 'item',
              displayName: 'Contributor',
              type: 'object',
              children: [
                {
                  id: 'contributors.item.login',
                  name: 'login',
                  displayName: 'Login',
                  type: 'string',
                  description: 'Contributor username',
                },
                {
                  id: 'contributors.item.id',
                  name: 'id',
                  displayName: 'ID',
                  type: 'number',
                  description: 'Contributor ID',
                },
                {
                  id: 'contributors.item.avatar_url',
                  name: 'avatar_url',
                  displayName: 'Avatar URL',
                  type: 'string',
                  description: 'Avatar image URL',
                },
                {
                  id: 'contributors.item.contributions',
                  name: 'contributions',
                  displayName: 'Contributions',
                  type: 'number',
                  description: 'Number of contributions',
                },
                {
                  id: 'contributors.item.type',
                  name: 'type',
                  displayName: 'Type',
                  type: 'string',
                  description: 'Contributor type',
                },
                {
                  id: 'contributors.item.permissions',
                  name: 'permissions',
                  displayName: 'Permissions',
                  type: 'object',
                  children: [
                    {
                      id: 'contributors.item.permissions.pull',
                      name: 'pull',
                      displayName: 'Pull',
                      type: 'boolean',
                      description: 'Can pull',
                    },
                    {
                      id: 'contributors.item.permissions.push',
                      name: 'push',
                      displayName: 'Push',
                      type: 'boolean',
                      description: 'Can push',
                    },
                    {
                      id: 'contributors.item.permissions.admin',
                      name: 'admin',
                      displayName: 'Admin',
                      type: 'boolean',
                      description: 'Is admin',
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: 'hooks',
          name: 'hooks',
          displayName: 'Webhooks',
          type: 'array',
          description: 'Repository webhooks',
          children: [
            {
              id: 'hooks.item',
              name: 'item',
              displayName: 'Webhook',
              type: 'object',
              children: [
                {
                  id: 'hooks.item.id',
                  name: 'id',
                  displayName: 'ID',
                  type: 'number',
                  description: 'Webhook ID',
                },
                {
                  id: 'hooks.item.url',
                  name: 'url',
                  displayName: 'URL',
                  type: 'string',
                  description: 'Webhook URL',
                },
                {
                  id: 'hooks.item.name',
                  name: 'name',
                  displayName: 'Name',
                  type: 'string',
                  description: 'Webhook name',
                },
                {
                  id: 'hooks.item.active',
                  name: 'active',
                  displayName: 'Active',
                  type: 'boolean',
                  description: 'Is webhook active',
                },
                {
                  id: 'hooks.item.events',
                  name: 'events',
                  displayName: 'Events',
                  type: 'array',
                  description: 'Webhook events',
                  children: [
                    {
                      id: 'hooks.item.events.item',
                      name: 'item',
                      displayName: 'Event',
                      type: 'string',
                      description: 'Event name',
                    },
                  ],
                },
                {
                  id: 'hooks.item.config',
                  name: 'config',
                  displayName: 'Config',
                  type: 'object',
                  children: [
                    {
                      id: 'hooks.item.config.url',
                      name: 'url',
                      displayName: 'URL',
                      type: 'string',
                      description: 'Webhook endpoint',
                    },
                    {
                      id: 'hooks.item.config.content_type',
                      name: 'content_type',
                      displayName: 'Content Type',
                      type: 'string',
                      description: 'Content type',
                    },
                    {
                      id: 'hooks.item.config.insecure_ssl',
                      name: 'insecure_ssl',
                      displayName: 'Insecure SSL',
                      type: 'string',
                      description: 'Allow insecure SSL',
                    },
                    {
                      id: 'hooks.item.config.secret',
                      name: 'secret',
                      displayName: 'Secret',
                      type: 'string',
                      description: 'Webhook secret',
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: 'organization',
          name: 'organization',
          displayName: 'Organization',
          type: 'object',
          children: [
            {
              id: 'organization.login',
              name: 'login',
              displayName: 'Login',
              type: 'string',
              description: 'Organization login',
            },
            {
              id: 'organization.id',
              name: 'id',
              displayName: 'ID',
              type: 'number',
              description: 'Organization ID',
            },
            {
              id: 'organization.avatar_url',
              name: 'avatar_url',
              displayName: 'Avatar URL',
              type: 'string',
              description: 'Organization avatar',
            },
            {
              id: 'organization.description',
              name: 'description',
              displayName: 'Description',
              type: 'string',
              description: 'Organization description',
            },
          ],
        },
        {
          id: 'security',
          name: 'security',
          displayName: 'Security',
          type: 'object',
          children: [
            {
              id: 'security.vulnerability_alerts',
              name: 'vulnerability_alerts',
              displayName: 'Vulnerability Alerts',
              type: 'boolean',
              description: 'Vulnerability alerts enabled',
            },
            {
              id: 'security.automated_security_fixes',
              name: 'automated_security_fixes',
              displayName: 'Automated Security Fixes',
              type: 'boolean',
              description: 'Automated security fixes enabled',
            },
            {
              id: 'security.secret_scanning',
              name: 'secret_scanning',
              displayName: 'Secret Scanning',
              type: 'object',
              children: [
                {
                  id: 'security.secret_scanning.status',
                  name: 'status',
                  displayName: 'Status',
                  type: 'string',
                  description: 'Secret scanning status',
                },
                {
                  id: 'security.secret_scanning.push_protection',
                  name: 'push_protection',
                  displayName: 'Push Protection',
                  type: 'boolean',
                  description: 'Push protection enabled',
                },
              ],
            },
          ],
        },
        {
          id: 'environments',
          name: 'environments',
          displayName: 'Environments',
          type: 'array',
          description: 'Deployment environments',
          children: [
            {
              id: 'environments.item',
              name: 'item',
              displayName: 'Environment',
              type: 'object',
              children: [
                {
                  id: 'environments.item.id',
                  name: 'id',
                  displayName: 'ID',
                  type: 'number',
                  description: 'Environment ID',
                },
                {
                  id: 'environments.item.name',
                  name: 'name',
                  displayName: 'Name',
                  type: 'string',
                  description: 'Environment name',
                },
                {
                  id: 'environments.item.url',
                  name: 'url',
                  displayName: 'URL',
                  type: 'string',
                  description: 'Environment URL',
                },
                {
                  id: 'environments.item.protection_rules',
                  name: 'protection_rules',
                  displayName: 'Protection Rules',
                  type: 'array',
                  description: 'Environment protection rules',
                  children: [
                    {
                      id: 'environments.item.protection_rules.item',
                      name: 'item',
                      displayName: 'Rule',
                      type: 'object',
                      children: [
                        {
                          id: 'environments.item.protection_rules.item.id',
                          name: 'id',
                          displayName: 'ID',
                          type: 'number',
                          description: 'Rule ID',
                        },
                        {
                          id: 'environments.item.protection_rules.item.type',
                          name: 'type',
                          displayName: 'Type',
                          type: 'string',
                          description: 'Rule type',
                        },
                        {
                          id: 'environments.item.protection_rules.item.wait_timer',
                          name: 'wait_timer',
                          displayName: 'Wait Timer',
                          type: 'number',
                          description: 'Wait timer in minutes',
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    pull_request: {
      id: 'github_pr',
      name: 'github_pull_request',
      displayName: 'GitHub Pull Request',
      type: 'object',
      children: [
        {
          id: 'number',
          name: 'number',
          displayName: 'Number',
          type: 'number',
          description: 'Pull request number',
        },
        {
          id: 'title',
          name: 'title',
          displayName: 'Title',
          type: 'string',
          description: 'Pull request title',
        },
        {
          id: 'body',
          name: 'body',
          displayName: 'Body',
          type: 'string',
          description: 'Pull request description',
        },
        {
          id: 'state',
          name: 'state',
          displayName: 'State',
          type: 'string',
          description: 'PR state (open/closed)',
        },
        {
          id: 'user',
          name: 'user',
          displayName: 'User',
          type: 'object',
          children: [
            {
              id: 'user.login',
              name: 'login',
              displayName: 'Login',
              type: 'string',
              description: 'User login name',
            },
            {
              id: 'user.id',
              name: 'id',
              displayName: 'ID',
              type: 'number',
              description: 'User ID',
            },
            {
              id: 'user.avatar_url',
              name: 'avatar_url',
              displayName: 'Avatar URL',
              type: 'string',
              description: 'User avatar URL',
            },
          ],
        },
        {
          id: 'head',
          name: 'head',
          displayName: 'Head',
          type: 'object',
          children: [
            {
              id: 'head.ref',
              name: 'ref',
              displayName: 'Reference',
              type: 'string',
              description: 'Source branch name',
            },
            {
              id: 'head.sha',
              name: 'sha',
              displayName: 'SHA',
              type: 'string',
              description: 'Head commit SHA',
            },
            {
              id: 'head.repo',
              name: 'repo',
              displayName: 'Repository',
              type: 'object',
              description: 'Source repository',
            },
          ],
        },
        {
          id: 'base',
          name: 'base',
          displayName: 'Base',
          type: 'object',
          children: [
            {
              id: 'base.ref',
              name: 'ref',
              displayName: 'Reference',
              type: 'string',
              description: 'Target branch name',
            },
            {
              id: 'base.sha',
              name: 'sha',
              displayName: 'SHA',
              type: 'string',
              description: 'Base commit SHA',
            },
            {
              id: 'base.repo',
              name: 'repo',
              displayName: 'Repository',
              type: 'object',
              description: 'Target repository',
            },
          ],
        },
        {
          id: 'labels',
          name: 'labels',
          displayName: 'Labels',
          type: 'array',
          description: 'PR labels',
        },
        {
          id: 'assignees',
          name: 'assignees',
          displayName: 'Assignees',
          type: 'array',
          description: 'Assigned users',
        },
        {
          id: 'requested_reviewers',
          name: 'requested_reviewers',
          displayName: 'Requested Reviewers',
          type: 'array',
          description: 'Users requested for review',
        },
      ],
    },
    commit: {
      id: 'github_commit',
      name: 'github_commit',
      displayName: 'GitHub Commit',
      type: 'object',
      children: [
        {
          id: 'sha',
          name: 'sha',
          displayName: 'SHA',
          type: 'string',
          description: 'Commit SHA',
        },
        {
          id: 'message',
          name: 'message',
          displayName: 'Message',
          type: 'string',
          description: 'Commit message',
        },
        {
          id: 'author',
          name: 'author',
          displayName: 'Author',
          type: 'object',
          children: [
            {
              id: 'author.name',
              name: 'name',
              displayName: 'Name',
              type: 'string',
              description: 'Author name',
            },
            {
              id: 'author.email',
              name: 'email',
              displayName: 'Email',
              type: 'string',
              description: 'Author email',
            },
            {
              id: 'author.date',
              name: 'date',
              displayName: 'Date',
              type: 'date',
              description: 'Author date',
            },
          ],
        },
        {
          id: 'committer',
          name: 'committer',
          displayName: 'Committer',
          type: 'object',
          children: [
            {
              id: 'committer.name',
              name: 'name',
              displayName: 'Name',
              type: 'string',
              description: 'Committer name',
            },
            {
              id: 'committer.email',
              name: 'email',
              displayName: 'Email',
              type: 'string',
              description: 'Committer email',
            },
            {
              id: 'committer.date',
              name: 'date',
              displayName: 'Date',
              type: 'date',
              description: 'Commit date',
            },
          ],
        },
        {
          id: 'tree',
          name: 'tree',
          displayName: 'Tree',
          type: 'object',
          children: [
            {
              id: 'tree.sha',
              name: 'sha',
              displayName: 'SHA',
              type: 'string',
              description: 'Tree SHA',
            },
            {
              id: 'tree.url',
              name: 'url',
              displayName: 'URL',
              type: 'string',
              description: 'Tree URL',
            },
          ],
        },
        {
          id: 'parents',
          name: 'parents',
          displayName: 'Parents',
          type: 'array',
          description: 'Parent commits',
        },
        {
          id: 'stats',
          name: 'stats',
          displayName: 'Statistics',
          type: 'object',
          children: [
            {
              id: 'stats.total',
              name: 'total',
              displayName: 'Total',
              type: 'number',
              description: 'Total changes',
            },
            {
              id: 'stats.additions',
              name: 'additions',
              displayName: 'Additions',
              type: 'number',
              description: 'Lines added',
            },
            {
              id: 'stats.deletions',
              name: 'deletions',
              displayName: 'Deletions',
              type: 'number',
              description: 'Lines deleted',
            },
          ],
        },
        {
          id: 'files',
          name: 'files',
          displayName: 'Files',
          type: 'array',
          description: 'Changed files',
        },
      ],
    },
    branch: {
      id: 'github_branch',
      name: 'github_branch',
      displayName: 'GitHub Branch',
      type: 'object',
      children: [
        {
          id: 'name',
          name: 'name',
          displayName: 'Name',
          type: 'string',
          description: 'Branch name',
        },
        {
          id: 'commit',
          name: 'commit',
          displayName: 'Commit',
          type: 'object',
          children: [
            {
              id: 'commit.sha',
              name: 'sha',
              displayName: 'SHA',
              type: 'string',
              description: 'Latest commit SHA',
            },
            {
              id: 'commit.url',
              name: 'url',
              displayName: 'URL',
              type: 'string',
              description: 'Commit URL',
            },
          ],
        },
        {
          id: 'protected',
          name: 'protected',
          displayName: 'Protected',
          type: 'boolean',
          description: 'Whether branch is protected',
        },
        {
          id: 'protection',
          name: 'protection',
          displayName: 'Protection',
          type: 'object',
          description: 'Protection settings',
        },
        {
          id: 'protection_url',
          name: 'protection_url',
          displayName: 'Protection URL',
          type: 'string',
          description: 'Protection settings URL',
        },
      ],
    },
  },
  gitlab: {
    organization: {
      id: 'gitlab_org',
      name: 'gitlab_organization',
      displayName: 'GitLab Organization',
      type: 'object',
      children: [
        {
          id: 'id',
          name: 'id',
          displayName: 'ID',
          type: 'number',
          description: 'GitLab group ID',
        },
        {
          id: 'path',
          name: 'path',
          displayName: 'Path',
          type: 'string',
          description: 'Group path',
        },
        {
          id: 'name',
          name: 'name',
          displayName: 'Name',
          type: 'string',
          description: 'Group name',
        },
        {
          id: 'web_url',
          name: 'web_url',
          displayName: 'Web URL',
          type: 'string',
          description: 'Group web URL',
        },
        {
          id: 'created_at',
          name: 'created_at',
          displayName: 'Created At',
          type: 'string',
          description: 'Creation date',
        },
        {
          id: 'parent_id',
          name: 'parent_id',
          displayName: 'Parent ID',
          type: 'number',
          description: 'Parent group ID',
        },
        {
          id: 'statistics',
          name: 'statistics',
          displayName: 'Statistics',
          type: 'object',
          children: [
            {
              id: 'statistics.storage_size',
              name: 'storage_size',
              displayName: 'Storage Size',
              type: 'number',
              description: 'Total storage size',
            },
            {
              id: 'statistics.repository_size',
              name: 'repository_size',
              displayName: 'Repository Size',
              type: 'number',
              description: 'Repository storage size',
            },
            {
              id: 'statistics.lfs_objects_size',
              name: 'lfs_objects_size',
              displayName: 'LFS Objects Size',
              type: 'number',
              description: 'LFS objects storage size',
            },
          ],
        },
      ],
    },
    repository: {
      id: 'gitlab_repo',
      name: 'gitlab_repository',
      displayName: 'GitLab Repository',
      type: 'object',
      children: [
        {
          id: 'id',
          name: 'id',
          displayName: 'ID',
          type: 'number',
          description: 'Project ID',
        },
        {
          id: 'name',
          name: 'name',
          displayName: 'Name',
          type: 'string',
          description: 'Project name',
        },
        {
          id: 'path_with_namespace',
          name: 'path_with_namespace',
          displayName: 'Path with Namespace',
          type: 'string',
          description: 'Full project path',
        },
        {
          id: 'description',
          name: 'description',
          displayName: 'Description',
          type: 'string',
          description: 'Project description',
        },
        {
          id: 'visibility',
          name: 'visibility',
          displayName: 'Visibility',
          type: 'string',
          description: 'Project visibility level',
        },
        {
          id: 'default_branch',
          name: 'default_branch',
          displayName: 'Default Branch',
          type: 'string',
          description: 'Default branch name',
        },
        {
          id: 'namespace',
          name: 'namespace',
          displayName: 'Namespace',
          type: 'object',
          children: [
            {
              id: 'namespace.id',
              name: 'id',
              displayName: 'ID',
              type: 'number',
              description: 'Namespace ID',
            },
            {
              id: 'namespace.name',
              name: 'name',
              displayName: 'Name',
              type: 'string',
              description: 'Namespace name',
            },
            {
              id: 'namespace.path',
              name: 'path',
              displayName: 'Path',
              type: 'string',
              description: 'Namespace path',
            },
            {
              id: 'namespace.kind',
              name: 'kind',
              displayName: 'Kind',
              type: 'string',
              description: 'Namespace type',
            },
          ],
        },
        {
          id: 'statistics',
          name: 'statistics',
          displayName: 'Statistics',
          type: 'object',
          children: [
            {
              id: 'statistics.commit_count',
              name: 'commit_count',
              displayName: 'Commit Count',
              type: 'number',
              description: 'Total number of commits',
            },
            {
              id: 'statistics.storage_size',
              name: 'storage_size',
              displayName: 'Storage Size',
              type: 'number',
              description: 'Total storage size',
            },
            {
              id: 'statistics.repository_size',
              name: 'repository_size',
              displayName: 'Repository Size',
              type: 'number',
              description: 'Repository size',
            },
          ],
        },
        {
          id: 'permissions',
          name: 'permissions',
          displayName: 'Permissions',
          type: 'object',
          children: [
            {
              id: 'permissions.project_access',
              name: 'project_access',
              displayName: 'Project Access',
              type: 'object',
              description: 'Project access level',
            },
            {
              id: 'permissions.group_access',
              name: 'group_access',
              displayName: 'Group Access',
              type: 'object',
              description: 'Group access level',
            },
          ],
        },
      ],
    },
  },
};