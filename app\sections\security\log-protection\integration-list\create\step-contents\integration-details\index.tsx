import { RefObject, useEffect, useMemo, useRef } from "react"; // React Hooks

// MUI Components
import { Divider, Stack, Typography } from "@mui/material";

// Custom Hooks - Service Profile
import { useServiceProfile } from "hooks/useServiceProfile";

// Global Store
import useLogProtection from "store/security/log-protection";

import Orchestrator from "./orchestrator";


const TITLE = 'Steps: Integration Details'

export default () => {
   const { values } = useLogProtection(); // Retrieve values and setRef from the useLogProtection hook
   const { loadImage } = useServiceProfile(); // Fetch the loadImage function from the useServiceProfile hook

   const selectedService = values?.service; // Extract the selected service from the values object

   // Memoize the header component to optimize rendering
   const header = useMemo(() => {
      return (
         <Stack direction="row" gap={1.5} alignItems="center">
            {/* Conditionally render the service profile image if it exists */}
            {selectedService?.serviceProfile &&
               loadImage(selectedService.serviceProfile, { size: 'small' })
            }
            {/* Vertical divider between the image and the title */}
            <Divider orientation="vertical" variant="fullWidth" flexItem />
            {/* Display the title */}
            <Typography variant="h4">{TITLE}</Typography>
         </Stack>
      );
   }, [selectedService?.serviceProfile]); // Recalculate only when the selected service profile changes

   return (
      <Stack gap={3.5}>
         {header}
         <Orchestrator />
      </Stack>
   );
};
