import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  IconButton,
  TextField,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Autocomplete,
  Chip,
  FormControlLabel,
  Alert,
  Tooltip,
  Paper,
  Divider,
  useTheme,
  alpha,
  <PERSON>lapse,
  Grid,
} from '@mui/material';
import {
  Plus,
  X,
  Edit2,
  Save,
  FileText,
  Hash,
  ToggleLeft,
  Calendar,
  List,
  Braces,
  ChevronRight,
  ChevronDown,
  Info,
  Code,
  Database,
  Trash2,
  Copy,
  FileJson,
  Eye,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react';
import CheckBox from '@mui/material/Checkbox';

interface AdditionalFieldsProps {
  dataModel: any;
  serviceProfile: any;
  onFieldsChange?: (fields: AdditionalField[]) => void;
}

interface AdditionalField {
  id: string;
  name: string;
  displayName: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  description?: string;
  required: boolean;
  defaultValue?: any;
  children?: AdditionalField[];
  parentId?: string;
  arrayItemType?: string;
  format?: string;
  enum?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    minLength?: number;
    maxLength?: number;
  };
}

const TYPE_INFO = {
  string: {
    icon: <FileText size={16} />,
    color: '#10b981',
    label: 'String',
    description: 'Text values like names, descriptions, IDs',
    examples: ['username', 'email', 'product_name'],
    formats: ['email', 'url', 'uuid', 'date-time', 'password'],
  },
  number: {
    icon: <Hash size={16} />,
    color: '#3b82f6',
    label: 'Number',
    description: 'Numeric values including integers and decimals',
    examples: ['price', 'quantity', 'rating'],
    formats: ['integer', 'float', 'decimal'],
  },
  boolean: {
    icon: <ToggleLeft size={16} />,
    color: '#f59e0b',
    label: 'Boolean',
    description: 'True/false values',
    examples: ['is_active', 'has_permission', 'enabled'],
    formats: [],
  },
  date: {
    icon: <Calendar size={16} />,
    color: '#8b5cf6',
    label: 'Date',
    description: 'Date and time values',
    examples: ['created_at', 'due_date', 'last_modified'],
    formats: ['date', 'time', 'date-time'],
  },
  array: {
    icon: <List size={16} />,
    color: '#ef4444',
    label: 'Array',
    description: 'List of values of the same type',
    examples: ['tags', 'categories', 'team_members'],
    formats: [],
  },
  object: {
    icon: <Braces size={16} />,
    color: '#6b7280',
    label: 'Object',
    description: 'Complex nested structure with multiple fields',
    examples: ['address', 'metadata', 'configuration'],
    formats: [],
  },
};

function FieldDialog({
  open,
  onClose,
  onSave,
  field,
  existingFields,
}: {
  open: boolean;
  onClose: () => void;
  onSave: (field: AdditionalField) => void;
  field?: AdditionalField;
  existingFields: AdditionalField[];
}) {
  const theme = useTheme();
  const isEdit = !!field;
  
  const [formData, setFormData] = useState<AdditionalField>({
    id: field?.id || '',
    name: field?.name || '',
    displayName: field?.displayName || '',
    type: field?.type || 'string',
    description: field?.description || '',
    required: field?.required || false,
    defaultValue: field?.defaultValue || '',
    arrayItemType: field?.arrayItemType || 'string',
    format: field?.format || '',
    enum: field?.enum || [],
    validation: field?.validation || {},
  });

  const [enumInput, setEnumInput] = useState('');

  const handleSave = () => {
    if (!formData.name.trim()) return;

    const newField: AdditionalField = {
      ...formData,
      id: formData.id || `field_${Date.now()}`,
      name: formData.name.trim().toLowerCase().replace(/\s+/g, '_'),
      displayName: formData.displayName.trim() || formData.name.trim(),
    };

    onSave(newField);
    onClose();
  };

  const handleAddEnum = () => {
    if (enumInput.trim() && !formData.enum?.includes(enumInput.trim())) {
      setFormData(prev => ({
        ...prev,
        enum: [...(prev.enum || []), enumInput.trim()],
      }));
      setEnumInput('');
    }
  };

  const handleRemoveEnum = (value: string) => {
    setFormData(prev => ({
      ...prev,
      enum: prev.enum?.filter(e => e !== value) || [],
    }));
  };

  const typeInfo = TYPE_INFO[formData.type];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Box sx={{ color: typeInfo.color }}>
            {typeInfo.icon}
          </Box>
          <Typography variant="h6">
            {isEdit ? 'Edit' : 'Create'} Additional Field
          </Typography>
        </Stack>
      </DialogTitle>
      
      <DialogContent dividers>
        <Stack spacing={3} sx={{ pt: 2 }}>
          {/* Basic Information */}
          <Stack spacing={2}>
            <Typography variant="subtitle2" color="text.secondary">
              Basic Information
            </Typography>
            
            <TextField
              label="Field Name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., customer_id"
              required
              helperText="Use lowercase with underscores (snake_case)"
              disabled={isEdit}
            />

            <TextField
              label="Display Name"
              value={formData.displayName}
              onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
              placeholder="e.g., Customer ID"
              helperText="Human-readable name for the field"
            />

            <TextField
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Brief description of the field purpose"
              multiline
              rows={2}
            />
          </Stack>

          <Divider />

          {/* Type Configuration */}
          <Stack spacing={2}>
            <Typography variant="subtitle2" color="text.secondary">
              Type Configuration
            </Typography>

            <Autocomplete
              value={typeInfo}
              onChange={(_, value) => {
                if (value) {
                  const type = Object.keys(TYPE_INFO).find(
                    k => TYPE_INFO[k as keyof typeof TYPE_INFO] === value
                  ) as AdditionalField['type'];
                  setFormData(prev => ({ ...prev, type }));
                }
              }}
              options={Object.values(TYPE_INFO)}
              getOptionLabel={(option) => option.label}
              renderInput={(params) => (
                <TextField {...params} label="Field Type" required />
              )}
              renderOption={(props, option) => (
                <Box component="li" {...props}>
                  <Stack direction="row" spacing={2} alignItems="center" sx={{ width: '100%' }}>
                    <Box sx={{ color: option.color }}>
                      {option.icon}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body2">{option.label}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {option.description}
                      </Typography>
                    </Box>
                  </Stack>
                </Box>
              )}
              disabled={isEdit}
            />

            {/* Format selection for applicable types */}
            {typeInfo.formats.length > 0 && (
              <Autocomplete
                value={formData.format || null}
                onChange={(_, value) => setFormData(prev => ({ ...prev, format: value || '' }))}
                options={typeInfo.formats}
                renderInput={(params) => (
                  <TextField {...params} label="Format (Optional)" />
                )}
              />
            )}

            {/* Array item type selection */}
            {formData.type === 'array' && (
              <Autocomplete
                value={TYPE_INFO[formData.arrayItemType as keyof typeof TYPE_INFO] || TYPE_INFO.string}
                onChange={(_, value) => {
                  if (value) {
                    const type = Object.keys(TYPE_INFO).find(
                      k => TYPE_INFO[k as keyof typeof TYPE_INFO] === value
                    );
                    setFormData(prev => ({ ...prev, arrayItemType: type || 'string' }));
                  }
                }}
                options={Object.values(TYPE_INFO).filter(t => t.label !== 'Array')}
                getOptionLabel={(option) => option.label}
                renderInput={(params) => (
                  <TextField {...params} label="Array Item Type" required />
                )}
                renderOption={(props, option) => (
                  <Box component="li" {...props}>
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Box sx={{ color: option.color }}>
                        {option.icon}
                      </Box>
                      <Typography variant="body2">{option.label}</Typography>
                    </Stack>
                  </Box>
                )}
              />
            )}

            {/* Enum values for string type */}
            {formData.type === 'string' && (
              <Box>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  Enum Values (Optional)
                </Typography>
                <Stack direction="row" spacing={1} sx={{ mb: 1 }}>
                  <TextField
                    size="small"
                    placeholder="Add enum value"
                    value={enumInput}
                    onChange={(e) => setEnumInput(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddEnum();
                      }
                    }}
                    sx={{ flex: 1 }}
                  />
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={handleAddEnum}
                    disabled={!enumInput.trim()}
                  >
                    Add
                  </Button>
                </Stack>
                <Stack direction="row" spacing={1} flexWrap="wrap">
                  {formData.enum?.map((value) => (
                    <Chip
                      key={value}
                      label={value}
                      size="small"
                      onDelete={() => handleRemoveEnum(value)}
                    />
                  ))}
                </Stack>
              </Box>
            )}

            {/* Default value */}
            <TextField
              label="Default Value (Optional)"
              value={formData.defaultValue}
              onChange={(e) => setFormData(prev => ({ ...prev, defaultValue: e.target.value }))}
              placeholder={`e.g., ${formData.type === 'boolean' ? 'false' : formData.type === 'number' ? '0' : '""'}`}
              helperText="Default value when field is not provided"
            />

            <FormControlLabel
              control={
                <CheckBox
                  checked={formData.required}
                  onChange={(e) => setFormData(prev => ({ ...prev, required: e.target.checked }))}
                />
              }
              label="Required field"
            />
          </Stack>

          {/* Validation Rules */}
          {(formData.type === 'string' || formData.type === 'number') && (
            <>
              <Divider />
              <Stack spacing={2}>
                <Typography variant="subtitle2" color="text.secondary">
                  Validation Rules (Optional)
                </Typography>
                
                {formData.type === 'string' && (
                  <>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <TextField
                          label="Min Length"
                          type="number"
                          value={formData.validation?.minLength || ''}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            validation: {
                              ...prev.validation,
                              minLength: e.target.value ? parseInt(e.target.value) : undefined,
                            },
                          }))}
                        />
                      </Grid>
                      <Grid item xs={6}>
                        <TextField
                          label="Max Length"
                          type="number"
                          value={formData.validation?.maxLength || ''}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            validation: {
                              ...prev.validation,
                              maxLength: e.target.value ? parseInt(e.target.value) : undefined,
                            },
                          }))}
                        />
                      </Grid>
                    </Grid>
                    <TextField
                      label="Pattern (Regex)"
                      value={formData.validation?.pattern || ''}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        validation: {
                          ...prev.validation,
                          pattern: e.target.value,
                        },
                      }))}
                      placeholder="e.g., ^[A-Z]{2}-\\d{4}$"
                      helperText="Regular expression for validation"
                    />
                  </>
                )}
                
                {formData.type === 'number' && (
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        label="Min Value"
                        type="number"
                        value={formData.validation?.min || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          validation: {
                            ...prev.validation,
                            min: e.target.value ? parseFloat(e.target.value) : undefined,
                          },
                        }))}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        label="Max Value"
                        type="number"
                        value={formData.validation?.max || ''}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          validation: {
                            ...prev.validation,
                            max: e.target.value ? parseFloat(e.target.value) : undefined,
                          },
                        }))}
                      />
                    </Grid>
                  </Grid>
                )}
              </Stack>
            </>
          )}

          {/* Examples */}
          <Alert severity="info" icon={<Info size={16} />}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              <strong>Common examples for {typeInfo.label} fields:</strong>
            </Typography>
            <Typography variant="caption" component="div">
              {typeInfo.examples.join(', ')}
            </Typography>
          </Alert>
        </Stack>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          variant="contained"
          onClick={handleSave}
          disabled={!formData.name.trim()}
          startIcon={<Save size={16} />}
        >
          {isEdit ? 'Update' : 'Create'} Field
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function FieldCard({
  field,
  onEdit,
  onDelete,
  onAddChild,
  level = 0,
}: {
  field: AdditionalField;
  onEdit: (field: AdditionalField) => void;
  onDelete: (field: AdditionalField) => void;
  onAddChild?: (parentId: string) => void;
  level?: number;
}) {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(true);
  const typeInfo = TYPE_INFO[field.type];
  const hasChildren = field.children && field.children.length > 0;
  const canHaveChildren = field.type === 'object';

  return (
    <Box sx={{ ml: level * 3 }}>
      <Card
        variant="outlined"
        sx={{
          mb: 1,
          backgroundColor: level > 0 ? alpha(theme.palette.primary.main, 0.02) : 'transparent',
          '&:hover': {
            backgroundColor: alpha(theme.palette.primary.main, 0.04),
          },
        }}
      >
        <CardContent sx={{ py: 2, '&:last-child': { pb: 2 } }}>
          <Stack spacing={1}>
            <Stack direction="row" alignItems="center" spacing={2}>
              {canHaveChildren && (
                <IconButton
                  size="small"
                  onClick={() => setExpanded(!expanded)}
                  sx={{ p: 0.5 }}
                >
                  {expanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                </IconButton>
              )}
              
              <Box sx={{ color: typeInfo.color }}>
                {typeInfo.icon}
              </Box>
              
              <Box sx={{ flex: 1 }}>
                <Stack direction="row" spacing={1} alignItems="center">
                  <Typography variant="subtitle2">
                    {field.displayName}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ({field.name})
                  </Typography>
                  {field.required && (
                    <Chip label="Required" size="small" color="error" sx={{ height: 20 }} />
                  )}
                  {field.format && (
                    <Chip label={field.format} size="small" sx={{ height: 20 }} />
                  )}
                </Stack>
                
                {field.description && (
                  <Typography variant="caption" color="text.secondary">
                    {field.description}
                  </Typography>
                )}
                
                <Stack direction="row" spacing={2} sx={{ mt: 0.5 }}>
                  <Typography variant="caption" color="text.secondary">
                    Type: <strong>{typeInfo.label}</strong>
                    {field.type === 'array' && field.arrayItemType && (
                      <span> of {field.arrayItemType}</span>
                    )}
                  </Typography>
                  
                  {field.defaultValue && (
                    <Typography variant="caption" color="text.secondary">
                      Default: <code>{field.defaultValue}</code>
                    </Typography>
                  )}
                  
                  {field.enum && field.enum.length > 0 && (
                    <Typography variant="caption" color="text.secondary">
                      Enum: {field.enum.join(', ')}
                    </Typography>
                  )}
                </Stack>
              </Box>
              
              <Stack direction="row" spacing={1}>
                {canHaveChildren && onAddChild && (
                  <Tooltip title="Add nested field">
                    <IconButton
                      size="small"
                      onClick={() => onAddChild(field.id)}
                      sx={{ color: 'primary.main' }}
                    >
                      <Plus size={16} />
                    </IconButton>
                  </Tooltip>
                )}
                
                <Tooltip title="Edit field">
                  <IconButton
                    size="small"
                    onClick={() => onEdit(field)}
                    sx={{ color: 'text.secondary' }}
                  >
                    <Edit2 size={16} />
                  </IconButton>
                </Tooltip>
                
                <Tooltip title="Delete field">
                  <IconButton
                    size="small"
                    onClick={() => onDelete(field)}
                    sx={{ color: 'error.main' }}
                  >
                    <Trash2 size={16} />
                  </IconButton>
                </Tooltip>
              </Stack>
            </Stack>
          </Stack>
        </CardContent>
      </Card>
      
      {canHaveChildren && hasChildren && expanded && (
        <Box sx={{ ml: 2, borderLeft: `2px solid ${theme.palette.divider}` }}>
          {field.children?.map((child) => (
            <FieldCard
              key={child.id}
              field={child}
              onEdit={onEdit}
              onDelete={onDelete}
              onAddChild={onAddChild}
              level={level + 1}
            />
          ))}
        </Box>
      )}
    </Box>
  );
}

export default function AdditionalFields({
  dataModel,
  serviceProfile,
  onFieldsChange,
}: AdditionalFieldsProps) {
  const theme = useTheme();
  const [fields, setFields] = useState<AdditionalField[]>([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingField, setEditingField] = useState<AdditionalField | undefined>();
  const [parentFieldId, setParentFieldId] = useState<string | undefined>();
  const [viewMode, setViewMode] = useState<'visual' | 'json'>('visual');
  const [jsonContent, setJsonContent] = useState('');
  const [jsonError, setJsonError] = useState<string | null>(null);
  const [jsonDialogOpen, setJsonDialogOpen] = useState(false);

  const handleAddField = (parentId?: string) => {
    setParentFieldId(parentId);
    setEditingField(undefined);
    setDialogOpen(true);
  };

  const handleEditField = (field: AdditionalField) => {
    setEditingField(field);
    setParentFieldId(undefined);
    setDialogOpen(true);
  };

  const handleDeleteField = (field: AdditionalField) => {
    const deleteFieldAndChildren = (fieldId: string, allFields: AdditionalField[]): AdditionalField[] => {
      return allFields.filter(f => f.id !== fieldId && f.parentId !== fieldId)
        .map(f => ({
          ...f,
          children: f.children ? deleteFieldAndChildren(fieldId, f.children) : undefined,
        }));
    };

    const updatedFields = deleteFieldAndChildren(field.id, fields);
    setFields(updatedFields);
    onFieldsChange?.(updatedFields);
  };

  const handleSaveField = (field: AdditionalField) => {
    if (editingField) {
      // Update existing field
      const updateField = (fields: AdditionalField[]): AdditionalField[] => {
        return fields.map(f => {
          if (f.id === field.id) {
            return { ...field, children: f.children };
          }
          if (f.children) {
            return { ...f, children: updateField(f.children) };
          }
          return f;
        });
      };
      
      const updatedFields = updateField(fields);
      setFields(updatedFields);
      onFieldsChange?.(updatedFields);
    } else {
      // Add new field
      if (parentFieldId) {
        // Add as child field
        const addChildField = (fields: AdditionalField[]): AdditionalField[] => {
          return fields.map(f => {
            if (f.id === parentFieldId) {
              return {
                ...f,
                children: [...(f.children || []), { ...field, parentId: parentFieldId }],
              };
            }
            if (f.children) {
              return { ...f, children: addChildField(f.children) };
            }
            return f;
          });
        };
        
        const updatedFields = addChildField(fields);
        setFields(updatedFields);
        onFieldsChange?.(updatedFields);
      } else {
        // Add as root field
        const updatedFields = [...fields, field];
        setFields(updatedFields);
        onFieldsChange?.(updatedFields);
      }
    }
  };

  const exportFieldsSchema = () => {
    const schema = {
      dataModel: dataModel.name,
      serviceProfile: serviceProfile?.name,
      additionalFields: fields,
      generatedAt: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(schema, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${dataModel.name}_additional_fields.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const validateJsonSchema = (json: string): { valid: boolean; error?: string; fields?: AdditionalField[] } => {
    try {
      const parsed = JSON.parse(json);
      
      // Check if it's an array
      if (!Array.isArray(parsed)) {
        return { valid: false, error: 'JSON must be an array of field definitions' };
      }
      
      // Validate each field
      const validatedFields: AdditionalField[] = [];
      
      for (let i = 0; i < parsed.length; i++) {
        const field = parsed[i];
        
        // Required properties
        if (!field.name || typeof field.name !== 'string') {
          return { valid: false, error: `Field at index ${i}: 'name' is required and must be a string` };
        }
        
        if (!field.type || !['string', 'number', 'boolean', 'date', 'array', 'object'].includes(field.type)) {
          return { valid: false, error: `Field at index ${i}: 'type' must be one of: string, number, boolean, date, array, object` };
        }
        
        // Validate array item type
        if (field.type === 'array' && field.arrayItemType) {
          if (!['string', 'number', 'boolean', 'date', 'object'].includes(field.arrayItemType)) {
            return { valid: false, error: `Field at index ${i}: 'arrayItemType' must be a valid type` };
          }
        }
        
        // Validate nested children
        if (field.children && !Array.isArray(field.children)) {
          return { valid: false, error: `Field at index ${i}: 'children' must be an array` };
        }
        
        // Validate validation rules
        if (field.validation) {
          if (typeof field.validation !== 'object') {
            return { valid: false, error: `Field at index ${i}: 'validation' must be an object` };
          }
        }
        
        // Create validated field with defaults
        const validatedField: AdditionalField = {
          id: field.id || `field_${Date.now()}_${i}`,
          name: field.name.trim().toLowerCase().replace(/\s+/g, '_'),
          displayName: field.displayName || field.name,
          type: field.type,
          description: field.description || '',
          required: field.required === true,
          defaultValue: field.defaultValue,
          arrayItemType: field.arrayItemType,
          format: field.format,
          enum: field.enum,
          validation: field.validation,
          parentId: field.parentId,
        };
        
        // Recursively validate children
        if (field.children) {
          const childResult = validateJsonSchema(JSON.stringify(field.children));
          if (!childResult.valid) {
            return { valid: false, error: `Field '${field.name}' children: ${childResult.error}` };
          }
          validatedField.children = childResult.fields;
        }
        
        validatedFields.push(validatedField);
      }
      
      return { valid: true, fields: validatedFields };
    } catch (e) {
      return { valid: false, error: `Invalid JSON: ${e instanceof Error ? e.message : 'Unknown error'}` };
    }
  };

  const handleJsonImport = () => {
    const result = validateJsonSchema(jsonContent);
    
    if (result.valid && result.fields) {
      setFields(result.fields);
      onFieldsChange?.(result.fields);
      setJsonError(null);
      setJsonDialogOpen(false);
      setViewMode('visual');
    } else {
      setJsonError(result.error || 'Invalid JSON');
    }
  };

  const switchToJsonView = () => {
    const jsonData = JSON.stringify(fields, null, 2);
    setJsonContent(jsonData);
    setJsonError(null);
    setViewMode('json');
  };

  const switchToVisualView = () => {
    if (jsonContent.trim()) {
      const result = validateJsonSchema(jsonContent);
      if (result.valid && result.fields) {
        setFields(result.fields);
        onFieldsChange?.(result.fields);
        setJsonError(null);
      } else {
        setJsonError(result.error || 'Invalid JSON');
        return;
      }
    }
    setViewMode('visual');
  };

  return (
    <Stack spacing={3} sx={{ height: '100%' }}>
      {/* Header */}
      <Box>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h6" gutterBottom>
              Additional Fields
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Define custom fields that can be used in field mappings. Create any data structure you need
              with support for nested objects, arrays, and validation rules.
            </Typography>
          </Box>
          
          <Stack direction="row" spacing={1}>
            <Tooltip title={viewMode === 'visual' ? 'Switch to JSON view' : 'Switch to visual view'}>
              <IconButton 
                onClick={() => viewMode === 'visual' ? switchToJsonView() : switchToVisualView()}
                color={viewMode === 'json' ? 'primary' : 'default'}
              >
                {viewMode === 'visual' ? <FileJson size={20} /> : <Eye size={20} />}
              </IconButton>
            </Tooltip>
            <Tooltip title="Export field schema">
              <IconButton onClick={exportFieldsSchema} disabled={fields.length === 0}>
                <Code size={20} />
              </IconButton>
            </Tooltip>
            {viewMode === 'visual' && (
              <Button
                variant="contained"
                startIcon={<Plus size={16} />}
                onClick={() => handleAddField()}
              >
                Add Field
              </Button>
            )}
          </Stack>
        </Stack>
      </Box>

      <Divider />

      {/* Fields List or JSON Editor */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {viewMode === 'visual' ? (
          // Visual Mode
          fields.length === 0 ? (
            <Paper
              sx={{
                p: 4,
                textAlign: 'center',
                backgroundColor: alpha(theme.palette.primary.main, 0.02),
                border: `2px dashed ${theme.palette.divider}`,
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Database size={40} color={theme.palette.text.secondary} />
              </Box>
              <Typography variant="h6" sx={{ mb: 1 }}>
                No Additional Fields Yet
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Start by creating custom fields that match your specific business requirements.
                These fields will be available for mapping in the Field Mappings tab.
              </Typography>
              <Stack direction="row" spacing={2} justifyContent="center">
                <Button
                  variant="outlined"
                  startIcon={<Plus size={16} />}
                  onClick={() => handleAddField()}
                >
                  Create Your First Field
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<FileJson size={16} />}
                  onClick={() => setJsonDialogOpen(true)}
                >
                  Import JSON
                </Button>
              </Stack>
            </Paper>
          ) : (
            <Stack spacing={1}>
              {fields.map((field) => (
                <FieldCard
                  key={field.id}
                  field={field}
                  onEdit={handleEditField}
                  onDelete={handleDeleteField}
                  onAddChild={field.type === 'object' ? handleAddField : undefined}
                />
              ))}
            </Stack>
          )
        ) : (
          // JSON Mode
          <Stack spacing={2} sx={{ height: '100%' }}>
            <Alert 
              severity={jsonError ? 'error' : 'info'} 
              icon={jsonError ? <AlertTriangle size={16} /> : <Info size={16} />}
            >
              {jsonError || 'Edit the JSON below to define your field structure. Changes are validated in real-time.'}
            </Alert>
            
            <Paper
              sx={{
                flex: 1,
                p: 2,
                backgroundColor: '#1e1e1e',
                position: 'relative',
              }}
            >
              <TextField
                multiline
                fullWidth
                value={jsonContent}
                onChange={(e) => {
                  setJsonContent(e.target.value);
                  // Validate on change
                  if (e.target.value.trim()) {
                    const result = validateJsonSchema(e.target.value);
                    setJsonError(result.valid ? null : result.error || 'Invalid JSON');
                  } else {
                    setJsonError(null);
                  }
                }}
                sx={{
                  '& .MuiInputBase-root': {
                    fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                    fontSize: '0.875rem',
                    color: '#d4d4d4',
                    backgroundColor: 'transparent',
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: 'none',
                  },
                }}
                InputProps={{
                  sx: {
                    minHeight: '100%',
                    alignItems: 'flex-start',
                  },
                }}
              />
              
              {/* JSON Schema Helper */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                }}
              >
                <Tooltip title="View JSON schema">
                  <IconButton
                    size="small"
                    onClick={() => {
                      const schema = `[
  {
    "name": "field_name",
    "displayName": "Field Display Name",
    "type": "string", // string | number | boolean | date | array | object
    "description": "Field description",
    "required": true,
    "defaultValue": "",
    "format": "email", // Optional: email, url, uuid, date-time, etc.
    "enum": ["option1", "option2"], // Optional for string type
    "validation": { // Optional
      "minLength": 3,
      "maxLength": 50,
      "pattern": "^[A-Z].*"
    },
    "children": [] // For object type
  }
]`;
                      alert(schema);
                    }}
                  >
                    <Info size={16} />
                  </IconButton>
                </Tooltip>
              </Box>
            </Paper>
            
            <Stack direction="row" spacing={2} justifyContent="flex-end">
              <Button
                variant="outlined"
                onClick={() => {
                  setJsonContent(JSON.stringify(fields, null, 2));
                  setJsonError(null);
                }}
              >
                Reset
              </Button>
              <Button
                variant="contained"
                onClick={() => switchToVisualView()}
                disabled={!!jsonError}
                startIcon={jsonError ? <AlertTriangle size={16} /> : <CheckCircle size={16} />}
              >
                Apply Changes
              </Button>
            </Stack>
          </Stack>
        )}
      </Box>

      {/* Field count summary */}
      {fields.length > 0 && (
        <Box sx={{ pt: 2, borderTop: 1, borderColor: 'divider' }}>
          <Typography variant="body2" color="text.secondary">
            {fields.length} field{fields.length !== 1 ? 's' : ''} defined
          </Typography>
        </Box>
      )}

      {/* Field Dialog */}
      <FieldDialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onSave={handleSaveField}
        field={editingField}
        existingFields={fields}
      />
      
      {/* JSON Import Dialog */}
      <Dialog open={jsonDialogOpen} onClose={() => setJsonDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          <Stack direction="row" alignItems="center" spacing={2}>
            <FileJson size={24} />
            <Typography variant="h6">Import JSON Schema</Typography>
          </Stack>
        </DialogTitle>
        <DialogContent dividers>
          <Stack spacing={2}>
            <Alert severity="info" icon={<Info size={16} />}>
              Paste your JSON field definitions below. The JSON should be an array of field objects.
            </Alert>
            
            <TextField
              multiline
              fullWidth
              rows={15}
              value={jsonContent}
              onChange={(e) => {
                setJsonContent(e.target.value);
                if (e.target.value.trim()) {
                  const result = validateJsonSchema(e.target.value);
                  setJsonError(result.valid ? null : result.error || 'Invalid JSON');
                } else {
                  setJsonError(null);
                }
              }}
              placeholder={`[
  {
    "name": "customer_id",
    "displayName": "Customer ID",
    "type": "string",
    "required": true
  }
]`}
              sx={{
                '& .MuiInputBase-root': {
                  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                  fontSize: '0.875rem',
                },
              }}
            />
            
            {jsonError && (
              <Alert severity="error" icon={<AlertTriangle size={16} />}>
                {jsonError}
              </Alert>
            )}
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setJsonDialogOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleJsonImport}
            disabled={!!jsonError || !jsonContent.trim()}
            startIcon={<Save size={16} />}
          >
            Import
          </Button>
        </DialogActions>
      </Dialog>
    </Stack>
  );
}