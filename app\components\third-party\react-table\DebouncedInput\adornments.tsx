import { CloseCircleOutlined } from "@ant-design/icons";
import { InputAdornment, IconButton } from "@mui/material";

type DateTimePickerAdornmentProps = {
   value: any,
   onClear: (value: any) => void
}

export const DateTimePickerAdornment = ({ value, onClear }: DateTimePickerAdornmentProps) => {

   if (!value) return null;

   return (
      <InputAdornment position="end">
         <IconButton
            onClick={() => {
               // Reset the value when clear button is clicked
               onClear(null);
            }}
            edge="end"
         >
            <CloseCircleOutlined />
         </IconButton>
      </InputAdornment>
   )
}

export const TextFieldAdornment = ({ value, onClear }: DateTimePickerAdornmentProps) => {

   if (!value) return null;

   return (
      <InputAdornment position="end">
         <IconButton
            onClick={() => {
               // Reset the value when clear button is clicked
               onClear({ target: { value: '' } });
            }}
            edge="end"
         >
            <CloseCircleOutlined />
         </IconButton>
      </InputAdornment>
   )
}