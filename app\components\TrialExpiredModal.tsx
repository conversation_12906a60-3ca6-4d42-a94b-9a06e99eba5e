import { Dialog, DialogContent, <PERSON>po<PERSON>, <PERSON>, Button, Stack, Grid, Divider } from '@mui/material';
import { styled, useTheme, alpha } from '@mui/material/styles';
import { AlertTriangle, LogOut } from 'lucide-react';
import { useNavigate } from '@remix-run/react';

const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: theme.spacing(2),
    padding: 0,
    overflow: 'hidden'
  }
}));

interface TrialExpiredModalProps {
  open: boolean;
}

export default function TrialExpiredModal({ open }: TrialExpiredModalProps) {
  const theme = useTheme();
  const navigate = useNavigate();

  const handleLogout = () => {
    // Clear any stored tokens or user data
    localStorage.clear();
    sessionStorage.clear();
    // Navigate to login page
    navigate('/auth/login');
  };
  
  return (
    <StyledDialog
      open={open}
      maxWidth="md"
      fullWidth
      disableEscapeKeyDown
      onClose={(event, reason) => {
        // Prevent closing the dialog
        if (reason === 'backdropClick') {
          return;
        }
      }}
    >
      <DialogContent sx={{ p: 0 }}>
        <Grid container>
          {/* Left Column - Trial Expired Info */}
          <Grid item xs={12} md={7} sx={{ p: 4, pr: { md: 3 } }}>
            <Stack spacing={3}>
              <Box
                sx={{
                  width: 56,
                  height: 56,
                  borderRadius: '50%',
                  bgcolor: alpha(theme.palette.warning.main, 0.1),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <AlertTriangle size={28} color={theme.palette.warning.main} />
              </Box>
              
              <Box>
                <Typography variant="h4" fontWeight={700} gutterBottom>
                  Trial Period Expired
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ lineHeight: 1.6 }}>
                  Your trial period has ended. To continue using Unizo and unlock all features, 
                  please reach out to our sales team.
                </Typography>
              </Box>

              <Box>
                <Typography variant="body2" fontWeight={600} gutterBottom>
                  What happens next?
                </Typography>
                <Stack spacing={1}>
                  <Typography variant="body2" color="text.secondary">
                    • Contact our sales team to discuss pricing
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Choose a plan that fits your needs
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Continue accessing all your integrations
                  </Typography>
                </Stack>
              </Box>
            </Stack>
          </Grid>

          {/* Divider */}
          <Grid item xs={12} md="auto" sx={{ display: { xs: 'none', md: 'block' } }}>
            <Divider orientation="vertical" sx={{ height: '100%' }} />
          </Grid>
          <Grid item xs={12} sx={{ display: { xs: 'block', md: 'none' } }}>
            <Divider />
          </Grid>

          {/* Right Column - Actions */}
          <Grid 
            item 
            xs={12} 
            md={5} 
            sx={{ 
              p: 4, 
              pl: { md: 3 },
              bgcolor: theme.palette.mode === 'dark' 
                ? alpha(theme.palette.background.paper, 0.6)
                : theme.palette.grey[50]
            }}
          >
            <Stack spacing={3} height="100%" justifyContent="center">
              <Box>
                <Typography variant="body1" fontWeight={600} gutterBottom>
                  Ready to continue?
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Our team is standing by to help you get started
                </Typography>
              </Box>

              <Stack spacing={2}>
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  fullWidth
                  onClick={() => window.location.href = 'mailto:<EMAIL>'}
                >
                  Contact Sales Team
                </Button>
                
                <Typography 
                  variant="caption" 
                  color="text.secondary" 
                  align="center"
                  sx={{ display: 'block' }}
                >
                  <EMAIL>
                </Typography>
              </Stack>

              <Divider sx={{ my: 2 }} />

              <Button
                variant="outlined"
                color="inherit"
                size="large"
                fullWidth
                startIcon={<LogOut size={18} />}
                onClick={handleLogout}
                sx={{ 
                  borderColor: theme.palette.divider,
                  '&:hover': {
                    borderColor: theme.palette.text.secondary,
                    bgcolor: alpha(theme.palette.text.primary, 0.04)
                  }
                }}
              >
                Sign Out
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </DialogContent>
    </StyledDialog>
  );
}