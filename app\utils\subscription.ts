type Subscription = {
  charge?: {
    pricingTier?: {
      name?: string;
    };
  };
};

export function getSubscriptionFlags(subscriptions: Subscription[]): string {
  if (!subscriptions.length) return "Free";

  let highestTier = "Free";

  for (const sub of subscriptions) {
    const tier = sub.charge?.pricingTier?.name;

    if (!tier) return "Free";

    if (tier === "Enterprise") {
      highestTier = "Enterprise";
    } else if (["Launch", "Scale"].includes(tier as string)) {
      highestTier = "Launch";
    } else {
      highestTier = "Free";
    }
  }

  return highestTier;
}
