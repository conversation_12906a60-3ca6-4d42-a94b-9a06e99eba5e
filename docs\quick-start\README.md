# Quick Start Flow Documentation

## Overview

The Quick Start flow is a guided onboarding experience designed to help new Unizo users set up their workspace with unified APIs and connectors. This flow is presented to users during their first login or as part of the signup process.

## Table of Contents

- [Architecture](./architecture.md) - Technical architecture and flow diagrams
- [Integration Guide](./integration-guide.md) - How to integrate during app bootstrap
- [Configuration](./configuration.md) - Predefined tenant configurations
- [API Reference](./api-reference.md) - API endpoints and contracts

## Key Features

### 1. Tenant-Based Configuration
The Quick Start flow automatically adapts based on the user's subscription plan:
- **Trial**: Full flexibility to explore all categories
- **Basic**: Pre-selected SCM and Ticketing categories
- **Enterprise**: Comprehensive set including SCM, Ticketing, PCR, and Incident
- **Security**: Focused on VMS, Identity, and EDR
- **Infrastructure**: INFRA, Monitoring, and Incident management

### 2. Three-Step Process
1. **Category Selection**: Choose which unified API categories to enable
2. **Connector Configuration**: Select and configure specific service connectors
3. **Get Started**: Final setup and resource links

### 3. Progress Tracking
- Automatic save of progress between steps
- Resume capability if user leaves and returns
- Visual progress indicators

## Quick Start Routes

### Production Routes
- `/console/quick-start` - Authenticated users within the console
- `/quick-start` - Standalone route for onboarding flow

### Components Structure
```
/app
  /pages
    quick-start.tsx           # Main page component
  /services  
    tenant-configuration.ts   # Service for managing configurations
  /types
    quick-start.ts           # TypeScript definitions
  /routes
    /console
      quick-start.tsx        # Console route
    quick-start.tsx          # Standalone route
```

## Usage

### Basic Implementation
```typescript
import QuickStartPage from 'pages/quick-start';

// The page handles all logic internally
export default function QuickStartRoute() {
  return <QuickStartPage />;
}
```

### With Custom Handling
```typescript
import { QuickStartProvider } from 'sections/getting-started/context/GettingStartedContext';
import { GettingStartedStepper } from 'sections/getting-started';

function CustomQuickStart() {
  const handleComplete = () => {
    // Custom completion logic
    console.log('Quick start completed!');
  };

  return (
    <QuickStartProvider>
      <GettingStartedStepper 
        onComplete={handleComplete}
        tenantConfig={customConfig}
      />
    </QuickStartProvider>
  );
}
```

## Development Guidelines

1. **No Hardcoded Colors**: All styling must use theme palette
2. **Responsive Design**: Works on all screen sizes
3. **Dark Mode Support**: Full support for light and dark themes
4. **Type Safety**: Fully typed with TypeScript
5. **Error Handling**: Graceful error states and recovery

## API Integration

The Quick Start flow integrates with several backend APIs:

- `GET /api/quick-start/status` - Check if quick start is needed
- `GET /api/quick-start/progress` - Get current progress
- `POST /api/quick-start/save` - Save progress for a step
- `POST /api/quick-start/complete` - Mark flow as complete
- `POST /api/quick-start/skip` - Skip the flow (if allowed)

See [API Reference](./api-reference.md) for detailed documentation.

## Best Practices

1. **Check Status First**: Always verify if quick start is needed before showing
2. **Handle Loading States**: Show appropriate loading indicators
3. **Save Progress**: Auto-save progress after each step
4. **Validate Selections**: Ensure required categories are selected based on plan
5. **Provide Help**: Include contextual help and documentation links

## Testing

### Manual Testing
1. Create new user accounts with different subscription plans
2. Verify correct predefined categories are shown
3. Test progress saving and resuming
4. Verify completion redirects correctly

### Automated Testing
```typescript
// Example test
describe('QuickStart Flow', () => {
  it('should show predefined categories for enterprise plan', () => {
    // Test implementation
  });
});
```

## Troubleshooting

### Common Issues

1. **Quick Start not showing**: Check `/api/quick-start/status` response
2. **Progress not saving**: Verify API endpoints are accessible
3. **Wrong categories shown**: Check tenant type detection logic
4. **Styling issues**: Ensure theme provider is properly wrapped

### Debug Mode
Enable debug logging:
```typescript
// In browser console
localStorage.setItem('quickstart.debug', 'true');
```

## Future Enhancements

1. **AI-Powered Recommendations**: Suggest connectors based on usage patterns
2. **Bulk Configuration**: Import/export configurations
3. **Team Templates**: Share configurations across team members
4. **Analytics Integration**: Track completion rates and drop-off points