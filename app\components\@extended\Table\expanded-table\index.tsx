import { Fragment, useMemo } from 'react';


// material-ui
import Paper from '@mui/material/Paper';
import TablePrimitive from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';

// third-party
import { flexRender, useReactTable, getCoreRowModel, getPaginationRowModel } from '@tanstack/react-table';
import { TablePagination } from 'components/third-party/react-table';

// project import
import ScrollX from 'components/ScrollX';
import MainCard from 'components/MainCard';
import { Box, Divider } from '@mui/material';

import { extractColumnMeta, extractHeaderColumnMeta, getPaginationTotal } from '../table.helper.js'
import { TableExpandedProps } from '../type.js';
import IconButton from 'components/@extended/IconButton.jsx';
import { DownOutlined, RightOutlined, StopOutlined } from '@ant-design/icons';

export default function ExpandedTable<TData>(props: TableExpandedProps<TData>) {

   const {
      striped = false,
      data = [],
      columns = [],
      title = null,

      // pagination
      totalData = 0,
      paginationPosition = 'bottom',
      manualPagination = true,
      disablePagination = false,

      // row-props
      rowClickable = false,
      onRowClick,

      // expanding
      renderSubRow,
      ...rest
   } = props;

   const table = useReactTable({
      data,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      manualPagination,
      pageCount: getPaginationTotal(totalData, rest?.state?.pagination?.pageSize),
      ...rest,
   });

   return (
      <MainCard
         content={false}
         title={title}
      >
         <ScrollX>
            <TableContainer component={Paper}>
               <TablePrimitive>
                  <TableHead>
                     {table.getHeaderGroups().map((headerGroup) => (
                        <TableRow key={headerGroup.id}>
                           <TableCell
                           >
                              {/* todo: */}
                           </TableCell>
                           {headerGroup?.headers.map((header) => (
                              <TableCell
                                 key={header.id}
                                 {...extractHeaderColumnMeta(header)}
                              >
                                 {header?.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                              </TableCell>
                           ))}
                        </TableRow>
                     ))}
                  </TableHead>
                  <TableBody {...(striped && { className: 'striped' })}>
                     {table.getRowModel().rows.map((row) => (
                        <Fragment key={row.id}>

                           <TableRow
                              onClick={() => (
                                 onRowClick && onRowClick(row)
                              )}
                              className={`${rowClickable ? 'cursor-pointer' : ''}`}

                           >
                              {/* expanded table */}
                              <TableCell
                              >
                                 {(row.getCanExpand() ? (
                                    <IconButton color={row.getIsExpanded() ? 'primary' : 'secondary'} onClick={row.getToggleExpandedHandler()} size="small">
                                       {row.getIsExpanded() ? <DownOutlined /> : <RightOutlined />}
                                    </IconButton>
                                 ) : (
                                    <IconButton color="secondary" size="small" disabled>
                                       <StopOutlined />
                                    </IconButton>
                                 ))}
                              </TableCell>
                              {/* expanded table */}

                              {row.getVisibleCells().map((cell: any) => {
                                 return (
                                    <TableCell
                                       key={cell.id}
                                       {...extractColumnMeta(
                                          cell,
                                       )}
                                    >
                                       {flexRender(cell?.column?.columnDef?.cell, cell?.getContext())}
                                    </TableCell>
                                 )
                              })}

                           </TableRow>

                           {/* sub-row */}
                           {row.getIsExpanded() && (
                              typeof renderSubRow === 'function' && renderSubRow(row)
                           )}
                        </Fragment>
                     ))}
                  </TableBody>
               </TablePrimitive>
            </TableContainer>

            {/* pagination block */}
            {!disablePagination ? (
               paginationPosition !== 'top' && (
                  <>
                     <Divider />
                     <Box sx={{ p: 2 }}>
                        <TablePagination
                           {...{
                              setPageSize: table.setPageSize,
                              setPageIndex: table.setPageIndex,
                              getState: table.getState,
                              getPageCount: table.getPageCount
                           }}
                        />
                     </Box>
                  </>
               )
            ) : null}

         </ScrollX>
      </MainCard>
   )
}