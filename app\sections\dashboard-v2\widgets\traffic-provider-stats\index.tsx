import ModernChartCard from "components/cards/ModernChartCard"
import useMediaQuery from '@mui/material/useMediaQuery';

import { Grid, GridProps } from "@mui/material";
import { useTheme } from '@mui/material/styles';
// project-import
import { ThemeMode } from 'config';

import Chart from './chart';
import DataGrid from './data-grid';

import Selection, { ItemTypes } from './selection';
import { GadgetConfig } from "../../layout/grid-type";
import { useEffect, useMemo, useState } from "react";
import useUserDetails from "store/user";
import { Subscription } from "types/subscription";
import { useDashboard } from "hooks/api/dashboard/useDashboard";
import { getStatsForSelectedCategory } from "./helper";

const GRID_1: GridProps = {
   xs: 12,
   sm: 8,
};

const GRID_2: GridProps = {
   xs: 12,
   sm: 4,
};

export default ({ gadget }: GadgetConfig) => {
   const { subscriptions } = useUserDetails();

   const theme: any = useTheme();

   const [selectedCategory, setSelectedCategory] = useState<ItemTypes | null>(null);

   const options: Array<ItemTypes> = useMemo(() => {
      return (
         subscriptions?.reduce((acc: Array<ItemTypes>, subscription: Subscription) => {
            const { id: value, name: title } = subscription;
            return acc.concat([{ value, title }])
         }, [])
      ) ?? []
   }, [subscriptions]);

   const { providerStats } = useDashboard()

   const stats = useMemo(() => {

      return (
         getStatsForSelectedCategory(
            selectedCategory?.value as string,
            providerStats
         ) ?? {}
      )
   }, [selectedCategory, providerStats]);

   const downSM = useMediaQuery((theme: any) => theme.breakpoints.down('sm'));

   const sx = useMemo(() => {
      const tempObj: Record<string, any> = {};

      if (!downSM) {
         tempObj['&:before'] = {
            content: '" "',
            position: 'absolute',
            top: '15%',
            left: '0%',
            height: '70%',
            borderLeft: `1px solid ${ThemeMode.DARK === theme.palette.mode ? theme.palette.divider: theme.palette.secondary[200]}`,
         }
      }
      return tempObj;
   }, [downSM, theme])

   useEffect(() => {
      if (options.length) {
         setSelectedCategory(options.at(0) as ItemTypes)
      }
   }, [options])

   return (
      <ModernChartCard
         title={gadget.name}
         subtitle="Traffic connector analytics"
      >
         <Grid container spacing={4}>
            <Grid item {...GRID_1} rowSpacing={2} >
               {/* selection bar */}
               <Selection
                  {...{
                     categories: options,
                     onCategorySelect(selected) {
                        setSelectedCategory(selected)
                     },
                     selectedCategory: selectedCategory as ItemTypes
                  }}
               />
               {/* selection bar */}
               <Chart
                  stats={stats}
               />
            </Grid>

            <Grid
               item
               {...GRID_2}
               sx={{
                  position: 'relative',
                  ...sx
               }}
            >
               <DataGrid
                  stats={stats}
               />
            </Grid>
         </Grid>
      </ModernChartCard>
   )
}