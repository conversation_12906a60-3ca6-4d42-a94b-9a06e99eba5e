import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';

// project import
import Search from './Search';
import Profile from './Profile';
import FullScreen from './FullScreen';
import MobileSection from './MobileSection';

import useConfig from 'hooks/useConfig';
import { MenuOrientation } from 'config';
import DrawerHeader from 'layout/Dashboard/Drawer/DrawerHeader';
import EnvironmentSwitcher from './EnvironmentSwitcher';
import { Stack } from '@mui/material';
import TrialExpirationIndicator from './TrialExpirationIndicator';
import { useTrialExpiration } from 'hooks/api/useTrialExpiration';

// ==============================|| HEADER - CONTENT ||============================== //

export default function HeaderContent() {
  const { menuOrientation } = useConfig();
  const { latestExpirationDate, isLoading, error } = useTrialExpiration();

  console.log('[HeaderContent] Trial expiration data:', { latestExpirationDate, isLoading, error });

  const downLG = useMediaQuery((theme) => theme.breakpoints.down('lg'));

  return (
    <>
      {menuOrientation === MenuOrientation.HORIZONTAL && !downLG && <DrawerHeader open={true} />}
      {!downLG && <Search />}
      {/* {!downLG && megaMenu} */}
      {/* {!downLG && localization} */}
      {downLG && <Box sx={{ width: '100%', ml: 1 }} />}
      <Stack direction={'row'} flex={1} gap={2} alignItems={'center'}>
        {latestExpirationDate && <TrialExpirationIndicator cancelledDate={latestExpirationDate} />}
        <EnvironmentSwitcher />
        {!downLG && <FullScreen />}

        {!downLG && <Profile />}
        {downLG && <MobileSection />}
      </Stack>
    </>
  );
}
