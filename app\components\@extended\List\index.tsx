
import { List as ListPrimitive, ListProps, Theme } from "@mui/material";

import { getBorderedStyle } from './helper';
import { useCallback } from "react";

type Props = { bordered?: boolean } & ListProps;

export const List = (
   {
      bordered,
      sx: sxProp = {},
      ...props
   }: Props) => {

   const getMergedSX = useCallback((theme: Theme): ListProps['sx'] => {

      if (typeof sxProp === 'function') {
         return sxProp(theme)
      } else {
         return sxProp;
      }

   }, [sxProp])

   return (
      <ListPrimitive
         {...props}
         sx={(theme) => ({
            ... (bordered ? getBorderedStyle(theme?.palette) : {}),
            ... getMergedSX(theme) as any
         })}
      />
   )
}