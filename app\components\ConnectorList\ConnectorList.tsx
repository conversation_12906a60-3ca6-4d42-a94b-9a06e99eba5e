import React, { useState, useMemo } from 'react';

import { SearchOutlined } from '@ant-design/icons';
import RefreshIcon from '@mui/icons-material/Refresh';
import { InputAdornment, Link } from '@mui/material';

import { ConnectorListProps } from './ConnectorList.types';
import { ConnectorCard } from './ConnectorCard';
import {
  PageContainer,
  ContentWrapper,
  HeaderSection,
  TitleRow,
  TitleContent,
  PageTitle,
  PageSubtitle,
  RefreshButton,
  SearchField,
  ConnectorGrid,
} from './ConnectorList.styles';

export const ConnectorList: React.FC<ConnectorListProps> = ({
  title,
  subtitle,
  docsLink,
  connectors,
  onToggle,
  onRefresh,
  onConnectorClick,
  searchPlaceholder = 'Search connectors',
  className,
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredConnectors = useMemo(() => {
    if (!searchTerm) return connectors;
    
    const term = searchTerm.toLowerCase();
    return connectors.filter(
      (connector) =>
        connector.displayName.toLowerCase().includes(term) ||
        connector.name.toLowerCase().includes(term)
    );
  }, [connectors, searchTerm]);

  const renderSubtitle = () => {
    if (!subtitle) return null;

    if (docsLink) {
      const parts = subtitle.split('{docs}');
      return (
        <PageSubtitle variant="button">
          {parts[0]}
          <Link
            href={docsLink}
            target="_blank"
            rel="noopener noreferrer"
            className="link"
          >
            Docs
          </Link>
          {parts[1]}
        </PageSubtitle>
      );
    }

    return <PageSubtitle variant="button">{subtitle}</PageSubtitle>;
  };

  return (
    <PageContainer className={className}>
      <ContentWrapper>
        <HeaderSection>
          <TitleRow>
            <TitleContent>
              <PageTitle variant="h5">{title}</PageTitle>
              {renderSubtitle()}
            </TitleContent>
            {onRefresh && (
              <RefreshButton onClick={onRefresh} aria-label="Refresh connectors">
                <RefreshIcon />
              </RefreshButton>
            )}
          </TitleRow>
          <SearchField
            fullWidth
            variant="outlined"
            placeholder={searchPlaceholder}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchOutlined />
                </InputAdornment>
              ),
            }}
          />
        </HeaderSection>
        <ConnectorGrid>
          {filteredConnectors.map((connector) => (
            <ConnectorCard
              key={connector.id}
              connector={connector}
              onToggle={(enabled) => onToggle(connector.id, enabled)}
              onClick={() => onConnectorClick?.(connector)}
            />
          ))}
        </ConnectorGrid>
      </ContentWrapper>
    </PageContainer>
  );
};