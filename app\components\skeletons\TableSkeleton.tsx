import React from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Skeleton, Box } from '@mui/material';
import MainCard from 'components/MainCard';

interface TableSkeletonProps {
  rows?: number;
  columns?: number;
  showHeader?: boolean;
  title?: string;
  showActions?: boolean;
  showPagination?: boolean;
}

const TableSkeleton: React.FC<TableSkeletonProps> = ({
  rows = 5,
  columns = 4,
  showHeader = true,
  title,
  showActions = false,
  showPagination = true
}) => {
  return (
    <MainCard
      content={false}
      title={title ? <Skeleton width={200} height={28} /> : undefined}
      secondary={showActions ? <Skeleton variant="rectangular" width={100} height={36} /> : undefined}
    >
      <TableContainer component={Paper}>
        <Table>
          {showHeader && (
            <TableHead>
              <TableRow>
                {Array.from({ length: columns }).map((_, index) => (
                  <TableCell key={index}>
                    <Skeleton width={`${Math.random() * 40 + 60}%`} height={20} />
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
          )}
          <TableBody>
            {Array.from({ length: rows }).map((_, rowIndex) => (
              <TableRow key={rowIndex}>
                {Array.from({ length: columns }).map((_, colIndex) => (
                  <TableCell key={colIndex}>
                    <Skeleton 
                      width={`${Math.random() * 30 + 70}%`} 
                      height={20} 
                      animation="wave"
                    />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      {showPagination && (
        <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Skeleton width={150} height={24} />
          <Box sx={{ display: 'flex', gap: 1 }}>
            {Array.from({ length: 5 }).map((_, index) => (
              <Skeleton key={index} variant="rectangular" width={32} height={32} />
            ))}
          </Box>
        </Box>
      )}
    </MainCard>
  );
};

export default TableSkeleton;