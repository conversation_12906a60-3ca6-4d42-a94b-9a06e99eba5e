# Unizo Brand Theme Guide

## Brand Colors

### Primary Color Palette
- **Primary Main**: `#21384f` - Deep navy blue (main brand color)
- **Primary Light**: `#4473a2` - Lighter blue for hover states
- **Primary Dark**: `#1a2b3d` - Darker navy for active states
- **Primary 50-950**: Full range from `#f8fafc` to `#0f1924`

### Secondary Color Palette
- **Secondary Main**: `#ea580c` - Vibrant orange (accent color)
- **Secondary Light**: `#f17238` - Lighter orange for hover states
- **Secondary Dark**: `#b52e0a` - Darker orange for active states
- **Secondary 50-950**: Full range from `#fef5ee` to `#410f06`

## Theme Implementation

### 1. MUI Theme Configuration
- Created `app/themes/theme/unizo.js` with complete theme definition
- Updated default theme to use Unizo brand colors
- Set 'unizo' as the default preset color in config

### 2. CSS Variables
Added to `app/tailwind.css`:
```css
:root {
  --unizo-brand-primary: #213350;
  --unizo-primary-800: #21384f;
  --unizo-secondary-500: #ea580c;
  /* ... and more */
}
```

### 3. Tailwind Configuration
Extended colors in `tailwind.config.ts`:
```js
colors: {
  unizo: {
    primary: { 50-950 },
    secondary: { 50-950 }
  }
}
```

### 4. Navigation Styling
- Created `Navigation.styles.ts` with custom navigation components
- Dark navigation background: `#21384f`
- Hover state: `#2a4765`
- Active state: `#1a2b3d`
- Highlight accent: `#ea580c`

### 5. Component Library
Created modern styled components in `app/components/styled/modern.ts`:
- `PrimaryButton` - Navy blue buttons
- `SecondaryButton` - Orange accent buttons
- `ModernCard` - Cards with subtle shadows
- `ModernTextField` - Styled form inputs
- `StatusChip` - Color-coded status indicators
- And more...

## Usage Examples

### Using Tailwind Classes
```jsx
<div className="bg-unizo-primary-800 text-white">
  <button className="bg-unizo-secondary-500 hover:bg-unizo-secondary-600">
    Action
  </button>
</div>
```

### Using Styled Components
```jsx
import { PrimaryButton, SecondaryButton, ModernCard } from 'components/styled/modern';

<ModernCard>
  <PrimaryButton>Primary Action</PrimaryButton>
  <SecondaryButton>Secondary Action</SecondaryButton>
</ModernCard>
```

### Using Theme in Custom Components
```jsx
import { styled } from '@mui/material/styles';

const CustomComponent = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.primary.main, // #21384f
  color: theme.palette.primary.contrastText,
  '&:hover': {
    backgroundColor: theme.palette.primary.dark, // #1a2b3d
  },
}));
```

## Key Features

1. **Consistent Color System**: All colors follow the Unizo brand guidelines
2. **Dark Navigation**: Professional dark-themed navigation with orange highlights
3. **Modern UI Elements**: Subtle shadows, smooth transitions, and hover effects
4. **Accessibility**: Proper contrast ratios maintained throughout
5. **Responsive**: Colors and components work across all device sizes

## Chart Colors
For dashboards and data visualization:
- Primary series: `['#21384f', '#2a4765', '#35597e', '#4473a2', '#6f99c3']`
- Secondary series: `['#ea580c', '#f17238', '#f6a372', '#fac9a9', '#fde8d6']`

## Migration Notes
- The theme is set to 'unizo' by default in the config
- All existing components will automatically use the new color palette
- Custom components should be updated to use theme values instead of hardcoded colors
- Navigation will display with the new dark theme automatically