import { useEffect, useState } from 'react';


const IconSelector = ({ iconName, icon: FallBack, style }: any): any => {
   const [IconComponent, setIconComponent] = useState<any>(null);

   useEffect(() => {
      const loadIcon = async () => {
         try {
            const iconMatch = iconName.match(/(.+)(Outlined|Filled|TwoTone)$/);

            if (iconMatch) {
               const [_, baseName] = iconMatch;
               const newIconName = `${baseName}Filled`;

               const icons: any = await import('@ant-design/icons');
               const SelectedIcon = icons[newIconName];

               setIconComponent(() => SelectedIcon);
            } else {
               console.error('Invalid icon name');
            }
         } catch (error) {
            console.error('Error loading icon:', error);
         }
      };

      loadIcon();
   }, [iconName]);

   return IconComponent ? <IconComponent style={style} /> : <FallBack style={style} />;
};

export default IconSelector;