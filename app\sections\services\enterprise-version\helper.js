

export const parseVersionData = (arr) => {
   return arr.map(({ name: title, id: value, isBeta = false }) => (
      {
         value,
         title,
         meta: { isBeta }
      }
   ))
}

export const parseInitialSelectedVersion = (allVersion, selected) => {
   const ids = selected?.map((i) => i?.serviceProfileVersion?.id);
   return (
      allVersion?.filter(({ value }) => {
         return ids?.includes(value)
      })
   )
}