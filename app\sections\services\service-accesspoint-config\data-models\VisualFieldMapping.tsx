import React, { useState, useCallback, useMemo, useRef } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  Handle,
  Position,
  MarkerType,
  ConnectionMode,
  ReactFlowProvider,
} from 'reactflow';
import 'reactflow/dist/style.css';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Stack,
  Typography,
  Box,
  useTheme,
  alpha,
  Paper,
  Chip,
  IconButton,
  Alert,
  Menu,
  MenuItem,
  TextField,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
} from '@mui/material';
import {
  Database,
  FileText,
  Hash,
  ToggleLeft,
  Calendar,
  List,
  Braces,
  Plus,
  Settings2,
  CheckCircle,
  AlertCircle,
  X,
  Link2,
  Info,
  Maximize2,
  Minimize2,
} from 'lucide-react';
import { DialogClose } from 'components/@extended/dialog';
import { PROVIDER_FIELDS } from './mock-data';

interface VisualFieldMappingProps {
  open: boolean;
  onClose: () => void;
  dataModel: any;
  serviceProfile: any;
  embedded?: boolean;
  existingMappings?: any[];
  onMappingsChange?: (mappings: any[]) => void;
}

const TYPE_ICONS: Record<string, React.ReactNode> = {
  string: <FileText size={14} />,
  number: <Hash size={14} />,
  boolean: <ToggleLeft size={14} />,
  date: <Calendar size={14} />,
  array: <List size={14} />,
  object: <Braces size={14} />,
};

const TYPE_COLORS: Record<string, string> = {
  string: '#4CAF50',
  number: '#2196F3',
  boolean: '#FF9800',
  date: '#9C27B0',
  array: '#F44336',
  object: '#795548',
};

// Custom node component for source fields
const SourceFieldNode = ({ data }: { data: any }) => {
  const theme = useTheme();
  const isParent = data.children && data.children.length > 0;
  
  return (
    <Paper
      elevation={2}
      sx={{
        padding: '12px 16px',
        borderRadius: 2,
        border: `1px solid ${data.connected ? theme.palette.success.main : theme.palette.divider}`,
        backgroundColor: data.connected 
          ? alpha(theme.palette.success.main, 0.08)
          : theme.palette.background.paper,
        minWidth: 200,
        cursor: 'pointer',
        transition: 'all 0.2s',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4],
        },
      }}
    >
      <Stack direction="row" alignItems="center" spacing={1}>
        <Box sx={{ color: TYPE_COLORS[data.type] || theme.palette.text.secondary }}>
          {TYPE_ICONS[data.type] || <FileText size={14} />}
        </Box>
        <Box flex={1}>
          <Typography variant="body2" fontWeight={500}>
            {data.label}
          </Typography>
          {data.description && (
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
              {data.description}
            </Typography>
          )}
        </Box>
        {data.required && (
          <Chip label="Required" size="small" color="error" sx={{ height: 18, fontSize: '0.7rem' }} />
        )}
      </Stack>
      
      {!isParent && (
        <Handle
          type="source"
          position={Position.Right}
          style={{
            background: data.connected ? theme.palette.success.main : theme.palette.divider,
            width: 8,
            height: 8,
          }}
        />
      )}
    </Paper>
  );
};

// Custom node component for target fields
const TargetFieldNode = ({ data }: { data: any }) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [mappingType, setMappingType] = useState(data.mappingType || 'direct');
  
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleClose = () => {
    setAnchorEl(null);
  };
  
  const handleMappingTypeChange = (type: string) => {
    setMappingType(type);
    data.onMappingTypeChange?.(data.id, type);
    handleClose();
  };
  
  return (
    <Paper
      elevation={2}
      sx={{
        padding: '12px 16px',
        borderRadius: 2,
        border: `1px solid ${data.connected ? theme.palette.primary.main : theme.palette.divider}`,
        backgroundColor: data.connected 
          ? alpha(theme.palette.primary.main, 0.08)
          : theme.palette.background.paper,
        minWidth: 220,
        cursor: 'pointer',
        transition: 'all 0.2s',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4],
        },
      }}
    >
      <Stack direction="row" alignItems="center" spacing={1}>
        <Handle
          type="target"
          position={Position.Left}
          style={{
            background: data.connected ? theme.palette.primary.main : theme.palette.divider,
            width: 8,
            height: 8,
          }}
        />
        
        <Box sx={{ color: TYPE_COLORS[data.type] || theme.palette.text.secondary }}>
          {TYPE_ICONS[data.type] || <FileText size={14} />}
        </Box>
        
        <Box flex={1}>
          <Typography variant="body2" fontWeight={500}>
            {data.label}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Type: {data.type}
          </Typography>
        </Box>
        
        <IconButton size="small" onClick={handleClick}>
          <Settings2 size={14} />
        </IconButton>
      </Stack>
      
      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>
        <MenuItem onClick={() => handleMappingTypeChange('direct')}>
          <Stack>
            <Typography variant="body2">Direct Mapping</Typography>
            <Typography variant="caption" color="text.secondary">
              Map field directly
            </Typography>
          </Stack>
        </MenuItem>
        <MenuItem onClick={() => handleMappingTypeChange('computed')}>
          <Stack>
            <Typography variant="body2">Computed Field</Typography>
            <Typography variant="caption" color="text.secondary">
              Apply transformation
            </Typography>
          </Stack>
        </MenuItem>
        <MenuItem onClick={() => handleMappingTypeChange('constant')}>
          <Stack>
            <Typography variant="body2">Constant Value</Typography>
            <Typography variant="caption" color="text.secondary">
              Set fixed value
            </Typography>
          </Stack>
        </MenuItem>
      </Menu>
      
      {mappingType !== 'direct' && (
        <Box sx={{ mt: 1 }}>
          {mappingType === 'computed' && (
            <TextField
              size="small"
              fullWidth
              placeholder="e.g., value.toUpperCase()"
              variant="outlined"
              sx={{ mt: 1 }}
            />
          )}
          {mappingType === 'constant' && (
            <TextField
              size="small"
              fullWidth
              placeholder="Enter constant value"
              variant="outlined"
              sx={{ mt: 1 }}
            />
          )}
        </Box>
      )}
    </Paper>
  );
};


const nodeTypes = {
  sourceField: SourceFieldNode,
  targetField: TargetFieldNode,
};

export default function VisualFieldMapping({
  open,
  onClose,
  dataModel,
  serviceProfile,
  embedded = false,
  existingMappings = [],
  onMappingsChange,
}: VisualFieldMappingProps) {
  const theme = useTheme();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Initialize nodes from data model and provider fields
  React.useEffect(() => {
    if (!open) return;
    
    const sourceNodes: Node[] = [];
    const targetNodes: Node[] = [];
    let yOffset = 0;
    
    // Create source nodes (data model fields)
    dataModel.fields.forEach((field: any, index: number) => {
      sourceNodes.push({
        id: `source-${field.id}`,
        type: 'sourceField',
        position: { x: 50, y: yOffset },
        data: {
          label: field.name,
          type: field.type,
          required: field.required,
          description: field.description,
          connected: false,
        },
      });
      yOffset += 100;
    });
    
    
    // Get provider fields
    const provider = serviceProfile?.name?.toLowerCase() || 'github';
    const providerFields = PROVIDER_FIELDS[provider]?.[dataModel.id] || [];
    
    // Create target nodes (provider fields) with hierarchy
    yOffset = 0;
    const processFields = (fields: any[], parentPath = '') => {
      fields.forEach((field: any) => {
        const fieldPath = parentPath ? `${parentPath}.${field.name}` : field.name;
        
        targetNodes.push({
          id: `target-${fieldPath}`,
          type: 'targetField',
          position: { x: 600, y: yOffset },
          data: {
            label: fieldPath,
            type: field.type,
            connected: false,
            onMappingTypeChange: (id: string, type: string) => {
              console.log('Mapping type changed:', id, type);
            },
          },
        });
        yOffset += 80;
        
        // Process nested fields
        if (field.type === 'object' && field.children) {
          processFields(field.children, fieldPath);
        }
      });
    };
    
    processFields(providerFields);
    
    setNodes([...sourceNodes, ...targetNodes]);
    setEdges([]);
  }, [open, dataModel, serviceProfile, setNodes, setEdges]);
  
  const onConnect = useCallback(
    (params: any) => {
      const newEdge = {
        ...params,
        type: 'smoothstep',
        animated: true,
        style: {
          stroke: theme.palette.primary.main,
          strokeWidth: 2,
        },
        markerEnd: {
          type: MarkerType.ArrowClosed,
          color: theme.palette.primary.main,
        },
      };
      
      setEdges((eds) => addEdge(newEdge, eds));
      
      // Update node states
      setNodes((nds) =>
        nds.map((node) => {
          if (node.id === params.source || node.id === params.target) {
            return {
              ...node,
              data: {
                ...node.data,
                connected: true,
              },
            };
          }
          return node;
        })
      );
    },
    [setEdges, setNodes, theme]
  );
  
  const proOptions = { hideAttribution: true };
  
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };
  
  // Handle fullscreen change events
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);
  
  // Embedded mode - no dialog wrapper
  if (embedded) {
    return (
      <Box 
        ref={containerRef} 
        sx={{ 
          height: '100%', 
          position: 'relative',
          backgroundColor: isFullscreen ? theme.palette.background.default : 'transparent',
          padding: isFullscreen ? 2 : 0,
        }}
      >
        <Stack sx={{ height: '100%' }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
            <Alert severity="info" icon={<Info size={20} />} sx={{ flex: 1, mr: 2 }}>
              Connect fields by dragging from source (left) to target (right). Click on target fields to configure mapping type.
            </Alert>
            <Tooltip title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}>
              <IconButton
                onClick={toggleFullscreen}
                sx={{
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 1,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.primary.main, 0.08),
                  },
                }}
              >
                {isFullscreen ? <Minimize2 size={20} /> : <Maximize2 size={20} />}
              </IconButton>
            </Tooltip>
          </Stack>
          
          <Box sx={{ flex: 1, position: 'relative', height: '100%' }}>
            <ReactFlowProvider>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                nodeTypes={nodeTypes}
                connectionMode={ConnectionMode.Loose}
                proOptions={proOptions}
                fitView
              >
                <Background variant="dots" gap={12} size={1} />
                <Controls />
                <MiniMap
                  nodeColor={(node) => {
                    if (node.type === 'sourceField') return theme.palette.success.main;
                    return theme.palette.primary.main;
                  }}
                  style={{
                    backgroundColor: theme.palette.background.default,
                    border: `1px solid ${theme.palette.divider}`,
                  }}
                />
              </ReactFlow>
            </ReactFlowProvider>
          </Box>
        </Stack>
      </Box>
    );
  }
  
  // Dialog mode
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={false}
      fullWidth
      PaperProps={{
        sx: {
          width: '90vw',
          height: '85vh',
          maxWidth: '1400px',
        },
      }}
    >
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Database size={20} />
          <Typography variant="h6">
            Visual Field Mapping - {dataModel.name}
          </Typography>
        </Stack>
      </DialogTitle>
      
      <DialogClose onClose={onClose} />
      
      <DialogContent sx={{ p: 0 }}>
        <Stack sx={{ height: '100%' }}>
          <Alert severity="info" icon={<Info size={20} />} sx={{ m: 2 }}>
            Connect fields by dragging from source (left) to target (right). Click on target fields to configure mapping type.
          </Alert>
          
          <Box sx={{ flex: 1, position: 'relative' }}>
            <ReactFlowProvider>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                nodeTypes={nodeTypes}
                connectionMode={ConnectionMode.Loose}
                proOptions={proOptions}
                fitView
              >
                <Background variant="dots" gap={12} size={1} />
                <Controls />
                <MiniMap
                  nodeColor={(node) => {
                    if (node.type === 'sourceField') return theme.palette.success.main;
                    return theme.palette.primary.main;
                  }}
                  style={{
                    backgroundColor: theme.palette.background.default,
                    border: `1px solid ${theme.palette.divider}`,
                  }}
                />
              </ReactFlow>
            </ReactFlowProvider>
          </Box>
        </Stack>
      </DialogContent>
      
      <DialogActions>
        <Box sx={{ flex: 1, pl: 2 }}>
          <Typography variant="body2" color="text.secondary">
            {edges.length} field{edges.length !== 1 ? 's' : ''} mapped
          </Typography>
        </Box>
        <Button onClick={onClose}>Cancel</Button>
        <Button variant="contained" onClick={onClose}>
          Save Mappings
        </Button>
      </DialogActions>
    </Dialog>
  );
}