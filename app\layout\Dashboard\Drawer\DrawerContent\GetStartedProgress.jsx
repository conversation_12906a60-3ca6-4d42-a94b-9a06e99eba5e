import React from 'react';
import { Box, ButtonBase, Typography, Stack, useTheme, alpha, keyframes } from '@mui/material';
import { styled } from '@mui/material/styles';
import { Rocket } from 'lucide-react';
import useOnboardingStore from 'store/onboarding';
import { organizationClient } from 'services/organization.service';
import useUserDetails from 'store/user';

// Keyframe animations
const gradientAnimation = keyframes`
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
`;

// Styled component with animated AI gradient border
const AIGradientButton = styled(ButtonBase)(({ theme }) => ({
  position: 'relative',
  padding: '2px',
  background: `linear-gradient(135deg, 
    #667eea 0%, 
    #764ba2 25%, 
    #f093fb 50%, 
    #4facfe 75%, 
    #667eea 100%
  )`,
  backgroundSize: '400% 400%',
  animation: `${gradientAnimation} 15s ease infinite`,
  borderRadius: theme.spacing(0.75),
  boxShadow: '0 0 0 0 rgba(102, 126, 234, 0)',
  overflow: 'hidden',
  width: '100%',
  transition: 'all 0.3s ease',
  '&:hover': {
    cursor: 'pointer',
    boxShadow: '0 0 20px rgba(102, 126, 234, 0.4)',
    '& .inner-content': {
      transform: 'translateY(-1px)',
    }
  }
}));

const InnerContent = styled(Box)(({ theme }) => ({
  background: theme.palette.background.paper,
  borderRadius: theme.spacing(0.5),
  padding: theme.spacing(1.5),
  width: '100%',
  position: 'relative',
  zIndex: 1,
  transition: 'transform 0.3s ease',
}));

const ProgressBar = styled(Box)(({ theme, value }) => ({
  height: 4,
  borderRadius: 2,
  backgroundColor: theme.palette.mode === 'dark' 
    ? alpha(theme.palette.grey[800], 0.5)
    : alpha(theme.palette.grey[300], 0.3),
  position: 'relative',
  overflow: 'hidden',
  '&::after': {
    content: '""',
    position: 'absolute',
    left: 0,
    top: 0,
    height: '100%',
    width: `${value}%`,
    borderRadius: 2,
    background: `linear-gradient(90deg, 
      #667eea 0%, 
      #764ba2 50%, 
      #f093fb 100%
    )`,
    transition: 'width 0.3s ease',
  },
}));

const CompletionBadge = styled(Box)(({ theme }) => ({
  background: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`,
  color: '#fff',
  borderRadius: theme.spacing(0.5),
  padding: '2px 6px',
  fontSize: '0.75rem',
  fontWeight: 700,
  minWidth: 20,
  textAlign: 'center',
}));

export default function GetStartedProgress({ drawerOpen }) {
  const theme = useTheme();
  const { openOnboarding } = useOnboardingStore();
  const userDetails = useUserDetails();
  const [isBackendComplete, setIsBackendComplete] = React.useState(false);
  const [backendProgress, setBackendProgress] = React.useState(0);
  const [backendCompletedSteps, setBackendCompletedSteps] = React.useState(0);
  const [isLoading, setIsLoading] = React.useState(true);
  
  // Fetch onboarding status from backend
  React.useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        setIsLoading(true);
        const orgId = userDetails?.organizationId;
        
        if (!orgId) {
          setIsLoading(false);
          return;
        }
        
        // Check if organization has services configured
        const [servicesResponse, watchesResponse] = await Promise.all([
          organizationClient.getOrgServices(orgId),
          organizationClient.getOrgWatches(orgId)
        ]);
        
        const services = servicesResponse?.data?.data || [];
        const watches = watchesResponse?.data?.data || [];
        
        const hasServices = services.length > 0;
        const hasWebhooks = watches.length > 0;
        
        // Calculate completion based on backend data
        let completed = 0;
        const total = 4; // Total steps
        
        // Step 1: Categories (check if services have categories)
        if (hasServices && services.some(s => s.serviceProfile?.type)) {
          completed++;
        }
        
        // Step 2: Services
        if (hasServices) {
          completed++;
        }
        
        // Step 3: Webhooks (Platform webhook is required)
        if (hasWebhooks && watches.some(w => w.type === 'PLATFORM_WATCH_HOOK')) {
          completed++;
        }
        
        // Step 4: Team (optional - always considered complete for now)
        completed++;
        
        setBackendCompletedSteps(completed);
        setBackendProgress((completed / total) * 100);
        setIsBackendComplete(completed === total);
        
      } catch (error) {
        console.error('Failed to fetch onboarding status:', error);
        // Fallback to showing the component
        setIsBackendComplete(false);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkOnboardingStatus();
  }, [userDetails?.organizationId]);
  
  // Hide the component when all steps are completed based on backend
  if (isLoading || (isBackendComplete && !isLoading)) {
    return null;
  }
  
  const handleClick = () => {
    openOnboarding();
  };

  if (!drawerOpen) {
    // Collapsed view - just show icon with number
    return (
      <Box sx={{ px: 1, py: 1.5 }}>
        <AIGradientButton onClick={handleClick}>
          <InnerContent className="inner-content" sx={{ 
            p: 1, 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center',
            gap: 0.5,
          }}>
            <Rocket 
              size={18} 
              color={theme.palette.primary.main}
            />
            <CompletionBadge>
              {backendCompletedSteps}
            </CompletionBadge>
          </InnerContent>
        </AIGradientButton>
      </Box>
    );
  }

  // Expanded view - compact button style
  return (
    <Box sx={{ px: 2.5, py: 1.5 }}>
      <AIGradientButton onClick={handleClick}>
        <InnerContent className="inner-content">
          <Stack spacing={1} width="100%">
            <Stack direction="row" alignItems="center" justifyContent="space-between">
              <Stack direction="row" alignItems="center" spacing={1}>
                <Rocket 
                  size={16} 
                  color={theme.palette.primary.main}
                />
                <Typography 
                  variant="body2" 
                  fontWeight={600}
                  sx={{ fontSize: '0.875rem' }}
                >
                  Get Started
                </Typography>
              </Stack>
              <Stack direction="row" alignItems="center" spacing={0.5}>
                <CompletionBadge>
                  {backendCompletedSteps}
                </CompletionBadge>
                <Typography 
                  variant="caption" 
                  sx={{ 
                    color: theme.palette.text.secondary,
                    fontSize: '0.75rem',
                  }}
                >
                  / 4
                </Typography>
              </Stack>
            </Stack>
            
            <ProgressBar value={backendProgress} />
          </Stack>
        </InnerContent>
      </AIGradientButton>
    </Box>
  );
}