import React, { useState, useRef, useCallback, useMemo } from "react";
import {
  Paper,
  Box,
  TextField,
  Button,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from "@mui/material";
import Checkbox from "@mui/material/Checkbox";
import ClickAwayListener from "@mui/material/ClickAwayListener";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import CloseIcon from "@mui/icons-material/Close";
import {
  FilterBarProps,
  Filter,
  FilterField,
  FilterOperator,
  FilterStep,
  FIELD_OPERATORS,
  OPERATOR_LABELS,
} from "./type";
import { ProgressiveFilterChip } from "./ProgressiveFilterChip";
import { MultiSelectFilter } from "./MultiSelectorFilter";
import { validateFilterValue } from "./Validation";

export const ProgressiveFilterBar: React.FC<FilterBarProps> = ({
  fields,
  filters,
  onFiltersChange,
  placeholder = "Search logs by any field...",
  searchValue = "",
  onSearchChange,
  resultCount,
  hideSelectedFields = true, // Default to true for the new behavior
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [editingFilter, setEditingFilter] = useState<Filter | null>(null);
  const [tempOperator, setTempOperator] = useState<FilterOperator>("is");
  const [tempValue, setTempValue] = useState<string>("");
  const [tempMultiValues, setTempMultiValues] = useState<string[]>([]);
  const [tempDateValue, setTempDateValue] = useState<Date | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [chipPosition, setChipPosition] = useState<{
    left: number;
    top: number;
    width: number;
  } | null>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const generateFilterId = () =>
    `filter_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Option 2: Smart filtering logic based on hideSelectedFields prop
  const availableFields = useMemo(() => {
    if (!hideSelectedFields) {
      // Show all fields if hiding is disabled
      return fields;
    }

    return fields.filter((field) => {
      const hasExistingFilter = filters.some(
        (filter) => filter.field === field.key && filter.step === "complete"
      );

      // Special case 1: Always show multiSelect fields (users can add multiple values)
      if (field.multiSelect) {
        if (field.useOnce) {
          return !hasExistingFilter;
        }
        return true;
      }

      // Special case 2: For date fields, you might want to allow both start and end dates
      // even if one is already selected (assuming you have startDate/endDate fields)
      if (field.type === "date") {
        // Example: Allow both start and end date fields
        const isStartDate = field.key.toLowerCase().includes("start");
        const isEndDate = field.key.toLowerCase().includes("end");

        if (isStartDate || isEndDate) {
          // Only hide if the specific date field is already used
          return !hasExistingFilter;
        }

        // For other date fields, apply normal hiding logic
        return !hasExistingFilter;
      }

      // Special case 3: Fields marked as useOnce should be hidden when used
      if (field.useOnce) {
        return !hasExistingFilter;
      }

      // Default behavior for enum and text fields
      if (field.type === "enum" || field.type === "text") {
        return !hasExistingFilter;
      }

      // Fallback: hide if already selected
      return !hasExistingFilter;
    });
  }, [fields, filters, hideSelectedFields]);

  // Alternative: More granular control with field-level configuration
  const getFieldAvailability = useCallback(
    (field: FilterField) => {
      const hasExistingFilter = filters.some(
        (filter) => filter.field === field.key && filter.step === "complete"
      );

      return {
        isSelected: hasExistingFilter,
        isAvailable: (() => {
          if (!hideSelectedFields) return true;

          // Field-specific logic
          if (field.multiSelect) {
            if (field.useOnce) {
              return !hasExistingFilter;
            }
            return true; // Always available for multi-select
          }
          switch (field.type) {
            case "date":
              // Custom logic for date fields
              if (field.allowMultiple) return true;
              return !hasExistingFilter;

            case "enum":
            case "text":
            default:
              return !hasExistingFilter;
          }
        })(),
        canModify: hasExistingFilter, // Can modify existing filters
      };
    },
    [filters, hideSelectedFields]
  );

  const calculateChipPosition = useCallback((chipElement: HTMLElement) => {
    try {
      const containerElement = chipElement.closest(
        "[data-filter-container]"
      ) as HTMLElement;
      if (!containerElement) return null;

      const chipRect = chipElement.getBoundingClientRect();
      const containerRect = containerElement.getBoundingClientRect();

      return {
        left: chipRect.left - containerRect.left,
        top: chipRect.bottom - containerRect.top,
        width: chipRect.width,
      };
    } catch (error) {
      console.error("Error calculating chip position:", error);
      return null;
    }
  }, []);

  const handleFieldSelect = useCallback(
    (field: FilterField) => {
      const availableOperators = FIELD_OPERATORS[field.type];
      const defaultOperator = availableOperators[0];

      // If there's only one operator available, skip operator step and go directly to value
      const initialStep: FilterStep =
        availableOperators.length === 1 ? "value" : "operator";

      const newFilter: Filter = {
        id: generateFilterId(),
        field: field.key,
        operator: defaultOperator,
        value: "",
        step: initialStep,
        key: field.key,
        isEditing: true,
      };

      const updatedFilters = [...filters, newFilter];
      onFiltersChange(updatedFilters);
      setEditingFilter(newFilter);
      setTempOperator(defaultOperator);
      setTempValue("");
      setTempMultiValues([]);
      setTempDateValue(null);
      setValidationError(null);

      // Calculate position for the newly created chip after it renders
      setTimeout(() => {
        const chipElement = document.querySelector(
          `[data-filter-id="${newFilter.id}"]`
        ) as HTMLElement;
        if (chipElement) {
          const position = calculateChipPosition(chipElement);
          setChipPosition(position);
        }
      }, 10);
    },
    [filters, onFiltersChange, calculateChipPosition]
  );

  const handleEditFilter = useCallback(
    (filter: Filter, chipElement?: HTMLElement) => {
      if (chipElement) {
        requestAnimationFrame(() => {
          const position = calculateChipPosition(chipElement);
          setChipPosition(position);
        });
      } else {
        setChipPosition(null);
      }

      if (filter.step === "complete") {
        const updatedFilters = filters.map((f) =>
          f.id === filter.id
            ? { ...f, isEditing: true }
            : { ...f, isEditing: false }
        );
        onFiltersChange(updatedFilters);
        setEditingFilter({ ...filter, step: "value", isEditing: true });
        setTempOperator(filter.operator);

        if (Array.isArray(filter.value)) {
          setTempMultiValues(filter.value);
          setTempValue("");
          setTempDateValue(null);
        } else if (
          fields.find((f) => f.key === filter.field)?.type === "date"
        ) {
          setTempDateValue(new Date(filter.value));
          setTempValue("");
          setTempMultiValues([]);
        } else {
          setTempValue(filter.value);
          setTempMultiValues([]);
          setTempDateValue(null);
        }
      } else {
        const updatedFilters = filters.map((f) =>
          f.id === filter.id
            ? { ...f, isEditing: true }
            : { ...f, isEditing: false }
        );
        onFiltersChange(updatedFilters);
        setEditingFilter({ ...filter, isEditing: true });

        if (filter.step === "operator" || filter.step === "value") {
          setTempOperator(filter.operator);
        }

        if (filter.step === "value") {
          const field = fields.find((f) => f.key === filter.field);
          if (Array.isArray(filter.value)) {
            setTempMultiValues(filter.value);
            setTempValue("");
            setTempDateValue(null);
          } else if (field?.type === "date" && filter.value) {
            setTempDateValue(new Date(filter.value));
            setTempValue("");
            setTempMultiValues([]);
          } else {
            setTempValue(filter.value as string);
            setTempMultiValues([]);
            setTempDateValue(null);
          }
        }
      }
      setDropdownOpen(true);
      setValidationError(null);
    },
    [fields, filters, onFiltersChange, calculateChipPosition]
  );

  const handleNextStep = useCallback(() => {
    if (!editingFilter) return;

    const field = fields.find((f) => f.key === editingFilter.field);
    if (!field) return;

    if (editingFilter.step === "operator") {
      const updatedFilter = {
        ...editingFilter,
        operator: tempOperator,
        step: "value" as FilterStep,
      };
      const updatedFilters = filters.map((f) =>
        f.id === editingFilter.id ? updatedFilter : f
      );
      onFiltersChange(updatedFilters);
      setEditingFilter(updatedFilter);
    } else if (editingFilter.step === "value") {
      let value: string | string[];
      if (field.multiSelect) {
        value = tempMultiValues;
      } else if (field.type === "date") {
        if (!tempDateValue) return;
        value = tempDateValue.toISOString();
      } else {
        value = tempValue;
      }

      const validationResult = validateFilterValue(value, field);
      if (!validationResult.isValid) {
        setValidationError(validationResult.error || "Invalid value");
        return;
      }

      const completedFilter = {
        ...editingFilter,
        operator: tempOperator,
        value: value,
        step: "complete" as FilterStep,
        isEditing: false,
      };
      const updatedFilters = filters.map((f) =>
        f.id === editingFilter.id ? completedFilter : f
      );
      onFiltersChange(updatedFilters);
      setEditingFilter(null);
      setDropdownOpen(false);
      setValidationError(null);
    }
  }, [
    editingFilter,
    fields,
    filters,
    tempOperator,
    tempValue,
    tempMultiValues,
    tempDateValue,
    onFiltersChange,
  ]);

  const handleCancelAndClose = useCallback(() => {
    // Remove incomplete filters from the array, and reset `isEditing` on the rest.
    const finalFilters = filters
      .filter((f) => f.step === "complete")
      .map((f) => (f.isEditing ? { ...f, isEditing: false } : f));

    onFiltersChange(finalFilters);

    setEditingFilter(null);
    setDropdownOpen(false);
    setValidationError(null);
    setChipPosition(null);
  }, [filters, onFiltersChange]);

  const handleDeleteFilter = useCallback(
    (filterId: string) => {
      const updatedFilters = filters.filter((f) => f.id !== filterId);
      onFiltersChange(updatedFilters);
      if (editingFilter?.id === filterId) {
        setEditingFilter(null);
        setDropdownOpen(false);
      }
    },
    [filters, editingFilter, onFiltersChange]
  );

  const handleClearAllFilters = useCallback(() => {
    onFiltersChange([]);
    setEditingFilter(null);
    setDropdownOpen(false);
    if (onSearchChange) {
      onSearchChange("");
    }
  }, [onFiltersChange, onSearchChange]);

  const handleClickAway = useCallback(() => {
    if (dropdownOpen) {
      handleCancelAndClose();
    }
  }, [dropdownOpen, handleCancelAndClose]);

  const handleSearchFocus = useCallback(() => {
    if (!editingFilter) {
      setChipPosition(null);
      setDropdownOpen(true);
    }
  }, [editingFilter]);

  const hasActiveFilters =
    filters.some((f) => f.step === "complete") || searchValue.length > 0;
  const editingField = editingFilter
    ? fields.find((f) => f.key === editingFilter.field)
    : null;
  const availableOperators = editingField
    ? FIELD_OPERATORS[editingField.type]
    : [];

  const canProceed = () => {
    if (!editingFilter || !editingField) return false;

    if (editingFilter.step === "value") {
      if (editingField.multiSelect) return tempMultiValues.length > 0;
      if (editingField.type === "date") return tempDateValue !== null;
      return tempValue.length > 0;
    }
    return false;
  };

  return (
    <ClickAwayListener onClickAway={handleClickAway}>
      <Paper
        elevation={2}
        sx={{
          mb: 4,
          position: "sticky",
          top: 0,
          zIndex: 100,
          backgroundColor: (theme) => theme.palette.background.paper,
          backgroundImage: 'none',
        }}
      >
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          {/* Filter Bar */}
          <Box sx={{ position: "relative" }}>
            <Box
              data-filter-container
              sx={{
                display: "flex",
                alignItems: "flex-start",
                gap: 1,
                minHeight: "40px",
                padding: "8px 12px",
                border: "1px solid",
                borderColor: (theme) => theme.palette.divider,
                borderRadius: 1,
                backgroundColor: (theme) => theme.palette.background.default,
                borderBottomLeftRadius: dropdownOpen ? 0 : undefined,
                borderBottomRightRadius: dropdownOpen ? 0 : undefined,
                "&:focus-within": {
                  borderColor: "primary.main",
                  borderWidth: "2px",
                  padding: "7px 11px",
                },
              }}
              onClick={handleSearchFocus}
            >
              {/* Left side content (search icon + chips + input) */}
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                  flex: 1,
                  minWidth: 0,
                  flexWrap: "wrap",
                }}
              >
                <SearchIcon color="action" sx={{ flexShrink: 0 }} />

                {/* Progressive Filter Chips */}
                {filters.map((filter) => (
                  <ProgressiveFilterChip
                    key={filter.id}
                    filter={filter}
                    fields={fields}
                    onEdit={(chipElement) =>
                      handleEditFilter(filter, chipElement)
                    }
                    onDelete={() => handleDeleteFilter(filter.id)}
                  />
                ))}

                {/* Search Input */}
                <Box
                  sx={{
                    flex: 1,
                    minWidth: "200px",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder={placeholder}
                    value={searchValue}
                    onChange={(e) => onSearchChange?.(e.target.value)}
                    onFocus={handleSearchFocus}
                    style={{
                      border: "none",
                      outline: "none",
                      background: "transparent",
                      flex: 1,
                      fontSize: "14px",
                      color: "inherit",
                      fontFamily: "inherit",
                      minWidth: 0,
                    }}
                  />
                </Box>
              </Box>

              {/* Action Buttons - Always on the right */}
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 0.5,
                  flexShrink: 0,
                  position: "sticky",
                  right: 0,
                  alignSelf: "center",
                }}
              >
                {searchValue && (
                  <Button
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      onSearchChange?.("");
                    }}
                    sx={{ minWidth: "auto", p: 0.5 }}
                  >
                    <ClearIcon fontSize="small" />
                  </Button>
                )}
                {hasActiveFilters && (
                  <Button
                    variant="text"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleClearAllFilters();
                    }}
                    size="small"
                    sx={{
                      minWidth: "auto",
                      width: 24,
                      height: 24,
                      borderRadius: "50%",
                      p: 0,
                      color: 'action.active',
                      backgroundColor: 'action.hover',
                      "&:hover": {
                        backgroundColor: 'action.selected',
                      },
                    }}
                  >
                    <ClearIcon fontSize="small" />
                  </Button>
                )}
              </Box>
            </Box>

            {/* Progressive Dropdown */}
            {dropdownOpen && (
              <Paper
                elevation={3}
                sx={{
                  position: "absolute",
                  top:
                    editingFilter && chipPosition
                      ? `${chipPosition.top + 4}px`
                      : "100%",
                  left:
                    editingFilter && chipPosition
                      ? `${chipPosition.left}px`
                      : 0,
                  right: editingFilter && chipPosition ? "auto" : 0,
                  width: editingFilter && chipPosition ? "auto" : "100%",
                  minWidth:
                    editingFilter && chipPosition
                      ? editingField?.type === "date"
                        ? "320px"
                        : "280px"
                      : "100%",
                  maxWidth: editingFilter && chipPosition ? "400px" : "none",
                  zIndex: 200,
                  maxHeight: "100vh",
                  overflow: "auto",
                  borderRadius: 1,
                  borderTopLeftRadius: editingFilter && chipPosition ? 1 : 0,
                  borderTopRightRadius: editingFilter && chipPosition ? 1 : 0,
                  borderTop:
                    editingFilter && chipPosition ? undefined : "1px solid",
                  borderColor: (theme) => theme.palette.divider,
                  borderWidth: 1,
                  borderStyle: "solid",
                  mt: editingFilter && chipPosition ? 0 : 0,
                  boxShadow: (theme) => theme.shadows[4],
                }}
              >
                {!editingFilter ? (
                  // Field Selection
                  <Box sx={{ p: 1.5 }}>
                    <Typography
                      variant="subtitle2"
                      color="text.secondary"
                      sx={{ px: 1, py: 0.5, fontWeight: 600 }}
                    >
                      ADD FILTER
                    </Typography>
                    <List dense>
                      {availableFields.length === 0 ? (
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ textAlign: "center", py: 2, px: 1 }}
                        >
                          {hideSelectedFields
                            ? "All filters have been used. Remove a filter to add a different one."
                            : "No filters available"}
                        </Typography>
                      ) : (
                        availableFields.map((field) => {
                          const fieldStatus = getFieldAvailability(field);
                          return (
                            <ListItem
                              key={field.key}
                              component="div"
                              onClick={() => handleFieldSelect(field)}
                              sx={{
                                borderRadius: 1,
                                cursor: "pointer",
                                opacity:
                                  fieldStatus.isSelected && !field.multiSelect
                                    ? 0.7
                                    : 1,
                                "&:hover": {
                                  backgroundColor: 'action.hover',
                                },
                              }}
                            >
                              <ListItemText
                                primary={
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      gap: 1,
                                    }}
                                  >
                                    <span>{field.label}</span>
                                    {/* {field.multiSelect && (
                                      <Typography 
                                        variant="caption" 
                                        sx={{ 
                                          backgroundColor: 'primary.main', 
                                          color: 'white', 
                                          px: 0.5, 
                                          py: 0.25, 
                                          borderRadius: 0.5,
                                          fontSize: '0.6rem'
                                        }}
                                      >
                                        MULTI
                                      </Typography>
                                    )} */}
                                    {fieldStatus.isSelected &&
                                      !field.multiSelect && (
                                        <Typography
                                          variant="caption"
                                          sx={{
                                            backgroundColor: 'grey.400',
                                            color: 'white',
                                            px: 0.5,
                                            py: 0.25,
                                            borderRadius: 0.5,
                                            fontSize: "0.6rem",
                                          }}
                                        >
                                          USED
                                        </Typography>
                                      )}
                                  </Box>
                                }
                                sx={{
                                  "& .MuiListItemText-primary": {
                                    fontSize: "0.875rem",
                                  },
                                }}
                              />
                            </ListItem>
                          );
                        })
                      )}
                    </List>
                  </Box>
                ) : (
                  // Step Configuration (rest of the component remains the same)
                  <Box sx={{ p: 1 }}>
                    {/* Step Indicator */}
                    <Box
                      sx={{
                        mb: 1,
                        px: 0.5,
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      <Typography
                        variant="subtitle2"
                        sx={{ fontWeight: 600, fontSize: "0.875rem" }}
                      >
                        {editingField?.label} -{" "}
                        {editingFilter.step === "operator"
                          ? "Select Operator"
                          : "Enter Value"}
                      </Typography>
                      <Button
                        size="small"
                        onClick={handleCancelAndClose}
                        sx={{
                          minWidth: "auto",
                          p: 0.5,
                          color: "text.secondary",
                        }}
                      >
                        <CloseIcon fontSize="small" />
                      </Button>
                    </Box>

                    {editingFilter.step === "operator" && (
                      <Box sx={{ mb: 1 }}>
                        <List dense disablePadding>
                          {availableOperators.map((operator) => (
                            <ListItem
                              key={operator}
                              component="div"
                              onClick={() => {
                                setTempOperator(operator);

                                // Auto-advance to value step when operator is selected
                                if (editingFilter) {
                                  const updatedFilter = {
                                    ...editingFilter,
                                    operator: operator,
                                    step: "value" as FilterStep,
                                  };
                                  const updatedFilters = filters.map((f) =>
                                    f.id === editingFilter.id
                                      ? updatedFilter
                                      : f
                                  );
                                  onFiltersChange(updatedFilters);
                                  setEditingFilter(updatedFilter);

                                  // Recalculate chip position for the updated chip
                                  setTimeout(() => {
                                    const chipElement = document.querySelector(
                                      `[data-filter-id="${editingFilter.id}"]`
                                    ) as HTMLElement;
                                    if (chipElement) {
                                      const position =
                                        calculateChipPosition(chipElement);
                                      setChipPosition(position);
                                    }
                                  }, 10);
                                }
                              }}
                              sx={{
                                minHeight: "32px",
                                borderRadius: 0.5,
                                px: 1,
                                py: 0.5,
                                cursor: "pointer",
                                "&:hover": {
                                  backgroundColor: 'action.hover',
                                },
                              }}
                            >
                              <ListItemText
                                primary={OPERATOR_LABELS[operator]}
                                sx={{
                                  margin: 0,
                                  "& .MuiListItemText-primary": {
                                    fontSize: "0.875rem",
                                    lineHeight: 1.2,
                                  },
                                }}
                              />
                            </ListItem>
                          ))}
                        </List>
                      </Box>
                    )}

                    {editingFilter.step === "value" && editingField && (
                      <>
                        {editingField.multiSelect && editingField.options ? (
                          <Box sx={{ mb: 1 }}>
                            <MultiSelectFilter
                              options={editingField.options.map((option) => ({
                                value: option,
                                label: option,
                              }))}
                              selectedValues={tempMultiValues}
                              onSelectionChange={(values) => {
                                setTempMultiValues(values);
                                setValidationError(null);
                              }}
                              title=""
                              searchable={false}
                              placeholder=""
                              maxHeight={120}
                              showSelectedCount={false}
                            />
                          </Box>
                        ) : editingField.type === "date" ? (
                          <Box sx={{ mb: 1 }}>
                            <TextField
                              value={
                                tempDateValue
                                  ? tempDateValue.toISOString().slice(0, 16)
                                  : ""
                              }
                              onChange={(e) => {
                                const dateValue = e.target.value
                                  ? new Date(e.target.value)
                                  : null;
                                setTempDateValue(dateValue);
                                setValidationError(null);
                              }}
                              size="small"
                              fullWidth
                              type="datetime-local"
                              InputLabelProps={{
                                shrink: true,
                              }}
                              error={!!validationError}
                              helperText={validationError}
                            />
                          </Box>
                        ) : editingField.type === "enum" &&
                          editingField.options ? (
                          <Box sx={{ mb: 1 }}>
                            <List dense disablePadding>
                              {editingField.options.map((option) => (
                                <ListItem
                                  key={option}
                                  component="div"
                                  onClick={() => {
                                    setTempValue(option);
                                    setValidationError(null);
                                  }}
                                  sx={{
                                    minHeight: "32px",
                                    borderRadius: 0.5,
                                    px: 1,
                                    py: 0.5,
                                    cursor: "pointer",
                                    "&:hover": {
                                      backgroundColor: "action.hover",
                                    },
                                  }}
                                >
                                  <ListItemIcon sx={{ minWidth: 40 }}>
                                    <Checkbox
                                      edge="start"
                                      checked={tempValue === option}
                                      size="small"
                                      color="primary"
                                      sx={{ p: 0 }}
                                      readOnly
                                    />
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={option}
                                    sx={{
                                      margin: 0,
                                      "& .MuiListItemText-primary": {
                                        fontSize: "0.875rem",
                                        lineHeight: 1.2,
                                      },
                                    }}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        ) : (
                          <Box sx={{ mb: 1 }}>
                            <TextField
                              value={tempValue}
                              onChange={(e) => {
                                setTempValue(e.target.value);
                                setValidationError(null);
                              }}
                              size="small"
                              fullWidth
                              placeholder="Enter value"
                              type={
                                editingField.type === "number"
                                  ? "number"
                                  : "text"
                              }
                              error={!!validationError}
                              helperText={validationError}
                            />
                          </Box>
                        )}
                      </>
                    )}

                    {validationError && editingField?.multiSelect && (
                      <Typography
                        variant="caption"
                        color="error"
                        sx={{ display: "block", mb: 1, px: 0.5 }}
                      >
                        {validationError}
                      </Typography>
                    )}

                    {editingFilter.step === "value" && (
                      <Button
                        variant="contained"
                        size="small"
                        fullWidth
                        onClick={handleNextStep}
                        disabled={!canProceed()}
                        sx={{
                          py: 0.75,
                          fontSize: "0.8rem",
                        }}
                      >
                        APPLY FILTER
                      </Button>
                    )}
                  </Box>
                )}
              </Paper>
            )}
          </Box>
        </Box>
      </Paper>
    </ClickAwayListener>
  );
};
