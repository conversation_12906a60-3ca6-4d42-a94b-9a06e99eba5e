import React, { useC<PERSON>back, useEffect, useMemo, useState } from "react";
import {
  <PERSON>,
  <PERSON>ack,
  <PERSON><PERSON><PERSON>,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useTheme,
  alpha,
  Divider,
  MenuItem,
  TextField,
} from "@mui/material";
import {
  ChevronDown,
  Settings2,
  Database,
  AlertCircle,
  CheckCircle,
  Info,
} from "lucide-react";
import { DataModel, getDataModelsForService } from "./mock-data";
import FieldMappingDialogV2 from "./FieldMappingDialogV2";
import { ServiceProfile } from "types/service-profile";
import { DEPLOYMENT_MODAL } from "sections/integration-details/constant";
import { useGetServiceProfile } from "hooks/api/service-profile/useGetServiceProfile";
import { useQuery } from "@tanstack/react-query";
import { State } from "hooks/useStatus";
import { LoadingOutlined } from "@ant-design/icons";

interface DataModelsConfigurationProps {
  serviceProfile: ServiceProfile;
  onConfigureModel?: (model: any) => void;
}

export default function DataModelsConfiguration({
  serviceProfile,
}: DataModelsConfigurationProps) {
  const theme = useTheme();
  const [expandedModel, setExpandedModel] = useState<string | false>(false);
  const [selectedModel, setSelectedModel] = useState<any>(null);
  const [mappingDialogOpen, setMappingDialogOpen] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<string | null>(null);

  const serviceProfileId = serviceProfile?.id;

  const {
    searchServiceProfileDataTypesQueryOptions,
    getAllVersionsServiceProfile,
  } = useGetServiceProfile();

  const { data: versionQueryData } = getAllVersionsServiceProfile({
    id: serviceProfileId,
  });

  const isSelfManaged =
    serviceProfile?.deploymentModel?.type === DEPLOYMENT_MODAL.SELF_MANAGED;

  const getSearchPayload = useCallback(() => {
    const criteria: Array<{
      property: string;
      operator: string;
      values: string[];
    }> = [
      {
        property: "/state",
        operator: "=",
        values: [State.ACTIVE],
      },
      {
        property: "/serviceProfile/id",
        operator: "=",
        values: [serviceProfileId],
      },
    ];

    if (isSelfManaged) {
      criteria.push({
        property: "/version",
        operator: "=",
        values: [selectedVersion as string],
      });
    }

    const queryPayload = {
      filter: {
        and: criteria,
      },
      pagination: {
        offset: 0,
        limit: 20,
      },
      sort: [
        {
          property: "/displayOrder",
          direction: "ASC",
        },
      ],
    };

    return queryPayload;
  }, [selectedVersion, serviceProfileId]);

  const {
    data: serviceProfileDataTypeQueryData,
    isLoading: isLoadingDataTypes,
  } = useQuery({
    ...searchServiceProfileDataTypesQueryOptions({
      id: serviceProfileId,
      payload: getSearchPayload(),
    }),
    enabled: isSelfManaged ? !!selectedVersion : true,
  });

  const dataTypes = serviceProfileDataTypeQueryData?.data?.data ?? [];

  const versionOptions: Array<{ label: string; value: string }> =
    versionQueryData?.data?.map((i: { id: string; name: string }) => ({
      label: i?.name,
      value: i?.id,
    }));

  const dataModels: DataModel[] = useMemo(() => {
    return dataTypes?.map((i) => {
      const tempObj = {
        id: i?.id,
        name: i?.name,
        key: i?.key,
        ref: i?.mappedTo,
        description: i?.description,
        fields: [],
        customFields: [],
        fieldMappings: [],
      };
      return tempObj;
    });
  }, [dataTypes]);

  const handleAccordionChange =
    (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedModel(isExpanded ? panel : false);
    };

  const handleConfigureModel = (model: any) => {
    setSelectedModel(model);
    setMappingDialogOpen(true);
  };

  const getModelStatus = (model: any) => {
    const customFieldsCount = model.customFields?.length || 0;
    const mappedFieldsCount =
      model.fieldMappings?.filter((m: any) => m.mapped).length || 0;
    const totalFields = model.fields.length;

    if (mappedFieldsCount === 0) return "not-configured";
    if (mappedFieldsCount < totalFields) return "partially-configured";
    return "configured";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "configured":
        return theme.palette.success.main;
      case "partially-configured":
        return theme.palette.warning.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "configured":
        return <CheckCircle size={16} />;
      case "partially-configured":
        return <AlertCircle size={16} />;
      default:
        return null;
    }
  };

  useEffect(() => {
    if (versionOptions?.length) {
      setSelectedVersion(versionOptions?.at(0)?.value as string);
    }
  }, [versionQueryData]);

  return (
    <Box>
      {/* Help Text and Version Dropdown */}
      <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 3 }}>
        <Box flex={1} display="flex" alignItems="center" gap={1}>
          <Info size={16} color={theme.palette.info.main} />
          <Typography variant="body2" color="text.secondary">
            You can map your data type attributes to additional fields for your
            use cases
          </Typography>
        </Box>

        {isSelfManaged && (
          <TextField
            value={selectedVersion}
            sx={{ minWidth: 150 }}
            select
            label={!selectedVersion ? "Version" : ""}
            onChange={(e) => setSelectedVersion(e?.target?.value)}
          >
            {versionOptions?.map((i) => (
              <MenuItem value={i?.value} key={i?.value}>
                {i?.label}
              </MenuItem>
            ))}
          </TextField>
        )}
      </Stack>

      <Stack spacing={2}>
        {isLoadingDataTypes && <LoadingOutlined />}
        {dataModels.map((model) => {
          const status = getModelStatus(model);
          const statusColor = getStatusColor(status);

          return (
            <Accordion
              key={model.id}
              expanded={expandedModel === model.id}
              onChange={handleAccordionChange(model.id)}
              elevation={0}
              sx={{
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1,
                "&:before": {
                  display: "none",
                },
                "&.Mui-expanded": {
                  margin: 0,
                },
              }}
            >
              <AccordionSummary
                expandIcon={<ChevronDown size={16} />}
                sx={{
                  minHeight: 56,
                  "&.Mui-expanded": {
                    minHeight: 56,
                  },
                  "& .MuiAccordionSummary-content": {
                    alignItems: "center",
                    margin: "12px 0",
                    marginRight: 2,
                    "&.Mui-expanded": {
                      margin: "12px 0",
                      marginRight: 2,
                    },
                  },
                  "& .MuiAccordionSummary-expandIconWrapper": {
                    transform: "rotate(-90deg)",
                    "&.Mui-expanded": {
                      transform: "rotate(0deg)",
                    },
                  },
                }}
              >
                <Stack
                  direction="row"
                  alignItems="center"
                  spacing={2}
                  width="100%"
                >
                  <Box sx={{ color: theme.palette.text.secondary }}>
                    <Database size={18} />
                  </Box>
                  <Typography variant="body2" fontWeight={500} flex={1}>
                    {model.name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {model.fields.length} fields •{" "}
                    {model.customFields?.length || 0} custom •{" "}
                    {model.fieldMappings?.filter((m: any) => m.mapped).length ||
                      0}{" "}
                    mapped
                  </Typography>
                  {status !== "not-configured" && (
                    <Box sx={{ color: statusColor, ml: 1 }}>
                      {getStatusIcon(status)}
                    </Box>
                  )}
                </Stack>
              </AccordionSummary>
              <AccordionDetails sx={{ pt: 1, pb: 2 }}>
                <Stack spacing={2}>
                  <Typography variant="body2" color="text.secondary">
                    {model.description}
                  </Typography>

                  {/* Summary Stats - Compact inline layout */}
                  <Stack direction="row" spacing={3} alignItems="center">
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Unified Fields
                      </Typography>
                      <Typography variant="h6" fontWeight={600}>
                        {model.fields.length}
                      </Typography>
                    </Box>
                    <Divider orientation="vertical" flexItem />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Additional Fields
                      </Typography>
                      <Typography variant="h6" fontWeight={600}>
                        {model.customFields?.length || 0}
                      </Typography>
                    </Box>
                    {/* <Divider orientation="vertical" flexItem />
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Mapped Fields
                      </Typography>
                      <Typography variant="h6" fontWeight={600}>
                        {model.fieldMappings?.filter((m: any) => m.mapped).length || 0}
                      </Typography>
                    </Box> */}
                    <Box flex={1} />
                    {/* Configure Button */}
                    <Button
                      size="small"
                      variant="outlined"
                      startIcon={<Settings2 size={14} />}
                      onClick={() => handleConfigureModel(model)}
                      sx={{
                        textTransform: "none",
                        borderColor: theme.palette.primary.main,
                        color: theme.palette.primary.main,
                        "&:hover": {
                          borderColor: theme.palette.primary.dark,
                          backgroundColor: alpha(
                            theme.palette.primary.main,
                            0.08
                          ),
                        },
                      }}
                    >
                      Configure
                    </Button>
                  </Stack>
                </Stack>
              </AccordionDetails>
            </Accordion>
          );
        })}
      </Stack>

      {/* Field Mapping Dialog */}
      {selectedModel && (
        <FieldMappingDialogV2
          open={mappingDialogOpen}
          onClose={() => {
            setMappingDialogOpen(false);
            setSelectedModel(null);
          }}
          dataModel={selectedModel}
          serviceProfile={serviceProfile}
        />
      )}
    </Box>
  );
}
