/**
 * GitHub API OpenAPI Specification for Repository
 * Based on GitHub's actual REST API structure
 */

export const githubOpenApiSpec = {
  "openapi": "3.0.0",
  "info": {
    "title": "GitHub API",
    "version": "v3"
  },
  "components": {
    "schemas": {
      "Repository": {
        "type": "object",
        "required": ["id", "name", "full_name", "private", "owner"],
        "properties": {
          "id": {
            "type": "integer",
            "format": "int64",
            "description": "Unique identifier of the repository"
          },
          "node_id": {
            "type": "string",
            "description": "GraphQL node ID"
          },
          "name": {
            "type": "string",
            "description": "The name of the repository"
          },
          "full_name": {
            "type": "string",
            "description": "The full name of the repository"
          },
          "private": {
            "type": "boolean",
            "description": "Whether the repository is private"
          },
          "owner": {
            "$ref": "#/components/schemas/User"
          },
          "html_url": {
            "type": "string",
            "format": "uri",
            "description": "The repository URL"
          },
          "description": {
            "type": "string",
            "description": "The repository description",
            "nullable": true
          },
          "fork": {
            "type": "boolean",
            "description": "Whether the repository is a fork"
          },
          "url": {
            "type": "string",
            "format": "uri",
            "description": "The API URL for the repository"
          },
          "archive_url": {
            "type": "string",
            "description": "Archive URL template"
          },
          "assignees_url": {
            "type": "string",
            "description": "Assignees URL template"
          },
          "blobs_url": {
            "type": "string",
            "description": "Blobs URL template"
          },
          "branches_url": {
            "type": "string",
            "description": "Branches URL template"
          },
          "collaborators_url": {
            "type": "string",
            "description": "Collaborators URL template"
          },
          "comments_url": {
            "type": "string",
            "description": "Comments URL template"
          },
          "commits_url": {
            "type": "string",
            "description": "Commits URL template"
          },
          "compare_url": {
            "type": "string",
            "description": "Compare URL template"
          },
          "contents_url": {
            "type": "string",
            "description": "Contents URL template"
          },
          "contributors_url": {
            "type": "string",
            "format": "uri",
            "description": "Contributors API URL"
          },
          "deployments_url": {
            "type": "string",
            "format": "uri",
            "description": "Deployments API URL"
          },
          "downloads_url": {
            "type": "string",
            "format": "uri",
            "description": "Downloads API URL"
          },
          "events_url": {
            "type": "string",
            "format": "uri",
            "description": "Events API URL"
          },
          "forks_url": {
            "type": "string",
            "format": "uri",
            "description": "Forks API URL"
          },
          "git_commits_url": {
            "type": "string",
            "description": "Git commits URL template"
          },
          "git_refs_url": {
            "type": "string",
            "description": "Git refs URL template"
          },
          "git_tags_url": {
            "type": "string",
            "description": "Git tags URL template"
          },
          "git_url": {
            "type": "string",
            "description": "Git clone URL"
          },
          "issue_comment_url": {
            "type": "string",
            "description": "Issue comment URL template"
          },
          "issue_events_url": {
            "type": "string",
            "description": "Issue events URL template"
          },
          "issues_url": {
            "type": "string",
            "description": "Issues URL template"
          },
          "keys_url": {
            "type": "string",
            "description": "Keys URL template"
          },
          "labels_url": {
            "type": "string",
            "description": "Labels URL template"
          },
          "languages_url": {
            "type": "string",
            "format": "uri",
            "description": "Languages API URL"
          },
          "merges_url": {
            "type": "string",
            "format": "uri",
            "description": "Merges API URL"
          },
          "milestones_url": {
            "type": "string",
            "description": "Milestones URL template"
          },
          "notifications_url": {
            "type": "string",
            "description": "Notifications URL template"
          },
          "pulls_url": {
            "type": "string",
            "description": "Pull requests URL template"
          },
          "releases_url": {
            "type": "string",
            "description": "Releases URL template"
          },
          "ssh_url": {
            "type": "string",
            "description": "SSH clone URL"
          },
          "stargazers_url": {
            "type": "string",
            "format": "uri",
            "description": "Stargazers API URL"
          },
          "statuses_url": {
            "type": "string",
            "description": "Statuses URL template"
          },
          "subscribers_url": {
            "type": "string",
            "format": "uri",
            "description": "Subscribers API URL"
          },
          "subscription_url": {
            "type": "string",
            "format": "uri",
            "description": "Subscription API URL"
          },
          "tags_url": {
            "type": "string",
            "format": "uri",
            "description": "Tags API URL"
          },
          "teams_url": {
            "type": "string",
            "format": "uri",
            "description": "Teams API URL"
          },
          "trees_url": {
            "type": "string",
            "description": "Trees URL template"
          },
          "clone_url": {
            "type": "string",
            "format": "uri",
            "description": "HTTPS clone URL"
          },
          "mirror_url": {
            "type": "string",
            "format": "uri",
            "description": "Mirror URL",
            "nullable": true
          },
          "hooks_url": {
            "type": "string",
            "format": "uri",
            "description": "Hooks API URL"
          },
          "svn_url": {
            "type": "string",
            "format": "uri",
            "description": "SVN URL"
          },
          "homepage": {
            "type": "string",
            "format": "uri",
            "description": "Repository homepage",
            "nullable": true
          },
          "language": {
            "type": "string",
            "description": "Primary programming language",
            "nullable": true
          },
          "forks_count": {
            "type": "integer",
            "description": "Number of forks"
          },
          "stargazers_count": {
            "type": "integer",
            "description": "Number of stars"
          },
          "watchers_count": {
            "type": "integer",
            "description": "Number of watchers"
          },
          "size": {
            "type": "integer",
            "description": "Repository size in kilobytes"
          },
          "default_branch": {
            "type": "string",
            "description": "Default branch name"
          },
          "open_issues_count": {
            "type": "integer",
            "description": "Number of open issues"
          },
          "is_template": {
            "type": "boolean",
            "description": "Whether this is a template repository"
          },
          "topics": {
            "type": "array",
            "items": {
              "type": "string"
            },
            "description": "Repository topics"
          },
          "has_issues": {
            "type": "boolean",
            "description": "Whether issues are enabled"
          },
          "has_projects": {
            "type": "boolean",
            "description": "Whether projects are enabled"
          },
          "has_wiki": {
            "type": "boolean",
            "description": "Whether wiki is enabled"
          },
          "has_pages": {
            "type": "boolean",
            "description": "Whether GitHub Pages is enabled"
          },
          "has_downloads": {
            "type": "boolean",
            "description": "Whether downloads are enabled"
          },
          "archived": {
            "type": "boolean",
            "description": "Whether the repository is archived"
          },
          "disabled": {
            "type": "boolean",
            "description": "Whether the repository is disabled"
          },
          "visibility": {
            "type": "string",
            "enum": ["public", "private", "internal"],
            "description": "Repository visibility"
          },
          "pushed_at": {
            "type": "string",
            "format": "date-time",
            "description": "Last push timestamp",
            "nullable": true
          },
          "created_at": {
            "type": "string",
            "format": "date-time",
            "description": "Repository creation timestamp"
          },
          "updated_at": {
            "type": "string",
            "format": "date-time",
            "description": "Last update timestamp"
          },
          "permissions": {
            "$ref": "#/components/schemas/Permissions"
          },
          "allow_rebase_merge": {
            "type": "boolean",
            "description": "Whether rebase merging is allowed"
          },
          "template_repository": {
            "$ref": "#/components/schemas/Repository"
          },
          "temp_clone_token": {
            "type": "string",
            "description": "Temporary clone token"
          },
          "allow_squash_merge": {
            "type": "boolean",
            "description": "Whether squash merging is allowed"
          },
          "allow_auto_merge": {
            "type": "boolean",
            "description": "Whether auto-merge is allowed"
          },
          "delete_branch_on_merge": {
            "type": "boolean",
            "description": "Whether to delete head branches after merge"
          },
          "allow_merge_commit": {
            "type": "boolean",
            "description": "Whether merge commits are allowed"
          },
          "allow_forking": {
            "type": "boolean",
            "description": "Whether forking is allowed"
          },
          "web_commit_signoff_required": {
            "type": "boolean",
            "description": "Whether web commit signoff is required"
          },
          "subscribers_count": {
            "type": "integer",
            "description": "Number of subscribers"
          },
          "network_count": {
            "type": "integer",
            "description": "Network count"
          },
          "license": {
            "$ref": "#/components/schemas/License"
          },
          "organization": {
            "$ref": "#/components/schemas/Organization"
          },
          "parent": {
            "$ref": "#/components/schemas/Repository"
          },
          "source": {
            "$ref": "#/components/schemas/Repository"
          },
          "security_and_analysis": {
            "$ref": "#/components/schemas/SecurityAndAnalysis"
          }
        }
      },
      "User": {
        "type": "object",
        "required": ["login", "id"],
        "properties": {
          "login": {
            "type": "string",
            "description": "Username"
          },
          "id": {
            "type": "integer",
            "format": "int64",
            "description": "Unique identifier"
          },
          "node_id": {
            "type": "string",
            "description": "GraphQL node ID"
          },
          "avatar_url": {
            "type": "string",
            "format": "uri",
            "description": "Avatar image URL"
          },
          "gravatar_id": {
            "type": "string",
            "description": "Gravatar ID",
            "nullable": true
          },
          "url": {
            "type": "string",
            "format": "uri",
            "description": "API URL"
          },
          "html_url": {
            "type": "string",
            "format": "uri",
            "description": "Profile URL"
          },
          "followers_url": {
            "type": "string",
            "format": "uri",
            "description": "Followers API URL"
          },
          "following_url": {
            "type": "string",
            "description": "Following URL template"
          },
          "gists_url": {
            "type": "string",
            "description": "Gists URL template"
          },
          "starred_url": {
            "type": "string",
            "description": "Starred URL template"
          },
          "subscriptions_url": {
            "type": "string",
            "format": "uri",
            "description": "Subscriptions API URL"
          },
          "organizations_url": {
            "type": "string",
            "format": "uri",
            "description": "Organizations API URL"
          },
          "repos_url": {
            "type": "string",
            "format": "uri",
            "description": "Repositories API URL"
          },
          "events_url": {
            "type": "string",
            "description": "Events URL template"
          },
          "received_events_url": {
            "type": "string",
            "format": "uri",
            "description": "Received events API URL"
          },
          "type": {
            "type": "string",
            "enum": ["Bot", "User", "Organization"],
            "description": "User type"
          },
          "site_admin": {
            "type": "boolean",
            "description": "Whether user is a site administrator"
          },
          "name": {
            "type": "string",
            "description": "Display name",
            "nullable": true
          },
          "company": {
            "type": "string",
            "description": "Company name",
            "nullable": true
          },
          "blog": {
            "type": "string",
            "description": "Blog URL",
            "nullable": true
          },
          "location": {
            "type": "string",
            "description": "Location",
            "nullable": true
          },
          "email": {
            "type": "string",
            "format": "email",
            "description": "Email address",
            "nullable": true
          },
          "hireable": {
            "type": "boolean",
            "description": "Whether user is available for hire",
            "nullable": true
          },
          "bio": {
            "type": "string",
            "description": "Biography",
            "nullable": true
          },
          "twitter_username": {
            "type": "string",
            "description": "Twitter username",
            "nullable": true
          },
          "public_repos": {
            "type": "integer",
            "description": "Number of public repositories"
          },
          "public_gists": {
            "type": "integer",
            "description": "Number of public gists"
          },
          "followers": {
            "type": "integer",
            "description": "Number of followers"
          },
          "following": {
            "type": "integer",
            "description": "Number of users following"
          },
          "created_at": {
            "type": "string",
            "format": "date-time",
            "description": "Account creation timestamp"
          },
          "updated_at": {
            "type": "string",
            "format": "date-time",
            "description": "Last update timestamp"
          }
        }
      },
      "Organization": {
        "allOf": [
          {
            "$ref": "#/components/schemas/User"
          },
          {
            "type": "object",
            "properties": {
              "billing_email": {
                "type": "string",
                "format": "email",
                "description": "Billing email",
                "nullable": true
              },
              "plan": {
                "$ref": "#/components/schemas/Plan"
              },
              "default_repository_permission": {
                "type": "string",
                "enum": ["read", "write", "admin", "none"],
                "description": "Default repository permission"
              },
              "members_can_create_repositories": {
                "type": "boolean",
                "description": "Whether members can create repositories"
              },
              "two_factor_requirement_enabled": {
                "type": "boolean",
                "description": "Whether 2FA is required"
              },
              "members_allowed_repository_creation_type": {
                "type": "string",
                "enum": ["all", "private", "none"],
                "description": "Repository creation permission"
              },
              "members_can_create_internal_repositories": {
                "type": "boolean",
                "description": "Whether members can create internal repos"
              },
              "members_can_create_pages": {
                "type": "boolean",
                "description": "Whether members can create GitHub Pages"
              },
              "members_can_fork_private_repositories": {
                "type": "boolean",
                "description": "Whether members can fork private repos"
              }
            }
          }
        ]
      },
      "Plan": {
        "type": "object",
        "required": ["name"],
        "properties": {
          "name": {
            "type": "string",
            "description": "Plan name"
          },
          "space": {
            "type": "integer",
            "description": "Storage space"
          },
          "private_repos": {
            "type": "integer",
            "description": "Number of private repositories"
          },
          "collaborators": {
            "type": "integer",
            "description": "Number of collaborators"
          }
        }
      },
      "Permissions": {
        "type": "object",
        "properties": {
          "admin": {
            "type": "boolean",
            "description": "Admin permission"
          },
          "maintain": {
            "type": "boolean",
            "description": "Maintain permission"
          },
          "push": {
            "type": "boolean",
            "description": "Push permission"
          },
          "triage": {
            "type": "boolean",
            "description": "Triage permission"
          },
          "pull": {
            "type": "boolean",
            "description": "Pull permission"
          }
        }
      },
      "License": {
        "type": "object",
        "required": ["key", "name", "spdx_id"],
        "properties": {
          "key": {
            "type": "string",
            "description": "License key"
          },
          "name": {
            "type": "string",
            "description": "License name"
          },
          "spdx_id": {
            "type": "string",
            "description": "SPDX identifier",
            "nullable": true
          },
          "url": {
            "type": "string",
            "format": "uri",
            "description": "License URL",
            "nullable": true
          },
          "node_id": {
            "type": "string",
            "description": "GraphQL node ID"
          }
        }
      },
      "SecurityAndAnalysis": {
        "type": "object",
        "properties": {
          "advanced_security": {
            "$ref": "#/components/schemas/AdvancedSecurity"
          },
          "secret_scanning": {
            "$ref": "#/components/schemas/SecretScanning"
          },
          "secret_scanning_push_protection": {
            "$ref": "#/components/schemas/SecretScanningPushProtection"
          },
          "dependabot_security_updates": {
            "$ref": "#/components/schemas/DependabotSecurityUpdates"
          }
        }
      },
      "AdvancedSecurity": {
        "type": "object",
        "properties": {
          "status": {
            "type": "string",
            "enum": ["enabled", "disabled"],
            "description": "Advanced security status"
          }
        }
      },
      "SecretScanning": {
        "type": "object",
        "properties": {
          "status": {
            "type": "string",
            "enum": ["enabled", "disabled"],
            "description": "Secret scanning status"
          }
        }
      },
      "SecretScanningPushProtection": {
        "type": "object",
        "properties": {
          "status": {
            "type": "string",
            "enum": ["enabled", "disabled"],
            "description": "Push protection status"
          }
        }
      },
      "DependabotSecurityUpdates": {
        "type": "object",
        "properties": {
          "status": {
            "type": "string",
            "enum": ["enabled", "disabled"],
            "description": "Dependabot security updates status"
          }
        }
      }
    }
  }
};