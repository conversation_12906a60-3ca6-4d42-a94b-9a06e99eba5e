/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo, useState, useCallback, useEffect } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    Drawer,
    TextField,
    IconButton,
    Divider,
    FormControl,
    Select,
    InputLabel,
    Chip,
    Menu,
    MenuItem,
    Alert,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useForm } from "react-hook-form";
import { useNavigate } from "@remix-run/react";
import { Form, FormField, FormItem } from "components/@extended/Form"
import Table from "components/@extended/Table";
import { useTable } from "hooks/table/useTable";
import { useGetDockProfile } from "hooks/api/dockProfiles/useDockProfile";
import useUserDetails from "store/user";
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Box } from "@mui/system";
import { EllipsisOutlined } from "@ant-design/icons";
import UnizoDockModal from "./UnizoDockModal";
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import Update from './updateDock'
import nameChecker from "constants/categoryMapper";
import { State } from "hooks/useStatus";
import { serviceProfileClient } from "services/service-profile.service";
import { useServiceProfile } from "hooks/useServiceProfile";
import MainCard from "components/MainCard";
import { useGetOrganization } from "hooks/api/organization/useGetOrganization";
import LearnMoreLink from "components/@extended/LearnMoreLink";

const TITLE = `Test run the authentication experience for your end users before integrating into your product.`;
const WARN_MESSAGE = (
  <>
    Configure a Platform webhook event listener to receive Integration related events.{" "}
    <LearnMoreLink
      href="https://docs.unizo.ai/docs/unizo-console/connect-ui/#webhook-integration"
      target="_blank"
      rel="noopener noreferrer"
    >
      Learn more
    </LearnMoreLink>
  </>
);


const defaultValues: WatchFormValues = {
    type: [] as any,
    customerKey: "",
    customerOrgName: "",
    provider: [] as any,
};

const alphaNumRegex = /^[a-zA-Z0-9-_]+$/;

const watchFormScheme = z.object({
    type: z
        .union([z.string(), z.array(z.string())])
        .optional()
        .transform(val => (typeof val === "string" ? [val] : val)),

    customerKey: z
        .string()
        .min(3, "Customer key must be at least 3 characters long")
        .max(36, "Customer key cannot exceed 36 characters")
        .regex(alphaNumRegex, "Key should be alphanumeric and may include hyphens and underscores only.")
        .nonempty("Customer key is required"),

    customerOrgName: z
        .string()
        .min(2, "Customer name must be at least 2 characters long")
        .max(15, "Customer name cannot exceed 15 characters")
        .regex(alphaNumRegex, "Name should be alphanumeric and may include hyphens and underscores only.")
        .nonempty("Customer name is required"),

    provider: z
        .union([z.string(), z.array(z.string())])
        .optional()
        .transform(val => (typeof val === "string" ? [val] : val)),
});

type WatchFormValues = z.infer<typeof watchFormScheme>


export default function UserFlow() {
    const navigate = useNavigate();

    const { getDockProfiles, attemptCreateServiceKey, attemptDeleteDockProfile } = useGetDockProfile();

    const { data, refetch } = getDockProfiles()

    const { subscriptions: subscriptionsData, user } = useUserDetails()

    const [open, setOpen] = useState(false);
    const [, setLinkVisible] = useState(false);
    const [isOpen, setIsOpen] = useState(false);

    const [isId, setIsId] = useState('');
    const [userFlow, setUserFlow] = useState('');

    const [selectOpen, setSelectOpen] = useState(false);
    const [providerSelectOpen, setProviderSelectOpen] = useState(false);
    const [dockProfiles, setDockProfiles] = useState(data?.data || []);
    const [magicLinkId, setMagicLinkId] = useState<string | null>(null);
    const [availableProviders, setAvailableProviders] = useState<any[]>([]);
    const [paginationState, setPaginationState] = useState<any>({
        pageIndex: 0,
        pageSize: 10,
    })
    const { loadImage } = useServiceProfile();
    const provider = userFlow === "PROVIDER";
    const { configs , isConfigLoading: isConfigsLoading} = useGetOrganization({ id: user?.organization?.id });

    const CreateServiceKey = async () => {
        const values = form.getValues();

        const payload = {
            "type": "INTEGRATION_TOKEN",
            "name": "organization-serviceKey",
            "subOrganization": {
                "name": values.customerOrgName,
                "externalKey": values.customerKey
            },
            "integration": {
                "type": "GENERIC",
                "target": userFlow === "PROVIDER" ?
                    {
                        "type": "Provider",
                        "providerSelectors": [
                            {
                                "type": "SVC",
                                "id": values.provider
                            }
                        ]
                    } :
                    {
                        "type": "Category",
                        "categorySelectors": Array.isArray(values.type)
                            ? values.type?.map((t) => ({ type: t })) || []
                            : [{ type: values.type }]
                    }
            },
            "dockProfile": {
                "id": isId
            }
        }
        try {
            await attemptCreateServiceKey(payload, (data: any) => {
                const link = data?.data?.formDescriptorUrl;
                setMagicLinkId(link);

                if (link) {
                    setOpen(false);
                    setIsOpen(true)
                }
                form.reset(defaultValues);
            });

        } catch (error) {
            console.error("Error creating service key:", error);
        }
    }

  const hasPlatformWatchHook = configs?.data?.some(
  (config: any) => config?.type === "PLATFORM_WATCH_HOOK") ?? false;

    const deleteDockProfile = async (id: string) => {
        try {
            await attemptDeleteDockProfile(id);
            setDockProfiles((prev: any) => prev.filter((item: any) => item.id !== id));
            await refetch();
        } catch (error) {
            console.log(error);
        }
    };

    const form = useForm<WatchFormValues>({
        resolver: zodResolver(watchFormScheme),
        defaultValues,
        mode: "onChange",
    })

    const closeDrawer = () => {
        form.reset(defaultValues);
        setOpen(false);
        setUserFlow("");
    }

    // const openUpdateModal = () => {
    //     setIsUpdateOpen(true)
    // }
    // const CloseUpdateModal = () => {
    //     setIsUpdateOpen(false)
    // }


    const updatedValues: Record<string, string> = {
        POP_UP: "Pop Up",
        EMBEDDED: "Embedded"
    }

    const getOptions = useCallback(() => {
        return subscriptionsData.map(({ product }: any) => ({
            value: product?.code,
            label: product?.name,
        }));
    }, [subscriptionsData]);

    const getItems = (record: any) => [
        {
            label: 'Test Run',
            icon: <PlayArrowIcon fontSize="small" />,
            onClick: () => {
                setOpen(true)
                setIsId(record.id)
                setUserFlow(record.userFlow.type)
            },
        },
        {
            label: 'Edit',
            icon: <EditIcon fontSize="small" />,
            onClick: () => {
               navigate("/console/connect-UI")
            },
        },
        {
            label: 'Delete',
            icon: <DeleteIcon fontSize="small" />,
            onClick: () => {
                setIsId(record.id)
                deleteDockProfile(record.id)
            },
            sx: { color: 'error.main' },
            danger: true.toString(),
        },
    ];

    const columns: any = useMemo(
        () => [
            {
                accessorKey: 'name',
                header: 'Name',
                cell({ row: { original: { name } } }: any) {
                    return name
                }
            },
            {
                accessorKey: 'id',
                header: 'Id',
                cell({ row: { original: { id } } }: any) {
                    return id
                }
            },
            {
                accessorKey: 'frontendUrl',
                header: 'Custom Domain',
                cell({ row: { original: { frontendUrl } } }: any) {
                    return frontendUrl
                }
            },
            {
                accessorKey: 'pageLayout',
                header: ' Layout Type',
                cell({ row: { original: { pageLayout } } }: any) {
                    return updatedValues[pageLayout] || pageLayout
                }

            },
            {
                accessorKey: 'action',
                header: 'Actions',
                cell({ row: { original } }: any) {
                    return <ActionsColumn record={original} getItems={getItems} />
                }
            },
        ],
        [],
    );

    const {
        paginationModal: { pagination }
    } = useTable();

    const getProviderOptions = useCallback(() => {
        if (!availableProviders.length) return [];

        return availableProviders.map((service: any) => ({
            value: service.id,
            label: service.name,
            serviceProfile: service.serviceProfile
        }));
    }, [availableProviders]);

    const getService = async () => {
        const values = form.getValues();

        // Only proceed if there are types selected
        if (!values.type || values.type.length === 0) return;

        const newPayload = {
            filter: {
                and: [
                    {
                        property: "/state",
                        operator: "=",
                        values: [
                            State.ACTIVE
                        ],
                    },
                    {
                        property: "/organization/id",
                        operator: "=",
                        values: [
                            user?.organization?.id
                        ],
                    },
                    {
                        property: "/type",
                        operator: "=",
                        values: [values.type],
                    },
                ]
            },
            paginationService: {
                limit: 33,
                offset: 0
            }
        };
        try {
            const response = await serviceProfileClient.searchServices(newPayload);
            setAvailableProviders(response.data.data || []);
        } catch (error) {
            console.log(error)
        }

    };

    useEffect(() => {
        provider && getService();
    }, [form.watch("type")]);

    useEffect(() => {
        if (data?.data) {
            setDockProfiles(data.data);
        }
    }, [data]);

    return (
        <Stack spacing={0}>
             <Stack spacing={2} marginBottom={'1rem'}>
    {!isConfigsLoading && !hasPlatformWatchHook && (
      <Alert severity="warning" sx={{textAlign:'justify'}}>
          {WARN_MESSAGE} 
      </Alert>
    )}
    </Stack>
            <MainCard>
                <Grid container justifyContent="space-between">
                    <Grid item xs={12} xl={8} lg={8} md={8} >
                        <Stack spacing={1}>
                            <Typography variant="h6">{TITLE}</Typography>
                        </Stack>
           
                    </Grid>
                    <Grid item xs={12} xl={4} lg={4} md={4} display='flex' justifyContent='flex-end'>
                        <Stack display='flex' flexDirection='row' gap='1rem' alignItems='flex-end'>
                            <Stack>
                                <Button
                                    variant="contained"
                                    onClick={() => navigate("/console/connect-UI")}
                                >
                                    New Connect UI
                                </Button>
                            </Stack>
                        </Stack>
                    </Grid>
                </Grid>
            </MainCard>
            <Stack gap={2} marginTop={'-4px'}>
                <Table
                    data={dockProfiles.filter((item: any) => item.state === "ACTIVE")}
                    columns={columns}
                    totalData={pagination?.total}
                    {...{
                        onPaginationChange: setPaginationState,
                        state: {
                            pagination: {
                                pageIndex: paginationState.pageIndex,
                                pageSize: paginationState?.pageSize,
                            }
                        } as any,
                    }}
                />
            </Stack>
            <Drawer anchor="right" open={open} onClose={(event, reason) => {
                if (reason === "backdropClick") return;
                closeDrawer();
            }}>
                <Box sx={{ flex: 1, overflowY: 'auto' }}>
                    <Stack p={3} width={400} spacing={2}>
                        <Grid container justifyContent="space-between" alignItems="center">
                            <Typography variant="h6" fontSize={20} fontWeight={600}>Test Run</Typography>
                            <IconButton onClick={() => { closeDrawer(); setLinkVisible(false); }}>
                                <CloseIcon />
                            </IconButton>
                        </Grid>
                        <Divider />
                        <Stack>
                            <Typography>
                                The Generated URL is for testing purposes only. Use the Service Key API to integrate it into your product. <LearnMoreLink href="https://docs.unizo.ai/docs/api/platform-reference/create-service-key/" target="_blank" rel="noopener noreferrer">
                                    Learn more
                                </LearnMoreLink>
                            </Typography>

                        </Stack>
                        <Form {...form}>
                            <FormField
                                control={form.control}
                                name='customerKey'
                                render={({ field }) => (
                                    <FormItem label='Customer Key' description="This key must be unique for each customer and is required." >
                                        <FormControl>
                                            <TextField placeholder="Enter your customer key" {...field} />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name='customerOrgName'
                                render={({ field }) => (
                                    <FormItem label='Customer Name' description="The name field is optional and displays the customer name in the integration section for better correlation." >
                                        <FormControl>
                                            <TextField placeholder="Enter your customer name" {...field} />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="type"
                                render={({ field }) => {
                                    const availableOptions = getOptions()?.filter(
                                        (option: any) => provider
                                            ? field.value !== option.value
                                            : !field.value?.includes(option.value)
                                    );

                                    return (
                                        <FormItem label="Category" description={provider ? "Select one category to define the integration scope for the customer." : "Select categories to define the integration scope for the customer."}>
                                            <FormControl>
                                                <InputLabel id="category-select-label">Select category</InputLabel>
                                                <Select
                                                    labelId="category-select-label"
                                                    multiple={!provider} // Single select when provider is true
                                                    {...field}
                                                    value={provider
                                                        ? (field.value || '') // Single value when provider is true
                                                        : (Array.isArray(field.value) ? field.value : []) // Array when provider is false
                                                    }
                                                    open={selectOpen}
                                                    onOpen={() => setSelectOpen(true)}
                                                    onClose={() => setSelectOpen(false)}
                                                    onChange={e => {
                                                        if (provider) {
                                                            // Direct value for single select
                                                            field.onChange(e.target.value);
                                                        } else {
                                                            // Array value for multiple select
                                                            field.onChange(e.target.value);
                                                        }
                                                        setSelectOpen(false);
                                                    }}
                                                    // Display selected value in the dropdown for single select
                                                    renderValue={(selected) => {
                                                        if (provider) {
                                                            const option = getOptions().find((opt) => opt.value === selected);
                                                            return option ? nameChecker(option.label) : "";
                                                        }
                                                        return ""; // For multiple select, keep empty string
                                                    }}
                                                >
                                                    {availableOptions?.map((domain: any) => (
                                                        <MenuItem key={domain?.value} value={domain?.value}>
                                                            {nameChecker(domain?.label)}
                                                        </MenuItem>
                                                    ))}
                                                </Select>
                                            </FormControl>
                                            {/* Only show chips for multiple select */}
                                            {!provider && (
                                                <Box mt={1}>
                                                    {(field?.value ?? []).length > 0 && (
                                                        <Typography variant="body2">Selected Categories:</Typography>
                                                    )}
                                                    {field.value?.map((val: any) => {
                                                        const option = getOptions().find((opt) => opt.value === val);
                                                        return option ? (
                                                            <Chip
                                                                key={val}
                                                                label={nameChecker(option.label)}
                                                                onDelete={() => {
                                                                    const newValues = field.value?.filter((item) => item !== val) || [];
                                                                    form.setValue("type", newValues);
                                                                }}
                                                                style={{ margin: 4 }}
                                                            />
                                                        ) : null;
                                                    })}
                                                </Box>
                                            )}
                                        </FormItem>
                                    );
                                }}
                            />

                            {provider && (
                                <FormField
                                    control={form.control}
                                    name="provider"
                                    render={({ field }) => {
                                        const providerOptions = getProviderOptions();
                                        const availableOptions = providerOptions?.filter(
                                            (option: any) => field.value !== option.value
                                        );

                                        return (
                                            <FormItem label="Service" description="Select one service for the integration.">
                                                <FormControl>
                                                    <InputLabel id="provider-select-label">Select service</InputLabel>
                                                    <Select
                                                        labelId="provider-select-label"
                                                        // Not multiple for single select
                                                        {...field}
                                                        value={field.value || ''}
                                                        open={providerSelectOpen}
                                                        onOpen={() => setProviderSelectOpen(true)}
                                                        onClose={() => setProviderSelectOpen(false)}
                                                        onChange={e => {
                                                            field.onChange(e.target.value);
                                                            setProviderSelectOpen(false);
                                                        }}
                                                        // Display selected value in the dropdown
                                                        // renderValue={(selected) => {
                                                        //     const option = getProviderOptions().find((opt) => opt.value === selected);

                                                        //     return option ? option.label : "";
                                                        // }}
                                                        renderValue={(selected) => {
                                                            const option = getProviderOptions().find((opt) => opt.value === selected);
                                                            return option ? (
                                                                <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                                                                    <Stack width="20px" height='20px'>
                                                                        {loadImage(option?.serviceProfile,
                                                                            { size: 'xSmall' }
                                                                        )}
                                                                    </Stack>

                                                                    {option.label}
                                                                </div>
                                                            ) : (
                                                                ""
                                                            );
                                                        }}
                                                        disabled={!form.watch('type')}
                                                    >
                                                        {availableOptions?.length > 0 ? (
                                                            availableOptions?.map((providerItem: any) => (
                                                                <MenuItem key={providerItem?.value} value={providerItem?.value}>
                                                                    <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                                                                        <Stack width="20px" height='20px'>
                                                                            {loadImage(providerItem?.serviceProfile, { size: "xSmall" })}
                                                                        </Stack>

                                                                        {providerItem?.label}
                                                                    </div>
                                                                </MenuItem>
                                                            ))
                                                        ) : (
                                                            <MenuItem disabled>
                                                                <Stack textAlign='center' margin='auto'>
                                                                    No connectors enabled
                                                                </Stack>
                                                            </MenuItem>
                                                        )}
                                                    </Select>
                                                </FormControl>
                                            </FormItem>

                                        );
                                    }}
                                />
                            )}
                        </Form>

                    </Stack>
                </Box>
                <Box className=" w-full" p={3}>
                    <Button
                        type="submit"
                        variant="contained"
                        onClick={form.handleSubmit(CreateServiceKey)}
                    >
                        Generate URL
                    </Button>
                </Box>
            </Drawer>
            <UnizoDockModal opened={isOpen} onClosed={() => setIsOpen(false)} magicLink={magicLinkId} />
        </Stack>
    );
}

const ActionsColumn = ({ record, getItems }: any) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleClick = (event: any) => {
        event.stopPropagation();
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const items = getItems(record);

    return (
        <div>
            <IconButton onClick={handleClick} sx={({ palette }) => ({ color: palette?.common?.black })}>
                <EllipsisOutlined />
            </IconButton>
            <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                onClick={(e) => e.stopPropagation()}
            >
                {items.map((item: any, index: number) => (
                    <MenuItem
                        key={index}
                        {...item}
                        onClick={() => {
                            item.onClick();
                            handleClose();
                        }}
                    >
                        {item.icon && <Box mr={1} display="flex" alignItems="center">{item.icon}</Box>}
                        {item.label}
                    </MenuItem>
                ))}
            </Menu>
        </div>
    );
};