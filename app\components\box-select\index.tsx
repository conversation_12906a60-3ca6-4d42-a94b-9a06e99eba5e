import { ReactNode, useMemo, useState } from 'react';

import { Checkbox, Grid, GridProps, Radio, Stack, SxProps, Typography, useTheme } from '@mui/material';
import MainCard from 'components/MainCard';

import { getHighlightedStyles, behaviorStyles } from './helper.js'
import { DISABLED_STATE_STYLE } from 'sections/services/constant.js';

type ItemTypes = {
   title: string
   value: string | number
   description?: string
   disabled?: boolean
}

type Props = {
   isMulti?: boolean
   items?: ItemTypes[]
   disableBehaviorForUnselectItem?: boolean
   disableBehavior?: boolean

   selectedItems?: ItemTypes['value'][]
   onSelectItems?: (selected: ItemTypes['value'][], fullBody: { selected: ItemTypes['value'] }) => void

   renderItem?: (item: ItemTypes) => ReactNode
   renderActions?: (item: ItemTypes) => ReactNode
} & {
   gridItemProps?: GridProps
   disabled?: boolean
}

const sample = [

]

export default (props: Props) => {

   const {
      isMulti = false,
      items = sample,
      disableBehavior = false,
      selectedItems: selectedItemsProp,
      onSelectItems: onSelectItemsProp,
      disableBehaviorForUnselectItem = false,

      // gridItemProps
      gridItemProps = {},
      disabled = false,

      renderItem,
      renderActions
   } = props;

   const [localSelected, setLocalSelected] = useState<ItemTypes['value']>();

   const [localSelectedS, setLocalSelectedS] = useState<ItemTypes['value'][]>([]);

   const selectedItems = selectedItemsProp ?? localSelectedS;

   const onValueSelected = (e: ItemTypes) => {
      setLocalSelected(e?.value);
   }

   const onSelectedItems = (e: ItemTypes, fullBody: { selected: ItemTypes['value'] }) => {
      let newSelectedItems: Array<ItemTypes['value']> = selectedItems ?? [];

      if (newSelectedItems.includes(e?.value)) {
         newSelectedItems = newSelectedItems.filter((i) => i !== e?.value)
      } else {
         newSelectedItems = newSelectedItems.concat(e?.value)
      }

      setLocalSelectedS(newSelectedItems);
      if (typeof onSelectItemsProp === 'function') {
         onSelectItemsProp(newSelectedItems, fullBody)
      }

   }

   const sxMemo: SxProps = useMemo(() => {
      let defaultSx: SxProps = {};

      // disabled state
      if (disabled) {
         defaultSx = {
            ...defaultSx,
            pointerEvents: 'none'
         }
      }

      return defaultSx
   }, [disabled])

   return (
      <Grid
         container
         spacing={1}
         sx={sxMemo}
      >
         {
            items.map((item, index) => {
               let isSelected: boolean;

               if (isMulti) {
                  isSelected = selectedItems.includes(item?.value)
               } else {
                  isSelected = localSelected === item?.value;
               };

               return (
                  <Item
                     key={index}
                     item={item}
                     renderItem={renderItem}
                     renderActions={renderActions}
                     gridItemProps={{
                        onClick: () => {
                           if (!isMulti) {
                              onValueSelected(item);
                           } else {
                              onSelectedItems(item, { selected: item?.value });
                           }
                        },
                        ...gridItemProps
                     }}
                     disableBehaviorForUnselectItem={disableBehaviorForUnselectItem}
                     isSelected={isSelected}
                     disableBehavior={disableBehavior}
                     isMulti={isMulti}
                  />
               )
            })
         }
      </Grid >
   )
}

type ItemProps = {
   item: ItemTypes
   isSelected: boolean
} & Pick<Props, 'disableBehaviorForUnselectItem' | 'gridItemProps' | 'renderItem' | 'renderActions' | 'disableBehavior' | 'isMulti'>;

function Item(props: ItemProps) {

   const {
      item,
      isSelected,
      gridItemProps,
      disableBehaviorForUnselectItem,
      disableBehavior,
      isMulti,
      renderItem,
      renderActions
   } = props;

   const { palette }: any = useTheme();

   const sxMemo: SxProps = useMemo(() => {

      let defaultSx = {}

      // disabled state
      if (item?.disabled) {
         defaultSx = { ...defaultSx, ...DISABLED_STATE_STYLE }
      }

      if (disableBehaviorForUnselectItem && !isSelected) {
         defaultSx = { ...defaultSx, ...DISABLED_STATE_STYLE }
      }

      return defaultSx
   }, [item?.disabled, isSelected, disableBehaviorForUnselectItem]);

   const indicator = useMemo(() => {

      if (isMulti) {
         return (
            <Checkbox
               checked={isSelected}
               disabled={item?.disabled || disableBehaviorForUnselectItem}
            />
         )
      }

      return (
         <Radio
            checked={isSelected}
            disabled={item?.disabled || disableBehaviorForUnselectItem}
         />
      )
   }, [isMulti, isSelected, disableBehaviorForUnselectItem, item?.disabled])

   return (
      <Grid
         item
         sm={4}
         {...gridItemProps}
         sx={{ ...sxMemo, ...gridItemProps?.sx }}

      >
         <MainCard
            sx={{
               borderRadius: '5px',
               borderWidth: .5,
               transition: 'all 0.3s ease',
               userSelect: 'none',
               ...(disableBehavior ? {} : behaviorStyles(palette)),
               ...((isSelected ? getHighlightedStyles(palette) : {})),

            }}
         >
            {typeof renderItem === 'function' ? (
               <Stack justifyContent={'space-between'} alignItems={'center'} direction={'row'}>
                  <Stack justifyContent={'flex-start'} gap={1} direction={'row'} alignItems={'flex-start'}>
                     {indicator}
                     {renderItem(item)}
                  </Stack>
               </Stack>
            ) : (
               <Stack justifyContent={'space-between'} alignItems={'start'} direction={'row'}>
                  <Stack justifyContent={'flex-start'} gap={1} direction={'row'} alignItems={'flex-start'}>
                     {indicator}
                     <Stack gap={.75}>
                        <Stack direction={'row'} justifyContent={'space-between'} alignItems={'center'}>
                           <Typography variant='h5' >{item?.title}</Typography>
                           {typeof renderActions === 'function' && (
                              renderActions(item)
                           )}
                        </Stack>
                        {item?.description && (
                           <Typography
                              color={'secondary.600'}
                           >
                              {item?.description}
                           </Typography>
                        )}
                     </Stack>
                  </Stack>
               </Stack>
            )}

         </MainCard>
      </Grid >
   )
}