import React from 'react';
import {
  Box,
  Typography,
  useTheme,
} from '@mui/material';

interface TabContentHeaderProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  iconBgColor?: string;
  iconColor?: string;
}

export default function TabContentHeader({ 
  icon, 
  title, 
  description,
  iconBgColor,
  iconColor,
}: TabContentHeaderProps) {
  const theme = useTheme();
  
  const bgColor = iconBgColor || theme.palette.primary.main;
  const color = iconColor || theme.palette.common.white;

  return (
    <Box sx={{ mb: 3 }}>
      <Typography variant="h5" fontWeight={600} sx={{ mb: 0.5 }}>
        {title}
      </Typography>
      <Typography 
        variant="body1" 
        sx={{
          color: theme.palette.mode === 'light' 
            ? theme.palette.grey[600] 
            : theme.palette.text.secondary
        }}
      >
        {description}
      </Typography>
    </Box>
  );
}