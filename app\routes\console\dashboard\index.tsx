
import { type MetaFunction } from "@remix-run/node";
import { ClientOnly } from 'remix-utils/client-only';

import Dashboard from 'sections/dashboard-v2/index'
import { DashboardSkeleton } from 'components/skeletons';

export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
   ];
};


export default function APIlogs() {

   return (
      <ClientOnly fallback={<DashboardSkeleton />}>
         {() => {
            return (
               <Dashboard />
            )
         }}
      </ClientOnly>
   );
}
