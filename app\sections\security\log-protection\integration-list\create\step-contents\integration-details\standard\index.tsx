import { useEffect, useMemo, useRef } from "react";

// Custom hooks and utilities
import useLogProtection from "store/security/log-protection";

// Helper functions
import {
   getAccessPointContainer,
   getAccessPointByType,
   buildValidationSchema,
} from './helper';

// Types
import { AccessPointConfigType } from "hooks/api/use-accesspoint-config/useGetAccessPoint";

// MUI components
import { Stack } from "@mui/material";

// Custom components
import { Form, FormField, FormItem, FormControl } from "components/@extended/Form";

// React Hook Form
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

// Integration create provider
import { useIntegrationCreateProvider } from "../../../../../integration-create-provider";
import customFields from "./custom-fields";
import FieldMapper from "./field-mapper";
import useTestIntegration from "./useTestIntegration";

const TARGET_FIELDS: Array<string> = ['/apiKey']

export default () => {
   // Custom hooks
   const { values, setValues, move, mode } = useLogProtection();
   const { actionRefs, searchAccessPoint } = useIntegrationCreateProvider();


   const selectedService = values?.service;

   const isEditMode = useMemo(() => mode === 'edit', [mode])

   const { data = [] } = searchAccessPoint({
      serviceId: selectedService?.id,
   });

   const fieldTypeConfig: Array<Record<string, any>> = useMemo(() => {
      const values = getAccessPointContainer(
         getAccessPointByType(data, AccessPointConfigType.AppFlow),
      )
         ?.authorizationProcessConfig
         ?.stepConfigs
         ?.[0]
         ?.fieldTypeConfigs;

      return customFields.concat(values ?? []) ?? [];
   }, [data]);

   const form = useForm<any>({
      resolver: zodResolver(
         buildValidationSchema(fieldTypeConfig)
            .all as any
      ),
      mode: 'onChange'
   });

   const { reset } = form;

   const onSubmit = (updatedValues: Record<string, any>) => {
      setValues('integrationDetails', updatedValues);
      move('next');
   };

   const { fieldProps } = useTestIntegration({
      form,
      accessPoint: data?.[0] as any,
      targetFields: TARGET_FIELDS
   })

   useEffect(() => {
      reset(values?.integrationDetails)
   }, [values?.integrationDetails, isEditMode]);


   if (!data?.length) return null;

   return (
      <Stack gap={2}>
         <Form {...form}>
            <Stack
               ref={actionRefs?.integrationDetails}
               onSubmit={(...args) => (
                  void form.handleSubmit(onSubmit)(...args)
               )}
               component={'form'}
               gap={3}
            >
               {fieldTypeConfig.map((item: { [key: string]: any }, index) => {
                  return (
                     <FormField<any>
                        control={form.control}
                        key={index}
                        name={item?.property as any}
                        render={({ field }) => {
                           if (!field?.value) field['value'] = '';

                           if (TARGET_FIELDS.includes(field?.name)) {
                              field = { ...field, ...fieldProps } as any
                           };

                           return (
                              <FormItem
                                 label={item?.label as string}
                              >
                                 <FormControl>
                                    <FieldMapper
                                       externalFieldProperties={item}
                                       field={field}
                                    />
                                 </FormControl>
                              </FormItem>
                           );
                        }}
                     />
                  )
               })}
            </Stack>
         </Form>
      </Stack>
   )
}