import React, { useState } from 'react';
import {
  Box,
  Checkbox,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  TextField,
  InputAdornment,
  Button,
  Chip,
  Stack,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';

export interface MultiSelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface MultiSelectFilterProps {
  options: MultiSelectOption[];
  selectedValues: string[];
  onSelectionChange: (values: string[]) => void;
  title: string;
  searchable?: boolean;
  placeholder?: string;
  maxHeight?: number;
  showSelectAll?: boolean;
  showSelectedCount?: boolean;
  emptyMessage?: string;
}

export const MultiSelectFilter: React.FC<MultiSelectFilterProps> = ({
  options,
  selectedValues,
  onSelectionChange,
  title,
  searchable = true,
  placeholder = "Search options...",
  maxHeight = 300,
  showSelectAll = true,
  showSelectedCount = true,
  emptyMessage = "No options available",
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  // Filter options based on search term
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    option.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleToggle = (value: string) => {
    const currentIndex = selectedValues.indexOf(value);
    const newSelected = [...selectedValues];

    if (currentIndex === -1) {
      newSelected.push(value);
    } else {
      newSelected.splice(currentIndex, 1);
    }

    onSelectionChange(newSelected);
  };

  const handleSelectAll = () => {
    const allAvailableValues = filteredOptions
      .filter(option => !option.disabled)
      .map(option => option.value);
    
    if (selectedValues.length === allAvailableValues.length) {
      // Deselect all
      onSelectionChange([]);
    } else {
      // Select all available options
      const newSelected = [...new Set([...selectedValues, ...allAvailableValues])];
      onSelectionChange(newSelected);
    }
  };

  const handleClearAll = () => {
    onSelectionChange([]);
  };

  const allFilteredSelected = filteredOptions.length > 0 && 
    filteredOptions.filter(option => !option.disabled).every(option => 
      selectedValues.includes(option.value)
    );

  const someFilteredSelected = filteredOptions.some(option => 
    selectedValues.includes(option.value)
  );

  return (
    <Box sx={{ width: '100%' }}>
      {/* Search */}
      {searchable && (
        <TextField
          size="small"
          fullWidth
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" color="action" />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 1 }}
        />
      )}

      {/* Compact Action Row */}
      {showSelectAll && filteredOptions.length > 0 && (
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          mb: 0.5,
          px: 0.5
        }}>
          <Button
            size="small"
            variant="text"
            onClick={handleSelectAll}
            disabled={filteredOptions.filter(opt => !opt.disabled).length === 0}
            sx={{ fontSize: '0.75rem', minWidth: 'auto', p: 0.5 }}
          >
            {allFilteredSelected ? 'DESELECT ALL' : 'SELECT ALL'}
          </Button>
          {selectedValues.length > 0 && (
            <Typography variant="caption" color="primary.main" sx={{ fontWeight: 600 }}>
              {selectedValues.length} selected
            </Typography>
          )}
        </Box>
      )}

      {/* Compact Options List */}
      <Box sx={{ maxHeight: Math.min(maxHeight, 180), overflow: 'auto' }}>
        {filteredOptions.length === 0 ? (
          <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
            {searchTerm ? `No options found` : emptyMessage}
          </Typography>
        ) : (
          <List dense disablePadding sx={{ '& .MuiListItem-root': { py: 0.1 } }}>
            {filteredOptions.map((option) => {
              const isSelected = selectedValues.includes(option.value);
              return (
                <ListItem
                  key={option.value}
                  button
                  onClick={() => !option.disabled && handleToggle(option.value)}
                  disabled={option.disabled}
                  sx={{
                    minHeight: '28px',
                    borderRadius: 0.5,
                    px: 1,
                    '&:hover': {
                      backgroundColor: option.disabled ? 'transparent' : 'action.hover',
                    },
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 24 }}>
                    <Checkbox
                      edge="start"
                      checked={isSelected}
                      disabled={option.disabled}
                      size="small"
                      color="primary"
                      sx={{ p: 1}}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={option.label}
                    sx={{
                      margin: 0,
                      '& .MuiListItemText-primary': {
                        fontSize: '0.8rem',
                        lineHeight: 1.1,
                        color: option.disabled ? 'text.disabled' : 'text.primary',
                      },
                    }}
                  />
                </ListItem>
              );
            })}
          </List>
        )}
      </Box>
    </Box>
  );
};