import { ReactNode } from 'react';
import { styled } from '@mui/material/styles';
import { Card, CardContent, Box, Typography, Avatar, alpha, useTheme } from '@mui/material';
import { SvgIconComponent } from '@mui/icons-material';

interface ModernStatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: SvgIconComponent;
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StyledCard = styled(Card)<{ color: string }>(({ theme, color }) => ({
  position: 'relative',
  overflow: 'hidden',
  height: '100%',
  background: theme.palette.mode === 'dark'
    ? theme.palette.background.paper
    : '#ffffff',
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: 0,
  boxShadow: theme.palette.mode === 'dark'
    ? '0 1px 3px rgba(0, 0, 0, 0.2)'
    : '0 1px 3px rgba(0, 0, 0, 0.05)',
  transition: 'all 0.2s ease',
  
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 4px 12px rgba(0, 0, 0, 0.3)'
      : '0 4px 12px rgba(0, 0, 0, 0.08)',
    borderColor: theme.palette[color].main,
  },
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '4px',
    height: '100%',
    background: theme.palette[color].main,
    opacity: 0,
    transition: 'opacity 0.2s ease',
  },
  
  '&:hover::before': {
    opacity: 1,
  }
}));

const IconWrapper = styled(Box)(({ theme }) => ({
  position: 'relative',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  
  '& .stat-icon': {
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  }
}));

const BackgroundDecoration = styled(Box)<{ color: string }>(({ theme, color }) => ({
  display: 'none', // Removed for cleaner enterprise look
}));

export default function ModernStatCard({
  title,
  value,
  subtitle,
  icon: Icon,
  color = 'primary',
  trend
}: ModernStatCardProps) {
  const theme = useTheme();

  return (
    <StyledCard color={color}>
      <BackgroundDecoration className="background-decoration" color={color} />
      <CardContent sx={{ p: 3, position: 'relative', zIndex: 1 }}>
        <Box display="flex" alignItems="flex-start" justifyContent="space-between">
          <Box flex={1}>
            <Typography 
              variant="overline" 
              display="block"
              sx={{ 
                color: 'text.secondary',
                mb: 1,
              }}
            >
              {title}
            </Typography>
            
            <Typography 
              variant="h3" 
              sx={{ 
                mb: 0.5,
                color: theme.palette.text.primary,
              }}
            >
              {value}
            </Typography>
            
            {subtitle && (
              <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                {subtitle}
              </Typography>
            )}
            
            {trend && (
              <Box display="flex" alignItems="center" gap={0.5} mt={1}>
                <Typography
                  variant="body2"
                  component="span"
                  sx={{
                    color: trend.isPositive ? 'success.main' : 'error.main',
                    fontWeight: 'medium',
                  }}
                >
                  {trend.isPositive ? '+' : ''}{trend.value}%
                </Typography>
                <Typography variant="body2" component="span" sx={{ color: 'text.secondary' }}>
                  vs last period
                </Typography>
              </Box>
            )}
          </Box>
          
          <IconWrapper>
            <Box
              className="stat-icon"
              sx={{
                width: 48,
                height: 48,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: alpha(theme.palette[color].main, 0.1),
                color: theme.palette[color].main,
                borderRadius: 0,
                transition: 'all 0.2s ease',
              }}
            >
              <Icon sx={{ fontSize: '1.5rem' }} />
            </Box>
          </IconWrapper>
        </Box>
      </CardContent>
    </StyledCard>
  );
}