import { create } from 'zustand';
import { ServiceProfile } from 'types/service-profile';

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  completed: boolean;
  skipped?: boolean;
}

export interface WebhookData {
  category: string;
  url: string;
  isValid: boolean;
}

interface OnboardingState {
  // Flow control
  isOpen: boolean;
  currentStep: number;
  completedSteps: number;
  totalSteps: number;
  
  // Step data
  steps: OnboardingStep[];
  
  // User selections
  selectedCategories: string[];
  selectedServices: Partial<ServiceProfile>[];
  webhookConfigs: WebhookData[];
  invitedMembers: { email: string; role: string }[];
  
  // Actions
  openOnboarding: () => void;
  closeOnboarding: () => void;
  setCurrentStep: (step: number) => void;
  nextStep: () => void;
  previousStep: () => void;
  skipStep: () => void;
  completeStep: (stepId: string) => void;
  
  // Data setters
  setSelectedCategories: (categories: string[]) => void;
  setSelectedServices: (services: Partial<ServiceProfile>[]) => void;
  addSelectedService: (service: ServiceProfile) => void;
  setWebhookConfigs: (configs: WebhookData[]) => void;
  setInvitedMembers: (members: { email: string; role: string }[]) => void;
  
  // Progress calculation
  calculateProgress: () => number;
  resetOnboarding: () => void;
}

const initialSteps: OnboardingStep[] = [
  {
    id: 'categories',
    title: 'Select Categories',
    description: 'Choose the integration categories you want to set up',
    completed: false,
  },
  {
    id: 'connectors',
    title: 'Choose Connectors',
    description: 'Select specific connectors for each category',
    completed: false,
  },
  {
    id: 'webhooks',
    title: 'Configure Webhooks',
    description: 'Set up webhook endpoints for real-time updates',
    completed: false,
  },
  {
    id: 'team',
    title: 'Invite Team Members',
    description: 'Add team members to collaborate',
    completed: false,
  },
];

const useOnboardingStore = create<OnboardingState>()(
  (set, get) => ({
      // Initial state
      isOpen: false,
      currentStep: 0,
      completedSteps: 0,
      totalSteps: 4,
      steps: initialSteps,
      selectedCategories: [],
      selectedServices: [],
      webhookConfigs: [],
      invitedMembers: [],
      
      // Actions
      openOnboarding: () => {
        // Reset to first step and clear selections when opening
        get().resetOnboarding();
        set({ isOpen: true });
      },
      closeOnboarding: () => set({ isOpen: false }),
      
      setCurrentStep: (step) => set({ currentStep: step }),
      
      nextStep: () => {
        const { currentStep, totalSteps } = get();
        if (currentStep < totalSteps - 1) {
          set({ currentStep: currentStep + 1 });
        }
      },
      
      previousStep: () => {
        const { currentStep } = get();
        if (currentStep > 0) {
          set({ currentStep: currentStep - 1 });
        }
      },
      
      skipStep: () => {
        const { currentStep, steps } = get();
        const updatedSteps = [...steps];
        updatedSteps[currentStep].skipped = true;
        set({ steps: updatedSteps });
        get().nextStep();
      },
      
      completeStep: (stepId) => {
        const { steps } = get();
        const updatedSteps = steps.map(step => 
          step.id === stepId ? { ...step, completed: true } : step
        );
        const completedSteps = updatedSteps.filter(step => step.completed).length;
        set({ steps: updatedSteps, completedSteps });
      },
      
      // Data setters
      setSelectedCategories: (categories) => {
        set({ selectedCategories: categories });
        if (categories.length > 0) {
          get().completeStep('categories');
        }
      },
      
      setSelectedServices: (services) => {
        set({ selectedServices: services });
        if (services.length > 0) {
          get().completeStep('connectors');
        }
      },
      
      addSelectedService: (service) => {
        const { selectedServices } = get();
        const exists = selectedServices.find(s => s.id === service.id);
        
        if (exists) {
          set({ selectedServices: selectedServices.filter(s => s.id !== service.id) });
        } else {
          set({ selectedServices: [...selectedServices, service] });
        }
        
        // Update completion status
        const updatedServices = get().selectedServices;
        if (updatedServices.length > 0) {
          get().completeStep('connectors');
        }
      },
      
      setWebhookConfigs: (configs) => {
        set({ webhookConfigs: configs });
        
        // Check if any valid webhooks are configured
        const validWebhooks = configs.filter(wc => wc.url && wc.isValid);
        if (validWebhooks.length > 0) {
          get().completeStep('webhooks');
        }
      },
      
      setInvitedMembers: (members) => {
        set({ invitedMembers: members });
        if (members.length > 0) {
          get().completeStep('team');
        }
      },
      
      // Progress calculation
      calculateProgress: () => {
        const { completedSteps, totalSteps } = get();
        return Math.round((completedSteps / totalSteps) * 100);
      },
      
      resetOnboarding: () => {
        set({
          isOpen: false,
          currentStep: 0,
          completedSteps: 0,
          steps: initialSteps.map(step => ({ ...step, completed: false, skipped: false })),
          selectedCategories: [],
          selectedServices: [],
          webhookConfigs: [],
          invitedMembers: [],
        });
      },
    })
);

export default useOnboardingStore;