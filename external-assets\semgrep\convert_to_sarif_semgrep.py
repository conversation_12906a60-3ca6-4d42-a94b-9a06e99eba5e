import json
import sys

# Function to load the JSON report
def load_json_report(json_file):
    with open(json_file, 'r') as file:
        return json.load(file)

# Function to convert the JSON data into SARIF format
def generate_sarif(report_data, output_file, tool_name):
    sarif_template = {
        "version": "2.1.0",
        "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0.json",
        "runs": [
            {
                "tool": {
                    "driver": {
                        "name": tool_name,
                        "informationUri": "https://semgrep.dev/",
                        "version": "latest",
                        "rules": []
                    }
                },
                "results": []
            }
        ]
    }

    for result in report_data.get("results", []):
        rule_id = result.get("check_id")
        message = result.get("extra", {}).get("message", "No message provided")
        severity = result.get("extra", {}).get("severity", "none").lower()
        file_path = result.get("path")
        start_line = result.get("start", {}).get("line", 0)
        end_line = result.get("end", {}).get("line", 0)
        snippet = result.get("extra", {}).get("lines", "")
        metadata = result.get("extra", {}).get("metadata", {})

        # Add rule to SARIF
        rule = {
            "id": rule_id,
            "shortDescription": {"text": message},
            "properties": {"severity": severity}
        }
        sarif_template["runs"][0]["tool"]["driver"]["rules"].append(rule)

        # Add result to SARIF
        sarif_result = {
            "ruleId": rule_id,
            "message": {"text": message},
            "locations": [
                {
                    "physicalLocation": {
                        "artifactLocation": {"uri": file_path},
                        "region": {
                            "startLine": start_line,
                            "endLine": end_line,
                            "snippet": {"text": snippet}
                        }
                    }
                }
            ],
            "properties": {
                "metadata": metadata
            }
        }
        sarif_template["runs"][0]["results"].append(sarif_result)

    # Write SARIF output to file
    with open(output_file, 'w') as file:
        json.dump(sarif_template, file, indent=2)

# Main function to convert JSON to SARIF
def main():
    if len(sys.argv) != 4:
        print("Usage: python3 convert_to_sarif.py <input_json> <output_sarif> <tool_name>")
        sys.exit(1)

    input_json = sys.argv[1]
    output_sarif = sys.argv[2]
    tool_name = sys.argv[3]

    report_data = load_json_report(input_json)
    generate_sarif(report_data, output_sarif, tool_name)

if __name__ == "__main__":
    main()
