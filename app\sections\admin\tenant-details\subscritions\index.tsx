import { <PERSON><PERSON>, <PERSON><PERSON>, Typography } from "@mui/material"
import { Link, useParams } from "@remix-run/react";
import { useGetSuperAdmin } from "hooks/api/super-admin/useGetSuperAdmin";
import { useTable } from "hooks/table/useTable";
import { useDate } from "hooks/useDate";
import { useStatus } from "hooks/useStatus";
import _ from "lodash";
import { useMemo, useState } from "react";
import { Create } from "./create";
import Table from "components/@extended/Table";
import nameChecker from "constants/categoryMapper";


const HEADER_NAMES = {
   NAME: 'Name',
   TIER: 'Tier',
   STATUS: 'Status',
   START_DATE: 'Start Date',
   END_DATE: '	End Date',
}

export const Subscriptions = () => {

   const [open, setOpen] = useState<boolean>(false)
   const [paginationState, setPaginationState] = useState<any>({
      pageIndex: 0,
      pageSize: 10,
   })

   const { id } = useParams()
   const { loadDate } = useDate(),
      { renderStatus } = useStatus(),
      {
         paginationModal: { pagination, onPaginationChange, setTotal }
      } = useTable();

   const { getOrgSubscription } = useGetSuperAdmin(),
      { data: subscriptions } = getOrgSubscription({ orgId: id as string }),
      data = subscriptions?.data ?? []

   const onOpen = () => {
      setOpen(true)
   },
      onClose = () => {
         setOpen(false)
      };

   const columns: any = useMemo<any[]>(
      () => [
         {
            accessorKey: 'name',
            header: HEADER_NAMES.NAME,
            cell({ row: { original } }: any) {
               const { name, id } = original
               return (
                  <Link to={`subscription/${id}`}>
                     <Typography
                        component={'a'}
                        className="link"

                     >
                        {nameChecker(_.capitalize(name))}
                     </Typography>
                  </Link>
               )
            },
         },
         {
            accessorKey: 'charge.pricingTier.name', //normal accessorKey
            header: HEADER_NAMES.TIER,
         },
         {
            accessorKey: 'state',
            header: HEADER_NAMES.STATUS,
            cell({ row: { original: { state } } }: any) {
               return renderStatus(state)
            },
         },
         {
            accessorKey: 'startDate',
            header: HEADER_NAMES.START_DATE,
            accessorFn(originalRow: any) {
               return loadDate(originalRow?.startDate)
            },
         },
         {
            accessorKey: 'cancelledDate',
            header: HEADER_NAMES.END_DATE,
            accessorFn(originalRow: any) {
               return loadDate(originalRow?.cancelledDate)
            },
         },
      ],
      [],
   );

   return (
      <Stack gap={1}>
         <Stack direction={'row'} justifyContent={'space-between'}>
            <Typography variant='h5'></Typography>
            <Button onClick={onOpen} variant='contained'>Add Subscription</Button>
         </Stack>
         <Table
            data={data}
            columns={columns}
            totalData={pagination?.total}
            {...{
               onPaginationChange: setPaginationState,
               state: {
                  pagination: {
                     pageIndex: paginationState.pageIndex,
                     pageSize: paginationState?.pageSize,
                  }
               } as any
            }}
         />
         <Create
            open={open} onClose={onClose}
         />
      </Stack>
   )
}