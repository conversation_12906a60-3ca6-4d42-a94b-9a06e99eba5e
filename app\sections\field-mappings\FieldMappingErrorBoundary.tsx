import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Box, Typography, Button, Alert, Stack } from '@mui/material';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onReset?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class FieldMappingErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Field mapping error caught:', error, errorInfo);
    this.setState({ errorInfo });
    
    // Log error to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      // TODO: Send error to monitoring service
      toast.error('An unexpected error occurred. Please refresh and try again.');
    }
  }

  private handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
    this.props.onReset?.();
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 400,
            p: 3
          }}
        >
          <AlertCircle size={48} color="error" />
          
          <Typography variant="h6" fontWeight={600} sx={{ mt: 2, mb: 1 }}>
            Something went wrong
          </Typography>
          
          <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3, maxWidth: 400 }}>
            An error occurred while loading the field mapping interface. This has been logged and our team will investigate.
          </Typography>

          {process.env.NODE_ENV !== 'production' && this.state.error && (
            <Alert severity="error" sx={{ mb: 3, maxWidth: 600, width: '100%' }}>
              <Typography variant="caption" component="pre" sx={{ fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
                {this.state.error.toString()}
                {this.state.errorInfo && (
                  <>
                    {'\n\nComponent Stack:'}
                    {this.state.errorInfo.componentStack}
                  </>
                )}
              </Typography>
            </Alert>
          )}

          <Stack direction="row" spacing={2}>
            <Button
              variant="contained"
              startIcon={<RefreshCw size={16} />}
              onClick={this.handleReset}
            >
              Try Again
            </Button>
            
            <Button
              variant="outlined"
              onClick={() => window.location.reload()}
            >
              Refresh Page
            </Button>
          </Stack>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default FieldMappingErrorBoundary;