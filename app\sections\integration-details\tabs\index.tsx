import Tabs from 'components/@extended/tab';
import MainCard from 'components/MainCard';
import { Overview } from './overview';
import { RateLimit } from './rate-limit';
import { Logs } from './activity-logs';
import { HealthCheck } from './health-check';
import { useEffect, useState } from 'react';
import { useGetIntegrationDetails } from 'hooks/api/integration/useIntegration-details';
import useCustomBreadcrumbs from 'store/custom-breadcrumbs';


export const RootTabs = () => {

   const { integration } = useGetIntegrationDetails();

   const { update, reset } = useCustomBreadcrumbs();

   useEffect(() => {
      update({
         links: [
            { title: 'Integrations', to: '/console/integrations' },
            { title: integration?.name },
         ]
      });
      return () => {
         reset()
      }
   }, [integration?.name])

   return (
      <TabContainer />
   )
}

const TabContainer = () => {
   const [tabIndex, setTabIndex] = useState(0);

   return (
      <Tabs
         classNames={{
            tab: 'max-w-[120px]'
         }}

         onTabChange={(_e, selected) => {
            setTabIndex(selected)
         }}
         value={tabIndex}
         items={[
            {
               title: 'Overview',
               key: 0,
               children: (
                  <Overview />
               )
            },
            {
               title: 'Rate limit',
               key: 1,
               children: <RateLimit />
            },
            {
               title: 'Activity logs',
               key: 2,
               children: <Logs />
            },
            {
               title: 'Health checks',
               key: 3,
               children: <HealthCheck />
            },
         ]}
      />
   )
}