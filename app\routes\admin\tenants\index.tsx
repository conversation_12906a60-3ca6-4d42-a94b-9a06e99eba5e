import { type MetaFunction } from "@remix-run/node";

import { Box, Button, Stack, Typography } from "@mui/material";
import MainCard from "components/MainCard";
import { PlusOutlined } from "@ant-design/icons";
import { useNavigate, useSearchParams } from "@remix-run/react";
import Tabs from "components/@extended/tab";
import TenantList from "./list";
import Prospects from "./prospects";

export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
      { name: "description", content: "Welcome to Remix!" },
   ];
};

export default function Index() {

   const navigate = useNavigate();

   return (
      <Box>
         <Stack >
            <Stack>
               <Typography variant="h5">
                  Admin Console
               </Typography>
               <Typography>
                  Streamline tenant management with our intuitive and professional tools. This user-friendly interface allows for efficient management of tenant details, ensuring seamless administration activities.
               </Typography>
            </Stack>
            <Stack direction={'row'} justifyContent={'space-between'}>
               <Stack>
                  <div>

                  </div>
               </Stack>
               <Stack>
                  <Button
                     variant='contained'
                     startIcon={<PlusOutlined />}
                     onClick={() => (
                        void navigate('/admin/tenants/create')
                     )}
                  >
                     Create Tenant
                  </Button>
               </Stack>
            </Stack>
            <Tabs
               classNames={{
                  tab: 'max-w-[150px]'
               }}
               items={[
                  {
                     title: 'Tenants',
                     key: 0,
                     children: (
                        <TenantList />
                     )
                  },
                  {
                     title: 'Prospects',
                     key: 1,
                     children: <Prospects />
                  },
               ]}
            />
         </Stack>
      </Box>
   );
}
