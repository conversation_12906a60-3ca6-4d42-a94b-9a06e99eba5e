import { useEffect, useMemo, useState } from 'react';

// material-ui
import Grid from '@mui/material/Grid';

import Typography from '@mui/material/Typography';

import { PieChart } from '@mui/x-charts';

import Dot from 'components/@extended/Dot';
import { useDashboard } from 'hooks/api/dashboard/useDashboard';
import { useServiceProfile } from 'hooks/useServiceProfile';
import { List, ListItemButton, ListItemText, Stack } from '@mui/material';


// ==============================|| INVOICE - PIE CHART ||============================== //

type AdoptionTypes = {
   value: number, label: string, color: string
}

export default () => {

   const { getColor, getLabel, getTotalServiceProfileCount } = useServiceProfile();

   const { serviceStats } = useDashboard({ subsIds: [] })


   return (
      <List sx={{ p: 0, }}>
         {serviceStats?.map((item: Record<string, any>, index: number) => {
            const total = getTotalServiceProfileCount(item?.type);
            const percentageCount = Math.round((item?.stats?.totalCount / total) * 100);
            const percentage = `${!isNaN(percentageCount) ? percentageCount : 0}%`;
            return (
               <ListItemButton divider key={index}>
                  <Dot sx={{ bgcolor: getColor(item?.type), mr: 1 }}></Dot>
                  <ListItemText
                     primary={getLabel(item?.type)}
                     primaryTypographyProps={{ variant: 'subtitle1' }}
                     secondaryTypographyProps={{ variant: 'body1', color: 'text.secondary', sx: { display: 'inline' } }}
                  />

                  <Stack alignItems="flex-end">
                     <Stack direction={'row'} sx={{ display: 'flex', alignItems: 'center', gap: .4 }}>
                        <Typography variant="h4" color="primary" >
                           {item?.stats?.totalCount}
                        </Typography>
                        <Typography variant='h5' color="secondary.600" >
                           /{total}
                        </Typography>
                     </Stack>
                     <Typography variant='overline' color="secondary.main" mt={.5} >
                        {percentage}
                     </Typography>
                  </Stack>
               </ListItemButton>
            )
         })}
      </List>
   );
}
