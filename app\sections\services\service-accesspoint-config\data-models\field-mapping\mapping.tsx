import { useMemo } from "react"
import { Box } from "@mui/material"
import { styles } from "./styles"
import TableView from "./views/table.view"
import { useMappingContext } from "./contexts/mapping.context"

const Mappings = () => {
   const { viewMode } = useMappingContext()
   const renderer = useMemo(() => viewMode === 'table' ? <TableView /> : <p></p>, [viewMode])
   return (
      <Box
         sx={(theme) => styles.mappingsRoot({ theme })}
      >
         {renderer}
      </Box>
   )
}

export default Mappings;
