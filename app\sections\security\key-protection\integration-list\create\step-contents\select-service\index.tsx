import {
   <PERSON><PERSON>,
   <PERSON><PERSON><PERSON>,
   <PERSON>rid,
   <PERSON>rid<PERSON><PERSON>,
   Skeleton,
   Stack,
   Typography,
} from "@mui/material";

import MainCard from "components/MainCard";

import { useServiceProfile } from "hooks/useServiceProfile";
import useLogProtection from "store/security/log-protection";

// service types
import { Service } from "types/service";

// webhook options
import customOptions, { StaticOption } from "./custom-integration-options";
import { useEffect, useMemo, useRef, useState } from "react";
import { LogProtectionConnectorType } from "constants/log-protection";
import { useIntegrationCreateProvider } from "../../../../integration-create-provider";
import { EditOutlined, LoadingOutlined } from "@ant-design/icons";
import { getNestedValue } from "./helper";
import { getAccessPointByType, getAccessPointContainer } from "../integration-details/standard/helper";
import { AccessPointConfigType } from "hooks/api/use-accesspoint-config/useGetAccessPoint";


export default () => {

   const {
      searchKeyProtections,
      getIsWebhookConfigExist,
      getIsServiceHaveIntegration,
      searchAccessPoint,
      getIntegration,
      searchMonitoringProfiles,


      isWebhookExist,
      isIntegrationConfigExceed
   } = useIntegrationCreateProvider();

   // states
   const [selectedEditable, setSelectEditable] = useState<Service | null>(null);
   const [selectedIntegration, setSelectedIntegration] = useState<string | null>(null);

   const { data } = searchKeyProtections();

   const {
      data: monitoringProfile,
      isPending: isMonitoringProfileLoading
   } = searchMonitoringProfiles({ logProtectionId: data?.id as string });

   const { data: accessPoints, isLoading } = searchAccessPoint({
      serviceId: selectedEditable?.id as string,
   });

   const { data: accessPointValue = {}, } = getIntegration({
      logProtectionId: data?.id,
      id: selectedIntegration as string,
   });

   const { loadImage } = useServiceProfile();
   const { setValues, values, move, setMode, resetWindowStates } = useLogProtection();

   const selectedService = values?.service;

   const onSelect = (profile: Service | StaticOption) => {
      setValues("service", profile.id !== selectedService?.id ? profile : null as any);
      setMode("create");
      setSelectEditable(null)
   };

   /**
 * Handles the editing logic for a profile based on its type.
 * If the profile type is Webhook, set up integration details for editing and move to the next step.
 * Otherwise, handle service-specific logic to determine and update the integration and editable state.
 *
 * @param profile - The profile to edit, which can either be a Service or a StaticOption.
 */
   const onEdit = (profile: Service | StaticOption) => {

      // Check if the profile type is a Webhook
      if (profile.type === LogProtectionConnectorType.Webhook) {
         // Set integration details from the existing Webhook configuration
         setValues("integrationDetails", getIsWebhookConfigExist()?.webhookConfig);

         // Set mode to edit
         setMode("edit");

         // Move to the next step
         move('next');

         // Mark the profile as selected
         onSelect(profile);
      } else {
         // For non-webhook profiles, mark the profile as selected
         onSelect(profile);

         // Check if the service has an integration
         const config = getIsServiceHaveIntegration(profile?.id);

         if (config) {
            // Set the selected integration ID if a configuration exists
            setSelectedIntegration(config?.integration?.id);

            // Mark the profile as editable
            setSelectEditable(profile as Service);
         }
      }
   };


   const tileList = useMemo(() => {

      return monitoringProfile?.concat(customOptions).map((profile: Service, index: number) => {
         const isSelected = selectedService?.id === profile?.id;
         let sx: GridProps['sx'] = {};

         let configured = false;

         if (profile?.type !== LogProtectionConnectorType.Webhook) {
            configured = !!getIsServiceHaveIntegration(profile?.id);

            if (isIntegrationConfigExceed && !configured) {
               sx = { ...sx, opacity: .4, pointerEvents: 'none' }
            };

         } else {
            configured = isWebhookExist;
         }
         const isPending = isLoading && selectedEditable?.id === profile?.id;

         return (
            <Grid
               item
               key={index}
               xs={12}
               sx={sx}
               md={4}
            >
               <MainCard boxShadow>
                  <Stack gap={2} sx={{ textAlign: "center" }}>
                     <Stack alignItems={"center"}>
                        {loadImage(profile?.serviceProfile as any, {
                           size: "medium",
                        })}
                        <Typography variant="h5" mt={1}>
                           {profile?.name}
                        </Typography>
                     </Stack>
                     <Divider />
                     {configured ? (
                        <Button
                           startIcon={isPending ? <LoadingOutlined /> : <EditOutlined />}
                           onClick={() => onEdit(profile)}
                        >
                           {isPending ? 'Getting integration' : 'Edit'}
                        </Button>
                     ) : (
                        <Button
                           variant={isSelected ? "contained" : "text"}
                           className="font-semibold"
                           color={"primary"}
                           onClick={() => void onSelect(profile)}
                        >
                           {isSelected ? "Selected" : "Select"}
                        </Button>
                     )}
                  </Stack>
               </MainCard>
            </Grid>
         );
      });
   }, [monitoringProfile,
      customOptions,
      selectedService,
      data?.connectorTypeConfig,
      isLoading,
      selectedEditable,
      isIntegrationConfigExceed
   ]);

   useEffect(() => {
      setValues("service", null as any);
      setMode("create");
   }, []);

   useEffect(() => {
      if (accessPoints) {

         const fieldTypeConfig = getAccessPointContainer(
            getAccessPointByType(accessPoints, AccessPointConfigType.AppFlow), // Fetch access point by type
         )
            ?.authorizationProcessConfig // Navigate to authorization process config
            ?.stepConfigs?.[0]?.fieldTypeConfigs; // Get field type configs from the first step

         const tempObj: Record<string, any> = {
            '/name': accessPointValue?.name
         };

         if (fieldTypeConfig?.length) {
            fieldTypeConfig.forEach(({ property }: any) => {
               tempObj[property] = getNestedValue(property, accessPointValue?.target?.accessPoint);
            });
         }

         setValues('integrationDetails', tempObj);
         // Move to the next step in the flow
         move('next');
         // Set mode to 'edit'
         setMode("edit");
      } else {
         setValues('integrationDetails', null as any);
      }
   }, [accessPoints]);


   useEffect(() => {
      resetWindowStates()
   }, [])
   return (
      <Grid container spacing={4}>
         {isMonitoringProfileLoading ? (
            <Skeleton />
         ) : (
            tileList
         )}
      </Grid>
   );
};
