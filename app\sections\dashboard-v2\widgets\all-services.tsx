import { AppstoreAddOutlined } from "@ant-design/icons"

import { GadgetConfig } from "../layout/grid-type"
import useUserDetails from "store/user"
import { useGetOrganization } from "hooks/api/organization/useGetOrganization"
import ModernStatCard from "components/cards/statistics/ModernStatCard";

export default ({ gadget }: GadgetConfig) => {

   const { user } = useUserDetails()

   const { services } = useGetOrganization({ id: user?.organization?.id });

   return (
      <ModernStatCard
         title={gadget?.name || "Services"}
         value={services?.pagination?.total ?? 0}
         icon={AppstoreAddOutlined}
         color="success"
         subtitle="Active connectors"
      />
   )
}