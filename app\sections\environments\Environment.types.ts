import Environment from 'types/environment';

export interface EnvironmentCardProps {
  env: Environment.Root & { color: string };
  onEdit: (env: Environment.Root) => void;
  onDelete: (env: Environment.Root) => void;
  onStar: (env: Environment.Root) => void;
  isDefault: boolean;
}

export interface LaunchStyleEnvironmentCardProps {
  card: {
    key: string;
    name: string;
    description: string;
    colorPicker: string;
    icon: string;
    locked: boolean;
  };
  onClick: (key: string, name: string) => void;
}

export interface PlanInfoCardProps {
  highestTier: string;
  isLaunch: boolean;
  isEnterprise: boolean;
}