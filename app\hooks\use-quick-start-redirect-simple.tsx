import { useEffect } from 'react';
import { useNavigate } from '@remix-run/react';
import useUserDetails from 'store/user';
import { Routes } from 'constants/route';
/**
 * Simplified hook that redirects to quick-start based on subscription data
 * Similar to useOnboarding but for quick-start flow
 */
export const useQuickStartRedirectSimple = () => {
  const navigate = useNavigate();
  const { user, subscriptions, isLoading, isSubscriptionLoading } = useUserDetails();

  useEffect(() => {
    // Wait for all data to load
    if (isLoading || isSubscriptionLoading) {
      return;
    }

    // Check if user has completed onboarding
    const checkOnboardingStatus = () => {
      console.log('Checking quick-start status:', {
        hasUser: !!user,
        organizationId: user?.organization?.id,
        subscriptionCount: subscriptions?.length || 0,
        organizationState: user?.organization?.state
      });

      // If no subscriptions or organization data, redirect to quick-start
      if (user && (!subscriptions || subscriptions.length === 0)) {
        console.log('No subscriptions found - redirecting to quick-start');
        navigate(Routes.QuickStart);
        return;
      }

      // Check localStorage for quick-start completion (temporary solution)
      const quickStartCompleted = localStorage.getItem('quickStartCompleted');
      if (!quickStartCompleted && user) {
        console.log('Quick-start not completed - redirecting');
        navigate(Routes.QuickStart);
      }
    };

    // Small delay to ensure components are ready
    const timeoutId = setTimeout(checkOnboardingStatus, 500);
    
    return () => clearTimeout(timeoutId);
  }, [user, subscriptions, isLoading, isSubscriptionLoading, navigate]);

  return { isLoading: isLoading || isSubscriptionLoading };
};