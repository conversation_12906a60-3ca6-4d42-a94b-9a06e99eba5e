/* eslint-disable @typescript-eslint/no-explicit-any */
import { <PERSON><PERSON>, <PERSON>ack } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import Header from "../header";
import Wrapper from "../wrapper";
import { useTenantMetadata } from "../contexts/tenant-metadata.provider";
import useSelfRegistration from "store/self-signUp/self-signup";
import { useServiceProfile } from 'hooks/useServiceProfile';
import { tenantsClient } from "services/tenants.service";
import { API_ENDPOINTS } from "utils/api/api-endpoints";
import { toast } from "sonner";
import { parseError } from "lib/utils";

interface GetStartedProps {
   title: string
   subTitle: string
}

enum OnboardingType {
   SelfSignUp = 'SELF_SIGNUP'
}

enum PromotionInfoType {
   MetaData = 'METADATA'
}

enum ProviderType {
   ServiceProfile = 'SERVICE_PROFILE'
}

const GetStarted = ({ title, subTitle }: GetStartedProps) => {
   const { onClose, tenantMetadata } = useTenantMetadata();
   const { selectedMembers, selectedServices, webhookData,selectedCategories, reset } = useSelfRegistration();
   const { getAllDomains } = useServiceProfile();
   

   /**
    * Creating the tenant for OnboardingType.SelfSignUp
    */
   const { mutateAsync: createTenantMutation, isPending: isCreating } = useMutation({
      mutationFn: (payload: any) => {
         return tenantsClient.create(payload)
      },
      mutationKey: [API_ENDPOINTS.ORGANIZATION],
   });

   const onConfirm = () => {

      const members = selectedMembers?.reduce((acc: any, item: any) => {

         const { role, email } = item;

         const exist = acc?.find((i: any) => i?.role?.type === role);

         if (!exist) {
            const obj = {
               role: { type: role },
               emails: [email]
            }
            acc.push(obj)
            return acc;
         }

         return acc.map((i: any) => {
            if (i?.role?.type === role) {
               return {
                  ...i,
                  emails: [...(i?.emails || []), email] as any
               }
            } else {
               return i
            }
         })
      }, [])

      // const categories = selectedServices?.reduce((acc: any, cur: any) => {

      //    const { type, id } = cur;

      //    const exist = acc?.find((i: any) => i?.type === type);

      //    if (!exist) {
      //       acc.push({ type, providers: [{ type: ProviderType.ServiceProfile, id }] });
      //       return acc;
      //    }
      //    return acc.map((i: any) => {
      //       if (i?.type === type) {
      //          return { ...i, providers: [...(i?.providers || []), { type: ProviderType.ServiceProfile, id }] }
      //       } else {
      //          return i
      //       }
      //    })
      // }, [])

      const categories = selectedServices
         ?.filter((i) => selectedCategories.includes(i?.type as string))
         ?.reduce((acc: any, cur: any) => {

            const { type, id } = cur;

            const exist = acc?.find((i: any) => i?.type === type);

            if (!exist) {
               acc.push({ type, providers: [{ type: ProviderType.ServiceProfile, id }] });
               return acc;
            }
            return acc.map((i: any) => {
               if (i?.type === type) {
                  return { ...i, providers: [...(i?.providers || []), { type: ProviderType.ServiceProfile, id }] }
               } else {
                  return i
               }
            })
         }, [])
 

      const allDomains = getAllDomains();

      const configurations = {
         data: webhookData
            .filter(webhook => webhook.url && webhook.isValid) // Only include valid webhooks
            .map(webhook => {
               const domainConfig = allDomains.find(domain => domain.key === webhook.category);
               return {
                  type: domainConfig?.hook, // Use the hook from domain config
                  data: {
                     contentType: "application/json",
                     url: webhook.url,
                     securedSSLRequired: true
                  }
               };
            })
            .filter(config => config.type) // Filter out any undefined hooks
      };

      const payload = {
         type: OnboardingType.SelfSignUp,
         description: "Provides application security services to enterprises",
         address: {},
         region: {},
         categories,
         subscriptions: [],
         reasons: [],
         members,
         configurations,
         promotionInfo: {
            type: PromotionInfoType.MetaData,
            metadata: {
               id: tenantMetadata?.id
            }
         },
         notifications: []
      }

      toast.promise(createTenantMutation(payload), {
         loading: 'Creating tenant...',
         success: () => {
            onClose?.({}, 'backdropClick');
            reset();
            return 'Tenant created successfully';
         },
         error: (error: any) => {
            return parseError(error?.response?.data)?.message;
         },
      });
   }

   return (
      <Wrapper
         sx={{
            alignItems: 'center',
            justifyContent: 'center',
            margin: 'auto'
         }}
      >
         <Header subTitle={subTitle} title={title} />
         <Stack alignItems={'center'}>
            <Button
               variant="contained"
               disabled={isCreating}
               fullWidth={false}
               onClick={onConfirm}
            >
               Get Started
            </Button>
         </Stack>
      </Wrapper>
   )
}

export default GetStarted;
