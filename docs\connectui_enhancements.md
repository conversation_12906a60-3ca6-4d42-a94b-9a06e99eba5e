# Connect UI Enhancement Plan

## Current State Analysis

### Overview
The Connect UI feature allows customers to deliver an in-browser docking experience and create integrations without any frontend code. It currently supports creating configurations called "dock profiles" that can be embedded or shown as pop-ups in customer applications.

### File Structure

#### Route Files
- `/app/routes/console/advanced/index.tsx` - Main route handler for `/console/connect-UI`
- `/app/routes/console/advanced/unizoDock/index.tsx` - List page route
- `/app/routes/console/advanced/userflow.tsx` - Create/Edit page route

#### Component Files
- `/app/sections/advanced/unizoDock/index.tsx` - List page component
- `/app/sections/advanced/userflow.tsx` - Create/Edit form component
- `/app/sections/advanced/unizoDock/UnizoDockModal.tsx` - Test URL modal
- `/app/sections/advanced/unizoDock/updateDock.tsx` - Update dialog (unused)

#### API Integration
- `/app/hooks/api/dockProfiles/useDockProfile.tsx` - Custom React Query hooks
- `/app/services/dock.service.ts` - API service layer

#### Navigation
- `/app/menu-items/integration.tsx` - Menu configuration

### Current UI/UX Issues

1. **Inconsistent Naming**
   - URL path uses "connect-UI" but internally called "unizoDock"
   - Page title "Connect UI" vs folder name "unizoDock"

2. **Limited Visual Hierarchy**
   - Plain table without clear visual separation
   - No status indicators or visual cues for configuration states
   - Actions hidden behind ellipsis menu

3. **Suboptimal Information Architecture**
   - ID column shows raw UUID (not user-friendly)
   - Important actions buried in dropdown menus
   - No quick way to see configuration status

4. **Missing Features**
   - No bulk actions
   - No search/filter capabilities
   - No configuration preview
   - Limited configuration options

## Proposed Enhancements

### 1. Visual Design Improvements

#### List Page Redesign
```typescript
// New component structure for list page
<Box>
  {/* Enhanced Header */}
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Grid container justifyContent="space-between" alignItems="center">
        <Grid item>
          <Typography variant="h3" fontWeight={700}>
            Connect UI Configurations
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Deliver seamless integration experiences without writing frontend code
          </Typography>
        </Grid>
        <Grid item>
          <Stack direction="row" spacing={2}>
            <Button variant="outlined" startIcon={<FileText />}>
              Documentation
            </Button>
            <Button variant="contained" startIcon={<Plus />}>
              Create Configuration
            </Button>
          </Stack>
        </Grid>
      </Grid>
    </Grid>
  </Grid>

  {/* Status Cards */}
  <Grid container spacing={3} sx={{ mt: 2 }}>
    <Grid item xs={12} sm={6} md={3}>
      <StatCard title="Total Configurations" value={totalConfigs} />
    </Grid>
    <Grid item xs={12} sm={6} md={3}>
      <StatCard title="Active" value={activeConfigs} color="success" />
    </Grid>
    <Grid item xs={12} sm={6} md={3}>
      <StatCard title="Test Mode" value={testConfigs} color="warning" />
    </Grid>
    <Grid item xs={12} sm={6} md={3}>
      <StatCard title="Custom Domains" value={customDomainConfigs} />
    </Grid>
  </Grid>

  {/* Enhanced Configuration Cards */}
  <Grid container spacing={3} sx={{ mt: 2 }}>
    {configurations.map((config) => (
      <Grid item xs={12} key={config.id}>
        <ConfigurationCard config={config} />
      </Grid>
    ))}
  </Grid>
</Box>
```

#### Configuration Card Component
```typescript
const ConfigurationCard = ({ config }) => (
  <Card sx={{ p: 3 }}>
    <Grid container spacing={3} alignItems="center">
      <Grid item xs={12} md={6}>
        <Stack spacing={1}>
          <Stack direction="row" alignItems="center" spacing={2}>
            <Typography variant="h6" fontWeight={600}>
              {config.name}
            </Typography>
            <Chip 
              label={config.pageLayout} 
              size="small" 
              color={config.pageLayout === 'EMBEDDED' ? 'primary' : 'secondary'}
            />
            {config.state === 'ACTIVE' && (
              <Chip label="Active" size="small" color="success" />
            )}
          </Stack>
          <Typography variant="body2" color="text.secondary">
            ID: {config.displayId || config.id.substring(0, 8) + '...'}
          </Typography>
        </Stack>
      </Grid>
      
      <Grid item xs={12} md={4}>
        <Stack spacing={0.5}>
          <Typography variant="caption" color="text.secondary">
            Frontend URL
          </Typography>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
              {config.frontendUrl}
            </Typography>
            <IconButton size="small" onClick={() => copyToClipboard(config.frontendUrl)}>
              <Copy size={16} />
            </IconButton>
          </Stack>
        </Stack>
      </Grid>
      
      <Grid item xs={12} md={2}>
        <Stack direction="row" spacing={1} justifyContent="flex-end">
          <Tooltip title="Test Configuration">
            <IconButton onClick={() => handleTestRun(config)}>
              <Play size={18} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Edit Configuration">
            <IconButton onClick={() => handleEdit(config)}>
              <Edit2 size={18} />
            </IconButton>
          </Tooltip>
          <Tooltip title="View Analytics">
            <IconButton onClick={() => handleAnalytics(config)}>
              <BarChart3 size={18} />
            </IconButton>
          </Tooltip>
          <Tooltip title="More Actions">
            <IconButton onClick={(e) => handleMoreActions(e, config)}>
              <MoreVertical size={18} />
            </IconButton>
          </Tooltip>
        </Stack>
      </Grid>
    </Grid>
  </Card>
);
```

### 2. Enhanced Create/Edit Form

#### Improved Form Layout
```typescript
// Enhanced form with better organization
<Grid container spacing={3}>
  {/* Basic Information */}
  <Grid item xs={12}>
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Basic Information
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <TextField
            label="Configuration Name"
            helperText="A descriptive name for your configuration"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            label="Display ID"
            helperText="Optional: A short, memorable ID"
          />
        </Grid>
      </Grid>
    </Paper>
  </Grid>

  {/* Layout Configuration */}
  <Grid item xs={12}>
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Display Settings
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <ToggleButtonGroup value={layout} exclusive>
            <ToggleButton value="EMBEDDED">
              <Stack spacing={1}>
                <Layers size={24} />
                <Typography>Embedded</Typography>
                <Typography variant="caption">
                  Seamlessly integrated into your UI
                </Typography>
              </Stack>
            </ToggleButton>
            <ToggleButton value="POP_UP">
              <Stack spacing={1}>
                <ExternalLink size={24} />
                <Typography>Pop-up</Typography>
                <Typography variant="caption">
                  Opens in a modal window
                </Typography>
              </Stack>
            </ToggleButton>
          </ToggleButtonGroup>
        </Grid>
      </Grid>
    </Paper>
  </Grid>

  {/* Advanced Settings */}
  <Grid item xs={12}>
    <Accordion>
      <AccordionSummary expandIcon={<ChevronDown />}>
        <Typography variant="h6">Advanced Settings</Typography>
      </AccordionSummary>
      <AccordionDetails>
        {/* Custom domain, branding options, etc. */}
      </AccordionDetails>
    </Accordion>
  </Grid>
</Grid>
```

### 3. New Features

#### A. Configuration Templates
```typescript
interface ConfigurationTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  settings: DockProfileSettings;
  category: 'basic' | 'advanced' | 'enterprise';
}

// Template selection component
const TemplateSelector = ({ onSelect }) => (
  <Grid container spacing={3}>
    {templates.map((template) => (
      <Grid item xs={12} md={4} key={template.id}>
        <Card 
          sx={{ 
            p: 3, 
            cursor: 'pointer',
            '&:hover': { boxShadow: 4 }
          }}
          onClick={() => onSelect(template)}
        >
          <Stack spacing={2} alignItems="center">
            <Avatar sx={{ width: 56, height: 56 }}>
              {template.icon}
            </Avatar>
            <Typography variant="h6">{template.name}</Typography>
            <Typography variant="body2" color="text.secondary" align="center">
              {template.description}
            </Typography>
          </Stack>
        </Card>
      </Grid>
    ))}
  </Grid>
);
```

#### B. Live Preview
```typescript
const ConfigurationPreview = ({ config }) => (
  <Box sx={{ p: 3, border: 1, borderColor: 'divider', borderRadius: 2 }}>
    <Typography variant="h6" gutterBottom>
      Live Preview
    </Typography>
    <Box sx={{ mt: 2, minHeight: 400 }}>
      <iframe
        src={`${config.frontendUrl}/preview?config=${encodeConfig(config)}`}
        style={{ width: '100%', height: '100%', border: 'none' }}
      />
    </Box>
  </Box>
);
```

#### C. Analytics Dashboard
```typescript
const ConfigurationAnalytics = ({ configId }) => {
  const { data: analytics } = useGetAnalytics(configId);

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={3}>
        <MetricCard
          title="Total Integrations"
          value={analytics.totalIntegrations}
          trend="+12%"
        />
      </Grid>
      <Grid item xs={12} md={3}>
        <MetricCard
          title="Success Rate"
          value={`${analytics.successRate}%`}
          trend="+5%"
        />
      </Grid>
      <Grid item xs={12} md={3}>
        <MetricCard
          title="Avg. Setup Time"
          value={analytics.avgSetupTime}
          trend="-2m"
        />
      </Grid>
      <Grid item xs={12} md={3}>
        <MetricCard
          title="Active Users"
          value={analytics.activeUsers}
          trend="+8%"
        />
      </Grid>
    </Grid>
  );
};
```

### 4. Code Organization

#### A. File Structure Reorganization
```
/app/sections/connect-ui/
├── index.tsx                 # Main router
├── list/
│   ├── index.tsx            # List page
│   ├── ConfigurationCard.tsx
│   ├── ConfigurationFilters.tsx
│   └── BulkActions.tsx
├── create-edit/
│   ├── index.tsx            # Create/Edit page
│   ├── BasicInfoForm.tsx
│   ├── LayoutSettings.tsx
│   ├── AdvancedSettings.tsx
│   └── ConfigPreview.tsx
├── analytics/
│   ├── index.tsx            # Analytics page
│   └── MetricCards.tsx
├── components/
│   ├── TestRunDrawer.tsx
│   └── TemplateSelector.tsx
└── hooks/
    ├── useConfigurations.tsx
    └── useAnalytics.tsx
```

#### B. Type Definitions
```typescript
// /app/types/connect-ui.ts
export interface ConnectUIConfiguration {
  id: string;
  displayId?: string;
  name: string;
  description?: string;
  frontendUrl: string;
  customDomain?: string;
  pageLayout: 'EMBEDDED' | 'POP_UP';
  userFlow: {
    type: 'CATEGORY' | 'PROVIDER';
    categories?: string[];
  };
  branding?: {
    primaryColor?: string;
    logo?: string;
    favicon?: string;
  };
  features?: {
    multiSelect?: boolean;
    search?: boolean;
    categorization?: boolean;
  };
  state: 'DRAFT' | 'ACTIVE' | 'ARCHIVED';
  analytics?: ConfigurationAnalytics;
  createdAt: string;
  updatedAt: string;
}
```

### 5. Implementation Roadmap

#### Phase 1: Foundation (Week 1-2)
- [ ] Rename files and update imports (unizoDock → connect-ui)
- [ ] Create new file structure
- [ ] Update type definitions
- [ ] Implement new list page design with cards

#### Phase 2: Core Features (Week 3-4)
- [ ] Enhanced create/edit form
- [ ] Configuration templates
- [ ] Live preview functionality
- [ ] Improved test run experience

#### Phase 3: Advanced Features (Week 5-6)
- [ ] Analytics dashboard
- [ ] Bulk operations
- [ ] Advanced search and filters
- [ ] Custom branding options

#### Phase 4: Polish (Week 7)
- [ ] Performance optimizations
- [ ] Comprehensive testing
- [ ] Documentation updates
- [ ] Migration guide for existing configurations

### 6. API Enhancements

#### New Endpoints Needed
```typescript
// Analytics
GET /api/dock-profiles/:id/analytics
GET /api/dock-profiles/:id/usage

// Templates
GET /api/dock-profile-templates
POST /api/dock-profiles/from-template

// Bulk Operations
POST /api/dock-profiles/bulk-update
POST /api/dock-profiles/bulk-delete

// Preview
GET /api/dock-profiles/:id/preview-token
```

### 7. Migration Strategy

1. **Backward Compatibility**
   - Keep existing endpoints functional
   - Add deprecation notices
   - Provide migration utilities

2. **Data Migration**
   - Add new fields with defaults
   - Run migration scripts for existing data
   - Validate all configurations post-migration

3. **User Communication**
   - In-app notifications about new features
   - Migration guide documentation
   - Support during transition period

### 8. Success Metrics

- **User Adoption**: 80% of users using new features within 3 months
- **Configuration Time**: 50% reduction in average setup time
- **Success Rate**: 95% successful integration setups
- **User Satisfaction**: NPS score > 8

### 9. Technical Considerations

- **Performance**: Implement virtual scrolling for large lists
- **Security**: Add RBAC for configuration management
- **Scalability**: Design for 10x current usage
- **Accessibility**: WCAG 2.1 AA compliance

This enhancement plan transforms Connect UI from a basic configuration tool into a comprehensive integration management platform that provides better user experience, more features, and clearer insights into integration performance.