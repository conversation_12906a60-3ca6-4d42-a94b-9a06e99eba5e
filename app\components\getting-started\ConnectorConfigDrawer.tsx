import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Typo<PERSON>,
  IconButton,
  Tabs,
  Tab,
  TextField,
  Button,
  Stack,
  Avatar,
  useTheme,
  Divider,
} from '@mui/material';
import { X, Upload } from 'lucide-react';

interface ConnectorConfigDrawerProps {
  open: boolean;
  onClose: () => void;
  connector: {
    name: string;
    logo: string;
    type: string;
  };
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`connector-tabpanel-${index}`}
      aria-labelledby={`connector-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

export default function ConnectorConfigDrawer({
  open,
  onClose,
  connector,
}: ConnectorConfigDrawerProps) {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState({
    appName: '',
    appUrl: '',
    appId: '',
    clientId: '',
    pemFile: null as File | null,
    redirectUrl: '',
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleInputChange = (field: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [field]: event.target.value,
    });
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData({
        ...formData,
        pemFile: file,
      });
    }
  };

  const handleSubmit = () => {
    console.log('Form submitted:', formData);
    onClose();
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: 600,
          maxWidth: '90vw',
        },
      }}
    >
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box
          sx={{
            p: 3,
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Stack direction="row" alignItems="center" spacing={2}>
              <Avatar
                src={connector.logo}
                sx={{
                  width: 40,
                  height: 40,
                  backgroundColor: theme.palette.background.paper,
                }}
              >
                {connector.name.charAt(0)}
              </Avatar>
              <Typography variant="h6" fontWeight={600}>
                Configure {connector.name} App
              </Typography>
            </Stack>
            <IconButton onClick={onClose} size="small">
              <X size={20} />
            </IconButton>
          </Stack>
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Credentials" />
            <Tab label="Custom Mapping" />
          </Tabs>
        </Box>

        {/* Tab Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          <TabPanel value={tabValue} index={0}>
            {/* Credentials Tab */}
            <Box>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                {connector.name} App Details
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Provide essential details for your {connector.name} application
              </Typography>

              <Stack spacing={3}>
                <Box>
                  <Typography variant="body2" fontWeight={600} gutterBottom>
                    App Name
                  </Typography>
                  <Typography variant="caption" color="text.secondary" gutterBottom display="block">
                    Enter the name of your {connector.name} app.
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder="Example: {{organization_name | hyphenated}}_app"
                    value={formData.appName}
                    onChange={handleInputChange('appName')}
                    size="small"
                    sx={{ mt: 1 }}
                  />
                </Box>

                <Box>
                  <Typography variant="body2" fontWeight={600} gutterBottom>
                    App URL
                  </Typography>
                  <Typography variant="caption" color="text.secondary" gutterBottom display="block">
                    Enter the base URL where your {connector.name} App is hosted.
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder="Example: https://{{organization_name | hyphenated}}.example.com"
                    value={formData.appUrl}
                    onChange={handleInputChange('appUrl')}
                    size="small"
                    sx={{ mt: 1 }}
                  />
                </Box>

                <Box>
                  <Typography variant="body2" fontWeight={600} gutterBottom>
                    App ID
                  </Typography>
                  <Typography variant="caption" color="text.secondary" gutterBottom display="block">
                    Enter the App ID of your {connector.name} app.
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder="Example: 1110117"
                    value={formData.appId}
                    onChange={handleInputChange('appId')}
                    size="small"
                    sx={{ mt: 1 }}
                  />
                </Box>

                <Box>
                  <Typography variant="body2" fontWeight={600} gutterBottom>
                    Client ID
                  </Typography>
                  <Typography variant="caption" color="text.secondary" gutterBottom display="block">
                    Enter the Client ID of your {connector.name} app.
                  </Typography>
                  <TextField
                    fullWidth
                    placeholder="Example: 3f2d1e4c5b6a7b8c9d0e"
                    value={formData.clientId}
                    onChange={handleInputChange('clientId')}
                    size="small"
                    sx={{ mt: 1 }}
                  />
                </Box>

                <Box>
                  <Typography variant="body2" fontWeight={600} gutterBottom>
                    PEM file
                  </Typography>
                  <Typography variant="caption" color="text.secondary" gutterBottom display="block">
                    Upload the .pem file generated during the creation of your {connector.name} App.
                  </Typography>
                  <Button
                    variant="outlined"
                    component="label"
                    startIcon={<Upload size={18} />}
                    sx={{
                      mt: 1,
                      textTransform: 'none',
                      color: theme.palette.text.primary,
                      borderColor: theme.palette.divider,
                    }}
                  >
                    {formData.pemFile ? formData.pemFile.name : 'Upload File'}
                    <input
                      type="file"
                      hidden
                      accept=".pem"
                      onChange={handleFileUpload}
                    />
                  </Button>
                </Box>

                <Box>
                  <Typography variant="body2" fontWeight={600} gutterBottom>
                    Redirect URL
                  </Typography>
                  <TextField
                    fullWidth
                    value="https://unizohub.com/v1/webhook/jira"
                    disabled
                    size="small"
                    sx={{ mt: 1 }}
                  />
                </Box>
              </Stack>
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            {/* Custom Mapping Tab */}
            <Box>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Custom Field Mapping
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Configure custom field mappings for your {connector.name} integration
              </Typography>
              
              <Box
                sx={{
                  p: 4,
                  textAlign: 'center',
                  backgroundColor: theme.palette.grey[50],
                  borderRadius: 2,
                  border: `1px dashed ${theme.palette.divider}`,
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  Custom mapping configuration coming soon
                </Typography>
              </Box>
            </Box>
          </TabPanel>
        </Box>

        {/* Footer */}
        <Box
          sx={{
            p: 3,
            borderTop: `1px solid ${theme.palette.divider}`,
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 2,
          }}
        >
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleSubmit}>
            Submit
          </Button>
        </Box>
      </Box>
    </Drawer>
  );
}