import React, {
   useState,
   useEffect,
   useRef,
   useCallback,
   useLayoutEffect,
} from "react";

import "./style.css";
import { cardExtendedStyle } from "components/MainCard";
import { Card, CardContent, useTheme } from "@mui/material";
import { grey } from '@mui/material/colors';
import { ThemeMode } from "config";


export type SvgUpTrianglePropsType = {
   className?: string;
   leftOffset?: number;
};

const SvgUpTriangle = ({ className, leftOffset }: SvgUpTrianglePropsType) => {

   const theme: any = useTheme()
   const colors = theme?.palette?.colors;

   const detailedPanelColor = theme.palette.mode === ThemeMode.DARK ? colors?.grey[0] : colors?.grey[3];
   const detailedStrokePanelColor = theme.palette.mode === ThemeMode.DARK ? colors?.grey[2] : colors?.grey[16];


   return (
      <svg
         xmlns="http://www.w3.org/2000/svg"
         viewBox="0 0 36 21"
         width="36"
         height="21"
         className={className}
         style={{ marginLeft: (leftOffset || 0) + 20 }}
      >
         <path
            fill={detailedPanelColor}
            stroke={detailedStrokePanelColor}
            d="M1 21h34L18 2z"
            strokeDasharray="0 34 50"
            strokeWidth="1"
         />
      </svg>
   );
};

export const useInvalidateOnViewportResize = () => {
   const [, setChangeCount] = useState(0);

   const handleResize = useCallback(() => {
      setChangeCount((count) => count + 1);
   }, []);

   useEffect(() => {
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
   }, [handleResize]);
};

export type GridPropsType = {
   tiles: React.JSX.Element[] | any;
   selectedIndex: number | any;
   detailPanelContent?: React.JSX.Element | null | undefined | boolean;
};

const ResponsiveGridWithDetailPanel = ({
   tiles,
   selectedIndex,
   detailPanelContent,
}: GridPropsType) => {
   useInvalidateOnViewportResize();
   const containerRef = useRef<HTMLDivElement | null>(null);
   const [panel, setPanel] = useState<JSX.Element | null>(null)


   useLayoutEffect(() => {
      if (Number.isFinite(selectedIndex)) {
         const renderDetailPanel = () => {
            if (!containerRef.current) return null;
            if (!Number.isFinite(selectedIndex)) return null;


            const gridComputedStyle = window.getComputedStyle(containerRef?.current);

            const gridTemplateColumns =
               gridComputedStyle.gridTemplateColumns.split(" ");
            const gridColumnCount = gridTemplateColumns.length;
            const targetRowIndex = Math.floor(selectedIndex! / gridColumnCount) + 1;
            const colWidth =
               Number.parseFloat(gridTemplateColumns[0]) +
               Number.parseFloat(gridComputedStyle.columnGap);

            return (
               <div
                  className="detailsPanel"
                  key={"detailsPanel" + selectedIndex}
                  style={{ gridRow: targetRowIndex + 1 + " / " + (targetRowIndex + 2) }}
               >
                  <SvgUpTriangle
                     className="detailsPanel__triangle"
                     leftOffset={((selectedIndex || 0) % gridColumnCount) * colWidth}
                  />
                  <Card
                     key={"details:" + selectedIndex}
                     sx={(theme) => {
                        return {
                           ...cardExtendedStyle(theme),
                           borderColor: theme.palette.mode === ThemeMode.DARK ? 'grey.100' : 'grey.A800',
                           backgroundColor: theme.palette.mode === ThemeMode.DARK ? 'grey.0' : 'grey.200',
                        }
                     }}
                  >
                     <CardContent>
                        {detailPanelContent}
                     </CardContent>
                  </Card>
               </div>
            );
         };
         setPanel(renderDetailPanel())
      }
   }, [selectedIndex, containerRef.current, detailPanelContent])

   return (
      <div className="responsive-grid-with-detail-panel w-full" ref={containerRef}>
         {tiles}
         {panel}
      </div>
   );
};

type GridProps = {
   tiles?: Array<any>
   selectedIndex?: number
   detailedPanel?: React.JSX.Element | null | undefined | boolean
}

export const ResponsiveGrid = (
   {
      tiles,
      selectedIndex,
      detailedPanel: detailedPanelContent
   }: GridProps
) => {

   return (
      <ResponsiveGridWithDetailPanel
         selectedIndex={selectedIndex}
         detailPanelContent={detailedPanelContent}
         tiles={tiles}
      />
   );
};
