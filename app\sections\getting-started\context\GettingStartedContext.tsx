import { TenantOnboardType } from 'constants/organization';
import React, { createContext, useContext, useState, ReactNode } from 'react';

interface SelectedService {
  serviceProfileId: string;
  category: string;
  name: string;
  logo?: string;
  configuration?: any;
}

interface GettingStartedContextType {
  onboardingType?: TenantOnboardType

  // Category Selection
  selectedCategories: string[];
  setSelectedCategories: (categories: string[]) => void;

  // Service Selection
  selectedServices: SelectedService[];
  setSelectedServices: (services: SelectedService[]) => void;
  addService: (service: SelectedService) => void;
  removeService: (serviceProfileId: string) => void;
  updateServiceConfig: (serviceProfileId: string, config: any) => void;

  // Overall state
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;

  // Predefined tenant configuration
  predefinedCategories?: string[];
  isPredefinedTenant: boolean;
  setPredefinedConfig: (categories: string[] | undefined, isPredefined: boolean) => void;
}

const GettingStartedContext = createContext<GettingStartedContextType | undefined>(undefined);

export const useGettingStarted = () => {
  const context = useContext(GettingStartedContext);
  if (!context) {
    throw new Error('useGettingStarted must be used within GettingStartedProvider');
  }
  return context;
};

interface GettingStartedProviderProps {
  children: ReactNode;
  value: Partial<GettingStartedContextType>
}

export const GettingStartedProvider: React.FC<GettingStartedProviderProps> = ({ children, value: valueProp }) => {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedServices, setSelectedServices] = useState<SelectedService[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [predefinedCategories, setPredefinedCategories] = useState<string[] | undefined>(undefined);
  const [isPredefinedTenant, setIsPredefinedTenant] = useState(false);

  const addService = (service: SelectedService) => {
    setSelectedServices(prev => [...prev, service]);
  };

  const removeService = (serviceProfileId: string) => {
    setSelectedServices(prev => prev.filter(s => s.serviceProfileId !== serviceProfileId));
  };

  const updateServiceConfig = (serviceProfileId: string, config: any) => {
    setSelectedServices(prev =>
      prev.map(s =>
        s.serviceProfileId === serviceProfileId
          ? { ...s, configuration: config }
          : s
      )
    );
  };

  const setPredefinedConfig = (categories: string[] | undefined, isPredefined: boolean) => {
    setPredefinedCategories(categories);
    setIsPredefinedTenant(isPredefined);
    if (categories && isPredefined) {
      setSelectedCategories(categories);
    } else if (!isPredefined) {
      // Reset to empty for trial mode
      setSelectedCategories([]);
    }
  };

  const value = {
    selectedCategories,
    setSelectedCategories,
    selectedServices,
    setSelectedServices,
    addService,
    removeService,
    updateServiceConfig,
    isLoading,
    setIsLoading,
    predefinedCategories,
    isPredefinedTenant,
    setPredefinedConfig,
    ...valueProp
  };

  return (
    <GettingStartedContext.Provider value={value}>
      {children}
    </GettingStartedContext.Provider>
  );
};