import { Box, Button, Stack } from "@mui/material";
import { Plus, PlusIcon } from "lucide-react";

import FieldMappingRow from "../field-mapping-row";

import { styles } from "../styles";

import { Fragment, useMemo } from "react";

import { useMappingContext } from "../contexts/mapping.context";
import { FieldMappingRowProps, HierarchyMappingValues, MappingValues } from "../type";

import { buildMenuOptions, getValidationError } from "../utils";
import ValidationError from "../components/validation-error";
import { ServiceProfileSpecificationFieldType } from "constants/service-profile";

const NestedMappingRow = ({ mappings, parentMapping, index }: { mappings: MappingValues[], index: number, parentMapping: MappingValues }) => {

   const { flattanedSourceFields, flattanedTargetFields, onAddMapping } = useMappingContext();

   const sourceOptions = useMemo(() => {
      const fieldSource = flattanedSourceFields?.find((i) =>
         i?.name === parentMapping?.source);

      if (fieldSource?.type === ServiceProfileSpecificationFieldType.Object) {
         return buildMenuOptions(flattanedSourceFields?.find((i) =>
            i?.name === parentMapping?.source)?.properties ?? [], 'specification')
      };

      return buildMenuOptions(flattanedSourceFields?.find((i) =>
         i?.name === parentMapping?.source)?.items?.properties ?? [], 'specification');
   }, [parentMapping, flattanedSourceFields])

   const targetOptions = useMemo(() => {
      return buildMenuOptions(flattanedTargetFields?.find((i) =>
         i?.key === parentMapping?.target)?.children ?? [], 'additional-attribute');
   }, [parentMapping, flattanedTargetFields])

   return (
      <Box
         sx={(theme) => styles.childFieldMappingRow({ theme, index })}
      >
         <Box sx={{ ml: 7, pr: 2, position: "relative" }}>
            <Box
               sx={(theme) => styles.hierarchyStraightLine({ theme })}
            />
            {mappings.map((mapping) => {
               const validationError = getValidationError(mapping, flattanedSourceFields, flattanedTargetFields);

               const meta = {
                  sourceValue: mapping?.source,
                  targetValue: mapping?.target,
                  excludeSourceValues: mappings?.map((i) => i?.source) ?? [],
                  hasError: !!validationError,
                  mapping,
                  canRemove: mappings?.length > 1,
                  showExpand: !!mapping?.children?.length,
               };

               return (
                  <Fragment key={mapping.id}>
                     <ChildMapItem
                        meta={meta}
                        sourceOptions={sourceOptions}
                        targetOptions={targetOptions}
                        mapping={mapping}
                        index={index}
                        validationError={validationError}
                     />
                  </Fragment>
               )
            })}

            {mappings?.length&& (
               <Button startIcon={<PlusIcon />} sx={{ mt: 1 }} onClick={() => onAddMapping(parentMapping)}>
                  Add Child Mapping
               </Button>
            )}
         </Box>
      </Box>
   )
}

const ChildMapItem = ({ validationError, meta, sourceOptions, targetOptions, mapping, index }:
   Pick<FieldMappingRowProps, 'meta' | 'sourceOptions' | 'targetOptions'> & {
      mapping: HierarchyMappingValues, index: number, validationError: string | null
   }) => {


   return (
      <>
         <Box
            sx={{ position: "relative", mb: 1 }}
         >
            <Box
               sx={(theme) => styles.hierarchyCurveLine({ theme })}
            />
            <Box sx={{ pl: 4 }}>
               <FieldMappingRow
                  meta={meta}
                  isChild
                  sourceOptions={sourceOptions}
                  targetOptions={targetOptions}
               />
               <ValidationError validationError={validationError} />
            </Box>
         </Box>
         {(mapping?.children?.length && !!mapping?.expanded) ?
            <NestedMappingRow mappings={mapping.children} parentMapping={mapping} index={index + 1} /> : null}
      </>
   )
}

const TableView = () => {
   const { onAddMapping, mappings, sourceFields, targetFields } = useMappingContext();

   const sourceOptions = useMemo(() =>
      buildMenuOptions(sourceFields, 'specification'), [sourceFields])
   const targetOptions = useMemo(() =>
      buildMenuOptions(targetFields, 'additional-attribute'), [targetFields])

   return (
      <Stack gap={3}>
         {mappings.map(mapping => {
            const validationError = getValidationError(mapping, sourceFields, targetFields);
            const meta = {
               sourceValue: mapping?.source,
               targetValue: mapping?.target,
               excludeSourceValues: mappings?.map((i) => i?.source) ?? [],
               hasError: !!validationError,
               mapping,
               canRemove: true,
               showExpand: !!mapping?.children?.length
            };

            return (
               <Box
                  key={mapping.id}
                  sx={(theme) => styles.mappingItemRoot({ theme })}
               >
                  <Box sx={() => ({ p: 2 })}>
                     <FieldMappingRow
                        meta={meta}
                        sourceOptions={sourceOptions}
                        targetOptions={targetOptions}
                     />
                     <ValidationError validationError={validationError} />
                  </Box>
                  {mapping?.children?.length && !!mapping?.expanded ? (
                     <NestedMappingRow mappings={mapping?.children} parentMapping={mapping} index={1} />
                  ) : null}
               </Box>
            )
         })}
         <Button
            startIcon={<Plus size={16} />}
            onClick={() => onAddMapping()}
            variant="outlined"
            sx={(theme) => styles.addMappingButtonRoot({ theme })}
         >
            Add mapping
         </Button>
      </Stack>
   )
}

export default TableView;