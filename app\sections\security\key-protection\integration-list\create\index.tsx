// Import Material-UI components and types
import {
   Button,
   ButtonProps,
   DialogActions,
   DialogContent,
   DialogProps,
   DialogTitle,
   Stack,
   Step,
   <PERSON><PERSON><PERSON><PERSON>,
   Stepper,
   Typography
} from "@mui/material";

// Import custom hooks and state management utilities
import useLogProtection from "store/security/log-protection";

// Import custom components and helpers
import { Dialog } from "components/@extended/dialog";
import mapper, { ComponentMap } from './component-mapper';
import IntegrationCreationCreateProvider, { useIntegrationCreateProvider } from '../../integration-create-provider';

import {
   getCurrentStepDetails,
   getPrimaryButtonText,
   getPrevBtnDisabled,
} from './helper';

import { useMemo } from "react";
import { Service } from "types/service";
import { LogProtectionConnectorType } from "constants/log-protection";
import { State } from "hooks/useStatus";

interface Props extends DialogProps {
   onConfirm: () => void;
}

const STEP_S = Object.values(mapper)

export default (props: Props) => {

   return (
      <IntegrationCreationCreateProvider value={{} as any}>
         <DialogBox {...props} />
      </IntegrationCreationCreateProvider>
   )
}

const DialogBox = (props: Props) => {

   const { open, onClose: onCloseProp, onConfirm } = props;
   const {
      step,
      values,
      windowStates,
      move,
   } = useLogProtection();

   const {
      actionRefs,
      updateWebhookConfig,
      createIntegrationConfig
   } = useIntegrationCreateProvider();

   // This logic checks the validity of the "Next" button based on the current step and form values.
   const isNextBtnValid: boolean = useMemo(() => {

      // For step 0, the "Next" button is valid if `values.service` exists (truthy).
      if (step === 0) {
         return !!values.service;
      } else if (step === 1) {
         return windowStates?.integrationDetails === State.ACTIVE;
      }

      // For other steps, the "Next" button is invalid by default.
      return true;

   }, [values, step, windowStates?.integrationDetails]);

   const onClose: ButtonProps['onClick'] = (e) => {
      if (typeof onCloseProp === 'function') {
         onCloseProp(e, 'escapeKeyDown')
      }
   }

   const currentContentDetails: ComponentMap | null = (
      useMemo(() =>
         getCurrentStepDetails(step),
         [step]
      )
   );

   const createIntegration = () => {

      createIntegrationConfig({
         selectedService: values?.service as Service,
         updated: values?.integrationDetails
      }, () => {

         onClose({} as any)
         onConfirm()
      })

   };

   const onNext: ButtonProps['onClick'] = () => {
      switch (step) {
         case 0:
            move('next');
            break;
         case 1:
            actionRefs?.integrationDetails?.current?.requestSubmit();
            break;
         case 2:
            createIntegration()
            break;
         default:
            console.warn('Unhandled step:', step);
      }
   };

   const onBack: ButtonProps['onClick'] = () => move('prev')

   return (
      <Dialog open={open} >
         <DialogTitle >

            {/* steps block */}
            <Stepper activeStep={step} className='py-3'>
               {STEP_S.map(({ label }) => (
                  <Step key={label}>
                     <StepLabel>{label}</StepLabel>
                  </Step>
               ))}
            </Stepper>

         </DialogTitle>

         <DialogContent dividers >
            <Stack gap={2} >

               {!currentContentDetails?.meta?.custom && (
                  <Stack gap={.5}>
                     <Typography variant='h4'>
                        {currentContentDetails?.label}
                     </Typography>
                     <Typography variant='body1' color={'secondary.600'}>
                        {currentContentDetails?.description}
                     </Typography>
                  </Stack>
               )}

               {mapper?.[step]?.component}
            </Stack>
         </DialogContent>

         <DialogActions
            sx={{
               justifyContent: 'space-between',
               px: 2.7
            }}
         >
            <Button color='error' onClick={onClose} variant="outlined" >Cancel</Button>
            <Stack direction={'row'} gap={1}>
               {!getPrevBtnDisabled(step) && (
                  <Button
                     onClick={onBack}
                  >
                     Back
                  </Button>
               )}

               <Button
                  variant="contained"
                  onClick={onNext}
                  disabled={!isNextBtnValid}
               >
                  {getPrimaryButtonText(step, STEP_S.length)}
               </Button>
            </Stack>
            {/* action block */}

         </DialogActions>
      </Dialog>
   )
}