
import { type MetaFunction } from "@remix-run/node";
import { ClientOnly } from 'remix-utils/client-only';

import EnvironmentsSection from 'sections/environments'
import { CardGridSkeleton } from 'components/skeletons';

export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
   ];
};


const Environments = () => {

   return (
      <ClientOnly fallback={<CardGridSkeleton count={4} columns={{ xs: 12, sm: 6, md: 4, lg: 3 }} showActions />}>
         {() => {
            return (
               <EnvironmentsSection />
            )
         }}
      </ClientOnly>
   );
}

export default Environments
