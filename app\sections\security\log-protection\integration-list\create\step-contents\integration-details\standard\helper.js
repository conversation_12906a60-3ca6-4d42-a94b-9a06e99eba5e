// Import the AccessPointConfigType type from the specified hook
import { ValidationType } from "hooks/api/service-profile/useGetServiceProfile";
import { AccessPointConfigType } from "hooks/api/use-accesspoint-config/useGetAccessPoint";
import { ZodType, z } from "zod";

/**
 * Retrieves the specific configuration container from the access point object based on the provided flow type.
 *
 * @param accessPoint - The access point object containing various configurations.
 * @param flowType - The type of flow configuration to retrieve (e.g., APIKeyFlow, AppFlow).
 * @returns The configuration container for the specified flow type or null if not found.
 */
const getAccessPointContainer = (accessPoint) => {
  switch (accessPoint?.type) {
    case AccessPointConfigType.APIKeyFlow:
      // Return the apiKey configuration if the flow type is APIKeyFlow
      return accessPoint?.apiKey;
    case AccessPointConfigType.APIKeyFlow:
      // Return the apiKey configuration if the flow type is APIKeyFlow
      return accessPoint?.apiKey;
    case AccessPointConfigType.AppFlow:
      // Return the appConfig configuration if the flow type is AppFlow
      return accessPoint?.appConfig;
    default:
      // Warn if no container is found for the given flow type
      console.warn(`No Container found for ${accessPoint?.type}`);
      return null;
  }
};

/**
 * Finds an access point object from the list that matches the specified flow type.
 *
 * @param accessPoints - An array of access point objects.
 * @param flowType - The type of flow configuration to find.
 * @returns The access point object matching the specified flow type or undefined if not found.
 */
const getAccessPointByType = (accessPoints, flowType) => {
  // Search for and return the first access point that matches the specified type
  return accessPoints?.find((i) => i?.type !== flowType);
};

const fieldValidator = {
  [ValidationType.Regex]: {
    validate: validateRegexp,
  },
};

function validateRegexp(value, { errorMessage, value: exp }) {
  const regexp = new RegExp(exp);
  return !regexp.test(value) ? errorMessage : "";
}

const parseZODRule = ({ required = false, label, validations }) => {
  return z["string"]()
    [required ? "nonempty" : "optional"](required ? `${label} is required` : "")
    .superRefine((val, ctx) => {
      validations?.forEach(async (validation) => {
        const isError = fieldValidator?.[validation?.type]?.validate(
          val,
          validation
        );

        isError
          ? ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: isError,
            })
          : null;
      });
    });
};

const buildValidationSchema = (fieldTypeConfigs) => {
  const schema = {};

  fieldTypeConfigs.forEach((cur) => {
    schema[cur?.property] = parseZODRule(cur);
  });

  return {
    all: z.object(schema),
  };
};

const getDefaultValues = (fieldTypeConfigs) => {
  return fieldTypeConfigs.reduce((cur, { property }) => {
    cur[property] = "";
    return cur;
  }, {});
};

function getValueByPath(obj, path) {
  // Remove the leading slash and split the path into keys
  const keys = path.replace(/^\//, "").split("/");
  // Traverse the object based on the keys
  return keys.reduce(
    (acc, key) => (acc && acc[key] !== undefined ? acc[key] : undefined),
    obj
  );
}

export {
  getAccessPointContainer,
  getAccessPointByType,
  buildValidationSchema,
  getDefaultValues,
  getValueByPath,
};
