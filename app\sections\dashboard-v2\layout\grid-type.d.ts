// Layout Position Interface
interface LayoutPosition {
   gridX: number;
   gridY: number;
   gridWidth: number;
   gridHeight: number;
   minWidth?: number;
   maxWidth?: number;
   minHeight?: number;
   maxHeight?: number;
   isFixedPosition: boolean;
}

// Data Source Interface
interface DataSource {
   href: string;
   type: string;
   id: string;
   name: string;
}

// Gadget Configuration Interface
interface GadgetConfig {
   gadget: {
      type: GadgetType;
      id?: string;
      name?: string;
      description?: string;
   };
   layoutPosition: {
      [key: string]: LayoutPosition
   };
   settings: {
      title?: string;
      description?: string;
      graphType?: string;
      heading?: string;
      dataSource:
      | DataSource
      | {
         type: string;
         details: {
            href: string;
         };
      };
      refreshInterval: string;
   };
}

// Tag Interface
interface Tag {
   [key: string]: string;
}

// Owner Interface
interface Owner {
   id: number;
   username: string;
   email: string;
   color: string;
   initials: string;
   profilePicture: string | null;
}

// Organization Interface
interface Organization {
   href: string;
   type: string;
   id: string;
   name: string;
   description: string;
}

// Change Log Interface
interface ChangeLog {
   createdBy: string;
   updatedBy: string;
   createdDateTime: string;
   updatedDateTime: string;
}

// Change Request Interface
interface ChangeRequest {
   type: string;
   description: string;
   status: string;
   createdDateTime: string;
   updatedDateTime: string;
}

// Grid Modal Interface
interface GridModal {
   href: string;
   type: string;
   id: number;
   name: string;
   description: string;
   state: string;
   layout: {
      Type: string;
   };
   gadgetConfigs: GadgetConfig[];
   tags: Tag[];
   owner: Owner;
   organization: Organization;
   changeLog: ChangeLog;
   changeRequest: ChangeRequest;
}

// Exporting all interfaces
export type {
   GridModal,
   ChangeRequest,
   ChangeLog,
   Organization,
   Owner,
   Tag,
   GadgetConfig,
   DataSource,
   LayoutPosition,
};
