import { ServiceProfileDataTypes } from "types/service-profile-datatypes";

export interface DataModelField {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  required: boolean;
  description: string;
}

export interface DataModel {
  id: string;
  name: string;
  description: string;
  fields: DataModelField[];
  customFields?: any[];
  fieldMappings?: any[];
  key?: string
  ref?: ServiceProfileDataTypes.Root['mappedTo']
}

const SCM_MODELS: DataModel[] = [
  {
    id: 'organization',
    name: 'Organization',
    description: 'Organization data model for managing company and team information',
    fields: [
      { id: 'org_id', name: 'Organization ID', type: 'string', required: true, description: 'Unique identifier for the organization' },
      { id: 'org_name', name: 'Organization Name', type: 'string', required: true, description: 'Display name of the organization' },
      { id: 'org_url', name: 'Organization URL', type: 'string', required: false, description: 'Website URL of the organization' },
      { id: 'created_at', name: 'Created Date', type: 'date', required: true, description: 'Date when the organization was created' },
      { id: 'member_count', name: 'Member Count', type: 'number', required: false, description: 'Total number of members' },
    ],
  },
  {
    id: 'repository',
    name: 'Repository',
    description: 'Repository data model for source code management',
    fields: [
      { id: 'repo_id', name: 'Repository ID', type: 'string', required: true, description: 'Unique identifier for the repository' },
      { id: 'repo_name', name: 'Repository Name', type: 'string', required: true, description: 'Name of the repository' },
      { id: 'description', name: 'Description', type: 'string', required: false, description: 'Repository description' },
      { id: 'private', name: 'Is Private', type: 'boolean', required: true, description: 'Whether the repository is private' },
      { id: 'default_branch', name: 'Default Branch', type: 'string', required: true, description: 'Default branch name' },
      { id: 'language', name: 'Primary Language', type: 'string', required: false, description: 'Primary programming language' },
      { id: 'size', name: 'Repository Size', type: 'number', required: false, description: 'Size of the repository in KB' },
    ],
  },
  {
    id: 'branch',
    name: 'Branch',
    description: 'Branch data model for version control',
    fields: [
      { id: 'branch_name', name: 'Branch Name', type: 'string', required: true, description: 'Name of the branch' },
      { id: 'commit_sha', name: 'Latest Commit SHA', type: 'string', required: true, description: 'SHA of the latest commit' },
      { id: 'protected', name: 'Is Protected', type: 'boolean', required: true, description: 'Whether the branch is protected' },
      { id: 'ahead_by', name: 'Commits Ahead', type: 'number', required: false, description: 'Number of commits ahead of base' },
      { id: 'behind_by', name: 'Commits Behind', type: 'number', required: false, description: 'Number of commits behind base' },
    ],
  },
  {
    id: 'commit',
    name: 'Commit',
    description: 'Commit data model for tracking code changes',
    fields: [
      { id: 'commit_sha', name: 'Commit SHA', type: 'string', required: true, description: 'Unique commit identifier' },
      { id: 'message', name: 'Commit Message', type: 'string', required: true, description: 'Commit message' },
      { id: 'author_name', name: 'Author Name', type: 'string', required: true, description: 'Name of the commit author' },
      { id: 'author_email', name: 'Author Email', type: 'string', required: true, description: 'Email of the commit author' },
      { id: 'committed_date', name: 'Commit Date', type: 'date', required: true, description: 'Date of the commit' },
      { id: 'files_changed', name: 'Files Changed', type: 'number', required: false, description: 'Number of files changed' },
      { id: 'additions', name: 'Lines Added', type: 'number', required: false, description: 'Number of lines added' },
      { id: 'deletions', name: 'Lines Deleted', type: 'number', required: false, description: 'Number of lines deleted' },
    ],
  },
  {
    id: 'pull_request',
    name: 'Pull Request',
    description: 'Pull request data model for code review and collaboration',
    fields: [
      { id: 'pr_number', name: 'PR Number', type: 'number', required: true, description: 'Pull request number' },
      { id: 'title', name: 'Title', type: 'string', required: true, description: 'Pull request title' },
      { id: 'description', name: 'Description', type: 'string', required: false, description: 'Pull request description' },
      { id: 'state', name: 'State', type: 'string', required: true, description: 'Current state (open, closed, merged)' },
      { id: 'source_branch', name: 'Source Branch', type: 'string', required: true, description: 'Branch to merge from' },
      { id: 'target_branch', name: 'Target Branch', type: 'string', required: true, description: 'Branch to merge into' },
      { id: 'created_by', name: 'Created By', type: 'string', required: true, description: 'User who created the PR' },
      { id: 'reviewers', name: 'Reviewers', type: 'array', required: false, description: 'List of reviewers' },
      { id: 'labels', name: 'Labels', type: 'array', required: false, description: 'Associated labels' },
    ],
  },
];

const CRM_MODELS: DataModel[] = [
  {
    id: 'contact',
    name: 'Contact',
    description: 'Contact data model for customer relationship management',
    fields: [
      { id: 'contact_id', name: 'Contact ID', type: 'string', required: true, description: 'Unique identifier' },
      { id: 'first_name', name: 'First Name', type: 'string', required: true, description: 'Contact first name' },
      { id: 'last_name', name: 'Last Name', type: 'string', required: true, description: 'Contact last name' },
      { id: 'email', name: 'Email', type: 'string', required: true, description: 'Primary email address' },
      { id: 'phone', name: 'Phone', type: 'string', required: false, description: 'Primary phone number' },
      { id: 'company', name: 'Company', type: 'string', required: false, description: 'Associated company' },
    ],
  },
  {
    id: 'lead',
    name: 'Lead',
    description: 'Lead data model for sales pipeline',
    fields: [
      { id: 'lead_id', name: 'Lead ID', type: 'string', required: true, description: 'Unique identifier' },
      { id: 'status', name: 'Status', type: 'string', required: true, description: 'Lead status' },
      { id: 'source', name: 'Source', type: 'string', required: false, description: 'Lead source' },
      { id: 'score', name: 'Score', type: 'number', required: false, description: 'Lead score' },
    ],
  },
];

const ECOMMERCE_MODELS: DataModel[] = [
  {
    id: 'product',
    name: 'Product',
    description: 'Product data model for e-commerce catalog',
    fields: [
      { id: 'product_id', name: 'Product ID', type: 'string', required: true, description: 'Unique identifier' },
      { id: 'name', name: 'Product Name', type: 'string', required: true, description: 'Product display name' },
      { id: 'price', name: 'Price', type: 'number', required: true, description: 'Product price' },
      { id: 'sku', name: 'SKU', type: 'string', required: true, description: 'Stock keeping unit' },
      { id: 'inventory', name: 'Inventory', type: 'number', required: false, description: 'Available quantity' },
    ],
  },
  {
    id: 'order',
    name: 'Order',
    description: 'Order data model for purchase transactions',
    fields: [
      { id: 'order_id', name: 'Order ID', type: 'string', required: true, description: 'Unique identifier' },
      { id: 'customer_id', name: 'Customer ID', type: 'string', required: true, description: 'Customer reference' },
      { id: 'total', name: 'Total Amount', type: 'number', required: true, description: 'Order total' },
      { id: 'status', name: 'Status', type: 'string', required: true, description: 'Order status' },
      { id: 'items', name: 'Order Items', type: 'array', required: true, description: 'List of items' },
    ],
  },
];

export function getDataModelsForService(serviceType: string): DataModel[] {
  switch (serviceType) {
    case 'SCM':
      return SCM_MODELS;
    case 'CRM':
      return CRM_MODELS;
    case 'ECOMMERCE':
      return ECOMMERCE_MODELS;
    default:
      return SCM_MODELS; // Default fallback
  }
}

// Mock provider fields for mapping with nested structures
export const PROVIDER_FIELDS: Record<string, any> = {
  github: {
    organization: [
      { id: 'id', name: 'id', type: 'number' },
      { id: 'login', name: 'login', type: 'string' },
      { id: 'name', name: 'name', type: 'string' },
      { id: 'blog', name: 'blog', type: 'string' },
      { id: 'created_at', name: 'created_at', type: 'string' },
      { id: 'public_repos', name: 'public_repos', type: 'number' },
      {
        id: 'owner',
        name: 'owner',
        type: 'object',
        children: [
          { id: 'owner.login', name: 'login', type: 'string' },
          { id: 'owner.id', name: 'id', type: 'number' },
          { id: 'owner.type', name: 'type', type: 'string' },
          { id: 'owner.site_admin', name: 'site_admin', type: 'boolean' },
        ]
      },
      {
        id: 'plan',
        name: 'plan',
        type: 'object',
        children: [
          { id: 'plan.name', name: 'name', type: 'string' },
          { id: 'plan.space', name: 'space', type: 'number' },
          { id: 'plan.private_repos', name: 'private_repos', type: 'number' },
        ]
      },
    ],
    repository: [
      { id: 'id', name: 'id', type: 'number' },
      { id: 'name', name: 'name', type: 'string' },
      { id: 'full_name', name: 'full_name', type: 'string' },
      { id: 'description', name: 'description', type: 'string' },
      { id: 'private', name: 'private', type: 'boolean' },
      { id: 'default_branch', name: 'default_branch', type: 'string' },
      { id: 'language', name: 'language', type: 'string' },
      { id: 'size', name: 'size', type: 'number' },
      {
        id: 'owner',
        name: 'owner',
        type: 'object',
        children: [
          { id: 'owner.login', name: 'login', type: 'string' },
          { id: 'owner.id', name: 'id', type: 'number' },
          { id: 'owner.avatar_url', name: 'avatar_url', type: 'string' },
          { id: 'owner.type', name: 'type', type: 'string' },
        ]
      },
      {
        id: 'permissions',
        name: 'permissions',
        type: 'object',
        children: [
          { id: 'permissions.admin', name: 'admin', type: 'boolean' },
          { id: 'permissions.push', name: 'push', type: 'boolean' },
          { id: 'permissions.pull', name: 'pull', type: 'boolean' },
        ]
      },
      {
        id: 'license',
        name: 'license',
        type: 'object',
        children: [
          { id: 'license.key', name: 'key', type: 'string' },
          { id: 'license.name', name: 'name', type: 'string' },
          { id: 'license.spdx_id', name: 'spdx_id', type: 'string' },
        ]
      },
    ],
    pull_request: [
      { id: 'number', name: 'number', type: 'number' },
      { id: 'title', name: 'title', type: 'string' },
      { id: 'body', name: 'body', type: 'string' },
      { id: 'state', name: 'state', type: 'string' },
      {
        id: 'user',
        name: 'user',
        type: 'object',
        children: [
          { id: 'user.login', name: 'login', type: 'string' },
          { id: 'user.id', name: 'id', type: 'number' },
          { id: 'user.avatar_url', name: 'avatar_url', type: 'string' },
        ]
      },
      {
        id: 'head',
        name: 'head',
        type: 'object',
        children: [
          { id: 'head.ref', name: 'ref', type: 'string' },
          { id: 'head.sha', name: 'sha', type: 'string' },
          { id: 'head.repo', name: 'repo', type: 'object' },
        ]
      },
      {
        id: 'base',
        name: 'base',
        type: 'object',
        children: [
          { id: 'base.ref', name: 'ref', type: 'string' },
          { id: 'base.sha', name: 'sha', type: 'string' },
          { id: 'base.repo', name: 'repo', type: 'object' },
        ]
      },
      { id: 'labels', name: 'labels', type: 'array' },
      { id: 'assignees', name: 'assignees', type: 'array' },
      { id: 'requested_reviewers', name: 'requested_reviewers', type: 'array' },
    ],
    commit: [
      { id: 'sha', name: 'sha', type: 'string' },
      { id: 'message', name: 'message', type: 'string' },
      {
        id: 'author',
        name: 'author',
        type: 'object',
        children: [
          { id: 'author.name', name: 'name', type: 'string' },
          { id: 'author.email', name: 'email', type: 'string' },
          { id: 'author.date', name: 'date', type: 'date' },
        ]
      },
      {
        id: 'committer',
        name: 'committer',
        type: 'object',
        children: [
          { id: 'committer.name', name: 'name', type: 'string' },
          { id: 'committer.email', name: 'email', type: 'string' },
          { id: 'committer.date', name: 'date', type: 'date' },
        ]
      },
      {
        id: 'tree',
        name: 'tree',
        type: 'object',
        children: [
          { id: 'tree.sha', name: 'sha', type: 'string' },
          { id: 'tree.url', name: 'url', type: 'string' },
        ]
      },
      { id: 'parents', name: 'parents', type: 'array' },
      {
        id: 'stats',
        name: 'stats',
        type: 'object',
        children: [
          { id: 'stats.total', name: 'total', type: 'number' },
          { id: 'stats.additions', name: 'additions', type: 'number' },
          { id: 'stats.deletions', name: 'deletions', type: 'number' },
        ]
      },
      { id: 'files', name: 'files', type: 'array' },
    ],
    branch: [
      { id: 'name', name: 'name', type: 'string' },
      {
        id: 'commit',
        name: 'commit',
        type: 'object',
        children: [
          { id: 'commit.sha', name: 'sha', type: 'string' },
          { id: 'commit.url', name: 'url', type: 'string' },
        ]
      },
      { id: 'protected', name: 'protected', type: 'boolean' },
      { id: 'protection', name: 'protection', type: 'object' },
      { id: 'protection_url', name: 'protection_url', type: 'string' },
    ],
  },
  gitlab: {
    organization: [
      { id: 'id', name: 'id', type: 'number' },
      { id: 'path', name: 'path', type: 'string' },
      { id: 'name', name: 'name', type: 'string' },
      { id: 'web_url', name: 'web_url', type: 'string' },
      { id: 'created_at', name: 'created_at', type: 'string' },
      { id: 'parent_id', name: 'parent_id', type: 'number' },
      {
        id: 'statistics',
        name: 'statistics',
        type: 'object',
        children: [
          { id: 'statistics.storage_size', name: 'storage_size', type: 'number' },
          { id: 'statistics.repository_size', name: 'repository_size', type: 'number' },
          { id: 'statistics.lfs_objects_size', name: 'lfs_objects_size', type: 'number' },
        ]
      },
    ],
    repository: [
      { id: 'id', name: 'id', type: 'number' },
      { id: 'name', name: 'name', type: 'string' },
      { id: 'path_with_namespace', name: 'path_with_namespace', type: 'string' },
      { id: 'description', name: 'description', type: 'string' },
      { id: 'visibility', name: 'visibility', type: 'string' },
      { id: 'default_branch', name: 'default_branch', type: 'string' },
      {
        id: 'namespace',
        name: 'namespace',
        type: 'object',
        children: [
          { id: 'namespace.id', name: 'id', type: 'number' },
          { id: 'namespace.name', name: 'name', type: 'string' },
          { id: 'namespace.path', name: 'path', type: 'string' },
          { id: 'namespace.kind', name: 'kind', type: 'string' },
        ]
      },
      {
        id: 'statistics',
        name: 'statistics',
        type: 'object',
        children: [
          { id: 'statistics.commit_count', name: 'commit_count', type: 'number' },
          { id: 'statistics.storage_size', name: 'storage_size', type: 'number' },
          { id: 'statistics.repository_size', name: 'repository_size', type: 'number' },
        ]
      },
      {
        id: 'permissions',
        name: 'permissions',
        type: 'object',
        children: [
          { id: 'permissions.project_access', name: 'project_access', type: 'object' },
          { id: 'permissions.group_access', name: 'group_access', type: 'object' },
        ]
      },
    ],
  },
};