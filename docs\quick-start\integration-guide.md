# Quick Start Integration Guide

## Overview

This guide explains how to integrate the Quick Start flow into your application's bootstrap process. The Quick Start can be integrated at various points in the user journey.

## Integration Points

### 1. Post-Signup Integration

Integrate Quick Start immediately after user registration:

```typescript
// In your signup flow
async function handleSignupComplete(user: User) {
  // Create user account
  const newUser = await createUser(user);
  
  // Set quick start flag
  await markUserNeedsQuickStart(newUser.id);
  
  // Redirect to quick start
  redirect('/quick-start');
}
```

### 2. First Login Integration

Show Quick Start on the user's first login:

```typescript
// In your login handler
export const loader: LoaderFunction = async ({ request }) => {
  const user = await authenticateUser(request);
  
  if (!user) {
    return redirect('/login');
  }
  
  // Check if user needs quick start
  const quickStartStatus = await checkQuickStartStatus(user.id);
  
  if (quickStartStatus.needsQuickStart) {
    return redirect('/console/quick-start');
  }
  
  return redirect('/console/dashboard');
};
```

### 3. Dashboard Integration

Show a prompt on the dashboard for incomplete Quick Start:

```tsx
function Dashboard() {
  const { data: quickStartStatus } = useQuickStartStatus();
  
  if (quickStartStatus?.needsQuickStart && !quickStartStatus.progress?.completed) {
    return (
      <DashboardLayout>
        <QuickStartBanner
          onStart={() => navigate('/console/quick-start')}
          onDismiss={() => dismissQuickStart()}
        />
        {/* Regular dashboard content */}
      </DashboardLayout>
    );
  }
  
  return <DashboardLayout>{/* Regular dashboard content */}</DashboardLayout>;
}
```

## Backend Integration

### Database Schema

Add these fields to track Quick Start status:

```sql
-- User or Tenant table extensions
ALTER TABLE users ADD COLUMN quick_start_completed BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN quick_start_skipped BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN quick_start_completed_at TIMESTAMP;

-- Quick Start progress table
CREATE TABLE quick_start_progress (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  tenant_id UUID REFERENCES tenants(id),
  current_step INTEGER DEFAULT 0,
  completed_steps JSONB DEFAULT '[]',
  selected_categories JSONB DEFAULT '[]',
  configured_services JSONB DEFAULT '[]',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### API Implementation

Implement the required API endpoints:

```typescript
// GET /api/quick-start/status
app.get('/api/quick-start/status', authenticate, async (req, res) => {
  const { userId, tenantId } = req.user;
  
  // Check if user has completed quick start
  const user = await getUserById(userId);
  const progress = await getQuickStartProgress(userId);
  const tenantConfig = await getTenantConfiguration(tenantId);
  
  const needsQuickStart = !user.quick_start_completed && !user.quick_start_skipped;
  
  res.json({
    needsQuickStart,
    progress,
    configuration: tenantConfig,
    settings: {
      showOnLogin: true,
      forceCompletion: tenantConfig.type !== 'trial',
      allowSkip: tenantConfig.skipAllowed,
    }
  });
});

// POST /api/quick-start/save
app.post('/api/quick-start/save', authenticate, async (req, res) => {
  const { userId } = req.user;
  const { step, data } = req.body;
  
  // Update progress
  const progress = await updateQuickStartProgress(userId, step, data);
  
  res.json({
    success: true,
    progress,
    nextStep: getNextStep(step),
  });
});
```

## Tenant Configuration

### Setting Up Predefined Configurations

Configure tenant types based on subscription plans:

```typescript
// In your subscription service
async function createSubscription(tenantId: string, plan: SubscriptionPlan) {
  const subscription = await createSubscriptionRecord(tenantId, plan);
  
  // Map subscription to quick start configuration
  const quickStartConfig = mapPlanToQuickStartConfig(plan);
  await setTenantQuickStartConfig(tenantId, quickStartConfig);
  
  return subscription;
}

function mapPlanToQuickStartConfig(plan: SubscriptionPlan): TenantConfiguration {
  switch (plan.type) {
    case 'enterprise':
      return {
        type: 'enterprise',
        predefinedCategories: ['SCM', 'TICKETING', 'PCR', 'INCIDENT'],
        isPredefined: true,
        allowCustomization: true,
        skipAllowed: false,
      };
    case 'basic':
      return {
        type: 'basic',
        predefinedCategories: ['SCM', 'TICKETING'],
        isPredefined: true,
        allowCustomization: true,
        skipAllowed: false,
      };
    default:
      return {
        type: 'trial',
        isPredefined: false,
        allowCustomization: true,
        skipAllowed: true,
      };
  }
}
```

### Dynamic Configuration

Allow admins to customize Quick Start for specific tenants:

```typescript
// Admin API to update tenant quick start config
app.put('/api/admin/tenants/:id/quick-start-config', adminAuth, async (req, res) => {
  const { id } = req.params;
  const config = req.body;
  
  await updateTenantQuickStartConfig(id, config);
  
  res.json({ success: true });
});
```

## Frontend Integration

### Route Protection

Ensure Quick Start completion before accessing certain features:

```typescript
// Route guard component
function RequireQuickStart({ children }: { children: ReactNode }) {
  const { data: status, isLoading } = useQuickStartStatus();
  const navigate = useNavigate();
  
  useEffect(() => {
    if (!isLoading && status?.needsQuickStart && status.settings.forceCompletion) {
      navigate('/console/quick-start');
    }
  }, [status, isLoading]);
  
  if (isLoading) return <LoadingSpinner />;
  
  return <>{children}</>;
}

// Usage
<Route
  path="/console/integrations"
  element={
    <RequireQuickStart>
      <IntegrationsPage />
    </RequireQuickStart>
  }
/>
```

### Context Provider Setup

Wrap your app with necessary providers:

```tsx
// App root
function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <Router>
            <Routes>
              {/* Your routes */}
            </Routes>
          </Router>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}
```

## Testing Integration

### Unit Tests

```typescript
describe('Quick Start Integration', () => {
  it('should redirect to quick start on first login', async () => {
    const user = await createTestUser({ isNew: true });
    const response = await loginUser(user);
    
    expect(response.redirectUrl).toBe('/console/quick-start');
  });
  
  it('should skip quick start for returning users', async () => {
    const user = await createTestUser({ 
      isNew: false,
      quickStartCompleted: true 
    });
    const response = await loginUser(user);
    
    expect(response.redirectUrl).toBe('/console/dashboard');
  });
});
```

### E2E Tests

```typescript
describe('Quick Start Flow E2E', () => {
  it('should complete full quick start flow', () => {
    cy.signup(testUser);
    cy.url().should('include', '/quick-start');
    
    // Step 1: Select categories
    cy.get('[data-testid="category-SCM"]').click();
    cy.get('[data-testid="category-TICKETING"]').click();
    cy.get('[data-testid="next-button"]').click();
    
    // Step 2: Configure services
    cy.get('[data-testid="service-jira"]').click();
    // ... configure service
    cy.get('[data-testid="next-button"]').click();
    
    // Step 3: Complete
    cy.get('[data-testid="complete-button"]').click();
    cy.url().should('include', '/console/dashboard');
  });
});
```

## Migration Guide

### For Existing Users

Handle existing users who haven't gone through Quick Start:

```typescript
// Migration script
async function migrateExistingUsers() {
  const users = await getExistingUsers();
  
  for (const user of users) {
    // Check if user has any integrations
    const hasIntegrations = await userHasIntegrations(user.id);
    
    if (hasIntegrations) {
      // Mark as completed for users with existing setup
      await markQuickStartCompleted(user.id);
    } else {
      // Flag for quick start on next login
      await markUserNeedsQuickStart(user.id);
    }
  }
}
```

### Feature Flag Rollout

Gradually roll out Quick Start:

```typescript
// Feature flag configuration
const quickStartFlags = {
  enabled: true,
  percentage: 100, // Percentage of users to show quick start
  excludeUserIds: [], // Users to exclude
  includeUserIds: [], // Users to always include
};

// Check if user should see quick start
function shouldShowQuickStartForUser(userId: string): boolean {
  if (!quickStartFlags.enabled) return false;
  if (quickStartFlags.excludeUserIds.includes(userId)) return false;
  if (quickStartFlags.includeUserIds.includes(userId)) return true;
  
  // Random percentage rollout
  const userHash = hashUserId(userId);
  return (userHash % 100) < quickStartFlags.percentage;
}
```

## Troubleshooting

### Common Integration Issues

1. **Quick Start Not Showing**
   - Verify API endpoints are accessible
   - Check user flags in database
   - Ensure proper authentication

2. **Progress Not Persisting**
   - Check API error logs
   - Verify database permissions
   - Check network requests in browser

3. **Redirect Loops**
   - Ensure proper completion marking
   - Check route guard logic
   - Verify API response format

### Debug Helpers

```typescript
// Add to browser console for debugging
window.debugQuickStart = {
  resetProgress: () => localStorage.removeItem('quickstart_progress'),
  forceShow: () => localStorage.setItem('quickstart_force', 'true'),
  checkStatus: async () => {
    const response = await fetch('/api/quick-start/status');
    console.log(await response.json());
  }
};