import { SketchOutlined } from "@ant-design/icons";

import ModernStatCard from 'components/cards/statistics/ModernStatCard'
import useUserDetails from "store/user"

import { GadgetConfig } from "../layout/grid-type"

export default ({ gadget }: GadgetConfig) => {

   const { subscriptions } = useUserDetails()

   return (
      <ModernStatCard
         title={gadget.name || "Subscriptions"}
         value={subscriptions?.length ?? 0}
         icon={SketchOutlined}
         color="primary"
         subtitle="Active plans"
      />
   )
}