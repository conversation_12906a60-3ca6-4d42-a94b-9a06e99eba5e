# known vulnerabilities which are not applicable to unizo business context


##############################################
## application dependencies vulnerabilities ##
##############################################
# CRITICAL level know vulnerabilities
CVE-2019-17571 # Remote code execution in Log4j, impacts deserialization of data.
CVE-2023-6378 # Denial of service vulnerability affecting Linux kernel's TCP processing.
CVE-2022-23305 # Log4j 1.x JDBCAppender vulnerable to SQL injection; upgrade to Log4j 2
CVE-2022-23307 # Chainsaw deserialization issue in Log4j 1.2.x; upgrade needed.
CVE-2022-0839  # XML External Entity issue in Liquibase < 4.8.0; upgrade required.
CVE-2023-20873 # Upgrade Spring Boot to 3.0.6+ or 2.7.11+ to secure.
CVE-2022-22965 # Spring MVC/WebFlux apps on Tomcat (WAR) may face RCE vulnerability.
CVE-2022-22978 # Spring Security versions before 5.4.11+, 5.5.7+, and 5.6.4+ may bypass authorization with RegexRequestMatcher
CVE-2016-1000027 # Spring Framework 5.3.16 may be vulnerable to RCE via untrusted data deserialization.

# HIGH level know vulnerabilities
CVE-2020-36518 # Jackson-databind < 2.13.0 is vulnerable to StackOverflow and DoS via deep nesting.
CVE-2021-46877 # Jackson-databind 2.10.x-2.12.x < 2.12.6 and 2.13.x < 2.13.1 allows DoS via JsonNode JDK serialization.
CVE-2022-42003 # Jackson-databind < 2.13.4.1 and < 2.12.17.1 can cause resource exhaustion with deep array nesting if UNWRAP_SINGLE_VALUE_ARRAYS is enabled.
CVE-2022-42004 # Jackson-databind < 2.13.4 can cause resource exhaustion with deep array nesting in certain deserialization configurations.
CVE-2022-23302 # Log4j 1.x JMSSink is vulnerable to RCE via deserialization if misconfigured or using LDAP. Upgrade to Log4j 2 for security.
CVE-2021-4104  # Log4j 1.2's JMSAppender can cause RCE via JNDI requests if misconfigured. Upgrade to Log4j 2 for improved security.
CVE-2023-1370  # Json-smart lacks limits on nested arrays/objects, leading to potential stack overflow.
CVE-2022-45143 # JsonErrorReportValve in Apache Tomcat (8.5.83, 9.0.40-68, 10.1.0-M1 to 10.1.1) lacks escaping, allowing manipulation of JSON output from user data.
CVE-2022-42252 # Tomcat (8.5.0-8.5.82, 9.0.0-M1-9.0.67, 10.0.0-M1-10.0.26, 10.1.0-M1-10.1.0) with `rejectIllegalHeader` set to false can be vulnerable to request smuggling via invalid Content-Length headers.
CVE-2023-46589 # Tomcat < 11.0.0-M11, < 10.1.16, < 9.0.83, < 8.5.96 has request smuggling risk via oversized HTTP trailer headers. Upgrade needed.
CVE-2023-24998 # Apache Commons FileUpload < 1.5 can cause DoS with unlimited request parts. Use `setFileCountMax` to limit uploads.
CVE-2023-20883 # Spring Boot versions 3.0.0-3.0.6, 2.7.0-2.7.11, 2.6.0-2.6.14, 2.5.0-2.5.14, and older are vulnerable to DoS with Spring MVC and reverse proxy cache.
CVE-2024-34750 # Apache Tomcat < 11.0.0-M21, < 10.1.25, < 9.0.90 has a resource consumption vulnerability due to incorrect handling of HTTP/2 headers. Upgrade to fix.
CVE-2024-22257 # Spring Security < 5.7.12, < 5.8.11, < 6.0.9, < 6.1.8, < 6.2.3 is vulnerable to broken access control with null Authentication in AuthenticatedVoter#vote.
CVE-2021-22119 # Spring Security < 5.5.1, < 5.4.7, < 5.3.10, < 5.2.11 is vulnerable to DoS via OAuth 2.0 Authorization Request, potentially exhausting resources.
CVE-2022-22968 # Spring Framework 5.3.0-18, 5.2.0-20, and older versions have case-sensitive disallowedFields patterns, requiring both cases for effective protection.
CVE-2022-22970 # Older Spring versions are vulnerable to DoS via file upload.
CVE-2023-20863 # Older Spring versions can be DoS vulnerable via crafted SpEL expressions.
CVE-2024-22259 # Applications using UriComponentsBuilder for URL parsing may be vulnerable to open redirect or SSRF attacks.
CVE-2024-22243 # Applications using UriComponentsBuilder to parse and validate URLs may be vulnerable to open redirect or SSRF attacks.
CVE-2023-34455 # In snappy-java versions before 1.1.10.1, unchecked chunk length can cause `NegativeArraySizeException` or `OutOfMemoryError`.
CVE-2024-22262 # Applications using UriComponentsBuilder for URL validation may face open redirect or SSRF vulnerabilities, like CVE-2024-22259 and CVE-2024-22243.
CVE-2022-25857 # `org.yaml:snakeyaml` (0 to before 1.31) is vulnerable to DoS due to unlimited nested depth.
CVE-2022-1471  # SnakeYaml's Constructor() allows unsafe deserialization. Use SafeConstructor or upgrade to 2.0+.
CVE-2023-43642 # All snappy-java versions up to 1.1.10.3 are vulnerable to DoS. Upgrade to 1.1.10.4 or use trusted data sources.
CVE-2024-38816 # Path traversal vulnerability in WebMvc.fn/WebFlux.fn with RouterFunctions and FileSystemResource.
CVE-2024-38816 # WebMvc.fn or WebFlux.fn apps vulnerable to path traversal attacks, mitigated by firewall or Tomcat/Jetty.

###########################################
## docker images related vulnerabilities ##
###########################################

# CRITICAL level know vulnerabilities

# HIGH level know vulnerabilities
CVE-2022-25647 # Gson before 2.8.9 vulnerable to DoS via writeReplace() method.
CVE-2021-22569 # Protobuf-java issue allows malicious payload causing parser delays.
CVE-2022-3509 # Parsing issue in protobuf-java versions before 3.21.7 causes DoS.
CVE-2022-3510 # Parsing issue in protobuf-java pre-3.21.7 can trigger DoS attacks.
CVE-2021-37136 # Bzip2 decompression lacks size limits, risking OOM and DoS attacks.
CVE-2021-37137 # Snappy frame decoder lacks chunk length limits, risking excessive memory usage.
GHSA-xpw8-rcwv-8f8p # Frequent RST frames cause DDOS; patch in 4.1.100.Final.