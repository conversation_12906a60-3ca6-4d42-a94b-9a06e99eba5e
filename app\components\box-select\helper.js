export const getHighlightedStyles = (palette) => {
   return {
      borderColor: palette.primary.main, // Change border color on hover
      background: palette.primary.lighter, // Change border color on hover
   }
}

export const getHoverStyles = (palette) => {
   return {
      borderColor: palette.primary[100], // Change border color on hover
      background: palette.primary.lighter, // Change border color on hover
   }
}

export const getActiveStyles = (palette) => {
   return {
      borderColor: palette.primary[200], // Change border color on hover
      background: palette.primary[100], // Change border color on hover
   }
}

export const getValuesInArr = (items) => {
   return items.map((item) => item?.value) ?? [];
}

export const behaviorStyles = (palette) => {
  return {
   '&:hover': {
      ...getHoverStyles(palette)
   },
   '&:active': {
      ...getActiveStyles(palette)
   },
  }
}