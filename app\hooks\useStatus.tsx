import { CheckCircleFilled, CheckCircleOutlined, CloseCircleFilled, CloseCircleOutlined, LoadingOutlined } from "@ant-design/icons";
import { AntdIconProps } from "@ant-design/icons/lib/components/AntdIcon";
import { Stack, Typography } from "@mui/material";
import Dot from 'components/@extended/Dot'

export enum State {
   ACTIVE = 'ACTIVE',
   IN_ACTIVE = 'IN_ACTIVE',

   // TODO:
   INACTIVE = 'INACTIVE',

   SUBMITTED = 'SUBMITTED',
   FAILED = 'FAILED',
   FAILURE = 'FAILURE',

   SUCCESS = 'SUCCESS',
   RUNNING = 'RUNNING',
   COMPLETED = 'COMPLETED',
   IN_PROGRESS = 'IN_PROGRESS',
   VERIFYING = 'VERIFYING',
   VERIFIED = 'VERIFIED',
   HEALTHY = 'HEALTHY',
   UN_HEALTHY = 'UNHEALTHY',
   PROVISIONED = "PROVISIONED",
   PROVISIONING = "PROVISIONING",
   DE_PROVISIONING = "DEPROVISIONING",
   DE_PROVISIONED = "DEPROVISIONED"
}

type Variant = 'badge' | 'text_with_badge' | 'icon';

type RenderStatusOptions = {
   variant?: Variant
   [key: string]: any
}

export const useStatus = () => {

   function resolveStatus(state: State) {

      switch (state) {
         case State.ACTIVE:
            return 'Active';
         case State.INACTIVE:
            return 'Inactive';
         case State.IN_ACTIVE:
            return 'Inactive';
         case State.SUCCESS:
            return 'Success';
         case State.FAILED:
            return 'Failed';
         case State.COMPLETED:
            return 'Completed';
         case State.IN_PROGRESS:
            return 'In Progress';
         case State.RUNNING:
            return 'Error';
         case State.VERIFIED:
            return 'Verified';
         case State.PROVISIONED:
            return 'Provisioned'
         case State.PROVISIONING:
            return 'Provisioning'
         case State.DE_PROVISIONING:
            return 'Deprovisioning'
         case State.DE_PROVISIONED:
            return 'Deprovisioned'
         default:
            if ([State.VERIFYING].includes(state)) {
               return 'Verifying'
            } else if ([State.HEALTHY].includes(state)) {
               return 'Healthy'
            } else if ([State.UN_HEALTHY].includes(state)) {
               return 'Unhealthy'
            } else {
               return ''
            }
      }
   };

   function resolveStatusColor(state: State) {

      switch (state) {
         case State.FAILED:
            return 'error';
         case State.COMPLETED:
            return 'success';
         case State.IN_PROGRESS:
            return 'warning';

         default:
            if ([State.VERIFYING, State.PROVISIONING].includes(state)) {
               return 'warning'
            } else if ([State.HEALTHY, State.VERIFIED, State.ACTIVE, State.SUCCESS, State.PROVISIONED].includes(state)) {
               return 'success'
            } else if ([State.RUNNING, State.UN_HEALTHY, State.INACTIVE, State.IN_ACTIVE, State.DE_PROVISIONED, State.DE_PROVISIONING].includes(state)) {
               return 'error'
            } else {
               return 'info'
            }
      }
   };

   function resolveIcon(state: State, options: AntdIconProps = {}) {
      if ([State.IN_PROGRESS].includes(state)) {
         return <LoadingOutlined className="text-orange-500" {...options} />
      } else if ([State.COMPLETED, State.HEALTHY, State.ACTIVE].includes(state)) {
         return <CheckCircleFilled className="text-green-500" {...options} />
      } else if ([State.FAILED, State.UN_HEALTHY, State.RUNNING].includes(state)) {
         return <CloseCircleFilled className="text-red-500" {...options} />
      } else {
         return ''
      }
   }

   function resolveOutlinedIcon(state: State, options: AntdIconProps = {}) {
      if ([State.IN_PROGRESS].includes(state)) {
         return <LoadingOutlined className="text-orange-500" {...options} />
      } else if ([State.COMPLETED, State.HEALTHY, State.ACTIVE].includes(state)) {
         return <CheckCircleOutlined className="text-green-500" {...options} />
      } else if ([State.FAILED, State.UN_HEALTHY, State.RUNNING, State.IN_ACTIVE, State.INACTIVE].includes(state)) {
         return <CloseCircleOutlined className="text-red-500" {...options} />
      } else {
         return ''
      }
   }

   return {
      renderStatus(state: State, options?: RenderStatusOptions) {

         const status = resolveStatus(state),
            variant = options?.variant ?? 'text_with_badge';
         delete options?.variant;
         if (variant === 'text_with_badge') {
            return (
               <Stack direction="row" spacing={1} alignItems="center" {...options} >
                  <Dot color={resolveStatusColor(state)} />
                  <Typography>
                     {status}
                  </Typography>
               </Stack >
            )
         }

         return status;
      },
      resolveStatusColor,
      resolveStatus,
      resolveIcon,
      resolveOutlinedIcon
   }
}