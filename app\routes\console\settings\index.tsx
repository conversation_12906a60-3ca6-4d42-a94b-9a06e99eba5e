import { type MetaFunction } from "@remix-run/node";
import Tabs from "components/@extended/tab";
import { ClientOnly } from 'remix-utils/client-only';
import { UserRoleEnum } from "hooks/api/permission/usePermission";
import { TabContentSkeleton } from 'components/skeletons';
import Users from './users';
import Subscriptions from './subscriptions';
import { useGetPermission } from "hooks/api/permission/usePermission";
import useUserDetails from "store/user";
export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
   ];
};

export default function Settings() {
   const { user } = useUserDetails();
   return (
      <ClientOnly fallback={<TabContentSkeleton tabCount={2} contentHeight={400} showActions />}>
         {() => {

            const { getIsSuperAdmin } = useGetPermission()

            const items: any = [
               (()=>{
                  if (UserRoleEnum.ORG_USER !== user?.role?.type && UserRoleEnum.ORG_OBSERVER !== user?.role?.type ) {
                     return {
                        title: 'Users',
                        key: 0,
                        children: (
                           <Users />
                        )
                     }
                  }
               })(),
               (() => {
                  if (!getIsSuperAdmin()) {
                     return (
                        {
                           title: 'Subscriptions',
                           key: 1,
                           children: <Subscriptions />
                        }
                     )
                  }
                  return null
               })(),
            ].filter((i) => Boolean(i))

            return (
               <Tabs
                  classNames={{
                     tab: 'max-w-[150px]'
                  }}
                  items={items}
               />
            )
         }}
      </ClientOnly>
   );
}
