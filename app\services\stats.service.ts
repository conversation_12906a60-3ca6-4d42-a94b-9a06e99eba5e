import moment from "moment";
import { API_ENDPOINTS } from "utils/api/api-endpoints";
import fetchInstance from "utils/api/fetchinstance";


const getQueryParams = (type: string) => {
   return {
      type,
      startDateTime: moment.utc().subtract('day', 6).format(),
      endDateTime: moment.utc().format(),
   }
}

export const statsClient = {
   getUtilization: (type: any) => {
      return fetchInstance(`${API_ENDPOINTS.STATS}`, {
         params: getQueryParams(type)
      });
   },
   getProviderStats: (type: any) => {
      return fetchInstance(`${API_ENDPOINTS.STATS}`, {
         params: getQueryParams(type)
      });
   },
   getIntegrationStats: () => {
      return fetchInstance(`${API_ENDPOINTS.INTEGRATIONS}/stats`);
   },
};