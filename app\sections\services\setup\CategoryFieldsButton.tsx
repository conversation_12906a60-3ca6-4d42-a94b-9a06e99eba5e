import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Icon<PERSON>utton,
  Tooltip,
  useTheme,
} from "@mui/material";
import { SettingOutlined, CloseOutlined } from "@ant-design/icons";
import CategoryFieldManager from "../category-fields";

interface CategoryFieldsButtonProps {
  category: string;
  categoryLabel: string;
  fieldCount?: number;
  disabled?: boolean;
}

export default function CategoryFieldsButton({
  category,
  categoryLabel,
  fieldCount = 0,
  disabled = false,
}: CategoryFieldsButtonProps) {
  const [open, setOpen] = useState(false);
  const theme = useTheme();

  const handleOpen = () => {
    if (!disabled) setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Tooltip
        title={
          disabled
            ? "Subscribe to enable field configuration"
            : "Configure additional fields for this category"
        }
      >
        <span style={{ display: "inline-block" }}>
          <Button
            variant="outlined"
            startIcon={<SettingOutlined />}
            onClick={handleOpen}
            size="small"
            disabled={disabled}
            sx={{
              height: 40,
              minWidth: 40,
              borderStyle: "dashed",
              opacity: disabled ? 0.8 : 1,
              color: disabled ? theme.palette.text.primary : undefined,
              backgroundColor: disabled ? "rgba(0,0,0,0.03)" : undefined,
              "&.Mui-disabled": {
                color: theme.palette.text.primary,
                opacity: 0.8,
                backgroundColor: "rgba(0,0,0,0.03)",
                borderColor: theme.palette.divider,
              },
              "&:hover": {
                borderStyle: "solid",
              },
            }}
          >
            Configure Fields
          </Button>
        </span>
      </Tooltip>

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth={false}
        fullWidth
        PaperProps={{
          variant: "outlined",
          sx: {
            width: "90vw",
            maxWidth: "1400px",
            height: "90vh",
            m: 2,
            boxShadow: "none",
            border: `1px solid ${theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.12)" : "#e5e5e5"}`,
          },
        }}
      >
        <IconButton
          onClick={handleClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            zIndex: 1,
            border: "none",
          }}
        >
          <CloseOutlined />
        </IconButton>
        <DialogContent sx={{ p: 0, height: "100%", overflow: "hidden" }}>
          <CategoryFieldManager
            category={category}
            categoryLabel={categoryLabel}
            fieldCount={fieldCount}
            onClose={handleClose}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
