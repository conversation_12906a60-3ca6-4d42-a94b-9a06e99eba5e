import { AxiosRequestConfig, AxiosResponse } from "axios";
import { ResponseModel } from "types/common";
import Pod from "types/environment";
import platformfetchInstance from "utils/api/fetchinstance/platform-fetch-Instance";


export const podClient = {
   createEnviroinment: (payload: Record<string, object | string | number | boolean>) => {
      return platformfetchInstance.post(`/environments`, payload)
   },
   getAllEnviroinment: async (options: AxiosRequestConfig = {}): Promise<AxiosResponse<ResponseModel<Pod.Root>>> => {
      return await platformfetchInstance.get(`/environments`, options)
   },
   getEnviroinmentById: (id: string): Promise<AxiosResponse<Pod.Root>> => {
      return platformfetchInstance.get(`/environments/${id}`)
   },
   updatEnviroinment: (id: string, payload: Record<string, object | string | number | boolean>) => {
      return platformfetchInstance.patch(`/environments/${id}`, payload)
   },
   actionEnviroinment: (id: string, payload: Record<string, object | string | number | boolean>) => {
      return platformfetchInstance.post(`/environments/${id}/actions`, payload)
   },
   deleteEnviroinment: (id: string) => {
      return platformfetchInstance.delete(`/environments/${id}`)
   },
};
