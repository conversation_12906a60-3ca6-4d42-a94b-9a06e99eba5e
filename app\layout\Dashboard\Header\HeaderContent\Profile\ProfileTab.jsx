import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { useLocation } from '@remix-run/react';

// material-ui
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';

// assets
import LogoutOutlined from '@ant-design/icons/LogoutOutlined';
import useConfig from 'hooks/useConfig';
import { Switch } from '@mui/material';
import {  SettingOutlined, UserOutlined } from '@ant-design/icons';
import { useGetUser } from 'hooks/api/user/useUser';
import { Link } from '@remix-run/react';
import { clearEnvironmentId } from 'utils/environment-id';

// ==============================|| HEADER PROFILE - PROFILE TAB ||============================== //

const MODE_LABEL = {
  light: 'Light Mode',
  dark: 'Dark Mode'
}

const LOGOUT = {
  color: 'error.main',
  '&:hover': { backgroundColor: 'error.lighter' }
}

const LINKS = {
  ACCOUNT_SETTINGS: {
    href: '/console/profile-settings'
  }
}

export default function ProfileTab() {
  const location = useLocation();
  const [selectedIndex, setSelectedIndex] = useState(0);

  const { onChangeMode, mode } = useConfig(),
    { attemptUserLogout } = useGetUser()

  useEffect(() => {
    const pathToIndex = {
      '/apps/profiles/user/personal': 0,
      '/apps/profiles/account/basic': 1,
      '/apps/profiles/account/personal': 3,
      '/apps/invoice/details/1': 4
    };

    setSelectedIndex(pathToIndex[location.pathname] ?? undefined);
  }, [location.pathname]);

  const logout = () => {
    clearEnvironmentId();
    attemptUserLogout()
  }

  return (
    <List component="nav" sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32 } }}>

      {/* theme mode (1) */}
      <ListItemButton
      >
        <ListItemIcon>
          <SettingOutlined />
        </ListItemIcon>
        <ListItemText primary={MODE_LABEL[mode]} />
        <Switch
          onChange={({ target: { checked } }) => {
            onChangeMode(checked ? 'dark' : 'light')
          }}
          size='small'
          edge='end'
          checked={mode === 'dark'}
        />
      </ListItemButton>

      {/* account settings (2) */}
      <Link to={LINKS.ACCOUNT_SETTINGS.href}>
        <ListItemButton
          selected={selectedIndex === 2}
        >
          <ListItemIcon>
            <UserOutlined />
          </ListItemIcon>
          <ListItemText primary="My Profile" />
        </ListItemButton>
      </Link>

      {/* logout (3) */}
      <ListItemButton
        onClick={logout}
        sx={LOGOUT}
      >
        <ListItemIcon sx={LOGOUT}>
          <LogoutOutlined />
        </ListItemIcon>
        <ListItemText primary="Logout" />
      </ListItemButton>
    </List>
  );
}

ProfileTab.propTypes = { handleLogout: PropTypes.func };
