import _ from 'lodash';

// Date field mappings with their operators
const DATE_FIELDS = {
   'changeLog/lastUpdatedDateTime': {
      defaultOperator: '='
   },
   'changeLog/createdDateTime': {
      defaultOperator: '='
   },
   'changeLog/startDateTime': {
      defaultOperator: '>'
   },
   'changeLog/endDateTime': {
      defaultOperator: '<'
   }
};

// Map filter operators to API operators
const OPERATOR_MAPPING = {
   'is': '=',
   'is_before': '<',
   'is_after': '>',
   'contains': 'LIKE',
   'greater_than': '>',
   'less_than': '<'
};

export function parseIntegrationFilterPayload(filter) {
   const criteria = [];

   filter.forEach(({ id, value, operator: filterOperator }) => {
      const property = _.replace(id, /_/g, '/');
      
      // Default operator
      let operator = 'LIKE';
      
      // Check if this is a date field
      if (Object.keys(DATE_FIELDS).includes(property)) {
         // For date fields, use the operator from the filter or default to '='
         if (filterOperator && OPERATOR_MAPPING[filterOperator]) {
            operator = OPERATOR_MAPPING[filterOperator];
         } else {
            operator = DATE_FIELDS[property].defaultOperator;
         }
         
         console.log('Date field processing:', {
            property,
            filterOperator,
            mappedOperator: operator,
            value
         });
      } else {
         // For non-date fields, check if we have a specific operator
         if (filterOperator && OPERATOR_MAPPING[filterOperator]) {
            operator = OPERATOR_MAPPING[filterOperator];
         } else {
            // Default operators based on field type
            if (property.includes('state') || property.includes('type') || property.includes('id')) {
               operator = '=';
            } else {
               operator = 'LIKE';
            }
         }
      }

      criteria.push({ 
         property: `/${property}`, 
         values: Array.isArray(value) ? value : [value], 
         operator 
      });
   });

   return criteria;
}
