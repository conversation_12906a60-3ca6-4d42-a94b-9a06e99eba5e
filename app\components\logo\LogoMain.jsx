
/**
 * if you want to use image instead of <svg> uncomment following.
 *
 * import logoDark from 'assets/images/logo-dark.svg';
 * import logo from 'assets/images/logo.svg';
 *
 */

import PropTypes from 'prop-types';
import { useTheme, useMediaQuery, Box } from "@mui/material";
import { useGetMenuMaster } from "api/menu";
import Image from "remix-image";

// ==============================|| LOGO SVG ||============================== //
const BASE_PATH = '/images/brand'

const Logo = ({ isIcon, sx }) => {

  const { menuMaster } = useGetMenuMaster();
  const drawerOpen = menuMaster.isDashboardDrawerOpened;
  const { palette: { mode }, breakpoints } = useTheme()
  
  // Media queries
  const downMD = useMediaQuery(breakpoints.down('md'));
  const downSM = useMediaQuery(breakpoints.down('sm'));
  
  // Determine if we should show icon version
  const showIcon = isIcon || !drawerOpen;
  
  // Responsive sizes
  const expandedSize = {
    width: downSM ? 80 : downMD ? 90 : 100,
    height: downSM ? 80 : downMD ? 90 : 100
  };
  
  const iconSize = {
    width: downSM ? 28 : 32,
    height: downSM ? 28 : 32
  };

  if (!showIcon) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', ...sx }}>
        <Image
          style={expandedSize}
          src={`${BASE_PATH}/unz_brand_${mode}.svg`}
          alt="Unizo Logo"
        />
      </Box>
    );
  }
  
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', ...sx }}>
      <Image
        style={iconSize}
        src={`${BASE_PATH}/unz_min_brand_${mode}.svg`}
        alt="Unizo Logo"
      />
    </Box>
  );
};

Logo.propTypes = {
  isIcon: PropTypes.bool,
  sx: PropTypes.object
};

export default Logo;
