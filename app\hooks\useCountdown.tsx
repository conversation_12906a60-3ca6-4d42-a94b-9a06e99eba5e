import { useState, useEffect, useRef, useCallback } from 'react';

interface UseCountdownProps {
  initialTime?: number;
  onComplete?: () => void;
  onTick?: (timeLeft: number) => void;
}

export const useCountdown = ({
  initialTime = 0,
  onComplete,
  onTick
}: UseCountdownProps = {}) => {
  const [timeLeft, setTimeLeft] = useState(initialTime);
  const [isActive, setIsActive] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const startCountdown = useCallback((time?: number) => {
    if (time !== undefined) {
      setTimeLeft(time);
    }
    setIsActive(true);
  }, []);

  const stopCountdown = useCallback(() => {
    setIsActive(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  const resetCountdown = useCallback((time?: number) => {
    stopCountdown();
    setTimeLeft(time ?? initialTime);
  }, [initialTime, stopCountdown]);

  useEffect(() => {
    if (!isActive) return;

    intervalRef.current = setInterval(() => {
      setTimeLeft((prevTime) => {
        const newTime = prevTime - 1;
        
        if (onTick) {
          onTick(newTime);
        }

        if (newTime <= 0) {
          setIsActive(false);
          if (onComplete) {
            onComplete();
          }
          return 0;
        }

        return newTime;
      });
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isActive, onComplete, onTick]);

  return {
    timeLeft,
    isActive,
    startCountdown,
    stopCountdown,
    resetCountdown
  };
};