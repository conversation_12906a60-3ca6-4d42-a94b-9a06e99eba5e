.responsive-grid-with-detail-panel {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    @media (min-width: 1210px) and (max-width: 1640px) {
        grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 767.98px) and (max-width: 1210px) {
        grid-template-columns: repeat(2, 1fr);
    }

    @media  (max-width: 767.98px) {
        grid-template-columns: repeat(1, 1fr);
    }
}

.detailsPanel {
    display: flex;
    flex-direction: column;
    gap: 0;
    margin-top: calc(20px / 2 * -1);

    grid-row: 1 / 2;
    grid-column: 1 / -1;
}

.detailsPanel__triangle {
    margin-bottom: -1px;
    z-index: 10;
}