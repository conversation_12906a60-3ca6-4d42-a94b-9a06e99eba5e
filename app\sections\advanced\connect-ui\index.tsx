/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo, useState, useCallback, useEffect } from "react";
import {
    Grid,
    Stack,
    Typography,
    Button,
    IconButton,
    Box,
    useTheme,
    alpha,
    Menu,
    MenuItem,
    Tooltip,
} from "@mui/material";
import { useNavigate } from "@remix-run/react";
import Table from "components/@extended/Table";
import { useGetDockProfile } from "hooks/api/dockProfiles/useDockProfile";
import useUserDetails from "store/user";
import ConnectUIModal from "./ConnectUIModal";
import ConfigurationDrawer from "./ConfigurationDrawer";
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import MoreVert from '@mui/icons-material/MoreVert';
import { State } from "hooks/useStatus";
import { serviceProfileClient } from "services/service-profile.service";
import { useServiceProfile } from "hooks/useServiceProfile";
import LearnMoreLink from "components/@extended/LearnMoreLink";
import MainCard from "components/MainCard";
import PageCard from "components/cards/PageCard";
import { Layers, Plus } from 'lucide-react';
import { generateUUID } from 'utils/uuid';
import CopyableText from 'components/@extended/Copyable-text';
import { toast } from 'sonner';
import CircularProgress from '@mui/material/CircularProgress';

const TITLE = `Test run the authentication experience for your end users before integrating into your product.`;

export default function ConnectUI() {
    const theme = useTheme();
    const navigate = useNavigate();
    const [openConfigDrawer, setOpenConfigDrawer] = useState(false);
    const [editConfigId, setEditConfigId] = useState<string | null>(null);
    const [paginationState, setPaginationState] = useState<any>({
        pageIndex: 0,
        pageSize: 10,
    })
    const [generatingKeys, setGeneratingKeys] = useState<{ [key: string]: boolean }>({});
    const [generatedServiceKeys, setGeneratedServiceKeys] = useState<{ [key: string]: string }>({});
    const [showTestLinkModal, setShowTestLinkModal] = useState(false);
    const [currentTestLink, setCurrentTestLink] = useState<string>('');

    const { getDockProfiles, attemptDeleteDockProfile, attemptCreateServiceKey } = useGetDockProfile()
    const { categories, user } = useUserDetails()
    const { loadImage } = useServiceProfile();

    const dockProfilesQuery = getDockProfiles()
    const { data: profileData, refetch, isLoading } = dockProfilesQuery
    
    // The hook returns the full response object with pagination and data
    const datas = Array.isArray(profileData?.data) ? profileData.data : []
    
    
    const updatedValues: Record<string, string> = {
        POP_UP: "Pop Up",
        EMBEDDED: "Embedded"
    };

    const columns = useMemo(
        () => [
            {
                accessorKey: 'name',
                header: 'Name',
                minSize: 150,
                size: 200,
                cell({ row: { original: { name, id } } }: any) {
                    return (
                        <Stack spacing={0.5}>
                            <Typography 
                                variant="body2" 
                                fontWeight={500}
                                sx={{
                                    fontSize: { xs: '0.813rem', sm: '0.875rem' },
                                    lineHeight: 1.4
                                }}
                            >
                                {name}
                            </Typography>
                            <Typography 
                                variant="caption" 
                                color="text.secondary"
                                sx={{ 
                                    wordBreak: 'break-word',
                                    fontSize: { xs: '0.688rem', sm: '0.75rem' },
                                    display: { xs: 'none', sm: 'block' }
                                }}
                            >
                                {id}
                            </Typography>
                        </Stack>
                    )
                }
            },
            {
                accessorKey: 'frontendUrl',
                header: () => (
                    <Typography sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                        Custom Domain
                    </Typography>
                ),
                cell({ row: { original: { frontendUrl } } }: any) {
                    const isCustomDomain = frontendUrl && frontendUrl.trim() !== 'https://dock.unizo.ai';
                    
                    if (!isCustomDomain) {
                        return (
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, sm: 1 } }}>
                                <Box
                                    sx={{
                                        width: { xs: 6, sm: 8 },
                                        height: { xs: 6, sm: 8 },
                                        borderRadius: '50%',
                                        backgroundColor: theme.palette.grey[400],
                                        flexShrink: 0
                                    }}
                                />
                                <Typography 
                                    variant="body2" 
                                    color="text.secondary"
                                    sx={{ 
                                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                                        display: { xs: 'none', sm: 'block' }
                                    }}
                                >
                                    Not configured
                                </Typography>
                            </Box>
                        );
                    }
                    
                    // Extract domain from URL
                    let displayDomain = frontendUrl;
                    try {
                        const url = new URL(frontendUrl);
                        displayDomain = url.hostname;
                    } catch (e) {
                        // If not a valid URL, show as is
                    }
                    
                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, sm: 1 } }}>
                            <Box
                                sx={{
                                    width: { xs: 6, sm: 8 },
                                    height: { xs: 6, sm: 8 },
                                    borderRadius: '50%',
                                    backgroundColor: theme.palette.success.main,
                                    flexShrink: 0
                                }}
                            />
                            <Typography 
                                variant="body2"
                                sx={{ 
                                    fontSize: { xs: '0.75rem', sm: '0.875rem' },
                                    wordBreak: 'break-word',
                                    maxWidth: { xs: 100, sm: 150, md: 200, lg: 'none' }
                                }}
                            >
                                {displayDomain}
                            </Typography>
                        </Box>
                    );
                }
            },
            {
                accessorKey: 'pageLayout',
                header: () => (
                    <Typography sx={{ 
                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                        display: { xs: 'none', md: 'block' }
                    }}>
                        Layout Type
                    </Typography>
                ),
                enableHiding: true,
                cell({ row: { original: { pageLayout } } }: any) {
                    return (
                        <Typography 
                            variant="body2"
                            sx={{ 
                                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                                display: { xs: 'none', md: 'block' }
                            }}
                        >
                            {updatedValues[pageLayout] || pageLayout}
                        </Typography>
                    )
                }

            },
            {
                accessorKey: 'changeLog.lastUpdatedDateTime',
                header: () => (
                    <Typography sx={{ 
                        fontSize: { xs: '0.75rem', sm: '0.875rem' },
                        display: { xs: 'none', sm: 'block' }
                    }}>
                        Last Updated
                    </Typography>
                ),
                cell({ row: { original: { changeLog } } }: any) {
                    const formatDate = (dateString: string) => {
                        if (!dateString) return '-';
                        
                        const date = new Date(dateString);
                        const now = new Date();
                        
                        // Format date and time based on user's locale
                        const dateOptions: Intl.DateTimeFormatOptions = {
                            month: 'short',
                            day: 'numeric',
                            year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
                        };
                        
                        const timeOptions: Intl.DateTimeFormatOptions = {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                        };
                        
                        const formattedDate = new Intl.DateTimeFormat(navigator.language || 'en-US', dateOptions).format(date);
                        const formattedTime = new Intl.DateTimeFormat(navigator.language || 'en-US', timeOptions).format(date);
                        
                        // Check if it's today
                        const isToday = date.toDateString() === now.toDateString();
                        const yesterday = new Date(now);
                        yesterday.setDate(yesterday.getDate() - 1);
                        const isYesterday = date.toDateString() === yesterday.toDateString();
                        
                        if (isToday) {
                            return `Today, ${formattedTime}`;
                        } else if (isYesterday) {
                            return `Yesterday, ${formattedTime}`;
                        } else {
                            return `${formattedDate}, ${formattedTime}`;
                        }
                    };
                    
                    return (
                        <Typography 
                            variant="body2" 
                            color="text.secondary"
                            sx={{ 
                                fontSize: { xs: '0.75rem', sm: '0.875rem' },
                                display: { xs: 'none', sm: 'block' }
                            }}
                        >
                            {formatDate(changeLog?.lastUpdatedDateTime)}
                        </Typography>
                    )
                }
            },
            {
                accessorKey: 'action',
                header: () => (
                    <Typography sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}>
                        Actions
                    </Typography>
                ),
                size: 120,
                minSize: 100,
                cell({ row: { original } }: any) {
                    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
                    const open = Boolean(anchorEl);
                    
                    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
                        event.stopPropagation();
                        setAnchorEl(event.currentTarget);
                    };
                    
                    const handleClose = () => {
                        setAnchorEl(null);
                    };

                    const isGenerating = generatingKeys[original.id];
                    const generatedKey = generatedServiceKeys[original.id];
                    
                    return (
                        <Stack direction="row" spacing={0.25} alignItems="center">
                            {isGenerating ? (
                                <Box sx={{ 
                                    display: 'flex', 
                                    alignItems: 'center', 
                                    px: 1,
                                    minWidth: 40
                                }}>
                                    <CircularProgress size={16} />
                                </Box>
                            ) : generatedKey ? (
                                <Stack direction="row" spacing={1} alignItems="center">
                                    <Button
                                        size="small"
                                        variant="text"
                                        onClick={() => {
                                            setCurrentTestLink(generatedKey);
                                            setShowTestLinkModal(true);
                                        }}
                                        sx={{
                                            textTransform: 'none',
                                            fontSize: '0.75rem',
                                            px: 1,
                                            minWidth: 'auto',
                                            color: theme.palette.primary.main,
                                            '&:hover': {
                                                backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                            }
                                        }}
                                    >
                                        Test Run
                                    </Button>
                                </Stack>
                            ) : (
                                <Tooltip title="Generate Test Link">
                                    <IconButton
                                        size="small"
                                        onClick={() => handleTestRun(original)}
                                        sx={{ 
                                            color: theme.palette.text.secondary,
                                            padding: { xs: '4px', sm: '8px' },
                                            '&:hover': {
                                                backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                                color: theme.palette.primary.main,
                                            }
                                        }}
                                    >
                                        <PlayArrowIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
                                    </IconButton>
                                </Tooltip>
                            )}
                            <Tooltip title="Edit">
                                <IconButton
                                    size="small"
                                    onClick={() => {
                                        setEditConfigId(original.id);
                                        setOpenConfigDrawer(true);
                                    }}
                                    sx={{ 
                                        color: theme.palette.text.secondary,
                                        padding: { xs: '4px', sm: '8px' },
                                        display: { xs: 'none', sm: 'inline-flex' },
                                        '&:hover': {
                                            backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                            color: theme.palette.primary.main,
                                        }
                                    }}
                                >
                                    <EditIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
                                </IconButton>
                            </Tooltip>
                            <IconButton
                                size="small"
                                onClick={handleClick}
                                sx={{ 
                                    color: theme.palette.text.secondary,
                                    padding: { xs: '4px', sm: '8px' },
                                    '&:hover': {
                                        backgroundColor: alpha(theme.palette.action.active, 0.08),
                                    }
                                }}
                            >
                                <MoreVert sx={{ fontSize: { xs: 18, sm: 20 } }} />
                            </IconButton>
                            <Menu
                                anchorEl={anchorEl}
                                open={open}
                                onClose={handleClose}
                                anchorOrigin={{
                                    vertical: 'bottom',
                                    horizontal: 'right',
                                }}
                                transformOrigin={{
                                    vertical: 'top',
                                    horizontal: 'right',
                                }}
                                PaperProps={{
                                    sx: {
                                        mt: 0.5,
                                        minWidth: 120,
                                        boxShadow: theme.shadows[3],
                                    }
                                }}
                            >
                                <MenuItem
                                    onClick={() => {
                                        setEditConfigId(original.id);
                                        setOpenConfigDrawer(true);
                                        handleClose();
                                    }}
                                    sx={{ 
                                        fontSize: '0.875rem',
                                        display: { xs: 'flex', sm: 'none' }
                                    }}
                                >
                                    <EditIcon fontSize="small" sx={{ mr: 1 }} />
                                    Edit
                                </MenuItem>
                                <MenuItem
                                    onClick={() => {
                                        onDelete(original.id);
                                        handleClose();
                                    }}
                                    sx={{ 
                                        fontSize: '0.875rem',
                                        color: theme.palette.error.main,
                                        '&:hover': {
                                            backgroundColor: alpha(theme.palette.error.main, 0.08),
                                        }
                                    }}
                                >
                                    <DeleteIcon fontSize="small" sx={{ mr: 1 }} />
                                    Delete
                                </MenuItem>
                            </Menu>
                        </Stack>
                    );
                }
            },
        ],
        [],
    );


    const onDelete = async (id: string) => {
        await attemptDeleteDockProfile(id);
        refetch();
    }


    const handleTestRun = async (config: any) => {
        const configId = config.id;
        
        // Prevent multiple simultaneous requests
        if (generatingKeys[configId]) {
            return;
        }
        
        setGeneratingKeys(prev => ({ ...prev, [configId]: true }));
        
        try {
            // Generate test data
            const customerKey = generateUUID();
            const customerName = 'Connect UI Test Run';
            
            // Get enabled categories
            const enabledCategories = categories?.filter((cat) => !cat?.disabled) || [];
            
            // If no categories, use a default configuration
            let categorySelectors;
            if (enabledCategories.length === 0) {
                // Use PROVIDER mode when no categories are available
                categorySelectors = [];
                console.warn('No categories available, using provider mode for test link');
            } else {
                categorySelectors = enabledCategories.map((cat) => ({
                    type: cat.value
                }));
            }
            
            const payload: any = {
                type: "INTEGRATION_TOKEN",
                name: customerName,
                subOrganization: {
                    name: customerName,
                    externalKey: customerKey
                },
                integration: {
                    type: "GENERIC",
                    target: categorySelectors.length > 0 ? {
                        type: "Category",
                        categorySelectors: categorySelectors
                    } : {
                        type: "Provider",
                        providerSelectors: []
                    }
                },
                dockProfile: {
                    id: configId
                }
            };
            
            attemptCreateServiceKey(
                payload, 
                (response: any) => {
                    try {
                        const url = response?.data?.formDescriptorUrl;
                        
                        if (url) {
                            setGeneratedServiceKeys(prev => ({ ...prev, [configId]: url }));
                            setCurrentTestLink(url);
                            setShowTestLinkModal(true);
                        } else {
                            toast.error('Failed to generate test link. Please try again.');
                        }
                    } catch (error) {
                        console.error('Error processing response:', error);
                        toast.error('Invalid response format. Please try again.');
                    } finally {
                        setGeneratingKeys(prev => ({ ...prev, [configId]: false }));
                    }
                },
                (error: any) => {
                    console.error('Error generating test session:', error);
                    
                    let errorMessage = 'Failed to generate test link. ';
                    if (error?.response?.status === 401) {
                        errorMessage = 'Authentication failed. Please log in again.';
                    } else if (error?.response?.status === 403) {
                        errorMessage = 'You don\'t have permission to generate test links.';
                    } else if (error?.response?.status >= 500) {
                        errorMessage = 'Server error. Please try again later.';
                    }
                    
                    toast.error(errorMessage);
                    setGeneratingKeys(prev => ({ ...prev, [configId]: false }));
                }
            );
        } catch (error) {
            console.error('Error in handleTestRun:', error);
            toast.error('An unexpected error occurred. Please try again.');
            setGeneratingKeys(prev => ({ ...prev, [configId]: false }));
        }
    };



    const activeProfiles = datas.filter((item: any) => item.state === "ACTIVE");
    const hasActiveProfiles = activeProfiles.length > 0;

    // Show loader while data is being fetched
    if (isLoading) {
        return (
            <Stack spacing={3}>
                <PageCard>
                    <Box sx={{ 
                        display: 'flex', 
                        justifyContent: 'center', 
                        alignItems: 'center',
                        minHeight: 400,
                        flexDirection: 'column',
                        gap: 2
                    }}>
                        <Box
                            sx={{
                                width: 40,
                                height: 40,
                                border: `3px solid ${theme.palette.divider}`,
                                borderTopColor: theme.palette.primary.main,
                                borderRadius: '50%',
                                animation: 'spin 1s linear infinite',
                                '@keyframes spin': {
                                    '0%': { transform: 'rotate(0deg)' },
                                    '100%': { transform: 'rotate(360deg)' }
                                }
                            }}
                        />
                        <Typography variant="body2" color="text.secondary">
                            Loading configurations...
                        </Typography>
                    </Box>
                </PageCard>
            </Stack>
        );
    }

    return (
        <Stack spacing={3}>
            {!hasActiveProfiles ? (
                // Empty state when no configurations exist
                <PageCard>
                    <Box 
                        sx={{ 
                            textAlign: 'center', 
                            py: { xs: 6, sm: 8, md: 10 },
                            px: { xs: 2, sm: 4 },
                            maxWidth: 600,
                            mx: 'auto'
                        }}
                    >
                        <Box
                            sx={{
                                width: { xs: 60, sm: 80 },
                                height: { xs: 60, sm: 80 },
                                borderRadius: '50%',
                                backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                mx: 'auto',
                                mb: 3
                            }}
                        >
                            <Layers 
                                size={40} 
                                style={{ 
                                    color: theme.palette.primary.main 
                                }} 
                            />
                        </Box>
                        
                        <Typography variant="h5" fontWeight={600} gutterBottom>
                            Create your first Connect UI
                        </Typography>
                        
                        <Typography 
                            variant="body1" 
                            color="text.secondary" 
                            sx={{ mb: 4, maxWidth: 450, mx: 'auto' }}
                        >
                            Set up pre-built authentication experiences for your end users. Test run the authentication flow before integrating into your product.
                        </Typography>
                        
                        <Button
                            variant="contained"
                            size="large"
                            startIcon={<Plus />}
                            onClick={() => {
                                setEditConfigId(null);
                                setOpenConfigDrawer(true);
                            }}
                            sx={{ 
                                px: 4,
                                textTransform: 'none',
                                fontWeight: 500
                            }}
                        >
                            New Connect UI
                        </Button>
                        
                        <Box sx={{ mt: 4 }}>
                            <Typography variant="body2" color="text.secondary">
                                Need help getting started?{' '}
                                <LearnMoreLink
                                    href="https://docs.unizo.ai/docs/unizo-console/connect-ui"
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    View documentation
                                </LearnMoreLink>
                            </Typography>
                        </Box>
                    </Box>
                </PageCard>
            ) : (
                // Normal view when configurations exist
                <>
                    <MainCard>
                        <Grid container justifyContent="space-between" alignItems="center">
                            <Grid item xs={12} md={8}>
                                <Stack spacing={1}>
                                    <Typography variant="h6">{TITLE}</Typography>
                                </Stack>
                            </Grid>
                            <Grid item xs={12} md={4} sx={{ textAlign: { xs: 'left', md: 'right' }, mt: { xs: 2, md: 0 } }}>
                                <Button
                                    variant="contained"
                                    onClick={() => {
                                        setEditConfigId(null);
                                        setOpenConfigDrawer(true);
                                    }}
                                >
                                    New Connect UI
                                </Button>
                            </Grid>
                        </Grid>
                    </MainCard>
                    
                    <Box sx={{ 
                        overflowX: 'auto',
                        '& .MuiTableContainer-root': {
                            minWidth: { xs: 300, sm: 'auto' }
                        }
                    }}>
                        <Table
                            data={activeProfiles}
                            columns={columns}
                            totalData={activeProfiles.length}
                            {...{
                                onPaginationChange: setPaginationState,
                                state: {
                                    pagination: {
                                        pageIndex: paginationState.pageIndex,
                                        pageSize: paginationState?.pageSize,
                                    }
                                } as any,
                            }}
                            sx={{
                                '& .MuiTableCell-root': {
                                    padding: { xs: '8px 4px', sm: '16px 8px', md: '16px' },
                                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                                },
                                '& .MuiTableHead-root .MuiTableCell-root': {
                                    padding: { xs: '12px 4px', sm: '16px 8px', md: '16px' },
                                    fontWeight: 600
                                }
                            }}
                        />
                    </Box>
                </>
            )}


            {/* Test Link Modal */}
            {showTestLinkModal && currentTestLink && (
                <ConnectUIModal
                    opened={showTestLinkModal}
                    onClosed={() => {
                        setShowTestLinkModal(false);
                        setCurrentTestLink('');
                    }}
                    magicLink={currentTestLink}
                />
            )}
            
            {/* Configuration Drawer */}
            <ConfigurationDrawer
                open={openConfigDrawer}
                onClose={() => {
                    setOpenConfigDrawer(false);
                    setEditConfigId(null);
                }}
                editId={editConfigId}
                onSuccess={() => {
                    refetch();
                }}
            />
        </Stack>
    );
}

