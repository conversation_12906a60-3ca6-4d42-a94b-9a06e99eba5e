import json
import sys
from jinja2 import Template

# Define HTML template
html_template = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
        th { background-color: #f4f4f4; }
        .severity-ERROR { color: #b30000; font-weight: bold; }
        .severity-WARNING { color: #e67e22; font-weight: bold; }
        .severity-INFO { color: #2980b9; }
        .no-findings { color: green; }
        .error-row { background-color: #f8d7da; }
        .warning-row { background-color: #fff3cd; }
        .info-row { background-color: #d4edda; }
    </style>
</head>
<body>
    <h1>{{ title }}</h1>
    <h2>Summary</h2>
    <table>
        <tr>
            <th>Severity</th>
            <th>Count</th>
        </tr>
        {% for sev, count in count.items() %}
        <tr class="{{ 'error-row' if sev == 'ERROR' else 'warning-row' if sev == 'WARNING' else 'info-row' if sev == 'INFO' else '' }}">
            <td>{{ sev }}</td>
            <td>{{ count }}</td>
        </tr>
        {% endfor %}
    </table>

    <h2>Details of Vulnerabilities</h2>
    <table>
        <thead>
            <tr>
                <th>Serial No.</th>
                <th>Severity</th>
                <th>Check ID</th>
                <th>Message</th>
                <th>Path</th>
                <th>Start Line</th>
                <th>End Line</th>
                <th>Match</th>
                <th>Metadata</th>
                <th>Fixes</th>
            </tr>
        </thead>
        <tbody>
            {% if report_data.results %}
                {% for result in report_data.results %}
                <tr>
                    <td>{{ loop.index }}</td>  <!-- Serial Number -->
                    <td class="severity-{{ result.extra.severity|upper }}">{{ result.extra.severity }}</td>
                    <td>{{ result.check_id }}</td>
                    <td>{{ result.extra.message }}</td>
                    <td>{{ result.path }}</td>
                    <td>{{ result.start.line }}</td>
                    <td>{{ result.end.line }}</td>
                    <td>{{ result.extra.lines }}</td>
                    <td>{{ result.extra.metadata }}</td>
                    <td>
                        {% if result.extra.autofix %}
                            {{ result.extra.autofix }}
                        {% else %}
                            No fixes available
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="10" class="no-findings">No security issues found.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</body>
</html>
"""

def generate_html(json_file, output_file, title):
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    allowed_severities = {"ERROR", "WARNING", "INFO"}
    vulnerabilities = []
    severity_count = {"ERROR": 0, "WARNING": 0, "INFO": 0}

    for result in data.get('results', []):
        sev = (result.get('severity') or result.get('extra', {}).get('severity', '')).upper()
        if sev in allowed_severities:
            severity_count[sev] += 1
            vulnerabilities.append(result)

    template = Template(html_template)
    html_content = template.render(report_data={'results': vulnerabilities}, title=title, count=severity_count)

    # Write the HTML content to the output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python3 convert_to_html_semgrep.py <json_file> <output_file> <title>")
        sys.exit(1)
    generate_html(sys.argv[1], sys.argv[2], sys.argv[3])
