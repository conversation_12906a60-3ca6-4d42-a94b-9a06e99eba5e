import type { GridModal, LayoutPosition } from './grid-type';
import { LAYOUT_BREAK_POINTS } from '../constant';
import { ResponsiveProps } from 'react-grid-layout';
import moment from 'moment';

const getRootLayOutConfig = (gadgetConfigs: GridModal['gadgetConfigs']) => {
   const breakPointKeys = Object.keys(LAYOUT_BREAK_POINTS);

   return (
      breakPointKeys.reduce((acc: any, breakPointKey) => {
         acc[breakPointKey] = gadgetConfigs.map(({ layoutPosition }, index) => {

            const {
               gridWidth: w,
               gridHeight: h,
               gridX: x,
               gridY: y,
               minHeight: minH,
               minWidth: minW,
            } = layoutPosition[breakPointKey] || {};

            return {
               w,
               h,
               x,
               y,
               i: index.toString(),
               minH: minH || h,
               minW: minW || w,
               static: true
            };
         });
         return acc;
      }, {})
   );
};

const getRootProps = (gadgetConfigs: GridModal['gadgetConfigs']): ResponsiveProps => {
   return {
      breakpoints: LAYOUT_BREAK_POINTS,
      cols: { xl: 4, lg: 4, md: 4, sm: 2, xs: 1 },
      layouts: getRootLayOutConfig(gadgetConfigs),
      margin: [20, 20],
      containerPadding: [0, 0]
   };
};

/**
 * Gets the last 7 days including today in "MMM D" format.
 * @returns {string[]} An array of formatted dates.
 */
function getLast7DaysStr(): string[] {
   return Array.from({ length: 7 }, (_, i) =>
      moment().subtract(6 - i, 'days').format('MMM D')
   );
}

function getFallbackDataForLineChart(): number[] {
   return getLast7DaysStr().map((_, index) => 0)
}

export { getRootProps, getLast7DaysStr, getFallbackDataForLineChart };
