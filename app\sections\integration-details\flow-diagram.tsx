import { <PERSON>, <PERSON><PERSON>, <PERSON>ack, Typography, useTheme } from "@mui/material"
import MainCard from "components/MainCard";

import { ConnectionMode, ConnectionLineType } from '@xyflow/react';
import { CSSProperties, memo, useEffect, useMemo, useState } from "react";

import './style.scss';
import { ErrorEdges } from "./edges";
import Image from "remix-image";
import { useGetIntegrationDetails } from "hooks/api/integration/useIntegration-details";
import { organizationClient } from "services/organization.service";
import { useServiceProfile } from "hooks/useServiceProfile";
import { DeleteIntegration } from "./delete-integration";
import { ThemeMode } from "config";

enum StateEnums {
   available = 'HEALTHY',
   notAvailable = 'UNHEALTHY',
};
const ENTERPRISE_URL = "/images/integration-details-enterprise.svg";


const SourceNode = ({ orgId }: Record<string, any>) => {

   const [org, setOrg] = useState<Record<string, any>>();

   useEffect(() => {
      if (orgId) {
         (async () => {
            try {
               const { data } = await organizationClient.getOrganizationById(orgId)
               setOrg(data);
            } catch (error) { }
         })()
      }
   }, [orgId])

   return (
      <div className="flex justify-center items-center gap-[.5rem]">
         <Image
            src={ENTERPRISE_URL}
            loading="lazy"
            alt="Office "
            className='h-5 w-5'
            title="Office "
            style={{ filter: 'grayscale(1)', height: 20, width: 30 }}
         />
         <span>
            {org?.name}
         </span>
      </div>
   )
}

const MiddleNode = ({ serviceProfile }: any) => {
   return (
      <Stack spacing={2}>
         <Typography variant='overline' className="middle-node !text-[6px]" sx={{ lineHeight: 2 }}>ACCESS TYPE</Typography>
         {/* <Box>{type}</Box> */}
      </Stack>
   )
}

const TargetNode = ({ provider, subOrg }: any) => {
   const { serviceProfile } = useGetIntegrationDetails();

   const { loadImage } = useServiceProfile()

   return (
      <Stack direction={'row'} gap={2} alignItems={'center'}>
         <div className="w-[20px]">
            {loadImage(serviceProfile, {
               size: 'large'
            })}
         </div>
         <Stack alignItems={'flex-start'} spacing={0.2}>
            <Typography
               variant="h6"
               component={'span'}
               sx={{ textAlign: 'start' }}
            >
               {provider?.name}
            </Typography>
            <Typography
               variant='h6'
               component={'span'}
            >
               {subOrg}
            </Typography>
         </Stack>
      </Stack>
   )
}

const OPTIONS = {
   zoomOnScroll: false,
   zoomOnPinch: false,
   panOnScroll: false,
   panOnDrag: false,
   fitView: true,
   connectionLineType: ConnectionLineType.Straight,
   nodesDraggable: false
};
const INPUT_TARGET_NODES = {
},
   NODE_STYLES: React.CSSProperties = {
      width: 150,
      height: 40,
      display: 'flex',
   }, MIDDLE_NODE_STYLES: React.CSSProperties = {
      width: 55,
      height: 55,
      borderRadius: '50%',
      textAlign: 'center',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
   },
   MERGED_NODE_STYLES = { ...NODE_STYLES, ...INPUT_TARGET_NODES }

export const EDGE_STYLES: React.CSSProperties = {
   strokeWidth: 2
};
export const STATES = {
   SUCCESS: '#237804',
   ERROR: '#cf1322',
   WARNING: 'orange'
};

export enum AccessTypeEnums {
   APIKEY_FLW = "Personal Access Token",
   CREDENTIALS_FLW = "Credentials",
   OAUTH_FLW = "OAuth",
   OAUTH_PASSWORD_FLW = "OAuth",
   APP_FLW = "App"
};

export const FlowDiagram = memo(() => {

   const { serviceProfile, integration } = useGetIntegrationDetails();

   const { palette }: any = useTheme();

   const orgId = useMemo(() => integration?.organization?.id, [integration?.organization]);

   const state: any = StateEnums.available;

   const themedStyle: CSSProperties = useMemo(() => {
      const colors = palette?.colors;

      if (palette.mode !== ThemeMode.DARK) {
         return {
            color: palette?.common?.black,
            borderColor: colors?.grey[4],
         }
      }

      return {
         color: palette?.common?.black,
         borderColor: colors?.grey[4],
      }
   }, [palette])

   const nodes: any = useMemo(() => {

      const nodeList = ([
         {
            id: '1',
            type: 'input',
            data: {
               label: (
                  <SourceNode
                     orgId={orgId}
                  />
               )
            },
            sourcePosition: 'right',
            position: { x: 0, y: 0 },
            className: 'source border-none',
            style: { ...MERGED_NODE_STYLES, ...themedStyle }
         },
         {
            id: '2',
            data: {
               label: (
                  <MiddleNode serviceProfile={serviceProfile} />
               )
            },
            type: 'selectorNode',
            targetPosition: 'left',
            sourcePosition: 'right',
            position: { x: 250, y: -7.5 },
            className: 'connector',
            style: {
               ...NODE_STYLES,
               ...MIDDLE_NODE_STYLES,
               ...themedStyle
            }
         },
         {
            id: '3',
            type: 'output',
            targetPosition: 'left',
            sourcePosition: 'right',
            data: {
               label: (
                  <TargetNode
                     provider={integration?.serviceProfile}
                     subOrg={integration?.subOrganization?.name}
                  />
               )
            },
            position: { x: 400, y: 0 },
            className: 'target border-none',
            style: { ...MERGED_NODE_STYLES, ...themedStyle }
         },
      ]);
      state === StateEnums.notAvailable && nodeList.splice(1, 1);

      return nodeList;
   }, [state, orgId, serviceProfile, themedStyle]);

   const edgeProps = useMemo(() => {
      return (
         {
            style: {
               stroke: state !== StateEnums.available ? STATES.ERROR : STATES.SUCCESS,
               strokeWidth: EDGE_STYLES.strokeWidth
            }
         }
      )
   }, [state])

   const edges: any = useMemo(() => {

      if (state === StateEnums.notAvailable) {
         return [
            { id: 'e1->2', source: '1', type: 'custom', target: '3', ...edgeProps },
         ]
      } else {
         return (
            [
               { id: 'e1->2', source: '1', target: '2', ...edgeProps },
               { id: 'e2->3', source: '2', target: '3', ...edgeProps }
            ]
         )
      }

   }, [state, nodes]);

   const edgeTypes: any = {
      custom: ErrorEdges,
   };

   const [ReactFlow, setReactFlow] = useState<any>(null);

   useEffect(() => {
      import("react-flow-renderer").then((module) => {
         setReactFlow(module.default);
      });
   }, []);

   return (
      <Box>

         <MainCard sx={{
            backgroundImage: 'url(https://unizopublicpaas.blob.core.windows.net/imgs/bg-abstract-blocks.png)',
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
            // height: '26vh',

         }}>
            <Stack justifyContent={'flex-end'}>
               <div className="ml-auto">
                  <DeleteIntegration />
               </div>
            </Stack>
            <Stack height={140}>
               {ReactFlow ? (
                  <ReactFlow
                     connectionMode={ConnectionMode.Loose}
                     nodes={nodes}
                     edgeTypes={edgeTypes}
                     edges={edges}
                     {...OPTIONS}
                  />
               ) : null}
            </Stack>
         </MainCard>
      </Box>
   )
})