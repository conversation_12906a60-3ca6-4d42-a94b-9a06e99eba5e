import { LogProtectionConnectorType } from "constants/log-protection";
import { useGetSecurity } from "hooks/api/security/useGetKeyProtection";
import React, { RefObject, createContext, useCallback, useContext, useMemo } from "react";
import { LogProtectionNamespace } from "types/log-protection";
import { Service } from "types/service";
import customFields from "./integration-list/create/step-contents/integration-details/standard/custom-fields";
import _ from "lodash";
import useUserDetails from "store/user";
import { AccessPointConfigType } from "hooks/api/use-accesspoint-config/useGetAccessPoint";
import { hasTiers } from "./helper";
import { AVAILABLE_SUBSCRIPTION_TIERS } from "./constant";

const defaultValues: Partial<Values> = {
   actionRefs: {
      integrationDetails: React.createRef()
   }
}

const IntegrationCreateContext = createContext<Partial<Values>>(defaultValues);

interface Props {
   children: React.ReactNode,
   value: { hasEnterpriseTier: boolean }
};

type CreateIntegrationConfig = {
   selectedService: Service,
   updated: LogProtectionNamespace.IntegrationConnectorConfig
}

type Values = {
   actionRefs: {
      integrationDetails: RefObject<HTMLFormElement>
   }
   getIsIntegrationExist: (serviceId: string) => Record<string, any>
   getIsWebhookConfigExist: () => Record<string, any>
   getIsIntegrationConfigExist: (serviceId: string) => Record<string, any>
   getIsWebhookConfigIndexExist: () => number

   updateWebhookConfig: (updated: LogProtectionNamespace.WebhookConfig, onSuccess: VoidFunction) => void
   createIntegrationConfig: (params: CreateIntegrationConfig, onSuccess: VoidFunction) => void
   getIsServiceHaveIntegration: (id: Service['id']) => Record<string, any> | undefined

   isWebhookExist: boolean
   isIntegrationExist: boolean
   isIntegrationConfigExceed: boolean
   hasEnterpriseTier: boolean

} & ReturnType<typeof useGetSecurity>

export default ({ value, ...props }: Props) => {
   const { subscriptions = [] } = useUserDetails();

   const hasEnterpriseTier = hasTiers(subscriptions, AVAILABLE_SUBSCRIPTION_TIERS);

   const security = useGetSecurity({
      hasEnterpriseTier: hasEnterpriseTier
   });

   const { user } = useUserDetails()

   const { attemptUpdateKeyProtection, attemptCreateIntegration, attemptUpdateIntegration } = security;

   const { data: keyProtections } = security?.searchKeyProtections();

   const getIsWebhookConfigExist = useCallback(() => {
      return keyProtections?.connectorTypeConfigs?.find((i: any) => i?.type === LogProtectionConnectorType.Webhook)
   }, [keyProtections]);

   const getIsWebhookConfigIndexExist = useCallback(() => {
      return keyProtections?.connectorTypeConfigs?.findIndex((i: any) => i?.type === LogProtectionConnectorType.Webhook)
   }, [keyProtections]);

   const getIsIntegrationConfigExist = useCallback((integrationId: string) => {
      return integrationId ? (
         keyProtections?.connectorTypeConfigs?.find((i: any) => (
            i?.integration?.id === integrationId
         ))
      ) : undefined
   }, [keyProtections]);

   const getIsServiceHaveIntegration = useCallback((serviceId: Service['id']): Record<string, any> => {
      return keyProtections?.connectorTypeConfigs?.find(
         (i: LogProtectionNamespace.IntegrationConfig) => i?.service?.id === serviceId)
   }, [keyProtections])

   const isWebhookExist = useMemo(() => {
      return keyProtections?.connectorTypeConfigs?.map((i: any) => i?.type)?.includes(LogProtectionConnectorType.Webhook)
   }, [keyProtections]);

   const isIntegrationExist = useMemo(() => {
      return keyProtections?.connectorTypeConfigs?.map((i: any) => i?.type)?.includes(LogProtectionConnectorType.Integration)
   }, [keyProtections]);

   const getOnlyIntegrationTypeConfigs = useMemo(() => {
      return keyProtections?.connectorTypeConfigs?.filter((i: any) => i?.type === LogProtectionConnectorType.Integration) ?? []
   }, [keyProtections?.connectorTypeConfigs]);

   const isIntegrationConfigExceed = useMemo(() => {
      return !!getOnlyIntegrationTypeConfigs?.length
   }, [keyProtections?.connectorTypeConfigs, getOnlyIntegrationTypeConfigs])

   const updateWebhookConfig = (
      updated: LogProtectionNamespace.WebhookConfig,
      onSuccess: VoidFunction
   ) => {

      const ROOT_PATH = `/connectorTypeConfigs${isWebhookExist ? `/${getIsWebhookConfigIndexExist()}` : '/-'}`
      const CONTAINER_PATH = 'webhookConfig';

      let value = {
         type: LogProtectionConnectorType.Webhook,
         [CONTAINER_PATH]: updated
      }


      attemptUpdateKeyProtection([{
         op: isWebhookExist ? 'replace' : 'add',
         path: ROOT_PATH,
         value
      }], keyProtections?.id, () => {
         typeof onSuccess === 'function' && onSuccess()
      })
   }

   const createIntegrationConfig = (
      params: CreateIntegrationConfig,
      onSuccess: VoidFunction
   ) => {

      const { updated, selectedService } = params as any;

      const existingIntegration = getIsServiceHaveIntegration(selectedService.id);

      if (!existingIntegration) {
         const customPaths = _.map(customFields, 'property');
         const customValues: Record<string, any> = {}

         const result = Object.keys(updated).reduce((acc, jsonPath) => {
            // Clean the path by removing leading slash if present
            const cleanPath = jsonPath.startsWith("/") ? jsonPath.slice(1) : jsonPath;

            if (customPaths.includes(jsonPath)) {
               customValues[cleanPath] = updated[jsonPath] as any
               return acc;
            }

            const dotNotationPath = cleanPath.split("/").join(".");

            _.set(acc, dotNotationPath, updated[jsonPath] as any);

            return acc;
         }, {});

         const payload = {
            name: customValues?.name,
            type: "KMS",
            subOrganization: {
               name: user?.organization?.name,
               externalKey: user?.organization?.id
            },
            target: {
               accessPoint: {
                  type: "SP",
                  service: {
                     id: selectedService?.id
                  },
                  accessPointTypeConfig: {
                     type: AccessPointConfigType.OAuthPasswordFlow
                  },
                  ...result,
               }
            }
         };

         attemptCreateIntegration(keyProtections?.id, payload, ({ data: { id } }: Record<string, any>) => {

            const ROOT_PATH = "/vaultConfigs";
            const payload = [
               {
                  "op": "add",
                  "path": ROOT_PATH,
                  "value": [
                     {
                        "type": LogProtectionConnectorType.Integration,
                        "integration": {
                           id
                        },
                        "service": {
                           "id": selectedService?.id
                        },
                        "serviceProfile": {
                           "id": selectedService?.serviceProfile?.id
                        },
                        "accountId": "********",
                        "collectionId": "********"
                     }
                  ]
               }
            ]

            // After successfully creating the integration, update log protection
            attemptUpdateKeyProtection(
               payload,
               keyProtections?.id,
               () => {
                  typeof onSuccess === 'function' && (
                     onSuccess()
                  )
               }
            );
         })
      } else {
         const ROOT_PATH = '/target'
         const CONTAINER_PATH = 'accessPoint';
         const CUSTOM_NAME_PATH = '/name';

         let payload: Record<string, any> = {}

         Object.entries(updated)?.forEach(([key, value]) => {
            const keys = key.split('/').filter(Boolean);
            const result = {};
            let current: any = result;

            if (CUSTOM_NAME_PATH !== key) {
               keys.forEach((key, index) => {
                  current[key] = index === keys.length - 1 ? value : {};
                  current = current[key];
               });
            }
            payload = { ...payload, ...result }
         })

         payload = [
            {
               op: "replace",
               path: CUSTOM_NAME_PATH,
               value: updated?.[CUSTOM_NAME_PATH]
            },
            {
               op: "replace",
               path: ROOT_PATH,
               value: {
                  [CONTAINER_PATH]: {
                     type: "SP",
                     service: {
                        id: selectedService?.id
                     },
                     accessPointTypeConfig: {
                        type: AccessPointConfigType.APIKeyFlow
                     },
                     ...payload
                  }
               }
            }
         ];

         attemptUpdateIntegration(
            keyProtections?.id,
            existingIntegration?.integration?.id as any,
            payload,
            () => {
               typeof onSuccess === 'function' && (
                  onSuccess()
               )
            }
         )
      }

   }

   return (
      <IntegrationCreateContext.Provider
         value={{
            ...defaultValues,
            ...security,
            ...value,

            getIsWebhookConfigExist,
            getIsIntegrationConfigExist,
            getIsWebhookConfigIndexExist,

            // function for updated the webhook config
            updateWebhookConfig,
            // function for updated the integration config
            createIntegrationConfig,

            getIsServiceHaveIntegration,

            isWebhookExist,
            isIntegrationExist,
            isIntegrationConfigExceed
         }}
         {...props}
      />
   )
}

export const useIntegrationCreateProvider = (): Values => {
   try {
      return useContext(IntegrationCreateContext) as any;
   } catch (error) {
      console.error(`should be use with <IntegrationCreationCreateProvider />`)
      return null as any
   }
}