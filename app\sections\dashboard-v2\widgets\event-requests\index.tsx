import ModernChartCard from "components/cards/ModernChartCard"
import { CONTAINER_STYLE_S } from "sections/dashboard-v2/constant";

import { GadgetConfig } from "../../layout/grid-type";
// chart
import { Box } from "@mui/material";
import Chart from "./chart";
import { memo } from "react";

const options: any = {
   responsive: true,
   plugins: {
      legend: {
         position: 'top',
      },
      title: {
         display: true,
      },
   },
   scales: {
      y: {
         beginAtZero: true // This ensures the Y-axis starts at 0
      }
   }
};

export default memo(({ gadget }: GadgetConfig) => {

   return (
      <ModernChartCard
         title={gadget.name || "Event Requests"}
         subtitle="Event processing analytics"
      >
         <Box sx={{ pr: 2 }}>
            <Chart />
         </Box>
      </ModernChartCard>
   )
})