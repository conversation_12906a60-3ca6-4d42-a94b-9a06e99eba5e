import { useEffect, useMemo, useRef, useState } from "react";

import { CheckOutlined, EyeInvisibleOutlined, EyeOutlined, LineOutlined } from "@ant-design/icons"
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from 'react-hook-form';

import {
   Box,
   Grid,
   List,
   Stack,
   Typography,
   ListItem,
   ListItemText,
   ListItemIcon,
   FormControl,
   Button,
   TextField
} from "@mui/material"
import { Form, FormField, FormItem } from "components/@extended/Form";
import { isNumber, isLowercaseChar, isUppercaseChar, isSpecialChar, minLength } from 'utils/password-validation';
import { z } from "zod";
import useUserDetails from "store/user";
import { useGetUser } from "hooks/api/user/useUser";
import { User } from "types/user";

const FormSchema: any = z.object({
   firstName: z
      .string()
      .nonempty("Please enter your first name")
      .regex(/^[a-zA-Z0-9]+$/, "First name can only contain letters and numbers")
      .max(15, "First name cannot be longer than 15 characters"),
   lastName: z
      .string()
      .nonempty("Please enter your last name")
      .regex(/^[a-zA-Z0-9]+$/, "Last name can only contain letters and numbers")
      .max(15, "Last name cannot be longer than 15 characters"),
   email: z
      .string()
      .email("Please enter a valid email address")
      .nonempty("Email is required"),
});

type FormValues = z.infer<typeof FormSchema>;

const defaultValues: Partial<FormValues> = {
   firstName: '',
   lastName: '',
   email: '',
}

export default () => {

   const form = useForm<FormValues>({
      resolver: zodResolver(FormSchema),
      defaultValues,
      mode: "onChange",
   }),
      { formState: { dirtyFields, isDirty }, reset, getValues } = form;

   const { user, setUser } = useUserDetails();

   const { attemptUpdateUserDetails } = useGetUser({});

   const formRef = useRef<HTMLFormElement>(null);

   const onSubmit = () => {
      if (user) {
         const allValues: any = getValues(),
            payload: Record<string, any> = [];

         Object.keys((dirtyFields || {})).forEach((key) => {
            payload.push(
               { op: "replace", path: `/${key}`, value: allValues[key] }
            )
         })
         payload?.length && (
            attemptUpdateUserDetails(user?.id, payload, (updated: User) => {
               setUser(updated)
            })
         )
      }
   }

   useEffect(() => {
      const { firstName, lastName, email } = user ?? defaultValues;
      reset({ firstName, lastName, email })
   }, [user])

   return (
      <Stack>
         <Grid container spacing={6}>
            <Grid item xs={12} >
               <Form {...form}>
                  <Stack
                     component={'form'}
                     onSubmit={(...args) => (
                        void form.handleSubmit(onSubmit)(...args)
                     )}
                     ref={formRef}
                     gap={3}
                  >
                     <Grid container spacing={3}>
                        <Grid item xs={12} sm={6}>
                           <FormField
                              control={form.control}
                              name='firstName'
                              render={({ field }) => (
                                 <FormItem label='First Name'>
                                    <FormControl>
                                       <TextField placeholder="Enter Password" {...field} />
                                    </FormControl>
                                 </FormItem>
                              )}
                           />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                           <FormField
                              control={form.control}
                              name='lastName'
                              render={({ field }) => (
                                 <FormItem label='Last Name'>
                                    <FormControl>
                                       <TextField placeholder="Enter Last Name" {...field} />
                                    </FormControl>
                                 </FormItem>
                              )}
                           />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                           <FormField
                              control={form.control}
                              name='email'
                              render={({ field }) => (
                                 <FormItem label='Email'>
                                    <FormControl>
                                       <TextField disabled placeholder="Enter Email" {...field} />
                                    </FormControl>
                                 </FormItem>
                              )}
                           />
                        </Grid>
                     </Grid>
                  </Stack>
               </Form>
            </Grid>
            <Grid item xs={12}>
               <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
                  <Button variant="outlined" color="secondary">
                     Cancel
                  </Button>
                  <Button
                     type="submit"
                     variant="contained"
                     onClick={() => {
                        formRef.current?.requestSubmit()
                     }}
                     disabled={!isDirty}
                  >
                     Save
                  </Button>
               </Stack>
            </Grid>
         </Grid>
      </Stack>
   )
}

type PasswordStateProps = {
   password?: string
}

function PasswordState({ password }: PasswordStateProps) {

   return (
      <Box >
         <Typography variant="h5">New password must contain:</Typography>
         <List sx={{ p: 0, mt: 1 }}>
            <ListItem divider>
               <ListItemIcon sx={{ color: minLength(password) ? 'success.main' : 'inherit' }}>
                  {minLength(password) ? <CheckOutlined /> : <LineOutlined />}
               </ListItemIcon>
               <ListItemText primary="At least 8 characters" />
            </ListItem>
            <ListItem divider>
               <ListItemIcon sx={{ color: isLowercaseChar(password) ? 'success.main' : 'inherit' }}>
                  {isLowercaseChar(password) ? <CheckOutlined /> : <LineOutlined />}
               </ListItemIcon>
               <ListItemText primary="At least 1 lower letter (a-z)" />
            </ListItem>
            <ListItem divider>
               <ListItemIcon sx={{ color: isUppercaseChar(password) ? 'success.main' : 'inherit' }}>
                  {isUppercaseChar(password) ? <CheckOutlined /> : <LineOutlined />}
               </ListItemIcon>
               <ListItemText primary="At least 1 uppercase letter (A-Z)" />
            </ListItem>
            <ListItem divider>
               <ListItemIcon sx={{ color: isNumber(password) ? 'success.main' : 'inherit' }}>
                  {isNumber(password) ? <CheckOutlined /> : <LineOutlined />}
               </ListItemIcon>
               <ListItemText primary="At least 1 number (0-9)" />
            </ListItem>
            <ListItem>
               <ListItemIcon sx={{ color: isSpecialChar(password) ? 'success.main' : 'inherit' }}>
                  {isSpecialChar(password) ? <CheckOutlined /> : <LineOutlined />}
               </ListItemIcon>
               <ListItemText primary="At least 1 special characters" />
            </ListItem>
         </List>
      </Box>
   )
}