import { Shield, Cable, TestTube2, Rocket } from 'lucide-react';

export interface GettingStartedStep {
  id: number;
  title: string;
  description: string;
  icon: any;
  status?: 'completed' | 'active' | 'pending';
  action?: {
    label: string;
    href?: string;
    onClick?: () => void;
  };
  details?: {
    title: string;
    content: string;
    statusText?: string;
    statusType?: 'success' | 'info' | 'warning';
    connectorInfo?: {
      name: string;
      logo: string;
      category: string;
    };
  };
}

export const gettingStartedSteps: GettingStartedStep[] = [
  {
    id: 1,
    title: 'Enable connectors',
    description: 'Make connectors available to your users.',
    icon: Cable,
    status: 'completed',
    details: {
      title: 'Connectors',
      content: 'The connectors you select become available to your users in Connect UI and can be used to push and pull data from using our Unified APIs.',
      statusText: 'You have enabled 1 connector',
      statusType: 'success',
      connectorInfo: {
        name: '<PERSON>ra',
        logo: 'https://cdn.simpleicons.org/jira/0052CC',
        category: 'Issue Tracking API',
      },
    },
  },
  {
    id: 2,
    title: 'Create a test consumer',
    description: 'Create a sandbox user and authorize a connection.',
    icon: Shield,
    status: 'active',
    action: {
      label: 'I have an account',
    },
    details: {
      title: 'Create a sandbox account',
      content: "For the following steps you'll need an account for at least one of the connectors you've enabled. If you haven't already, visit one of these third-party platforms and create a free account.",
      statusText: 'Suggested connectors with a free trial',
      statusType: 'info',
      connectorInfo: {
        name: 'Jira',
        logo: 'https://cdn.simpleicons.org/jira/0052CC',
        category: 'Click any of these links to go to the sign up page and create an account.',
      },
    },
  },
  {
    id: 3,
    title: 'Configure Event Listener specific to API Category ',
    description: 'Make an API request with one of our Unified APIs.',
    icon: TestTube2,
    status: 'pending',
  },
  {
    id: 4,
    title: 'Connect Unizo to your Product',
    description: 'Enable secure API access and decide how authentication is handled in your app.',
    icon: Rocket,
    status: 'pending',
  },
];

export interface ConnectorSuggestion {
  id: string;
  name: string;
  logo: string;
  signUpUrl: string;
}

export const suggestedConnectors: ConnectorSuggestion[] = [
  {
    id: 'jira',
    name: 'Jira',
    logo: 'https://cdn.simpleicons.org/jira/0052CC',
    signUpUrl: 'https://www.atlassian.com/software/jira/free',
  },
];

// Statistics for the dashboard
export interface DashboardStats {
  connectorsEnabled: number;
  apiCallsMade: number;
  activeIntegrations: number;
}

export const dashboardStats: DashboardStats = {
  connectorsEnabled: 1,
  apiCallsMade: 0,
  activeIntegrations: 0,
};