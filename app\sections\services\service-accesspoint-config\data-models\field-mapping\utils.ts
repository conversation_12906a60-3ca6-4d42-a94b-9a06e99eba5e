import { ServiceProfileSpecificationFieldType } from "constants/service-profile";
import { AdditionalFields } from "types/additional-attributes";
import { ServiceProfileSpecifications } from "types/service-profile-specification";
import { MappingValues } from "./type";

export const getServiceProfileSpecificationFields = (serviceProfileSpecification: ServiceProfileSpecifications.Root) => {
   return serviceProfileSpecification?.fields ?? []
}

export const buildMenuOptions = (fields: ServiceProfileSpecifications.Fields[] | AdditionalFields.Root[],
   type: 'specification' | 'additional-attribute') => {

   if (type === 'specification') {
      return (
         (fields as ServiceProfileSpecifications.Fields[]).map((i) =>
            ({ label: i.name, value: i.name, type: i.type }))
      )
   }

   return (fields as AdditionalFields.Root[]).map((i) =>
      ({ label: i.name, value: i.name, type: i?.dataType?.type }))
}

export const getSourceFieldType = (fieldValue: ServiceProfileSpecifications.Fields['name'], fields: ServiceProfileSpecifications.Fields[]): ServiceProfileSpecificationFieldType => {
   const field = fields.find((f) => f.name === fieldValue);
   return field?.type as ServiceProfileSpecificationFieldType;
};

export const getTargetFieldType = (fieldValue: ServiceProfileSpecifications.Fields['name'], fields: AdditionalFields.Root[]): ServiceProfileSpecificationFieldType => {
   const field = fields.find((f) => f.name === fieldValue);
   return field?.dataType?.type as ServiceProfileSpecificationFieldType;
};

export const getIsObjectFieldType = (fieldValue: ServiceProfileSpecifications.Fields['name'], fields: ServiceProfileSpecifications.Fields[]) => {
   return fieldValue && getSourceFieldType(fieldValue, fields) === ServiceProfileSpecificationFieldType.Object;
}

export const getFlattendedSourceFields = (fields: ServiceProfileSpecifications.Fields[]) => {
   const tempArr = [] as ServiceProfileSpecifications.Fields[];
   fields.forEach((field) => {
      tempArr.push(field);
      if (field.children?.length) {
         tempArr.push(...getFlattendedSourceFields(field.children || []))
      }
   })

   return tempArr;
}



export const getValidationError = (mapping: MappingValues,
   flatSourceFields: ServiceProfileSpecifications.Fields[], flatTargetFields: AdditionalFields.Root[]) => {
   if (!mapping.source || !mapping.target) return null;

   const sourceType = getSourceFieldType(mapping.source, flatSourceFields);
   const targetType = getTargetFieldType(mapping.target, flatTargetFields);

   if (sourceType !== targetType) {
      return `Type mismatch: Cannot map ${sourceType} to ${targetType}`;
   }
   return null;
}

export const getFlattanedSourceFields = (sourceFields: ServiceProfileSpecifications.Fields[]) => {
   const tempArr: ServiceProfileSpecifications.Fields[] = [];

   sourceFields.forEach((sourceField) => {
      tempArr.push(sourceField);
      if (sourceField?.properties?.length) {
         tempArr.push(...getFlattanedSourceFields(sourceField?.properties))
      }
      if (sourceField?.items?.properties?.length) {
         tempArr.push(...getFlattanedSourceFields(sourceField?.items?.properties))
      }
   })

   return tempArr

}

export const getFlattendedTargetFields = (fields: AdditionalFields.Root[]) => {
   const tempArr = [] as AdditionalFields.Root[];
   fields.forEach((field) => {
      tempArr.push(field);
      if (field.children?.length) {
         tempArr.push(...getFlattendedTargetFields(field.children || []))
      }
   })

   return tempArr;
}