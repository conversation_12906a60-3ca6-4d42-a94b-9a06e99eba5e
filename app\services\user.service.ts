import axios from "axios";
import { API_ENDPOINTS } from "utils/api/api-endpoints";
import platformfetchInstance from "utils/api/fetchinstance/platform-fetch-Instance";
import { getFetchConfigsHeaders } from "lib/fetch";



export const userClient = {
  getUser: (id: string) => {
    return platformfetchInstance.get(`${API_ENDPOINTS.USERS}/${id}`);
  },
  searchUserEmail:( payload: Record<string, any>)=>{
    return platformfetchInstance.post(`${API_ENDPOINTS.USERS}/search`, payload);
  },
  getAllUser: (payload: Record<string, any>) => {
    return platformfetchInstance.post(`${API_ENDPOINTS.USERS}/search`, payload);
  },
  inviteBulkUsers: (payload: Record<string, any>) => {
    return platformfetchInstance.post(`${API_ENDPOINTS.USERS}/bulk`, payload);
  },
  updateRoleToUsers: (id: string, payload: Record<string, any>) => {
    return platformfetchInstance.patch(`${API_ENDPOINTS.USERS}/${id}`, payload)
  },
  searchSubscriptions: (payload: Record<string, any>) => {
    return platformfetchInstance.post(`${API_ENDPOINTS.SUBSCRIPTION}s/search`, payload)
  },
  updateUserDetails: (id: string, payload: Record<string, any>) => {
    return platformfetchInstance.patch(`${API_ENDPOINTS.USERS}/${id}`, payload)
  },
  updateUserPassword: (id: string, payload: Record<string, any>) => {
    return platformfetchInstance.post(`${API_ENDPOINTS.USERS}/${id}/actions`, payload)
  },
  logout: () => {
    return axios.get('/logout', {
      headers: getFetchConfigsHeaders()
    })
  }
};
