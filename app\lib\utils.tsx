import clsx, { ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

type ErrorProps = { [key: string]: any } & { [key: string]: any }[]
export const parseError = (error: ErrorProps) => {

  if (Array.isArray(error)) {
    if (error.length) {
      const [first] = error;

      return {
        message: first?.details || first?.errorMessage || first?.error,
        status: first?.statusCode || first?.status
      }

    }
  }

  return {
    message: error?.details || error?.errorMessage || error?.error || 'Something went wrong!',
    status: error?.statusCode || error?.status
  }
}


export const getIsValidEmail = (email: string) => {
  const regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return regex.test(email)
}

export function extractProperties<T>(arr: Array<T>, property: string) {
  return arr.reduce((acc: any, cur: any) => {
       if (cur) {
            acc.push(cur[property]);
       }
       return acc;
  }, [])
}
