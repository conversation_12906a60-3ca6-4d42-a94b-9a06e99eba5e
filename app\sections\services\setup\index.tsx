import React from 'react';
import { CircularProgress, Grid, Stack, useMediaQuery, OutlinedInput, Skeleton } from "@mui/material"

import { Menu as ExtendedMenu, MenuSkeleton } from "components/@extended/Menu";
import { ResponsiveGrid } from "components/@extended/ResponsiveGrid"

import InputAdornment from "@mui/material/InputAdornment";

import { SearchOutlined } from "@ant-design/icons";

import ServiceList from './list';
import MdMenu from './Menu-md';
import ContentHeader from "./content-header";
import { RIGHT_GRID, LEFT_GRID } from "./constant";
import DetailedPanel from "./detailed-panel";

// hook for manage the profile details
import useManageProfile, { ServiceProvider } from "../service-context/use-manage-profile";
import useUserDetails from "store/user";

const Container = () => {

   const { isSubscriptionLoading } = useUserDetails();

   const {
      domain,
      setDomain,

      menu,

      isProfileLoading,
      profiles,
      selectedIndex,
      selectedService,

      createService,
      updateService,

      onSearch,
      onSelectService,

      refetchAll,
   } = useManageProfile()


   const matchDownMD = useMediaQuery((theme: any) => theme?.breakpoints?.down('md'));

   return (
      <Stack direction={'row'} gap={3} component={'div'} >
         <Grid container spacing={4}>
            {/* LEFT */}
            <Grid item {...LEFT_GRID}>

               {isSubscriptionLoading ? (
                  <Stack gap={2}>
                     <MenuSkeleton />
                     <MenuSkeleton />
                     <MenuSkeleton />
                  </Stack>
               ) : (
                  matchDownMD ? (
                     <MdMenu
                        setDomain={setDomain}
                        domain={domain}
                        items={menu}
                     />
                  ) : (
                     <ExtendedMenu
                        items={menu}
                        selected={domain}
                        onValueChange={({ id }: any) => setDomain(id)}
                     />
                  )
               )}

            </Grid>

            {/* RIGHT */}
            <Grid item {...RIGHT_GRID}>
               <Stack gap={3} className="w-full">

                  {/* header bar */}
                  <ContentHeader
                     onReload={() => (
                        void refetchAll()
                     )}
                     domain={domain}
                     menu={menu}
                  />

                  {/* search bar */}
                  <OutlinedInput
                     placeholder="Search connectors"
                     startAdornment={
                        <InputAdornment position="start">
                           <SearchOutlined />
                        </InputAdornment>
                     }
                     onChange={({ target: { value } }) => {
                        onSearch(value);
                     }}
                  />

                  {isProfileLoading ? (
                     <div className="w-full justify-center flex">
                        <CircularProgress className="w-full" />
                     </div>
                  ) : (
                     <ResponsiveGrid
                        selectedIndex={selectedIndex as number}
                        detailedPanel={
                           selectedService ? (
                              <DetailedPanel />
                           ) : null
                        }
                        tiles={(
                           <ServiceList
                              createService={createService}
                              updateService={updateService}
                              profiles={profiles}
                              onSelect={onSelectService}
                           />
                        ) as any}
                     />
                  )}
               </Stack>
            </Grid>
         </Grid>

      </Stack>
   )
}

export default () => {
   return (
      <ServiceProvider>
         <Container />
      </ServiceProvider>
   )
}
