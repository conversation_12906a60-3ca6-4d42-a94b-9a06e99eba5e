import React, { useState } from "react";
import { DialogTitle, DialogContent, Typography, TextField, IconButton, Button, Tooltip } from "@mui/material";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import OpenInNewIcon from "@mui/icons-material/OpenInNew";
import { Stack } from "@mui/system";
import { DialogClose, Dialog } from "components/@extended/dialog";

const ConnectUIModal = ({ opened, onClosed, magicLink }: any) => {
  const [copied, setCopied] = useState(false);
  const [tooltipTitle, setTooltipTitle] = useState("Copy");

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(magicLink);
      setCopied(true);
      setTooltipTitle("Copied!");
      
      // Reset tooltip text after 2 seconds
      setTimeout(() => {
        setCopied(false);
        setTooltipTitle("Copy");
      }, 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  return (
    <Dialog 
      open={opened} 
      onClose={(event, reason) => {
        if (reason === "backdropClick") return; // Prevent closing on backdrop click
        onClosed();
      }}
      maxWidth="sm" 
      fullWidth
    >
      <DialogTitle variant="h5">Test your Connect UI</DialogTitle>
      <DialogClose onClose={onClosed} />
      <DialogContent dividers>
        <Typography variant="body1" gutterBottom>
          Use this link to validate the Unizo Connect UI experience for your customers and troubleshoot configuration. This URL will expire in 30 minutes.
        </Typography>
        <TextField
          fullWidth
          variant="outlined"
          value={magicLink}
          InputProps={{
            readOnly: true,
            endAdornment: (
              <>
                <Tooltip title={tooltipTitle} placement="top" arrow>
                  <IconButton onClick={handleCopy} size="small">
                    <ContentCopyIcon />
                  </IconButton>
                </Tooltip>
              </>
            ),
          }}
        />

        <Stack direction="row" justifyContent="flex-end" sx={{ mt: 2 }}>
          <Button 
            variant="outlined" 
            sx={{ width: "120px" }}
            component="a"
            href={magicLink}
            target="_blank"
            rel="noopener noreferrer"
            endIcon={<OpenInNewIcon />}
          >
            Run Test
          </Button>
        </Stack>
      </DialogContent>
    </Dialog>
  );
};

export default ConnectUIModal;