import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'sonner';
import { type MetaFunction, LoaderFunction } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import QuickStartPage from 'pages/quick-start-v2';
import ThemeCustomization from 'themes';
import { authenticateRequest } from 'utils/auth.server';

// Create a query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

export const meta: MetaFunction = () => {
  return [
    { title: "Quick Start | Unizo" },
    { name: "description", content: "Set up your Unizo workspace" },
  ];
};

export const loader: LoaderFunction = async ({ request }) => {
  // Check if user is authenticated
  const user = await authenticateRequest(request);

  // This standalone route can be accessed during onboarding
  // It should handle both authenticated and unauthenticated states
  return {
    isAuthenticated: !!user,
    user,
  };
};

/**
 * Standalone quick-start route for onboarding flow
 * Can be embedded in the signup process or accessed directly
 */
export default function QuickStart() {
  const { isAuthenticated } = useLoaderData<typeof loader>();

  return (
    <QuickStartPage />
  )

  // return (
  //   <QueryClientProvider client={queryClient}>
  //     <ThemeCustomization>
  //       <CssBaseline />
  //       <QuickStartPage />
  //       <Toaster
  //         position="top-right"
  //         toastOptions={{
  //           style: {
  //             background: '#333',
  //             color: '#fff',
  //           },
  //         }}
  //       />
  //     </ThemeCustomization>
  //   </QueryClientProvider>
  // );
}