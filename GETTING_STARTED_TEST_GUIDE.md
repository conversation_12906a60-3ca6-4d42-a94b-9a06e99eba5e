# Getting Started Flow - Test Guide

## Testing Different Flows

The getting started flow now supports two distinct modes:

### 1. Trial Account Mode (Default)
- Users can freely select any API categories
- All categories are clickable and selectable
- Standard flow for new trial users

### 2. Predefined Tenant Mode
- Categories are pre-selected based on tenant configuration
- Pre-selected categories are locked and cannot be changed
- Visual indicators show locked state (lock icon, info message)
- Different messaging throughout the flow

## How to Test

### Method 1: Using the Test Mode Selector UI
1. Navigate to: `http://localhost:8000/test-getting-started`
2. Use the Test Mode selector panel on the right side
3. Switch between different modes:
   - **Trial Account**: Free selection mode
   - **Basic Tenant**: Pre-selected SCM and Ticketing
   - **Enterprise Tenant**: Pre-selected SCM, Ticketing, PCR, and Incident
   - **Security Focused**: Pre-selected VMS, Identity, and EDR
   - **Infrastructure**: Pre-selected Infra, Monitoring, and Incident

### Method 2: Using URL Parameters
Access the page with different query parameters:
- Trial mode: `http://localhost:8000/test-getting-started`
- Basic tenant: `http://localhost:8000/test-getting-started?mode=basic`
- Enterprise: `http://localhost:8000/test-getting-started?mode=enterprise`
- Security: `http://localhost:8000/test-getting-started?mode=security`
- Infrastructure: `http://localhost:8000/test-getting-started?mode=infra`

## Visual Differences

### Trial Account Mode
- Header: "Which API categories would you like to start with?"
- Categories are fully interactive
- Next button shows "Next"
- No lock icons or info messages

### Predefined Tenant Mode
- Header: "Your account has been pre-configured with specific API categories"
- Info box shows pre-configured categories message with Shield icon
- Lock icons appear next to pre-selected category names
- Pre-selected categories have check marks and cannot be unselected
- Non-selected categories are grayed out and disabled
- Button shows "Continue" instead of "Next"
- Service Configuration page shows "Configure Connectors" and "Complete Setup"

## Implementation Details

### Context Updates
- Added `predefinedCategories` and `isPredefinedTenant` to GettingStartedContext
- Added `setPredefinedConfig` method to update configuration

### Component Updates
- CategorySelection: Handles locked states and visual indicators
- ServiceConfiguration: Updates messaging for predefined tenants
- Test page: Provides easy switching between modes

### Testing Checklist
- [ ] Trial mode allows free selection of categories
- [ ] Predefined mode shows locked categories correctly
- [ ] Lock icons appear for predefined categories
- [ ] Info message displays with correct categories
- [ ] Non-selected categories are disabled in predefined mode
- [ ] Button text changes appropriately
- [ ] Service configuration page reflects the mode
- [ ] URL parameters work correctly
- [ ] UI mode selector updates the flow immediately