import PropTypes from 'prop-types';
import { useRef, useState } from 'react';
import { useNavigate } from '@remix-run/react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import ButtonBase from '@mui/material/ButtonBase';
import CardContent from '@mui/material/CardContent';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import Stack from '@mui/material/Stack';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';

// project import
import ProfileTab from './ProfileTab';
import SettingTab from './SettingTab';
import Avatar from 'components/@extended/Avatar';
import ProfileAvatar from 'components/@extended/ProfileAvatar';
import MainCard from 'components/MainCard';
import Transitions from 'components/@extended/Transitions';
import IconButton from 'components/@extended/IconButton';

import { ThemeDirection, ThemeMode } from 'config';

// assets
import LogoutOutlined from '@ant-design/icons/LogoutOutlined';
import SettingOutlined from '@ant-design/icons/SettingOutlined';
import UserOutlined from '@ant-design/icons/UserOutlined';
import avatar1 from '/images/users/avatar-1.png';
import useUserDetails from 'store/user';
import { Divider, useMediaQuery } from '@mui/material';

// tab panel wrapper
function TabPanel({ children, value, index, ...other }) {
  return (
    <div role="tabpanel" hidden={value !== index} id={`profile-tabpanel-${index}`} aria-labelledby={`profile-tab-${index}`} {...other}>
      {value === index && children}
    </div>
  );
}

function a11yProps(index) {
  return {
    id: `profile-tab-${index}`,
    'aria-controls': `profile-tabpanel-${index}`
  };
}

// ==============================|| HEADER CONTENT - PROFILE ||============================== //

export default function Profile() {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, isSupeAdmin } = useUserDetails();


  const handleLogout = async () => {
    try {
      navigate(`/login`, {
        state: {
          from: ''
        }
      });
    } catch (err) {
      console.error(err);
    }
  };

  const anchorRef = useRef(null);
  const [open, setOpen] = useState(false);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };

  const [value, setValue] = useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const avatarTheme = {
    bg: theme?.palette?.colors?.gold[5],
    initials: user ? `${user?.firstName?.charAt(0)}${user?.lastName?.charAt(0)}`.toUpperCase() : '',
    fullName: user ? `${user?.firstName} ${user?.lastName}` : ''
  }

  const iconBackColorOpen = theme.palette.mode === ThemeMode.DARK ? 'background.default' : 'grey.100';
  const downMD = useMediaQuery((theme) => theme.breakpoints.down('md'));
  return (
    <Box sx={{ flexShrink: 0, ml: 0.75 }}>
      <ButtonBase
        sx={{
          p: 0.25,
          bgcolor: open ? iconBackColorOpen : 'transparent',
          borderRadius: 1,
          '&:hover': { bgcolor: theme.palette.mode === ThemeMode.DARK ? 'secondary.light' : 'secondary.lighter' },
          '&:focus-visible': { outline: `2px solid ${theme.palette.secondary.dark}`, outlineOffset: 2 }
        }}
        aria-label="open profile"
        ref={anchorRef}
        aria-controls={open ? 'profile-grow' : undefined}
        aria-haspopup="true"
        onClick={handleToggle}
      >
        <Stack direction="row" spacing={1.25} alignItems="center" sx={{ p: 0.5 }}>
          <ProfileAvatar size="small">
            {avatarTheme?.initials}
          </ProfileAvatar>

          {!downMD && (
            <Typography variant="subtitle1" sx={{ textTransform: 'capitalize' }}>
              {user?.firstName}
            </Typography>
          )}

        </Stack>
      </ButtonBase>
      <Popper
        placement="bottom-end"
        open={open}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        popperOptions={{
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, 9]
              }
            }
          ]
        }}
      >
        {({ TransitionProps }) => (
          <Transitions type="grow" position="top-right" in={open} {...TransitionProps}>
            <Paper sx={{ boxShadow: theme.customShadows.z1, width: 290, minWidth: 240, maxWidth: { xs: 250, md: 290 } }}>
              <ClickAwayListener onClickAway={handleClose}>
                <MainCard elevation={0} border={false} content={false}>
                  <Stack direction="row" spacing={1.25} alignItems="center" className='px-4 pt-3'>
                    <Typography variant="h6" className='truncate font-medium'>{user?.organization?.name}</Typography>
                  </Stack>

                  <CardContent sx={{ px: 2.5, pt: 2 }}>
                    <Grid container spacing={2} justifyContent="space-between" alignItems="center">

                      <Grid item>
                        <Stack direction="row" spacing={1.25} alignItems="center">
                          <ProfileAvatar size="small">
                            {avatarTheme?.initials}
                          </ProfileAvatar>
                          <Stack>
                            <Typography variant="h6">{avatarTheme.fullName}</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {user?.email}
                            </Typography>
                          </Stack>
                        </Stack>
                      </Grid>
                    </Grid>
                  </CardContent>

                  <Divider />

                  {isSupeAdmin ? (
                    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                      <Tabs variant="fullWidth" value={value} onChange={handleChange} aria-label="profile tabs">
                        <Tab
                          sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignItems: 'center',
                            textTransform: 'capitalize'
                          }}
                          icon={
                            <UserOutlined
                              style={{
                                marginBottom: 0,
                                marginRight: theme.direction === ThemeDirection.RTL ? 0 : 10,
                                marginLeft: theme.direction === ThemeDirection.RTL ? 10 : 0
                              }}
                            />
                          }
                          label="Profile"
                          {...a11yProps(0)}
                        />

                        <Tab
                          sx={{
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'center',
                            alignItems: 'center',
                            textTransform: 'capitalize'
                          }}
                          icon={
                            <SettingOutlined
                              style={{
                                marginBottom: 0,
                                marginRight: theme.direction === ThemeDirection.RTL ? 0 : 10,
                                marginLeft: theme.direction === ThemeDirection.RTL ? 10 : 0
                              }}
                            />
                          }
                          label="Admin"
                          {...a11yProps(1)}
                        />
                      </Tabs>
                    </Box>
                  ) : null}
                  <TabPanel value={value} index={0} dir={theme.direction}>
                    <ProfileTab handleLogout={handleLogout} />
                  </TabPanel>

                  {isSupeAdmin ? (
                    <TabPanel value={value} index={1} dir={theme.direction}>
                      <SettingTab />
                    </TabPanel>
                  ) : null}

                </MainCard>
              </ClickAwayListener>
            </Paper>
          </Transitions>
        )}
      </Popper>
    </Box>
  );
}

TabPanel.propTypes = { children: PropTypes.node, value: PropTypes.number, index: PropTypes.number, other: PropTypes.any };
