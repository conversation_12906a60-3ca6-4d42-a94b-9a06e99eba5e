import { ApiOutlined } from "@ant-design/icons";
import { useGetIntegration } from "hooks/api/integration/useGetIntegration";
import { DEFAULT_PAGINATION } from "constants/common";
import { GadgetConfig } from "../layout/grid-type";
import ModernStatCard from "components/cards/statistics/ModernStatCard";

export default ({ gadget }: GadgetConfig) => {
  const { search } = useGetIntegration({ orgId: null });
  const { data: resp }: any = search(DEFAULT_PAGINATION);
  const total = resp?.data?.pagination?.total ?? 0;

  return (
    <ModernStatCard
      title={gadget?.name || "Integrations"}
      value={total}
      icon={ApiOutlined}
      color="warning"
      subtitle="Active integrations"
    />
  );
};