// assets
import { Settings } from 'lucide-react';

// icons
const icons = {
  SettingOutlined: Settings,
};

export const ADMIN_BASE_URL = '/console/settings';

// ==============================|| MENU ITEMS - ADMIN ||============================== //
export default {
  id: 'admin',
  title: 'Admin',
  type: 'group',
  children: [
    {
      id: 'settings',
      title: 'Account Settings',
      description: 'Organize user accounts, determine roles and permissions, and look at your active subscription details.',
      type: 'item',
      url: '/console/settings',
      icon: icons.SettingOutlined,
      hideFor: [],
    },
  ],
};