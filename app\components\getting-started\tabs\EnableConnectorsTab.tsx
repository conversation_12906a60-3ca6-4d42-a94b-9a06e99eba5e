import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  useTheme,
  alpha,
  Grid,
  CircularProgress,
  Stack,
} from '@mui/material';
import { AlertCircle, Check, ExternalLink, Plug } from 'lucide-react';
import { useNavigate } from '@remix-run/react';
import TabContentHeader from 'components/getting-started/TabContentHeader';
import ConfigurationSection from 'components/getting-started/ConfigurationSection';
import TabNavigationFooter from 'components/getting-started/TabNavigationFooter';
import ConnectorAuthenticationDrawer from 'components/getting-started/ConnectorAuthenticationDrawer';

import { getQuickStartDomains, Domain } from 'data/domains';

// Component interfaces
export interface Connector {
  id: string;
  serviceId?: string; // The actual service ID for API calls
  name: string;
  icon: string;
  type: string;
  typeLabel: string;
  category?: string; // Domain key for grouping
  requiresConfiguration?: boolean;
  isConfigured?: boolean;
}

interface PaginationInfo {
  total: number;
  limit: number;
  offset: number;
}

interface EnableConnectorsTabProps {
  connectors?: Connector[];
  pagination?: PaginationInfo;
  onLoadMore?: () => void;
  isLoading?: boolean;
  onNext?: () => void;
  onPrevious?: () => void;
  isFirstTab?: boolean;
  isLastTab?: boolean;
  nextLabel?: string;
  previousLabel?: string;
  onRefreshConnectors?: () => void;
}

// API Response interfaces for adapter
interface ServiceProfile {
  orgServiceId: string;
  orgId: string;
  serviceId: string;
  serviceName: string;
  active: boolean;
  isBeta: boolean;
  serviceIconUrl?: string;
  category?: string;
}

interface AccessPointsSummary {
  requiresAction: boolean;
  readyForConfiguration: number;
  totalAccessPointCount: number;
}

interface ConnectorApiResponse {
  serviceProfile: ServiceProfile;
  accessPointsSummary: AccessPointsSummary;
}

// Adapter function to transform API response to component props
export const connectorApiAdapter = (apiData: ConnectorApiResponse[]): Connector[] => {
  return apiData.map((item) => {
    // Handle edge cases and determine configuration status
    const accessPointsSummary = item.accessPointsSummary;
    let isReadyToUse = true; // Default to ready if no access points summary
    
    if (accessPointsSummary) {
      // readyForConfiguration actually means "number of access points that NEED configuration"
      const needsConfigCount = accessPointsSummary.readyForConfiguration ?? 0;
      const totalCount = accessPointsSummary.totalAccessPointCount ?? 0;
      
      // Logic:
      // - Ready to use: when needsConfigCount === 0 (all access points are configured)
      // - Ready to use: when at least one is configured (totalCount - needsConfigCount > 0)
      // - Needs configuration: when needsConfigCount === totalCount (none are configured)
      
      if (totalCount === 0) {
        // No access points, ready to use
        isReadyToUse = true;
      } else if (needsConfigCount === totalCount) {
        // All access points need configuration
        isReadyToUse = false;
      } else {
        // At least one access point is configured (totalCount - needsConfigCount > 0)
        isReadyToUse = true;
      }
    }
    
    return {
      id: item.serviceProfile.serviceId,
      serviceId: item.serviceProfile.orgServiceId, // Assuming orgServiceId is the service instance ID
      name: item.serviceProfile.serviceName,
      icon: item.serviceProfile.serviceIconUrl || '',
      type: item.serviceProfile.category || 'GENERAL',
      typeLabel: getTypeLabelFromCategory(item.serviceProfile.category),
      requiresConfiguration: !isReadyToUse,
      isConfigured: isReadyToUse,
    };
  });
};

// Helper function to get type label from category
const getTypeLabelFromCategory = (category?: string): string => {
  const categoryMap: Record<string, string> = {
    TICKETING: 'Ticketing',
    SCM: 'Source Code',
    INCIDENT: 'Incident Management',
    COMMS: 'Communications',
    GENERAL: 'General',
  };
  return categoryMap[category || 'GENERAL'] || 'General';
};

// Map connector type to domain key
const mapConnectorTypeToDomain = (type?: string): string => {
  // Map service types to domain keys
  const typeMap: Record<string, string> = {
    // Source control
    'SCM': 'SCM',
    'SOURCE_CONTROL': 'SCM',
    
    // Ticketing
    'TICKETING': 'TICKETING',
    'ISSUE_TRACKING': 'TICKETING',
    
    // Package & Container Registry
    'PCR': 'PCR',
    'CONTAINER_REGISTRY': 'PCR',
    'PACKAGE_REGISTRY': 'PCR',
    
    // Communications
    'COMMS': 'COMMS',
    'COMMUNICATIONS': 'COMMS',
    'MESSAGING': 'COMMS',
    
    // Incident Management
    'INCIDENT': 'INCIDENT',
    'INCIDENT_MANAGEMENT': 'INCIDENT',
    
    // Vulnerability Management
    'VMS': 'VMS',
    'VULNERABILITY': 'VMS',
    'SECURITY_SCANNING': 'VMS',
    
    // Observability/Monitoring
    'MONITORING': 'MONITORING',
    'OBSERVABILITY': 'MONITORING',
    'APM': 'MONITORING',
    
    // Identity
    'IDENTITY': 'IDENTITY',
    'IAM': 'IDENTITY',
    'SSO': 'IDENTITY',
    
    // Public Cloud (Infra)
    'INFRA': 'INFRA',
    'CLOUD': 'INFRA',
    'CLOUD_INFRA': 'INFRA',
    
    // EDR & XDR
    'EDR': 'EDR',
    'XDR': 'EDR',
    'ENDPOINT_SECURITY': 'EDR',
  };
  
  return typeMap[type || ''] || 'GENERAL';
};

export default function EnableConnectorsTab({
  connectors = [],
  pagination,
  onLoadMore,
  isLoading = false,
  onNext,
  onPrevious,
  isFirstTab,
  isLastTab,
  nextLabel,
  previousLabel,
  onRefreshConnectors,
}: EnableConnectorsTabProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const [hoveredConnectorId, setHoveredConnectorId] = useState<string | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedConnector, setSelectedConnector] = useState<Connector | null>(null);

  // Check if any connectors are enabled
  const [visibleCount, setVisibleCount] = useState(12); // Show 12 initially (3 rows of 4)

  // Get domains for quick-start
  const quickStartDomains = getQuickStartDomains();

  // Group connectors by domain category
  const groupedConnectors = React.useMemo(() => {
    const grouped: Record<string, { domain: Domain; connectors: Connector[] }> = {};
    
    // Initialize groups for all quick-start domains
    quickStartDomains.forEach(domain => {
      grouped[domain.key] = {
        domain,
        connectors: []
      };
    });

    // Group connectors by their category/type
    connectors.forEach(connector => {
      // Map connector type to domain key
      const domainKey = mapConnectorTypeToDomain(connector.type || connector.category);
      if (grouped[domainKey]) {
        grouped[domainKey].connectors.push(connector);
      }
    });

    // Sort connectors within each group alphabetically
    Object.values(grouped).forEach(group => {
      group.connectors.sort((a, b) => a.name.localeCompare(b.name));
    });

    // Return as array in domain order
    return quickStartDomains
      .map(domain => grouped[domain.key])
      .filter(group => group.connectors.length > 0); // Only show categories with connectors
  }, [connectors, quickStartDomains]);

  // Flatten grouped connectors for display with limit
  const displayedConnectors = React.useMemo(() => {
    const flattened: Connector[] = [];
    for (const group of groupedConnectors) {
      flattened.push(...group.connectors);
      if (flattened.length >= visibleCount) break;
    }
    return flattened.slice(0, visibleCount);
  }, [groupedConnectors, visibleCount]);

  const hasConnectors = connectors.length > 0;

  const handleConfigureConnectors = () => {
    navigate('/console/setup-integrations/connectors');
  };

  const handleConfigureConnector = (connector: Connector) => {
    setSelectedConnector(connector);
    setDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setDrawerOpen(false);
    setSelectedConnector(null);
  };

  const handleGetStarted = () => {
    navigate('/console/setup-integrations/connectors');
  };

  const handleLoadMore = () => {
    setVisibleCount((prev) => Math.min(prev + 12, connectors.length));
  };

  // Connector Card Component - Compact vertical layout
  const ConnectorCard = ({ connector }: { connector: Connector }) => {
    const isHovered = hoveredConnectorId === connector.id;
    const requiresConfig = connector.requiresConfiguration;

    return (
      <Grid item xs={12} sm={6} md={4} lg={2.4} xl={2}>
        <Box
          onMouseEnter={() => setHoveredConnectorId(connector.id)}
          onMouseLeave={() => setHoveredConnectorId(null)}
          sx={{
            p: 3,
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: 1,
            backgroundColor: theme.palette.background.paper,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center',
            gap: 1,
            transition: 'all 0.2s',
            cursor: 'pointer',
            minHeight: 180,
            position: 'relative',
            '&:hover': {
              borderColor: theme.palette.primary.main,
              backgroundColor: alpha(theme.palette.primary.main, 0.02),
              transform: 'translateY(-2px)',
            },
          }}
          onClick={() => handleConfigureConnector(connector)}
        >
          {/* Icon */}
          <Box
            component="img"
            src={connector.icon}
            alt={connector.name}
            sx={{
              width: 56,
              height: 56,
              objectFit: 'contain',
              mb: 0.5,
            }}
          />

          {/* Name */}
          <Typography
            variant="body2"
            sx={{
              fontWeight: 600,
              color: theme.palette.text.primary,
              mb: 0.25,
            }}
          >
            {connector.name}
          </Typography>

          {/* Type */}
          {/* <Typography
            variant="caption"
            sx={{
              color: theme.palette.text.secondary,
              display: 'block',
              mb: 1,
            }}
          >
            {connector.typeLabel}
          </Typography> */}

          {/* Status or Configure link */}
          <Box
            sx={{
              mt: 'auto',
              minHeight: 20,
            }}
          >
            {isHovered ? (
              <Typography
                component="a"
                variant="caption"
                sx={{
                  color: theme.palette.primary.main,
                  textDecoration: 'none',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  '&:hover': {
                    textDecoration: 'underline',
                  },
                }}
              >
                Configure
                <ExternalLink size={12} />
              </Typography>
            ) : (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 0.5,
                }}
              >
                {requiresConfig ? (
                  <>
                    <AlertCircle 
                      size={14} 
                      color={theme.palette.warning.main}
                    />
                    <Typography
                      variant="caption"
                      sx={{ 
                        color: theme.palette.warning.main, 
                        fontSize: '0.813rem',
                        fontWeight: 500,
                      }}
                    >
                      Needs configuration
                    </Typography>
                  </>
                ) : (
                  <>
                    <Check 
                      size={14} 
                      color={theme.palette.primary.main}
                    />
                    <Typography
                      variant="caption"
                      sx={{ 
                        color: theme.palette.primary.main, 
                        fontSize: '0.813rem',
                        fontWeight: 500,
                      }}
                    >
                      Ready to use
                    </Typography>
                  </>
                )}
              </Box>
            )}
          </Box>
        </Box>
      </Grid>
    );
  };

  // Configured content
  const configuredContent = (
    <Box>
      <Box
        sx={{
          backgroundColor: theme.palette.background.default,
          borderRadius: 1,
          p: 4,
        }}
      >
        <Stack spacing={4}>
          {/* Header Section */}
          <Box>
        <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 0.5 }}>
          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: '50%',
              backgroundColor: theme.palette.primary.main,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: theme.palette.common.white,
            }}
          >
            <Plug size={18} strokeWidth={3} />
          </Box>
          <Typography variant="h5" fontWeight={600}>
            Connectors
          </Typography>
        </Stack>
        <Typography
          variant="body1"
          sx={{
            color: theme.palette.mode === 'light'
              ? theme.palette.grey[600]
              : theme.palette.text.secondary,
            mb: 2,
          }}
        >
          The connectors you select become available to your users in Connect UI and can be used to push and pull data from using our Unified API. Each connector works with one or more authentication methods — like OAuth, PAT, or Basic.
          Choose which ones you'd like your product to support. Just enable at least one method to make a connector available for integration.
        </Typography>
      </Box>

      {/* Connectors Content */}
      <Box>
        <Box 
          sx={{ 
            mb: 3,
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: { xs: 'flex-start', sm: 'center' },
            justifyContent: 'space-between',
            gap: { xs: 1, sm: 2 }
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            You have enabled {pagination?.total || connectors.length} connector{(pagination?.total || connectors.length) !== 1 ? 's' : ''}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexShrink: 0 }}>
            <Typography
              variant="body2"
              sx={{
                color: theme.palette.text.secondary,
              }}
            >
              Want to add more?
            </Typography>
            <Typography
              component="a"
              variant="body2"
              onClick={handleConfigureConnectors}
              sx={{
                color: theme.palette.primary.main,
                cursor: 'pointer',
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              Enable additional Connectors
            </Typography>
          </Box>
        </Box>

        <Box>
          {groupedConnectors.map((group, groupIndex) => {
            // Calculate how many connectors to show from this group
            const previousGroupsCount = groupedConnectors
              .slice(0, groupIndex)
              .reduce((sum, g) => sum + g.connectors.length, 0);
            const remainingCount = Math.max(0, visibleCount - previousGroupsCount);
            const connectorsToShow = group.connectors.slice(0, remainingCount);
            
            if (connectorsToShow.length === 0) return null;
            
            return (
              <Box key={group.domain.key} sx={{ mb: 3 }}>
                {/* Category Header */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    mb: 2,
                    pb: 1.5,
                    borderBottom: `1px solid ${theme.palette.divider}`,
                  }}
                >
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                    }}
                  >
                    {group.domain.name}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: theme.palette.text.secondary,
                      ml: 'auto',
                    }}
                  >
                    {group.connectors.length} connector{group.connectors.length !== 1 ? 's' : ''}
                  </Typography>
                </Box>
                
                {/* Connectors Grid */}
                <Grid container spacing={2}>
                  {connectorsToShow.map((connector) => (
                    <ConnectorCard key={connector.id} connector={connector} />
                  ))}
                </Grid>
              </Box>
            );
          })}
        </Box>

        {/* Load More Button */}
        {visibleCount < connectors.length && (
          <Box sx={{ mt: 3, textAlign: 'center', pb: 2 }}>
            <Button
              variant="text"
              onClick={handleLoadMore}
              disabled={isLoading}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                color: theme.palette.primary.main,
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.08),
                },
              }}
            >
              {isLoading ? (
                <CircularProgress size={20} sx={{ mr: 1 }} />
              ) : null}
              Load more ({displayedConnectors.length} of {connectors.length})
            </Button>
          </Box>
        )}
      </Box>
        </Stack>
      </Box>
    </Box>
  );

  return (
    <Box>
      <ConfigurationSection
        stepNumber="1"
        title="Enable connectors"
        description="Each connector works with one or more authentication methods — like OAuth, PAT, or Basic. Choose which ones you'd like your product to support. Just enable at least one method to make a connector available for integration."
        isConfigured={true}
        onGetStarted={handleGetStarted}
        configuredContent={configuredContent}
        isaddView={!hasConnectors}
      />
      <TabNavigationFooter
        onNext={onNext}
        onPrevious={onPrevious}
        isFirstTab={isFirstTab}
        isLastTab={isLastTab}
        nextLabel={nextLabel}
        previousLabel={previousLabel}
      />
      
      {/* Connector Authentication Drawer */}
      {selectedConnector && (
        <ConnectorAuthenticationDrawer
          open={drawerOpen}
          onClose={handleDrawerClose}
          connector={selectedConnector}
          serviceId={selectedConnector.serviceId || selectedConnector.id} // Use serviceId if available, fallback to id
          onRefreshConnectors={onRefreshConnectors}
        />
      )}
    </Box>
  );
}