import { Box, Typography } from "@mui/material";
import LinearProgress, { LinearProgressProps } from "@mui/material/LinearProgress";

type ExtendedTypes = {
   value: number
   percentage?: boolean
}

export function LinearProgressWithLabel(
   { percentage = true ,...props }: LinearProgressProps & ExtendedTypes) {
   return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
         <Box sx={{ width: '100%', mr: 1 }}>
            <LinearProgress variant="determinate" {...props} />
         </Box>
         {props.value ? (
            <Box >
               <Typography
                  variant='caption'
               // sx={{ color: 'text.secondary' }}
               > {`${props.value.toFixed(2)}${percentage ? '%' : ''}`}</Typography>
            </Box>
         ) : null}
      </Box>
   );
}
