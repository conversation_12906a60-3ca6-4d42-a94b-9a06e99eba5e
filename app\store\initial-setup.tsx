import { create } from "zustand";
import { persist } from 'zustand/middleware';

type SegmentValues = {
   services: Record<string, any>[]
   watches: Record<string, any>[]
   accessKey: Record<string, any>[]
   users: Record<string, any>[]
}

type ValueType = 'services' | 'watches' | 'accessKey' | 'users'
type Step = number | 'welcome' | 'confirm';

interface InitialSetup {
   values: SegmentValues
   setValues: (type: ValueType, value: Record<string, any>[]) => void

   step: Step,
   move: (type: 'next' | 'prev') => void
   jump: (step: Step) => void

   done: boolean,
   setDone: (isDone: boolean) => void,

   reset: () => void;
}

const STORE_KEY: string = 'initial-setup';

const DEFAULT_VALUES = {
   values: {
      services: [],
      watches: [],
      accessKey: [],
      users: []
   },
   step: 'welcome',
   acceptedWelcome: false,
   done: false
}

const useInitialSetup = create(
   persist<InitialSetup>(
      (set, get) => ({

         // segment values
         values: DEFAULT_VALUES.values,
         setValues: (type, value) => {
            set({
               values: {
                  ...get().values,
                  [type]: value
               }
            })
         },

         // step values
         step: DEFAULT_VALUES.step as Step,
         move: (type) => {

            set(({ step, ...prevState }) => (
               // support both front and back
               {
                  ...prevState, step: (() => {
                     if (step === 'welcome') {
                        return 0;
                     } else if (step === 'confirm') {
                        return 'welcome';
                     } else {
                        return (type === 'next' ? step + 1 : step - 1)
                     }
                  })()
               }
            ))
         },
         jump: (step) => {
            set((prevState) => (
               // hard move, setting the index what you are passing
               { ...prevState, step }
            ))
         },

         // conclusion message, container for agree the conclusion
         done: DEFAULT_VALUES.done,
         setDone: (done) => {

            set((prev) => {
               return { ...prev, done }
            });

            // rest the state
            get().reset();
         },

         reset: () => {
            set(DEFAULT_VALUES as Partial<InitialSetup>)
         }
      }),
      {
         name: `${STORE_KEY}_${window.authUserOrgId}`,
      }
   )
);

export default useInitialSetup;