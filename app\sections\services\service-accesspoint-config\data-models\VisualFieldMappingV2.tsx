import React, { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Background,
  Controls,
  useNodesState,
  useEdgesState,
  Handle,
  Position,
  MarkerType,
  ConnectionMode,
  ReactFlowProvider,
  Connection,
  Panel,
  NodeProps,
  EdgeProps,
  getBezierPath,
  BaseEdge,
  useUpdateNodeInternals,
} from 'reactflow';
import 'reactflow/dist/style.css';
import {
  Box,
  Stack,
  Typography,
  useTheme,
  alpha,
  Paper,
  IconButton,
  Collapse,
  TextField,
  InputAdornment,
} from '@mui/material';
import {
  ChevronRight,
  ChevronDown,
  X,
  Search,
  Maximize2,
  Minimize2,
  RotateCcw,
  Download,
} from 'lucide-react';
import { UNIFIED_DATA_MODELS, PROVIDER_DATA_MODELS } from './hierarchical-mock-data';
import { getOpenApiBasedDataModels } from './openapi-field-mapping';

interface VisualFieldMappingV2Props {
  dataModel: any;
  serviceProfile: any;
  existingMappings?: any[];
  onMappingsChange?: (mappings: any[]) => void;
  embedded?: boolean;
}

// Field node data structure
interface FieldNodeData {
  id: string;
  name: string;
  displayName: string;
  type: string;
  path: string;
  description?: string;
  required?: boolean;
  isArrayItem?: boolean;
  arrayItemType?: string;
  arrayIndex?: number;
  isDynamicIndex?: boolean;
  children?: FieldNodeData[];
  expanded?: boolean;
  parentId?: string;
  level: number;
  matched?: boolean;
  connected?: boolean;
  childCount?: number;
}

// Group node component (for objects with children - acts as a container)
const GroupNode: React.FC<NodeProps<FieldNodeData>> = ({ data, id }) => {
  const theme = useTheme();
  const childCount = data.childCount || 0;
  const height = 35 + (childCount * 45) + 20; // Header + children + padding
  const isDarkMode = theme.palette.mode === 'dark';
  
  // Use #e4e6ec for light mode and a dark equivalent for dark mode
  const backgroundColor = isDarkMode ? '#2a2d35' : '#e4e6ec';
  const headerBackgroundColor = isDarkMode ? '#1f2228' : '#d5d8e1';
  
  return (
    <Box
      sx={{
        position: 'absolute',
        width: 240,
        height: height,
        border: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
        borderRadius: 1,
        backgroundColor: alpha(backgroundColor, 0.5),
        pointerEvents: 'none',
        boxShadow: `0 1px 3px ${alpha(theme.palette.common.black, 0.05)}`,
      }}
    >
      <Box
        sx={{
          px: 2,
          py: 1,
          backgroundColor: alpha(headerBackgroundColor, 0.7),
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
          borderTopLeftRadius: 4,
          borderTopRightRadius: 4,
        }}
      >
        <Typography 
          variant="caption" 
          sx={{ 
            fontWeight: 600,
            color: theme.palette.text.secondary,
            textTransform: 'uppercase',
            letterSpacing: 0.5,
            fontSize: '0.75rem',
          }}
        >
          {data.displayName || data.name}
        </Typography>
      </Box>
    </Box>
  );
};

// Child field node component (for nested attributes)
const ChildFieldNode: React.FC<NodeProps<FieldNodeData>> = ({ data, id, selected }) => {
  const theme = useTheme();
  const isSource = id.startsWith('source-');
  const isConnected = data.connected;
  
  return (
    <Paper
      elevation={selected ? 2 : 1}
      sx={{
        p: 1.2,
        minWidth: 160,
        maxWidth: 220,
        backgroundColor: theme.palette.background.paper,
        border: `1px solid ${
          selected 
            ? theme.palette.primary.main 
            : isConnected 
              ? theme.palette.primary.light 
              : theme.palette.divider
        }`,
        borderRadius: 1,
        transition: 'all 0.2s ease',
      }}
    >
      <Typography 
        variant="body2" 
        sx={{ 
          fontWeight: isConnected ? 600 : 400,
          fontSize: '0.875rem',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {data.displayName || data.name}
      </Typography>
      
      {/* Connection handles */}
      {isSource ? (
        <Handle
          type="source"
          position={Position.Right}
          id={`${id}-source`}
          style={{
            background: theme.palette.grey[400],
            width: 8,
            height: 8,
            border: `2px solid ${theme.palette.background.paper}`,
          }}
        />
      ) : (
        <Handle
          type="target"
          position={Position.Left}
          id={`${id}-target`}
          style={{
            background: theme.palette.grey[400],
            width: 8,
            height: 8,
            border: `2px solid ${theme.palette.background.paper}`,
          }}
        />
      )}
    </Paper>
  );
};

// Simplified edge with color-coded validation
const ValidatedEdge: React.FC<EdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  data,
  selected,
}) => {
  const theme = useTheme();
  const [edgePath] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetPosition,
    targetX,
    targetY,
  });
  
  const isValid = data?.isValid;
  
  return (
    <BaseEdge
      id={id}
      path={edgePath}
      style={{
        stroke: isValid ? theme.palette.success.main : theme.palette.error.main,
        strokeWidth: selected ? 3 : 2,
      }}
      markerEnd={{
        type: MarkerType.ArrowClosed,
        color: isValid ? theme.palette.success.main : theme.palette.error.main,
      }}
    />
  );
};

// Helper to flatten field hierarchy
const flattenFields = (field: any, parentPath = '', level = 0): FieldNodeData[] => {
  const result: FieldNodeData[] = [];
  const currentPath = parentPath ? `${parentPath}.${field.name}` : field.name;
  
  const nodeData: FieldNodeData = {
    id: field.id || currentPath,
    name: field.name,
    displayName: field.displayName || field.name,
    type: field.type,
    path: currentPath,
    description: field.description,
    required: field.required,
    isArrayItem: field.isArrayItem,
    arrayItemType: field.arrayItemType,
    arrayIndex: field.arrayIndex,
    isDynamicIndex: field.isDynamicIndex,
    level,
    children: [],
  };
  
  result.push(nodeData);
  
  if (field.children && field.children.length > 0) {
    nodeData.children = field.children.map((child: any) => ({
      ...child,
      parentId: nodeData.id,
    }));
    
    field.children.forEach((child: any) => {
      result.push(...flattenFields(child, currentPath, level + 1));
    });
  }
  
  return result;
};

// Main visual field mapping component
const VisualFieldMappingV2: React.FC<VisualFieldMappingV2Props> = ({
  dataModel,
  serviceProfile,
  existingMappings = [],
  onMappingsChange,
  embedded = false,
}) => {
  const theme = useTheme();
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // Load field data
  const { sourceFields, targetFields } = useMemo(() => {
    const provider = serviceProfile?.name?.toLowerCase() || 'github';
    
    // Try OpenAPI first
    try {
      const openApiModels = getOpenApiBasedDataModels();
      const source = openApiModels.PROVIDER_DATA_MODELS[provider]?.[dataModel.id];
      const target = openApiModels.UNIFIED_DATA_MODELS[dataModel.id];
      
      if (source && source.children?.length > 0) {
        return { sourceFields: source, targetFields: target };
      }
    } catch (error) {
      console.warn('Failed to load OpenAPI models:', error);
    }
    
    // Fallback to mock data
    return {
      sourceFields: PROVIDER_DATA_MODELS[provider]?.[dataModel.id] || {},
      targetFields: UNIFIED_DATA_MODELS[dataModel.id] || {},
    };
  }, [dataModel, serviceProfile]);
  
  // Create nodes from field data
  useEffect(() => {
    const sourceNodes: Node[] = [];
    const targetNodes: Node[] = [];
    
    let sourceYOffset = 50;
    let targetYOffset = 50;
    
    // Process source fields
    if (sourceFields.children) {
      sourceFields.children.forEach((field: any) => {
        const matched = searchTerm ? 
          field.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          field.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) : true;
        
        if (!matched && searchTerm) return;
        
        if (field.type === 'object' && field.children && field.children.length > 0) {
          // Count visible children
          const visibleChildren = field.children.filter((child: any) => {
            if (!searchTerm) return true;
            return child.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                   child.displayName?.toLowerCase().includes(searchTerm.toLowerCase());
          });
          
          if (visibleChildren.length === 0) return;
          
          // Create group node
          const groupId = `source-group-${field.id || field.name}`;
          sourceNodes.push({
            id: groupId,
            type: 'group',
            position: { x: 30, y: sourceYOffset - 5 },
            data: { 
              ...field, 
              matched,
              childCount: visibleChildren.length,
            },
            draggable: false,
            selectable: false,
            focusable: false,
          });
          
          sourceYOffset += 40; // Space for group header
          
          // Create child nodes
          visibleChildren.forEach((child: any, childIndex: number) => {
            const childId = `source-${field.name}.${child.name}`;
            sourceNodes.push({
              id: childId,
              type: 'child',
              position: { x: 50, y: sourceYOffset },
              data: { 
                ...child,
                path: `${field.name}.${child.name}`,
                matched: true,
                parentId: groupId,
              },
            });
            
            sourceYOffset += 45;
          });
          
          sourceYOffset += 25; // Extra space after object group
        } else {
          // Regular field
          sourceNodes.push({
            id: `source-${field.id || field.name}`,
            type: 'child',
            position: { x: 50, y: sourceYOffset },
            data: { ...field, matched },
          });
          sourceYOffset += 60;
        }
      });
    }
    
    // Process target fields
    if (targetFields.children) {
      targetFields.children.forEach((field: any) => {
        const matched = searchTerm ? 
          field.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          field.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) : true;
        
        if (!matched && searchTerm) return;
        
        if (field.type === 'object' && field.children && field.children.length > 0) {
          // Count visible children
          const visibleChildren = field.children.filter((child: any) => {
            if (!searchTerm) return true;
            return child.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                   child.displayName?.toLowerCase().includes(searchTerm.toLowerCase());
          });
          
          if (visibleChildren.length === 0) return;
          
          // Create group node
          const groupId = `target-group-${field.id || field.name}`;
          targetNodes.push({
            id: groupId,
            type: 'group',
            position: { x: 580, y: targetYOffset - 5 },
            data: { 
              ...field, 
              matched,
              childCount: visibleChildren.length,
            },
            draggable: false,
            selectable: false,
            focusable: false,
          });
          
          targetYOffset += 40; // Space for group header
          
          // Create child nodes
          visibleChildren.forEach((child: any, childIndex: number) => {
            const childId = `target-${field.name}.${child.name}`;
            targetNodes.push({
              id: childId,
              type: 'child',
              position: { x: 600, y: targetYOffset },
              data: { 
                ...child,
                path: `${field.name}.${child.name}`,
                matched: true,
                parentId: groupId,
              },
            });
            
            targetYOffset += 45;
          });
          
          targetYOffset += 25; // Extra space after object group
        } else {
          // Regular field
          targetNodes.push({
            id: `target-${field.id || field.name}`,
            type: 'child',
            position: { x: 600, y: targetYOffset },
            data: { ...field, matched },
          });
          targetYOffset += 60;
        }
      });
    }
    
    setNodes([...sourceNodes, ...targetNodes]);
  }, [sourceFields, targetFields, searchTerm]);
  
  // Create edges from existing mappings
  useEffect(() => {
    const newEdges: Edge[] = [];
    
    existingMappings.forEach((mapping, index) => {
      const sourceNode = nodes.find(n => 
        n.id === `source-${mapping.sourceFieldPath}` || 
        n.data.path === mapping.sourceFieldPath ||
        n.id === `source-${mapping.source}` ||
        n.data.path === mapping.source
      );
      const targetNode = nodes.find(n => 
        n.id === `target-${mapping.targetFieldPath}` || 
        n.data.path === mapping.targetFieldPath ||
        n.id === `target-${mapping.target}` ||
        n.data.path === mapping.target
      );
      
      if (sourceNode && targetNode) {
        const isValid = mapping.sourceType === mapping.targetType;
        
        newEdges.push({
          id: `edge-${index}`,
          source: sourceNode.id,
          target: targetNode.id,
          type: 'validated',
          data: {
            isValid,
            sourceType: mapping.sourceType,
            targetType: mapping.targetType,
          },
        });
        
        // Update node connection status
        sourceNode.data = { ...sourceNode.data, connected: true };
        targetNode.data = { ...targetNode.data, connected: true };
      }
    });
    
    setEdges(newEdges);
  }, [existingMappings, nodes]);
  
  // Handle new connections
  const onConnect = useCallback((params: Connection) => {
    if (!params.source || !params.target) return;
    
    const sourceNode = nodes.find(n => n.id === params.source);
    const targetNode = nodes.find(n => n.id === params.target);
    
    if (!sourceNode || !targetNode) return;
    
    // Only allow connections between child nodes (not group nodes)
    if (sourceNode.type === 'group' || targetNode.type === 'group') return;
    
    // Validate connection
    const sourceType = sourceNode.data.type;
    const targetType = targetNode.data.type;
    const isValid = sourceType === targetType;
    
    // Create new edge
    const newEdge: Edge = {
      ...params,
      id: `edge-${Date.now()}`,
      type: 'validated',
      data: {
        isValid,
        sourceType,
        targetType,
      },
    };
    
    setEdges((eds) => addEdge(newEdge, eds));
    
    // Update mappings
    if (onMappingsChange) {
      const newMapping = {
        id: `mapping-${Date.now()}`,
        source: sourceNode.data.path || sourceNode.data.name,
        target: targetNode.data.path || targetNode.data.name,
        sourceType,
        targetType,
      };
      
      const updatedMappings = [
        ...existingMappings.filter(m => 
          m.source !== newMapping.source && m.target !== newMapping.target
        ),
        newMapping,
      ];
      
      onMappingsChange(updatedMappings);
    }
  }, [nodes, existingMappings, onMappingsChange]);
  
  // Handle edge deletion
  const onEdgesDelete = useCallback((deletedEdges: Edge[]) => {
    if (!onMappingsChange) return;
    
    const deletedMappingIds = deletedEdges.map(edge => {
      const sourceNode = nodes.find(n => n.id === edge.source);
      const targetNode = nodes.find(n => n.id === edge.target);
      const sourcePath = sourceNode?.data.path || sourceNode?.data.name;
      const targetPath = targetNode?.data.path || targetNode?.data.name;
      return `${sourcePath}-${targetPath}`;
    });
    
    const updatedMappings = existingMappings.filter(mapping => {
      const mappingId = `${mapping.source}-${mapping.target}`;
      return !deletedMappingIds.includes(mappingId);
    });
    
    onMappingsChange(updatedMappings);
  }, [nodes, existingMappings, onMappingsChange]);
  
  // Auto-layout function
  const autoLayout = useCallback(() => {
    const updatedNodes = [...nodes];
    let sourceYOffset = 50;
    let targetYOffset = 50;
    
    // Layout source nodes
    const sourceGroups = updatedNodes.filter(n => n.id.startsWith('source-') && n.type === 'group');
    const sourceChildren = updatedNodes.filter(n => n.id.startsWith('source-') && n.type === 'child');
    
    sourceGroups.forEach(group => {
      group.position = { x: 30, y: sourceYOffset - 5 };
      sourceYOffset += 40; // Space for group header
      
      // Position children of this group
      const children = sourceChildren.filter(child => child.data.parentId === group.id);
      children.forEach(child => {
        child.position = { x: 50, y: sourceYOffset };
        sourceYOffset += 45;
      });
      
      sourceYOffset += 25; // Extra space after group
    });
    
    // Position standalone source fields
    sourceChildren.filter(n => !n.data.parentId).forEach(node => {
      node.position = { x: 50, y: sourceYOffset };
      sourceYOffset += 60;
    });
    
    // Layout target nodes
    const targetGroups = updatedNodes.filter(n => n.id.startsWith('target-') && n.type === 'group');
    const targetChildren = updatedNodes.filter(n => n.id.startsWith('target-') && n.type === 'child');
    
    targetGroups.forEach(group => {
      group.position = { x: 580, y: targetYOffset - 5 };
      targetYOffset += 40; // Space for group header
      
      // Position children of this group
      const children = targetChildren.filter(child => child.data.parentId === group.id);
      children.forEach(child => {
        child.position = { x: 600, y: targetYOffset };
        targetYOffset += 45;
      });
      
      targetYOffset += 25; // Extra space after group
    });
    
    // Position standalone target fields
    targetChildren.filter(n => !n.data.parentId).forEach(node => {
      node.position = { x: 600, y: targetYOffset };
      targetYOffset += 60;
    });
    
    setNodes(updatedNodes);
  }, [nodes, setNodes]);
  
  // Export mappings
  const exportMappings = useCallback(() => {
    const mappingData = {
      serviceProvider: serviceProfile?.name,
      dataModel: dataModel.name,
      mappings: edges.map(edge => {
        const sourceNode = nodes.find(n => n.id === edge.source);
        const targetNode = nodes.find(n => n.id === edge.target);
        
        return {
          source: sourceNode?.data.path,
          target: targetNode?.data.path,
          sourceType: sourceNode?.data.type,
          targetType: targetNode?.data.type,
          isValid: edge.data?.isValid,
        };
      }),
      exportedAt: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(mappingData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${dataModel.name}_field_mappings.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [edges, nodes, dataModel, serviceProfile]);
  
  const nodeTypes = useMemo(() => ({ 
    group: GroupNode,
    child: ChildFieldNode,
  }), []);
  
  const edgeTypes = useMemo(() => ({ 
    validated: ValidatedEdge,
  }), []);
  
  const mappingEdges = edges.filter(e => e.type === 'validated');
  const validMappings = mappingEdges.filter(e => e.data?.isValid).length;
  const invalidMappings = mappingEdges.filter(e => !e.data?.isValid).length;
  
  // Fullscreen handlers
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      reactFlowWrapper.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);
  
  // Handle fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);
  
  return (
    <Box 
      ref={reactFlowWrapper}
      sx={{ 
        height: '100%', 
        width: '100%',
        backgroundColor: isFullscreen ? theme.palette.background.default : 'transparent',
        borderRadius: embedded && !isFullscreen ? 1 : 0,
        overflow: 'hidden',
        position: isFullscreen ? 'fixed' : 'relative',
        top: isFullscreen ? 0 : 'auto',
        left: isFullscreen ? 0 : 'auto',
        right: isFullscreen ? 0 : 'auto',
        bottom: isFullscreen ? 0 : 'auto',
        zIndex: isFullscreen ? theme.zIndex.modal : 'auto',
      }}
    >
      <ReactFlowProvider>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onEdgesDelete={onEdgesDelete}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          connectionMode={ConnectionMode.Loose}
          fitView
          fitViewOptions={{
            padding: 0.2,
            includeHiddenNodes: false,
          }}
          proOptions={{ hideAttribution: true }}
        >
          {/* Minimal control panel */}
          <Panel position="top-left">
            <Paper sx={{ p: 1.5 }}>
              <Stack spacing={1.5}>
                {/* Search */}
                <TextField
                  size="small"
                  placeholder="Search fields..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  sx={{ minWidth: 200 }}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search size={16} />
                      </InputAdornment>
                    ),
                    endAdornment: searchTerm && (
                      <InputAdornment position="end">
                        <IconButton size="small" onClick={() => setSearchTerm('')}>
                          <X size={16} />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
                
                {/* Actions */}
                <Stack direction="row" spacing={1}>
                  <IconButton
                    size="small"
                    onClick={autoLayout}
                    title="Auto Layout"
                  >
                    <RotateCcw size={18} />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={exportMappings}
                    title="Export"
                  >
                    <Download size={18} />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={toggleFullscreen}
                    title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
                  >
                    {isFullscreen ? <Minimize2 size={18} /> : <Maximize2 size={18} />}
                  </IconButton>
                </Stack>
              </Stack>
            </Paper>
          </Panel>
          
          {/* Simple status */}
          <Panel position="bottom-left">
            <Paper sx={{ p: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="caption" color="text.secondary">
                {edges.length} mappings
              </Typography>
              {invalidMappings > 0 && (
                <>
                  <Typography variant="caption" color="text.secondary">•</Typography>
                  <Typography variant="caption" color="error">
                    {invalidMappings} type mismatch{invalidMappings > 1 ? 'es' : ''}
                  </Typography>
                </>
              )}
            </Paper>
          </Panel>
          
          <Background 
            variant="dots" 
            gap={16} 
            size={1} 
            color={alpha(theme.palette.text.primary, 0.03)}
          />
          <Controls />
        </ReactFlow>
      </ReactFlowProvider>
    </Box>
  );
};

export default VisualFieldMappingV2;