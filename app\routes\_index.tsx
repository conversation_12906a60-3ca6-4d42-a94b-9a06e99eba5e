import { redirect, type MetaFunction } from "@remix-run/node";
import DashboardLayout from '../layout/Dashboard/index';
import { ClientOnly } from 'remix-utils/client-only';

export const meta: MetaFunction = () => {
  return [
    { title: "New Remix App" },
    { name: "description", content: "Welcome to Remix!" },
  ];
};

export const loader = () => {
  return redirect('/console/dashboard');
}

export default function Index() {
  return (
    <ClientOnly fallback={null}>
      {() => (
        <DashboardLayout>
          Welcome to Unizo
        </DashboardLayout>
      )}
    </ClientOnly>
  );
}
