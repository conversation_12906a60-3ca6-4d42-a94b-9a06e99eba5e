import { Box, IconButton, Stack, Typography } from "@mui/material";
import { ArrowRight, ChevronDown, X } from "lucide-react";

import { styles } from "./styles";

import AutocompleteField from "./components/auto-complete";
import { useMappingContext } from "./contexts/mapping.context";

import { FieldMappingRowProps } from "./type";

const FieldMappingRow = ({ meta, isChild, sourceOptions, targetOptions }: FieldMappingRowProps) => {
   const { onToggleExpand, onRemoveMapping, onUpdateMappings } = useMappingContext();
   const {
      canRemove,
      mapping,
      sourceValue,
      targetValue,
      hasError,
      excludeSourceValues,
      showExpand,
   } = meta;

   const isExpanded = mapping.expanded;

   const onSourceChange = (requestedValue: string) => {
      onUpdateMappings(mapping, 'source', requestedValue);
   };

   const onTargetChange = (requestedValue: string) => {
      onUpdateMappings(mapping, 'target', requestedValue);
   };

   return (
      <Stack direction="row" spacing={1.5} alignItems="flex-end">
         {!isChild && (
            <IconButton
               size="small"
               onClick={() => onToggleExpand(mapping.id)}
               sx={() => styles.toggleButtonRoot({ isExpanded: !!showExpand })}
            >
               <ChevronDown
                  size={16}
                  style={styles.toggleButtonIcon({ isExpanded })}
               />
            </IconButton>
         )}

         <Box sx={{ flex: 1 }}>
            {!isChild && (
               <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mb: 0.5, display: "block", fontSize: "0.75rem" }}
               >
                  Source Field
               </Typography>
            )}

            <AutocompleteField
               value={sourceValue}
               onChange={onSourceChange}
               options={sourceOptions}
               placeholder="Select source field"
               hasError={hasError}
               excludeValues={excludeSourceValues}
            />
         </Box>

         <Box
            sx={(theme) => styles.seperationArrowRoot({ theme, isChild: isChild as boolean })}
         >
            <ArrowRight size={20} />
         </Box>

         <Box sx={{ flex: 1 }}>
            {!isChild && (
               <Typography
                  variant="caption"
                  color="text.secondary"
                  sx={{ mb: 0.5, display: "block", fontSize: "0.75rem" }}
               >
                  Target Field
               </Typography>
            )}

            <AutocompleteField
               value={targetValue}
               onChange={onTargetChange}
               options={targetOptions}
               placeholder="Target source field"
               hasError={hasError}
               excludeValues={[]}
            />
         </Box>

         <IconButton
            size="small"
            onClick={() => {
               onRemoveMapping(mapping.id, mapping?.parentSource?.id as string)
            }}
            disabled={!canRemove}
            sx={(theme) => styles.mappingRowDeleteButtonRoot({ theme, canRemove, isChild: isChild as boolean })}
         >
            <X size={16} />
         </IconButton>
      </Stack>
   )
}

export default FieldMappingRow;