import { styled } from '@mui/material/styles';
import { <PERSON>, <PERSON>u, MenuItem, IconButton, Typography, Box } from '@mui/material';
import { MoreVertical } from 'lucide-react';
import { useState, MouseEvent } from 'react';
import { WebhookStatus, WebhookEventType } from 'types/webhook';

// Styled Components
export const WebhookStatusChip = styled(Chip)<{ status: WebhookStatus }>(({ theme, status }) => {
  const getStatusStyles = () => {
    switch (status) {
      case 'active':
        return {
          backgroundColor: theme.palette.success.lighter,
          color: theme.palette.success.darker,
          borderColor: theme.palette.success.light,
        };
      case 'inactive':
        return {
          backgroundColor: theme.palette.grey[100],
          color: theme.palette.grey[700],
          borderColor: theme.palette.grey[300],
        };
      case 'error':
        return {
          backgroundColor: theme.palette.error.lighter,
          color: theme.palette.error.darker,
          borderColor: theme.palette.error.light,
        };
      default:
        return {
          backgroundColor: theme.palette.grey[100],
          color: theme.palette.grey[700],
          borderColor: theme.palette.grey[300],
        };
    }
  };

  return {
    ...getStatusStyles(),
    border: '1px solid',
    fontWeight: 500,
    fontSize: '0.75rem',
    textTransform: 'capitalize',
  };
});

export const WebhookTypeBadge = styled(Box)(({ theme }) => ({
  display: 'inline-flex',
  alignItems: 'center',
  padding: theme.spacing(0.5, 1.5),
  borderRadius: theme.spacing(1),
  backgroundColor: theme.palette.primary.lighter,
  color: theme.palette.primary.darker,
  fontSize: '0.75rem',
  fontWeight: 500,
  fontFamily: 'monospace',
}));

// Status Chip Component
interface StatusChipProps {
  status: WebhookStatus;
}

export const StatusChip: React.FC<StatusChipProps> = ({ status }) => {
  return (
    <WebhookStatusChip
      label={status}
      status={status}
      size="small"
    />
  );
};

// Event Type Badge Component
interface EventTypeBadgeProps {
  eventType: WebhookEventType;
}

export const EventTypeBadge: React.FC<EventTypeBadgeProps> = ({ eventType }) => {
  return (
    <WebhookTypeBadge>
      {eventType}
    </WebhookTypeBadge>
  );
};

// Actions Menu Component
interface ActionsMenuProps {
  onEdit: () => void;
  onDelete: () => void;
  onTest?: () => void;
  disabled?: boolean;
}

export const ActionsMenu: React.FC<ActionsMenuProps> = ({ 
  onEdit, 
  onDelete, 
  onTest, 
  disabled = false 
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = (event: MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAction = (action: () => void) => {
    action();
    handleClose();
  };

  return (
    <>
      <IconButton
        size="small"
        onClick={handleClick}
        disabled={disabled}
        sx={{ 
          '&:hover': { 
            backgroundColor: (theme) => theme.palette.action.hover 
          } 
        }}
      >
        <MoreVertical size={16} />
      </IconButton>
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={() => handleAction(onEdit)}>
          <Typography variant="body2">Edit</Typography>
        </MenuItem>
        {onTest && (
          <MenuItem onClick={() => handleAction(onTest)}>
            <Typography variant="body2">Test Webhook</Typography>
          </MenuItem>
        )}
        <MenuItem onClick={() => handleAction(onDelete)}>
          <Typography variant="body2" color="error">
            Delete
          </Typography>
        </MenuItem>
      </Menu>
    </>
  );
};

// Webhook URL Display Component
interface WebhookUrlDisplayProps {
  url: string;
}

export const WebhookUrlDisplay: React.FC<WebhookUrlDisplayProps> = ({ url }) => {
  return (
    <Typography
      variant="body2"
      sx={{
        fontFamily: 'monospace',
        color: (theme) => theme.palette.text.secondary,
        wordBreak: 'break-all',
        maxWidth: 400,
      }}
    >
      {url}
    </Typography>
  );
};