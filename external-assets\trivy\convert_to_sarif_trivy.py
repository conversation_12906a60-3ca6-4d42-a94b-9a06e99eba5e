import json
import sys
from datetime import datetime

def generate_sarif(json_file, output_file, report_type):
    with open(json_file, 'r') as f:
        data = json.load(f)

    sarif_report = {
        "version": "2.1.0",
        "runs": [{
            "tool": {
                "driver": {
                    "name": report_type,
                    "informationUri": "https://semgrep.dev",
                    "rules": []
                }
            },
            "results": []
        }]
    }

    vulnerabilities = []
    for result in data.get('Results', []):
        for vuln in result.get('Vulnerabilities', []):
            severity = vuln.get('Severity', 'Unknown').capitalize()

            # Define SARIF rule for each unique vulnerability
            rule = {
                "id": vuln.get('VulnerabilityID', 'N/A'),
                "name": vuln.get('PkgName', 'N/A'),
                "shortDescription": {
                    "text": vuln.get('Description', 'N/A')
                },
                "fullDescription": {
                    "text": f"Package {vuln.get('PkgName', 'N/A')} has vulnerability {vuln.get('VulnerabilityID', 'N/A')}."
                },
                "helpUri": vuln.get('References', [''])[0] if vuln.get('References') else "",
                "properties": {
                    "severity": severity,
                    "security-severity": vuln.get('CVSS', {}).get('nvd', {}).get('V3Score', 'N/A')
                }
            }
            sarif_report['runs'][0]['tool']['driver']['rules'].append(rule)

            # Define SARIF result (occurrence of the rule)
            result = {
                "ruleId": vuln.get('VulnerabilityID', 'N/A'),
                "message": {
                    "text": vuln.get('Description', 'N/A')
                },
                "locations": [{
                    "physicalLocation": {
                        "artifactLocation": {
                            "uri": "N/A"  # Replace with file/package location if available
                        },
                        "region": {
                            "startLine": 1,  # Default to 1 if line is unknown
                            "endLine": 1
                        }
                    }
                }],
                "properties": {
                    "severity": severity,
                    "packageName": vuln.get('PkgName', 'N/A'),
                    "installedVersion": vuln.get('InstalledVersion', 'N/A'),
                    "fixedVersion": vuln.get('FixedVersion', 'N/A'),
                    "cvssScore": vuln.get('CVSS', {}).get('nvd', {}).get('V3Score', 'N/A'),
                    "cvssVector": vuln.get('CVSS', {}).get('nvd', {}).get('V3Vector', 'N/A'),
                    "publishedDate": vuln.get('PublishedDate', 'N/A'),
                    "lastModifiedDate": vuln.get('LastModifiedDate', 'N/A')
                }
            }
            sarif_report['runs'][0]['results'].append(result)

    # Write the SARIF content to the output file
    with open(output_file, 'w') as f:
        json.dump(sarif_report, f, indent=4)

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python3 convert_to_sarif.py <json_file> <output_file> <report_type>")
        sys.exit(1)

    json_file = sys.argv[1]
    output_file = sys.argv[2]
    report_type = sys.argv[3]

    generate_sarif(json_file, output_file, report_type)
