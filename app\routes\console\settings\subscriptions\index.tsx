import { type MetaFunction } from "@remix-run/node";
import { ClientOnly } from 'remix-utils/client-only';

import { Box, Divider, Grid, Stack, Tooltip, Typography } from "@mui/material";
import MainCard from "components/MainCard";
import { Fragment } from "react/jsx-runtime";
import { CaretRightOutlined, CheckCircleOutlined, CheckCircleTwoTone, LockOutlined, RightOutlined } from "@ant-design/icons";
import { useEffect, useMemo, useState } from "react";
import { cn } from "lib/utils";
import useUserDetails from "store/user";
import { Entitlement, Subscription } from "types/subscription";
import { useSubscription } from "hooks/api/useSubscription";
import { billingClient } from "services/billing.service";
import { EntitlementLevel } from "constants/subscription.constant";
import moment from "moment";
export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
   ];
};

export default function Settings() {
   const { subscriptions } = useUserDetails()

   return (
      <ClientOnly fallback={null}>
         {() => (
            <Box sx={{ mt: 5 }}>
               <Stack gap={10}>
                  {subscriptions.map((i, index) => {
                     return (
                        <Fragment key={index}>
                           <Item item={i} />
                        </Fragment>
                     )
                  })}
               </Stack>
            </Box>
         )}
      </ClientOnly>
   );
}

type ItemProps = {
   item: Subscription
}
const Item = (props: ItemProps) => {

   const { item } = props;

   const [open, setIsOpen] = useState(false);
   const [usage, setUsage] = useState<Record<string, any>[]>([]);
   const [loading, setLoading] = useState<boolean>(true);

   const {
      getChargeType,
      getEntitlementsUsage,
   } = useSubscription();

   const cancelledDate = item?.cancelledDate;
   const startDate = item?.startDate;
   const incrementType = getChargeType(item?.charge?.type)?.period;
   const entitlementsUsage = getEntitlementsUsage(item?.entitlements);

   const current = useMemo(() => {
      return item?.entitlements
   }, [item?.entitlements])


   useEffect(() => {
      (async () => {
         if (item?.id) {
            setLoading(true);
            try {
               const { data } = await billingClient.getBillingById(item.id);
               setUsage(data?.entitlements ?? [])
            } catch (error) { } finally {
               setLoading(false);
            }
         }
      })();

   }, [item?.id])

   const isAPIExpired = useMemo(() => {
      if (!cancelledDate) return false;
      return !moment().local().isBefore(cancelledDate);
   }, [cancelledDate]);

   return (
      <Grid container columnSpacing={5} rowSpacing={3}>
         <Grid item xs={12} md={5} xl={4}>
            <Stack gap={1}>
               <Typography variant='h4'>
                  {item?.name}
               </Typography>
               <Typography variant='button' className="font-normal" sx={{ textTransform: 'none', color: 'secondary.600' }}>
                  {item?.description}
               </Typography>
            </Stack>
         </Grid>
         <Grid item xs={12} md={7} xl={8}>
            <Grid container columnSpacing={3} rowSpacing={4}>
               <Grid xs={12} sm={6} md={12} item>
                  <Stack gap={1}>
                     <Typography variant='overline' className="font-bold" sx={{ textTransform: 'none' }}>Current Plan</Typography>
                     <MainCard>
                        <Stack gap={2}>
                           <Typography variant='h5'>{item?.charge?.pricingTier?.name}</Typography>
                           <Stack gap={1}>
                              {current.map((i, index) => {
                                 return <Typography variant='button' key={index}>{i?.displayName}</Typography>
                              })}
                           </Stack>
                        </Stack>
                     </MainCard>
                  </Stack>
               </Grid>
               <Grid xs={12} sm={6} md={12} item>
                  <Stack gap={1}>
                     <Typography variant='overline' className="font-bold" sx={{ textTransform: 'none' }}>Usage</Typography>

                     <MainCard>
                        <Stack>
                           <Stack direction={'row'} justifyContent={'space-between'}>
                              <Typography variant='h5'>Features</Typography>
                              {!isAPIExpired ? (
                                 <Stack direction={'row'}>
                                    <Typography>
                                       Next Reset:
                                    </Typography>
                                    {(<Typography sx={{ color: "secondary.600" }}>
                                       {startDate ? (
                                          moment(startDate).add(1, incrementType).local().format("YYYY-MM-DD")
                                       ) : null}
                                    </Typography>)}
                                 </Stack>
                              ) : null}
                           </Stack>
                           <Stack gap={1} mt={2} className="xl:max-w-[50%]">
                              <Stack gap={1} >
                                 {!loading && entitlementsUsage.topLevel.map(({ type, ...rest }: Entitlement, index) => {
                                    const { label, parseMessage } = entitlementsUsage.getCatalogByType(type as any);
                                    const entitlement = usage?.find((i) => i?.type === type);

                                    return (
                                       <Stack direction={'row'} gap={10} key={index}>
                                          <Grid container>
                                             <Grid item sm={6}>
                                                <Typography variant='button'  >{label}</Typography>
                                             </Grid>
                                             <Grid item sm={6}>
                                                <Typography variant='button' className="font-normal" >
                                                   <b>{entitlement ? parseMessage(entitlement) : 0}</b>
                                                   {` / `} {rest ? parseMessage(rest) : 0}
                                                </Typography>
                                             </Grid>
                                          </Grid>
                                       </Stack>
                                    )
                                 })}
                              </Stack>
                              <Stack mt={2}>
                                 <Stack direction={'row'} gap={1} alignItems={'center'} onClick={() => setIsOpen(!open)}>
                                    <Typography className="link">View all entitlements</Typography>
                                    <RightOutlined className={cn(`${open ? 'rotate-90' : ''} text-xs link`)} />
                                 </Stack>


                                 <Stack className={cn(`${open ? 'block' : 'hidden'}`)} mt={2} gap={1}>
                                    <Feature name={item?.charge?.pricingTier?.name} entitlements={entitlementsUsage.collapsed} />
                                 </Stack>
                              </Stack>
                           </Stack>
                        </Stack>

                     </MainCard>
                  </Stack>
               </Grid>
            </Grid>
         </Grid>
      </Grid>
   )
}


const Feature = ({ entitlements, name }: Record<string, any>) => {

   const [collapsedList, setCollapsedList] = useState<number[]>([])


   const { getBillingCatalog } = useSubscription();

   const catalog = getBillingCatalog()

   const entitleMentsList = useMemo(() => {
      const mapperList = Object.values(catalog);
      const available: string[] = [], notAvailable: string[] = [];
      mapperList.filter((i) => i.level === EntitlementLevel.Collapsed).forEach((item: any) => {
         const { planIncludes } = item;
         const isAvailable: any = planIncludes.includes(name);
         isAvailable ? available.push(item) : notAvailable.push(item)
      });
      return available.concat(notAvailable)
   }, [name]);

   const onToggle = (selectedIndex: number) => {
      if (collapsedList.includes(selectedIndex)) {
         setCollapsedList(collapsedList.filter((i) => i !== selectedIndex));
      } else {
         setCollapsedList([...collapsedList, selectedIndex])
      }
   }

   return (
      <Stack gap={1} >
         {entitleMentsList.map(({ planIncludes, type, ...feature }: any, index) => {
            const collapsed = collapsedList.includes(index);
            const available = planIncludes.includes(name);
            const entitlement = entitlements?.find((i: any) => i.type === type)
            return (
               <Stack key={index} onClick={() => onToggle(index)} className="cursor-pointer select-none">
                  <Stack gap={1} direction={'row'} alignItems={'center'} >
                     <CaretRightOutlined className={cn(`${collapsed ? 'rotate-90' : ''}`)} />
                     <Typography variant="button" >{feature?.label}</Typography>
                     <Stack direction={'row'} gap={1}>
                        {available ? <CheckCircleTwoTone /> : (
                           <Tooltip title="Not supported with your current plan">
                              <LockOutlined />
                           </Tooltip>
                        )}
                        <Typography>
                           {feature.parseMessage(entitlement)}
                        </Typography>
                     </Stack>
                  </Stack>
                  {
                     collapsed ? (
                        <Typography variant='button' sx={{ color: "secondary.600",marginLeft:"1.5rem" }}>
                           {feature?.description}
                        </Typography>
                     ) : null
                  }
               </Stack>
            )
         })}
      </Stack>
   )
}