import React from 'react';
import { Box, Typography, Stack, useTheme, alpha, Divider } from '@mui/material';
import { Check, Circle } from 'lucide-react';
import Image from 'remix-image';

interface Step {
  id: string;
  title: string;
  helpText: string;
}

interface StepperNavigationProps {
  steps: Step[];
  currentStep: number;
  onStepClick: (index: number) => void;
}

export default function StepperNavigation({ steps, currentStep, onStepClick }: StepperNavigationProps) {
  const theme = useTheme();

  const getStepStyles = (index: number) => {
    const isActive = index === currentStep;
    const isClickable = index <= currentStep;

    return {
      cursor: isClickable ? 'pointer' : 'default',
      backgroundColor: 'transparent',
      '&:hover': isClickable ? {
        '& .step-indicator': {
          backgroundColor: alpha(theme.palette.common.white, 0.1),
        }
      } : {},
      transition: 'all 0.2s ease',
      position: 'relative' as const,
    };
  };

  const getTitleColor = (index: number) => {
    if (index < currentStep) return theme.palette.common.white;
    if (index === currentStep) return theme.palette.common.white;
    return alpha(theme.palette.common.white, 0.4);
  };

  const getHelpTextColor = (index: number) => {
    if (index < currentStep) return alpha(theme.palette.common.white, 0.7);
    if (index === currentStep) return alpha(theme.palette.common.white, 0.5);
    return alpha(theme.palette.common.white, 0.3);
  };

  const getStepIndicator = (index: number) => {
    const isCompleted = index < currentStep;
    const isActive = index === currentStep;
    
    if (isCompleted) {
      return (
        <Box
          className="step-indicator"
          sx={{
            width: 24,
            height: 24,
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: theme.palette.common.white,
            color: theme.palette.primary.main,
          }}
        >
          <Check size={16} strokeWidth={3} />
        </Box>
      );
    }
    
    return (
      <Box
        className="step-indicator"
        sx={{
          width: 24,
          height: 24,
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: `2px solid ${isActive ? theme.palette.common.white : alpha(theme.palette.common.white, 0.3)}`,
          backgroundColor: 'transparent',
        }}
      >
        <Circle 
          size={8} 
          fill={isActive ? theme.palette.common.white : 'transparent'}
          color={isActive ? theme.palette.common.white : 'transparent'}
        />
      </Box>
    );
  };

  return (
    <Box 
      sx={{ 
        height: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        backgroundColor: '#2D3748', // Dark blue-grey like in the image
        color: theme.palette.common.white,
      }}
    >
      {/* Logo/Header */}
      <Box sx={{ pb: 3 }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '60px',
            paddingTop: '8px',
            paddingBottom: '16px',
          }}
        >
          <Image
            style={{ width: 100 }}
            src="/images/brand/unz_brand_dark.svg"
            alt="Unizo Logo"
          />
        </Box>
        <Box sx={{ px: 2.5, pb: 2 }}>
          <Divider sx={{
            borderColor: '#ea580c',
            borderBottomWidth: '1px',
            mx: 1,
            width: 'calc(100% - 16px)'
          }} />
        </Box>
      </Box>

      {/* Steps */}
      <Box sx={{ flex: 1, px: 3 }}>
        {steps.map((step, index) => (
          <Box
            key={step.id}
            onClick={() => onStepClick(index)}
            sx={{
              ...getStepStyles(index),
              position: 'relative',
              pb: index < steps.length - 1 ? 6 : 0,
            }}
          >
            <Stack direction="row" spacing={2} alignItems="flex-start">
              {/* Step Indicator with connecting line */}
              <Box sx={{ position: 'relative', flexShrink: 0 }}>
                {getStepIndicator(index)}
                
                {/* Connecting line */}
                {index < steps.length - 1 && (
                  <Box
                    sx={{
                      position: 'absolute',
                      left: '50%',
                      top: 24,
                      width: 2,
                      height: 'calc(100% + 24px)',
                      backgroundColor: index < currentStep 
                        ? theme.palette.common.white 
                        : alpha(theme.palette.common.white, 0.2),
                      transform: 'translateX(-50%)',
                      transition: 'background-color 0.3s ease',
                    }}
                  />
                )}
              </Box>

              {/* Step Content */}
              <Box sx={{ flex: 1 }}>
                <Typography
                  variant="body2"
                  fontWeight={600}
                  sx={{ 
                    color: getTitleColor(index),
                    mb: 0.25,
                    fontSize: '0.875rem',
                    textTransform: 'uppercase',
                    letterSpacing: 0.5,
                  }}
                >
                  {step.title}
                </Typography>
                <Typography
                  variant="caption"
                  sx={{ 
                    display: 'block',
                    color: getHelpTextColor(index),
                    fontSize: '0.75rem',
                    lineHeight: 1.4,
                  }}
                >
                  {step.helpText}
                </Typography>
              </Box>
            </Stack>
          </Box>
        ))}
      </Box>

      {/* Footer - Remove step counter for cleaner look */}
      <Box sx={{ p: 3 }}>
        {/* Empty footer for spacing */}
      </Box>
    </Box>
  );
}