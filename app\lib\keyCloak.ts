// Import the KeyCloak JavaScript library
import KeyCloak from "keycloak-js";

// Retrieve the Keycloak realm ID from environment variables
const REALM_ID = window.ENV?.KEYCLOAK_REALM_ID ?? '1tegrate-dev';

// Retrieve the Keycloak client ID from environment variables
const CLIENT_ID = window.ENV?.KEYCLOAK_CLIENT_ID ?? 'web-gateway-service';

// Retrieve the Keycloak URI (usually the server URL) from environment variables
const URI = window.ENV?.KEYCLOAK_URI ?? 'http://api-dev.unz.net:32350/auth';

const keyCloak = new KeyCloak({
  url: URI,
  realm: REALM_ID,
  clientId: CLIENT_ID
} as any);

export default keyCloak;
