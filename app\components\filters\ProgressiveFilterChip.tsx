import React from "react";
import { <PERSON>, Chip, Typography } from "@mui/material";
import { Filter, FilterField, OPERATOR_LABELS } from "./type";

interface ProgressiveFilterChipProps {
  filter: Filter;
  fields: FilterField[];
  onEdit: (chipElement: HTMLElement) => void;
  onDelete: () => void;
}

export const ProgressiveFilterChip: React.FC<ProgressiveFilterChipProps> = ({
  filter,
  fields,
  onEdit,
  onDelete,
}) => {
  const field = fields.find((f) => f.key === filter.field);
  const fieldLabel = field?.label || filter.field;

  const getChipContent = () => {
    switch (filter.step) {
      case "field":
        return (
          <Typography
            variant="body2"
            component="span"
            sx={{ color: "text.secondary" }}
          >
            {fieldLabel}
          </Typography>
        );

      case "operator":
        return (
          <Typography variant="body2" component="span">
            <strong>{fieldLabel}</strong>{" "}
            <Box component="span" sx={{ color: 'text.secondary' }}>operator...</Box>
          </Typography>
        );

      case "value":
        const operatorLabel = OPERATOR_LABELS[filter.operator];
        return (
          <Typography variant="body2" component="span">
            <strong>{fieldLabel}</strong> {operatorLabel}{" "}
            <Box component="span" sx={{ color: 'text.secondary' }}>value...</Box>
          </Typography>
        );

      case "complete":
        const formatValue = (value: string | string[]) => {
          if (Array.isArray(value)) {
            if (value.length === 1) return value[0];
            if (value.length <= 3) return value.join(", ");
            return `${value.slice(0, 2).join(", ")} +${value.length - 2} more`;
          }
          if (field?.type === "date") {
            try {
              return new Date(value).toLocaleDateString();
            } catch {
              return value;
            }
          }
          return value;
        };

        const displayValue = formatValue(filter.value);
        const operatorText = Array.isArray(filter.value)
          ? "in"
          : OPERATOR_LABELS[filter.operator];

        return (
          <Typography variant="body2" component="span">
            <strong>{fieldLabel}</strong> {operatorText} {displayValue}
          </Typography>
        );

      default:
        return fieldLabel;
    }
  };

  const getChipColor = () => {
    switch (filter.step) {
      case "field":
        return "default";
      case "operator":
        return "primary";
      case "value":
        return "secondary";
      case "complete":
        return "primary";
      default:
        return "default";
    }
  };

  const getChipVariant = () => {
    return filter.step === "complete" ? "filled" : "outlined";
  };

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    onEdit(event.currentTarget);
  };

  return (
    <Chip
      label={getChipContent()}
      onClick={handleClick}
      onDelete={filter.step === "complete" ? onDelete : undefined}
      color={getChipColor()}
      variant={getChipVariant()}
      size="small"
      data-filter-id={filter.id}
      sx={{
        cursor: "pointer",
        "& .MuiChip-label": {
          px: 1,
        },
        "& .MuiChip-deleteIcon": {
          fontSize: "16px",
        },
        ...(filter.isEditing && {
          outline: "2px solid",
          outlineColor: "primary.main",
          outlineOffset: "1px",
        }),
      }}
    />
  );
};
