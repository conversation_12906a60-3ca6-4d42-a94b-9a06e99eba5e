import React from 'react';
import { 
  <PERSON>, 
  Container, 
  <PERSON>po<PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>rid,
  <PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@mui/material';
import {
  PrimaryButton,
  SecondaryButton,
  OutlineButton,
  ModernCard,
  ModernTextField,
  StatusChip,
  StatCard,
  PageHeader,
  SubHeader,
  FeatureCard,
  GradientBox,
  ModernDivider,
} from 'components/styled/modern';

export default function ThemeDemo() {
  // Clear localStorage and set correct theme
  React.useEffect(() => {
    const config = {
      fontFamily: "'Public Sans', sans-serif",
      i18n: 'en',
      menuOrientation: 'vertical',
      miniDrawer: false,
      container: false,
      mode: 'light',
      presetColor: 'unizo',
      themeDirection: 'ltr',
      defaultPath: '/console/dashboard',
    };
    localStorage.setItem('mantis-react-ts-config', JSON.stringify(config));
  }, []);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <PageHeader>Unizo Theme Demo</PageHeader>
      <SubHeader>
        Showcasing the new brand colors and modern components
      </SubHeader>

      <Stack spacing={4}>
        {/* Typography Examples */}
        <ModernCard>
          <Typography variant="h5" gutterBottom>
            Typography
          </Typography>
          <Stack spacing={2}>
            <Typography variant="h1">Heading 1 - Unizo Brand</Typography>
            <Typography variant="h2">Heading 2 - Unizo Brand</Typography>
            <Typography variant="h3">Heading 3 - Unizo Brand</Typography>
            <Typography variant="h4">Heading 4 - Unizo Brand</Typography>
            <Typography variant="body1">
              Body 1 - This is a paragraph with Unizo secondary color. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            </Typography>
            <Typography variant="body2">
              Body 2 - Smaller paragraph text also using the Unizo secondary color.
            </Typography>
            <h1>Native H1 Element</h1>
            <h2>Native H2 Element</h2>
            <h3>Native H3 Element</h3>
            <h4>Native H4 Element</h4>
            <p>Native paragraph element with Unizo branding color.</p>
          </Stack>
        </ModernCard>

        {/* Color Palette */}
        <ModernCard>
          <Typography variant="h5" gutterBottom>
            Brand Colors
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Primary Palette</Typography>
              <Stack spacing={1}>
                {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 950].map((shade) => (
                  <Box
                    key={shade}
                    sx={{
                      bgcolor: shade === 800 ? '#21384f' : `unizo.primary.${shade}`,
                      color: shade >= 500 ? 'white' : 'black',
                      p: 2,
                      borderRadius: 1,
                    }}
                  >
                    {shade === 800 ? 'Primary 800 (#21384f) - Main' : `Primary ${shade}`}
                  </Box>
                ))}
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Secondary Palette</Typography>
              <Stack spacing={1}>
                {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900].map((shade) => (
                  <Box
                    key={shade}
                    sx={{
                      bgcolor: shade === 500 ? '#ea580c' : `unizo.secondary.${shade}`,
                      color: shade >= 400 ? 'white' : 'black',
                      p: 2,
                      borderRadius: 1,
                    }}
                  >
                    {shade === 500 ? 'Secondary 500 (#ea580c) - Main' : `Secondary ${shade}`}
                  </Box>
                ))}
              </Stack>
            </Grid>
          </Grid>
        </ModernCard>

        {/* Buttons */}
        <ModernCard>
          <Typography variant="h5" gutterBottom>
            Buttons
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap">
            <PrimaryButton>Primary Button</PrimaryButton>
            <SecondaryButton>Secondary Button</SecondaryButton>
            <OutlineButton>Outline Button</OutlineButton>
            <MuiButton variant="contained">MUI Primary</MuiButton>
            <MuiButton variant="contained" color="secondary">MUI Secondary</MuiButton>
          </Stack>
        </ModernCard>

        {/* Form Elements */}
        <ModernCard>
          <Typography variant="h5" gutterBottom>
            Form Elements
          </Typography>
          <Stack spacing={2}>
            <ModernTextField
              label="Modern Text Field"
              placeholder="Enter some text..."
              fullWidth
            />
            <ModernTextField
              label="With Helper Text"
              helperText="This is a helper text"
              fullWidth
            />
          </Stack>
        </ModernCard>

        {/* Status Chips */}
        <ModernCard>
          <Typography variant="h5" gutterBottom>
            Status Indicators
          </Typography>
          <Stack direction="row" spacing={2} flexWrap="wrap">
            <StatusChip label="Active" status="active" />
            <StatusChip label="Pending" status="pending" />
            <StatusChip label="Error" status="error" />
            <StatusChip label="Success" status="success" />
            <StatusChip label="Inactive" status="inactive" />
          </Stack>
        </ModernCard>

        {/* Cards */}
        <Typography variant="h5">Cards & Surfaces</Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <StatCard>
              <Typography variant="h6">Stat Card</Typography>
              <Typography variant="h3" color="primary">1,234</Typography>
              <Typography variant="body2" color="text.secondary">
                Total Users
              </Typography>
            </StatCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <FeatureCard>
              <Typography variant="h6">Feature Card</Typography>
              <Typography variant="body2" color="text.secondary">
                Hover me to see the effect
              </Typography>
            </FeatureCard>
          </Grid>
          <Grid item xs={12} md={4}>
            <ModernCard>
              <Typography variant="h6">Modern Card</Typography>
              <Typography variant="body2" color="text.secondary">
                Clean and minimal design
              </Typography>
            </ModernCard>
          </Grid>
        </Grid>

        {/* Gradient Box */}
        <GradientBox>
          <Typography variant="h4" color="white">
            Gradient Background
          </Typography>
          <Typography variant="body1" color="white">
            Using Unizo primary color gradients
          </Typography>
        </GradientBox>

        {/* Alerts */}
        <ModernCard>
          <Typography variant="h5" gutterBottom>
            Alerts
          </Typography>
          <Stack spacing={2}>
            <Alert severity="info">This is an info alert with Unizo theme colors</Alert>
            <Alert severity="success">Success alert message</Alert>
            <Alert severity="warning">Warning alert message</Alert>
            <Alert severity="error">Error alert message</Alert>
          </Stack>
        </ModernCard>
      </Stack>
    </Container>
  );
}