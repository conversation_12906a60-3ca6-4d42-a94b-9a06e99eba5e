import ModernChartCard from "components/cards/ModernChartCard"

import { Stack, Typography } from "@mui/material";

import { GadgetConfig } from "../../layout/grid-type";
import DataGrid from './data-grid'


export default ({ gadget }: GadgetConfig) => {
   return (
      <ModernChartCard
         title={gadget.name}
         subtitle="Error analysis and trends"
         secondary={
            <Stack>
               <Typography className="link" >View All</Typography>
            </Stack>
         }
         content={false}
      >
         <DataGrid />
      </ModernChartCard>
   )
}