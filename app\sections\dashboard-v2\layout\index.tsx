import { useCallback, useMemo } from 'react';

import { Responsive, WidthProvider, ResponsiveProps } from 'react-grid-layout';
import { Box } from '@mui/material';

import gridModal, { GadgetType } from './grid-modal';
import gatGetMapper from './gadget-mapper';
import { GadgetConfig } from './grid-type';
import { getRootProps } from './helper';

const ResponsiveGridLayout = WidthProvider(Responsive);

export default () => {

   const rootProps: ResponsiveProps = useMemo(() => {
      return (
         getRootProps(gridModal.gadgetConfigs)
      )
   }, [gridModal.gadgetConfigs]);

   const resolvedGadget = useCallback((type: GadgetType, item: GadgetConfig) => {
      return gatGetMapper(item)?.[type]
   }, [gridModal.gadgetConfigs])

   return (
      <ResponsiveGridLayout
         {...rootProps}
      >
         {gridModal.gadgetConfigs.map((item, index) => (
            <Box key={index} >
               {resolvedGadget(item.gadget.type, item)}
            </Box>
         ))}
      </ResponsiveGridLayout>
   )
}