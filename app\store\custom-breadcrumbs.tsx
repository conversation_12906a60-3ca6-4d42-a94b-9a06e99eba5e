import { ReactElement } from "react";
import { create } from "zustand";

type CustomBreadcrumbItem = {
   title?: ReactElement | string
   description?: string,
   to?: string
}

type CustomBreadcrumbValue = {
   links?: CustomBreadcrumbItem[]
   title?: string
   description?: string
}

type CustomBreadcrumbType = {
   breadcrumbs: CustomBreadcrumbValue | null,
   update: (updated: CustomBreadcrumbValue) => void
   reset: () => void
}

const useCustomBreadcrumbs = create<CustomBreadcrumbType>((set, get) => ({
   breadcrumbs: null,
   update: (updated: any) => {
      set({ ...get(), breadcrumbs: updated });
   },
   reset: () => {
      set({ ...get(), breadcrumbs: null });
   }
}));

export default useCustomBreadcrumbs;