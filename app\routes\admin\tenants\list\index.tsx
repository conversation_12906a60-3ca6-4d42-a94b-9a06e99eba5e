import {
   <PERSON><PERSON>, <PERSON><PERSON><PERSON>, IconButton, Menu, MenuItem,
   DialogTitle,
   DialogContent,
   DialogActions,
   Alert,
   Button,
   TextField,
   Box
} from "@mui/material";
import { Link } from "@remix-run/react";
import { useGetSuperAdmin } from "hooks/api/super-admin/useGetSuperAdmin";
import { useTable } from "hooks/table/useTable";
import { useDate } from "hooks/useDate";
import { State, useStatus } from "hooks/useStatus";
import { useEffect, useMemo, useState } from "react";
import Table from "components/@extended/Table";
import { EllipsisOutlined } from "@ant-design/icons";
import { DialogClose, Dialog } from "components/@extended/dialog";

const renderTableLeader = (originalRow: Record<string, any>) => {
  const clickable = originalRow?.state === State.PROVISIONED;
  const companyUrl = originalRow?.companyUrl;

  return (
    <Link to={`${originalRow?.organization?.id}`}>
      <Stack className={`${clickable ? 'link' : ''}`}>
        <Typography>{originalRow?.name}</Typography>
        {companyUrl && (
          <Typography>{`(${companyUrl})`}</Typography>
        )}
      </Stack>
    </Link>
  );
};




const TenantList = () => {

   const [paginationState, setPaginationState] = useState<any>({
      pageIndex: 0,
      pageSize: 10,
   })

   const { searchTenants, delete:deleteTenant} = useGetSuperAdmin();
const { data, isFetching, refetch } = searchTenants({ pagination: paginationState });
   const [isOpen, setIsOpen] = useState(false);
   const [tenantName, setTenantName] = useState('');
   const [confirmText, setConfirmText] = useState('');
   const [isId, setIsId] = useState('')


   const handleModalClose = () => {
      setIsOpen(false);
      setTenantName('');
      setConfirmText('');
   };
   const handleConfirmDelete = () => {
      try {
          deleteTenant(isId, () => {
              handleModalClose(); // Close modal after deletion
             refetch();
          });
      } catch (error) {
          console.error("Error while deleting tenant:", error);
      }
  };

   const { loadDate } = useDate(),
      {
         extendedProps,
         applyColumnSize,
         paginationModal: { pagination, onPaginationChange, setTotal }
      } = useTable(),
      { renderStatus } = useStatus()

   const ActionsColumn = ({ record, getItems }: any) => {
      const [anchorEl, setAnchorEl] = useState(null);
      const open = Boolean(anchorEl);

      const handleClick = (event:any) => {
         event.stopPropagation();
         setAnchorEl(event.currentTarget);
      };

      const handleClose = () => {
         setAnchorEl(null);
      };

      const items = getItems(record);

      return (
         <div>
            <IconButton onClick={handleClick}>
               <EllipsisOutlined />
            </IconButton>
            <Menu
               anchorEl={anchorEl}
               open={open}
               onClose={handleClose}
               onClick={(e) => e.stopPropagation()}
            >
               {items.map((item:any, index:number) => (
                  <MenuItem
                     key={index}
                     {...item}
                     onClick={() => {
                        item.onClick();
                        handleClose();
                     }}
                  >
                     {item.label}
                  </MenuItem>
               ))}
            </Menu>
         </div>
      );
   };

   const getItems = (record: any) => [
      {
         label: 'Manage',
         onClick: () => console.log(`Manage ${record.name}`),
         disabled: record.state !== 'PROVISIONED',
      },
      {
         label: 'Delete (force)',
         onClick: () => {
            setTenantName(record.name);
            setIsId(record.id)
            setIsOpen(true);
         },
         sx:{ color: 'error.main' },
         danger: true,
         disabled: record.state !== 'PROVISIONED',
      },
   ];


   
   

   const columns: any = useMemo(
      () => [
         {
            accessorKey: 'resource.action.apiDetails.url',
            header: 'Company',
            muiTableBodyCellProps: {
               className: 'whitespace-nowrap max-w-[50px] overflow-hidden text-ellipsis hover:link cursor-pointer',
            },
            cell: ({ row: { original } }: any) => {
               return renderTableLeader(original)
            },
         },
         {
            accessorKey: 'lastName', //normal accessorKey
            header: 'Org Admin Name',
            cell({row:{original}}:any){
               return `${original.firstName} ${original.lastName}`
            }
         },
         {
            accessorKey: 'members?.[0]?.emails?.[0]',
            header: 'Org Admin Email',
            cell({ row: { original } }: any) {
               return original?.members?.[0]?.emails?.[0] 
            },
         },
         {
            accessorKey: 'state',
            header: 'Status',
            cell({ row: { original } }: any) {
               return renderStatus(original?.state)
            },
         },
         {
            accessorKey: 'subscription.name',
            header: 'Subscription	',
         },
         {
            accessorKey: 'date',
            header: 'Date',
            cell({ row: { original } }: any) {
               return loadDate(original?.changeLog?.lastUpdatedDateTime)
            },
         },
         {
            title: 'Actions',
            header: 'Actions',
            cell({ row: { original } }: any) {
               return <ActionsColumn record={original} getItems={getItems} />
            }
         },
      ],
      [],
   );

   const tenants = useMemo(() => data?.data, [data])
   const total = data?.pagination?.total;


   useEffect(() => {
      setTotal(total);
   }, [total])

   return (
      <>
         <Table
            data={tenants}
            columns={columns}
            totalData={pagination?.total}
            {...{
               onPaginationChange: setPaginationState,
               state: {
                  pagination: {
                     pageIndex: paginationState.pageIndex,
                     pageSize: paginationState?.pageSize,
                  }
               } as any
            }}
         />

         {/* delete tenants */}
         <Dialog open={isOpen} onClose={handleModalClose} maxWidth="sm" fullWidth>
            <DialogTitle variant="h5">Force Delete Tenant</DialogTitle>
            <DialogClose onClose={handleModalClose} />
            <DialogContent dividers>
               <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
                  <Typography>
                     Force delete tenant <strong>{`"${tenantName}"`}</strong> permanently? This action cannot be undone.
                  </Typography>
                  <Alert severity="warning">
                     Proceeding with this action will delete the tenant with all content and can impact related resources.
                  </Alert>
                  <Typography>
                     To avoid accidental changes, we ask you to provide additional written consent.
                  </Typography>
                  <Typography>
                     Type <strong>{tenantName}</strong> to agree.
                  </Typography>
                  <TextField
                     value={confirmText}
                     onChange={(e) => setConfirmText(e.target.value)}
                     placeholder="Enter the value"
                     fullWidth
                  />
               </Box>
            </DialogContent>
            <DialogActions>
               <Button onClick={handleModalClose} color="inherit">
                  Cancel
               </Button>
               <Button
                  onClick={handleConfirmDelete}
                  color="error"
                  variant="contained"
                  disabled={confirmText !== tenantName}
               >
                  Delete
               </Button>
            </DialogActions>
         </Dialog>
      </>
   )
}

export default TenantList;