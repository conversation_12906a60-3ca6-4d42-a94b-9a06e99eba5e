import moment from "moment";
import _ from 'lodash';

// min date endDate field
const minDate = (options = {}) => {
   const { lookFor = 'changeLog_startDateTime', filters = [] } = options;
   const value = _.find(filters, { id: lookFor })?.value ?? null;
   return value ? moment(value) : null
}

// max date startDate field
const maxDate = (options = {}) => {
   const { lookFor = 'changeLog_endDateTime', filters = [] } = options;
   const value = _.find(filters, { id: lookFor })?.value ?? null;
   return value ? moment(value) : null
}

// select the required the prop for the date-time picker
const selectProps = (props = {}) => {
   return _.pick(props, ['minDateTime', 'maxDateTime'])
}

export { minDate, maxDate, selectProps }