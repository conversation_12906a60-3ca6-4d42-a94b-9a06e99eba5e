# AI Agent Guidelines for IPASS Project

This document provides comprehensive guidelines for AI agents working on the IPASS integration platform project. Follow these patterns, conventions, and best practices to ensure consistency and quality.

## Project Overview

IPASS is an integration platform built with modern web technologies. The project follows a well-structured architecture with clear separation of concerns.

## Technology Stack

### Core Frameworks
- **Remix** (v2.13.1) - Full-stack React framework with server-side rendering
- **React** (v19.0.0) - UI library
- **TypeScript** - Type-safe development
- **Vite** - Build tool and development server

### UI Libraries
- **Material-UI (MUI)** (v5.16.8) - Primary component library
- **Ant Design Icons** - Icon library
- **Tailwind CSS** - Utility-first CSS (with tailwind-merge)
- **Emotion** - CSS-in-JS styling

### State Management
- **Zustand** (v5.0.0) - Client state management
- **TanStack Query** (v5.59.15) - Server state and data fetching
- **React Hook Form** (v7.45.4) - Form state management

### API Layer
- **Axios** - HTTP client with interceptors
- **SWR** - Additional data fetching library

### Validation
- **Zod** (v3.23.8) - Schema validation
- **Yup** (v1.4.0) - Alternative validation library

## Project Structure

```
app/
├── components/       # Reusable UI components
├── constants/        # Application constants
├── contexts/         # React contexts
├── hooks/           # Custom React hooks
│   └── api/         # API-specific hooks
├── lib/             # Library configurations
├── routes/          # Remix routes and pages
├── sections/        # Feature-specific components
├── services/        # API service layer
├── store/           # Zustand stores
├── themes/          # Theme configurations
├── types/           # TypeScript type definitions
└── utils/           # Utility functions
```

## Coding Standards

### TypeScript Guidelines
1. **Strict Mode**: Project uses TypeScript strict mode
2. **Type Files**: Place type definitions in `app/types/*.d.ts`
3. **Avoid `any`**: ESLint warns on `@typescript-eslint/no-explicit-any`
4. **Module Exports**: Use ES modules, not CommonJS

### Import Order (Enforced by ESLint)
```typescript
// 1. React imports
import React, { useState, useEffect } from 'react';

// 2. External libraries
import { useNavigate } from '@remix-run/react';

// 3. MUI imports
import { Box, Button } from '@mui/material';

// 4. Internal imports
import ComponentName from 'components/ComponentName';
```

### Path Aliases
Use these configured aliases:
- `components/*` → `app/components/*`
- `utils/*` → `app/utils/*`
- `hooks/*` → `app/hooks/*`
- `services/*` → `app/services/*`
- `types/*` → `app/types/*`

## API Integration Patterns

### Service Layer Pattern
```typescript
// app/services/[service-name].ts
import platformfetchInstance from "utils/api/fetchinstance/platform-fetch-Instance";

export const serviceNameClient = {
  getAll: () => {
    return platformfetchInstance.get(`/endpoint`)
  },
  getById: (id: string) => {
    return platformfetchInstance.get(`/endpoint/${id}`)
  }
};
```

### Custom Hook Pattern
```typescript
// app/hooks/api/[hook-name].ts
import { queryOptions } from "@tanstack/react-query"
import { serviceNameClient } from "services/[service-name]"

const useGetResource = () => {
  return {
    getResourceQuery: (id: string) => {
      return queryOptions({
        queryKey: ['resource', id],
        queryFn: () => serviceNameClient.getById(id)
      })
    }
  }
}

export default useGetResource;
```

### Axios Instance Configuration
- Base URL and headers configured in `platform-fetch-Instance.tsx`
- Automatic organization ID injection via interceptors
- Built-in 401/403 error handling

## React Component Patterns

### Component Structure
```typescript
import React, { useState, useEffect } from 'react';
import { Box, Card, Typography } from '@mui/material';
import { useQuery } from '@tanstack/react-query';

interface ComponentProps {
  id: string;
  onSubmit: (data: FormData) => void;
}

export default function ComponentName({ id, onSubmit }: ComponentProps) {
  const [state, setState] = useState<StateType>();
  
  // Hooks
  const { data, isLoading } = useQuery(queryOptions);
  
  // Effects
  useEffect(() => {
    // Effect logic
  }, [dependency]);
  
  // Handlers
  const handleClick = () => {
    // Handler logic
  };
  
  // Render
  if (isLoading) return <Loading />;
  
  return (
    <Box>
      {/* Component JSX */}
    </Box>
  );
}
```

### MUI Theme Usage
- Use MUI's `useTheme` hook for theme access
- Prefer MUI components over custom implementations
- Use `sx` prop for styling when possible

## Routing with Remix

### Route Structure
- Routes defined in `vite.config.ts`
- Nested routes for feature organization
- File-based routing with explicit configuration

### Route Conventions
```typescript
// app/routes/console/feature/index.tsx
import { MetaFunction } from "@remix-run/node";

export const meta: MetaFunction = () => {
  return [{ title: "Page Title" }];
};

export default function FeaturePage() {
  return <FeatureComponent />;
}
```

## Best Practices

### 1. State Management
- Use Zustand for global client state
- Use TanStack Query for server state
- Keep component state local when possible

### 2. Error Handling
- Use React Error Boundaries
- Handle API errors in interceptors
- Provide user-friendly error messages

### 3. Performance
- Use React.memo for expensive components
- Implement proper loading states
- Utilize TanStack Query caching

### 4. Security
- Never expose secrets in client code
- Use environment variables properly
- Implement proper authentication checks

### 5. Testing
- Check for test scripts in package.json
- Follow existing test patterns
- Never assume test framework

### 6. Code Comments
- Do not add comments unless explicitly requested
- Code should be self-documenting
- Use TypeScript types for clarity

## Common Pitfalls to Avoid

1. **No Inline Styles**: ESLint forbids inline style props
2. **Import Order**: Must follow the configured order
3. **Path Imports**: Use aliases, not relative paths
4. **API Calls**: Always use the service layer pattern
5. **Type Safety**: Avoid `any` types

## Environment-Specific Notes

### Development
```bash
npm run dev         # Start development server
npm run lint        # Run linting
npm run typecheck   # Check TypeScript
```

### Build Process
- Vite handles bundling
- TypeScript configured for `noEmit`
- Remix handles SSR compilation

## File Naming Conventions

- Components: `PascalCase.tsx`
- Hooks: `camelCase.ts` (prefix with `use`)
- Services: `kebab-case.ts`
- Types: `kebab-case.d.ts`
- Constants: `UPPER_SNAKE_CASE.ts`

## Summary

When implementing features:
1. Follow the established patterns
2. Use the configured technology stack
3. Maintain consistency with existing code
4. Leverage TypeScript for type safety
5. Use the service/hook pattern for API calls
6. Follow the import order and aliases
7. Avoid inline styles and use MUI theming