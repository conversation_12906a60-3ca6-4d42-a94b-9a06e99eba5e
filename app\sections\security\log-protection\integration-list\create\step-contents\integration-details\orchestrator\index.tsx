import { useMemo } from "react"; // React Hooks
import { Box, Typography } from "@mui/material"; // MUI Components

// Types for Log Protection
import { LogProtectionConnectorType } from "constants/log-protection";

// Components
import WebHook from "../webhook";
import Standard from "../standard";

// Global Store
import useLogProtection from "store/security/log-protection";

export default () => {
   // Access Log Protection Values
   const { values } = useLogProtection();

   // Memoized Form Container Based on Service Type:
   // - If the service type matches 'Webhook', render the WebHook component.
   // - For all other cases, render the Standard component.
   const formContainer = useMemo(() => {
      switch (values?.service?.type) {
         case LogProtectionConnectorType.Webhook:
            return <WebHook />;
         default:
            return <Standard />;
      }
   }, [values?.service?.type]);

   return <Box>{formContainer}</Box>;
};
