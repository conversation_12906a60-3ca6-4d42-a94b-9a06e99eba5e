import * as React from 'react';
import { styled } from '@mui/material';
import ArrowForwardIosSharpIcon from '@mui/icons-material/ArrowForwardIosSharp';
import MuiAccordion, { AccordionProps } from '@mui/material/Accordion';
import MuiAccordionSummary, {
   AccordionSummaryProps,
} from '@mui/material/AccordionSummary';
import MuiAccordionDetails from '@mui/material/AccordionDetails';
import Typography from '@mui/material/Typography';

const Accordion = styled((props: AccordionProps) => (
   <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
   border: `1px solid ${theme.palette.divider}`,
   '&:not(:last-child)': {
      borderBottom: 0,
   },
   '&::before': {
      display: 'none',
   },
}));

const AccordionSummary = styled((props: AccordionSummaryProps) => (
   <MuiAccordionSummary
      expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: '0.9rem' }} />}
      {...props}
   />
))(({ theme }) => ({
   backgroundColor: 'rgba(0, 0, 0, .03)',
   flexDirection: 'row-reverse',
   '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
      transform: 'rotate(90deg)',
   },
   '& .MuiAccordionSummary-content': {
      marginLeft: theme.spacing(1),
   },
   ...theme.applyStyles('dark', {
      backgroundColor: 'rgba(255, 255, 255, .05)',
   }),
}));

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
   padding: theme.spacing(2),
   borderTop: '1px solid rgba(0, 0, 0, .125)',
}));

type ItemTypes = {
   title?: string
   children?: any
   value?: string
}

type CustomizedAccordionsProps = {
   items?: ItemTypes[]
   expanded?: string[]
   onChange?: (event: React.SyntheticEvent, newExpanded: boolean) => void
   defaultExpanded?: string[]
}

export default function Collapse({
   expanded: expandedProp,
   onChange: onChangeProp,
   defaultExpanded =[],
   ...props
}: CustomizedAccordionsProps) {
   const [expandedLocal, setExpandedLocal] = React.useState<string[]>(defaultExpanded);

   const expanded = expandedProp ?? expandedLocal;

   const handleChange =
      (panel: string) => (event: React.SyntheticEvent, newExpanded: boolean) => {
         setExpandedLocal((prev) => {
            if (prev.includes(panel)) {
               return prev.filter((p) => p!== panel);
            } else {
               return [...prev, panel]
            }
         });
         onChangeProp && onChangeProp(event, newExpanded)
      };

   const items = props?.items ?? []

   return (
      <div>

         {items?.map((item, index) => {
            const isExpanded = expanded.includes(item?.value as string)
            return (
               <Accordion key={index} expanded={isExpanded} onChange={handleChange(item?.value as string)}>
                  <AccordionSummary aria-controls="panel1d-content" id="panel1d-header">
                     <Typography>{item?.title}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                     {item?.children}
                  </AccordionDetails>
               </Accordion>
            )
         })}
      </div>
   );
}
