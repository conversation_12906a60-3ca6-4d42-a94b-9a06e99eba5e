/**
 * Quick Start Flow Types and Interfaces
 */

export type TenantType = 'trial' | 'basic' | 'enterprise' | 'security' | 'infra' | 'custom';

export interface TenantConfiguration {
  type: TenantType;
  predefinedCategories?: string[];
  isPredefined: boolean;
  allowCustomization: boolean;
  skipAllowed: boolean;
}

export interface QuickStartProgress {
  completed: boolean;
  currentStep: number;
  completedSteps: string[];
  selectedCategories: string[];
  configuredServices: string[];
  lastUpdated: string;
}

export interface QuickStartSettings {
  showOnLogin: boolean;
  forceCompletion: boolean;
  allowSkip: boolean;
  customMessage?: string;
}

export interface ServiceConfiguration {
  serviceProfileId: string;
  category: string;
  name: string;
  logo?: string;
  isConfigured: boolean;
  configuration?: Record<string, any>;
  webhookUrl?: string;
  authType?: 'oauth' | 'api_key' | 'basic' | 'custom';
}

export interface QuickStartStepConfig {
  id: string;
  title: string;
  description: string;
  helpText?: string;
  isOptional?: boolean;
  validationRules?: QuickStartValidation[];
}

export interface QuickStartValidation {
  type: 'required' | 'minSelection' | 'maxSelection' | 'custom';
  value?: any;
  message: string;
  validate?: (data: any) => boolean;
}

export interface QuickStartCompletionData {
  tenantId: string;
  userId: string;
  completedAt: string;
  selectedCategories: string[];
  configuredServices: ServiceConfiguration[];
  skippedSteps?: string[];
  totalDuration: number; // in seconds
}

// Predefined configurations for different tenant types
export const TENANT_CONFIGURATIONS: Record<TenantType, TenantConfiguration> = {
  trial: {
    type: 'trial',
    isPredefined: false,
    allowCustomization: true,
    skipAllowed: true,
  },
  basic: {
    type: 'basic',
    predefinedCategories: ['SCM', 'TICKETING'],
    isPredefined: true,
    allowCustomization: true,
    skipAllowed: false,
  },
  enterprise: {
    type: 'enterprise',
    predefinedCategories: ['SCM', 'TICKETING', 'PCR', 'INCIDENT'],
    isPredefined: true,
    allowCustomization: true,
    skipAllowed: false,
  },
  security: {
    type: 'security',
    predefinedCategories: ['VMS', 'IDENTITY', 'EDR'],
    isPredefined: true,
    allowCustomization: false,
    skipAllowed: false,
  },
  infra: {
    type: 'infra',
    predefinedCategories: ['INFRA', 'MONITORING', 'INCIDENT'],
    isPredefined: true,
    allowCustomization: true,
    skipAllowed: false,
  },
  custom: {
    type: 'custom',
    isPredefined: false,
    allowCustomization: true,
    skipAllowed: true,
  }
};

// API response types
export interface QuickStartStatusResponse {
  needsQuickStart: boolean;
  progress?: QuickStartProgress;
  configuration: TenantConfiguration;
  settings: QuickStartSettings;
}

export interface QuickStartSaveRequest {
  step: string;
  data: Record<string, any>;
  isComplete?: boolean;
}

export interface QuickStartSaveResponse {
  success: boolean;
  progress: QuickStartProgress;
  nextStep?: string;
  errors?: string[];
}