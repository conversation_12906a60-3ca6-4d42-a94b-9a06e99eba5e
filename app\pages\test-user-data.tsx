import React, { useEffect } from 'react';
import { Box, Typography, Paper, Stack, Chip } from '@mui/material';
import useUserDetails from 'store/user';

export default function TestUserData() {
  const { 
    user, 
    subscriptions, 
    categories, 
    isLoading, 
    isSubscriptionLoading,
    fetchUser 
  } = useUserDetails();
  
  const USER_ID = '80e3d37d-7b80-4275-99d1-1263fdcf099c';
  
  useEffect(() => {
    // Set window auth variables
    window.authUserId = USER_ID;
    
    // Fetch user data
    fetchUser(USER_ID);
  }, []);
  
  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h4" gutterBottom>User Data Test Page</Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Testing with User ID: {USER_ID}
      </Typography>
      
      <Stack spacing={3} sx={{ mt: 3 }}>
        {/* User Info */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>User Information</Typography>
          {isLoading ? (
            <Typography>Loading user...</Typography>
          ) : user ? (
            <Stack spacing={1}>
              <Typography>ID: {user.id}</Typography>
              <Typography>Organization ID: {user.organization?.id}</Typography>
              <Typography>Organization Name: {user.organization?.name}</Typography>
            </Stack>
          ) : (
            <Typography color="error">No user data</Typography>
          )}
        </Paper>
        
        {/* Subscriptions */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>Subscriptions</Typography>
          {isSubscriptionLoading ? (
            <Typography>Loading subscriptions...</Typography>
          ) : subscriptions?.length > 0 ? (
            <Stack spacing={2}>
              {subscriptions.map((sub, index) => (
                <Box key={sub.id} sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                  <Typography variant="subtitle2">Subscription {index + 1}</Typography>
                  <Typography variant="body2">Product Key: {sub.productKey}</Typography>
                  <Typography variant="body2">Product Name: {sub.product?.name}</Typography>
                  <Typography variant="body2">State: {sub.state}</Typography>
                  <Typography variant="body2">
                    Pricing Tier: {sub.charge?.pricingTier?.name || 'N/A'}
                  </Typography>
                </Box>
              ))}
            </Stack>
          ) : (
            <Typography>No subscriptions (Trial mode)</Typography>
          )}
        </Paper>
        
        {/* Categories */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>Categories</Typography>
          {categories?.length > 0 ? (
            <Stack direction="row" spacing={1} flexWrap="wrap">
              {categories.map(cat => (
                <Chip
                  key={cat.value}
                  label={`${cat.label} (${cat.value})`}
                  color={cat.disabled ? 'default' : 'primary'}
                  variant={cat.disabled ? 'outlined' : 'filled'}
                  size="small"
                />
              ))}
            </Stack>
          ) : (
            <Typography>No categories loaded</Typography>
          )}
        </Paper>
      </Stack>
    </Box>
  );
}