name: Build unz-web-portal
on:
  push:
    branches: [ "main" ]
  workflow_dispatch:

env:
  APPROVAL_REQUIRED: true
  ENVIRONMENT: dev

jobs:
  build:
    runs-on: ui
    outputs:
      build-version: ${{ steps.version.outputs.version }}
      build-status: ${{ steps.build-status.outputs.status }}
    steps:
      - uses: actions/checkout@v3

      - name: Generate Build Version
        id: version
        run: |
          VERSION=$(date +%Y%m%d%H%M%S)-$(git rev-parse --short HEAD)
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "BUILD_VERSION=$VERSION" >> $GITHUB_ENV
          
      - name: Load environment variables from .env file
        run: |
          cat .github/workflows/.env-dev | grep -v '^#' | while IFS= read -r line;do
          echo "$line" >> $GITHUB_ENV
          done

      - name: Build
        run: |
          docker build --rm --no-cache --build-arg NODE_ENV=$NODE_ENV -t $IMAGE_REGISTRY/$DOCKER_IMAGE .
          docker tag $IMAGE_REGISTRY/$DOCKER_IMAGE $IMAGE_REGISTRY/$DOCKER_IMAGE:$DOCKER_TAG
          docker push $IMAGE_REGISTRY/$DOCKER_IMAGE:$DOCKER_TAG

      - name: Cleanup dangling and unused docker resources
        run: docker system prune --force

      - name: Set Build Status
        id: build-status
        run: |
          echo "status=success" >> $GITHUB_OUTPUT

  scan_code:
    runs-on: ui
    needs: build
    outputs:
      security-status: ${{ steps.security-check.outputs.status }}
      vulnerability-count: ${{ steps.vulnerability-count.outputs.count }}
    steps:
      - uses: actions/checkout@v4
      - name: Install Semgrep
        run: |
          sudo apt-get update && sudo apt-get install -y python3 python3-pip
          pip3 install semgrep
      - name: Run Semgrep security scan
        run: |
          semgrep --config p/security-audit --json > semgrep-code-report.json || true
      - name: Analyze Security Results
        id: security-check
        run: |
          HIGH_VULN=$(cat semgrep-code-report.json | jq '[.results[] | select(.extra.severity == "ERROR")] | length')
          MEDIUM_VULN=$(cat semgrep-code-report.json | jq '[.results[] | select(.extra.severity == "WARNING")] | length')
          echo "High vulnerabilities: $HIGH_VULN"
          echo "Medium vulnerabilities: $MEDIUM_VULN"
          if [ "$HIGH_VULN" -gt 0 ]; then
            echo "status=failed" >> $GITHUB_OUTPUT
          elif [ "$MEDIUM_VULN" -gt 0 ]; then
            echo "status=review_required" >> $GITHUB_OUTPUT
          else
            echo "status=passed" >> $GITHUB_OUTPUT
          fi
      - name: Count Vulnerabilities
        id: vulnerability-count
        run: |
          TOTAL_VULN=$(cat semgrep-code-report.json | jq '.results | length')
          echo "count=$TOTAL_VULN" >> $GITHUB_OUTPUT
      - name: Install Jinja2 for HTML conversion
        run: |
          sudo apt-get install -y python3 python3-pip
          pip3 install jinja2
      - name: Convert Semgrep JSON report to HTML
        run: python3 external-assets/semgrep/convert_to_html_semgrep.py semgrep-code-report.json semgrep-code-report.html Semgrep
      - name: Upload Semgrep HTML report
        uses: actions/upload-artifact@v4
        with:
          name: semgrep-code-report-html
          path: semgrep-code-report.html

  scan_dependencies:
    runs-on: ui
    needs: build
    outputs:
      dependency-status: ${{ steps.dependency-check.outputs.status }}
    steps:
      - uses: actions/checkout@v4
      - name: Install Trivy
        run: |
          sudo apt-get update && sudo apt-get install wget -y
          wget https://github.com/aquasecurity/trivy/releases/download/v0.45.0/trivy_0.45.0_Linux-64bit.deb
          sudo dpkg -i trivy_0.45.0_Linux-64bit.deb
      - name: Scan project dependencies with Trivy
        run: |
          trivy fs --ignorefile external-assets/trivy/.trivyignore --format json --output trivy-dependencies-report.json . || true
      - name: Analyze Dependency Results
        id: dependency-check
        run: |
          CRITICAL_VULN=$(cat trivy-dependencies-report.json | jq '[.Results[]?.Vulnerabilities[]? | select(.Severity == "CRITICAL")] | length')
          HIGH_VULN=$(cat trivy-dependencies-report.json | jq '[.Results[]?.Vulnerabilities[]? | select(.Severity == "HIGH")] | length')
          echo "Critical vulnerabilities: $CRITICAL_VULN"
          echo "High vulnerabilities: $HIGH_VULN"
          if [ "$CRITICAL_VULN" -gt 0 ]; then
            echo "status=failed" >> $GITHUB_OUTPUT
          elif [ "$HIGH_VULN" -gt 0 ]; then
            echo "status=review_required" >> $GITHUB_OUTPUT
          else
            echo "status=passed" >> $GITHUB_OUTPUT
          fi
      - name: Install Python and dependencies
        run: |
          sudo apt-get install -y python3 python3-pip
          pip3 install jinja2
      - name: Convert Trivy JSON report to HTML
        run: python3 external-assets/trivy/convert_to_html_trivy.py trivy-dependencies-report.json trivy-dependencies-report.html Dependencies
      - name: Upload Trivy Dependencies HTML report
        uses: actions/upload-artifact@v4
        with:
          name: trivy-dependencies-report-html
          path: trivy-dependencies-report.html

  scan_images:
    runs-on: ui
    needs: build
    outputs:
      image-status: ${{ steps.image-check.outputs.status }}
    steps:
      - uses: actions/checkout@v4
      - name: Load environment variables from .env file
        run: |
          cat .github/workflows/.env-dev | grep -v '^#' | while IFS= read -r line;do
          echo "$line" >> $GITHUB_ENV
          done
      - name: Docker pull image
        run: |
          docker pull $IMAGE_REGISTRY/$DOCKER_IMAGE:$DOCKER_TAG
      - name: Install Trivy
        run: |
          sudo apt-get update && sudo apt-get install wget -y
          wget https://github.com/aquasecurity/trivy/releases/download/v0.45.0/trivy_0.45.0_Linux-64bit.deb
          sudo dpkg -i trivy_0.45.0_Linux-64bit.deb
      - name: Scan Docker image with Trivy
        run: |
          trivy image --ignorefile external-assets/trivy/.trivyignore --scanners vuln --format json --output trivy-images-scan-report.json $IMAGE_REGISTRY/$DOCKER_IMAGE:$DOCKER_TAG || true
      - name: Analyze Image Results
        id: image-check
        run: |
          CRITICAL_VULN=$(cat trivy-images-scan-report.json | jq '[.Results[]?.Vulnerabilities[]? | select(.Severity == "CRITICAL")] | length')
          HIGH_VULN=$(cat trivy-images-scan-report.json | jq '[.Results[]?.Vulnerabilities[]? | select(.Severity == "HIGH")] | length')
          echo "Critical vulnerabilities: $CRITICAL_VULN"
          echo "High vulnerabilities: $HIGH_VULN"
          if [ "$CRITICAL_VULN" -gt 0 ]; then
            echo "status=failed" >> $GITHUB_OUTPUT
          elif [ "$HIGH_VULN" -gt 0 ]; then
            echo "status=review_required" >> $GITHUB_OUTPUT
          else
            echo "status=passed" >> $GITHUB_OUTPUT
          fi
      - name: Install Python and dependencies
        run: |
          sudo apt-get install -y python3 python3-pip
          pip3 install jinja2
      - name: Convert Trivy JSON report to HTML
        run: python3 external-assets/trivy/convert_to_html_trivy.py trivy-images-scan-report.json trivy-images-scan-report.html Images
      - name: Upload Trivy Images HTML report
        uses: actions/upload-artifact@v4
        with:
          name: trivy-images-scan-report-html
          path: trivy-images-scan-report.html
      - name: Cleanup dangling and unused docker resources
        run: docker system prune --force

  approval:
    runs-on: ui
    needs: [build, scan_code, scan_dependencies, scan_images]
    outputs:
      approved: ${{ steps.final-status.outputs.approved }}
    steps:
      - name: Check Security Results
        id: check-approval
        run: |
          # Check if any security scan failed
          if [[ "${{ needs.scan_code.outputs.security-status }}" == "failed" || 
                "${{ needs.scan_dependencies.outputs.dependency-status }}" == "failed" || 
                "${{ needs.scan_images.outputs.image-status }}" == "failed" ]]; then
            echo " Security scans failed - manual approval required"
            echo "needs_manual_approval=true" >> $GITHUB_OUTPUT
          else
            echo " Security scans passed - auto-approved"
            echo "needs_manual_approval=false" >> $GITHUB_OUTPUT
          fi

      - name: Create Approval Issue and Wait
        if: steps.check-approval.outputs.needs_manual_approval == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GH_PROMOTE_TOKEN }}
          script: |
            const issue = await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: ` Manual Approval Required: ${{ needs.build.outputs.build-version }}`,
              body: `## Security Issues Found
              
              **Version**: ${{ needs.build.outputs.build-version }}
              **Build Status**: ${{ needs.build.outputs.build-status }}
              **Requested by**: ${{ github.actor }}
              
              ### Scan Results:
              - Code Security: ${{ needs.scan_code.outputs.security-status }}
              - Dependencies: ${{ needs.scan_dependencies.outputs.dependency-status }}
              - Docker Image: ${{ needs.scan_images.outputs.image-status }}
              
              **To approve this release, comment:**
              \`\`\`
              /approve
              \`\`\`
              
              **To reject this release, comment:**
              \`\`\`
              /reject
              \`\`\`
              `,
              labels: ['approval-required', 'security-review']
            });
            
            // Wait for approval (check every 30 seconds for 20 minutes)
            let approved = false;
            const maxWait = 20 * 60 * 1000; // 20 minutes
            const checkInterval = 10 * 1000; // 10 seconds
            let elapsed = 0;
            
            while (!approved && elapsed < maxWait) {
              await new Promise(resolve => setTimeout(resolve, checkInterval));
              elapsed += checkInterval;
              
              const comments = await github.rest.issues.listComments({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue.data.number
              });
              
              for (const comment of comments.data) {
                if (comment.body.includes('/approve')) {
                  approved = true;
                  console.log(' Release approved by: ' + comment.user.login);
                  break;
                } else if (comment.body.includes('/reject')) {
                  await github.rest.issues.update({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    issue_number: issue.data.number,
                    state: 'closed'
                  });
                  throw new Error(' Release rejected by: ' + comment.user.login);
                }
              }
              
              if (!approved) {
                console.log(` Waiting for approval... (${elapsed/1000}s elapsed)`);
              }
            }
            
            if (!approved) {
              await github.rest.issues.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue.data.number,
                state: 'closed'
              });
              throw new Error(' Approval timeout - no response received');
            }
            
            // Close the issue
            await github.rest.issues.update({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: issue.data.number,
              state: 'closed'
            });

      - name: Set Final Approval Status
        id: final-status
        run: |
          if [[ "${{ steps.check-approval.outputs.needs_manual_approval }}" == "false" ]]; then
            echo "approved=true" >> $GITHUB_OUTPUT
            echo " Auto-approved due to passing security scans"
          else
            # If we reach here, manual approval was successful (otherwise the previous step would have failed)
            echo "approved=true" >> $GITHUB_OUTPUT
            echo " Manual approval received"
          fi
  deployment:
    runs-on: ui
    needs: [build, approval]
    if: always() && needs.build.result == 'success' && needs.approval.result == 'success' && needs.approval.outputs.approved == 'true'
    steps:

      - uses: actions/checkout@v3

      - name: Load environment variables from .env file
        run: |
          cat .github/workflows/.env-dev | grep -v '^#' | while IFS= read -r line;do
          echo "$line" >> $GITHUB_ENV
          done

      - name: Pull k8s manifests
        uses: actions/checkout@v3
        with:
          repository: Unizo-Inc/unizo-infra-manifests
          path: unizo-infra-manifests
          token: ${{ secrets.PAT_TOKEN }}
          ref: main

      - name: Prepare kubernetes manifest
        run: |
          cat ${K8S_MANIFEST} | envsubst > deployment.yaml

      - name: Set up kubectl
        uses: azure/setup-kubectl@v1
        with:
          version: 'latest'

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -f deployment.yaml -n ${NAMESPACE} --kubeconfig=${K8S_CONFIG}

      - name: Restart Kubernetes Deployment
        run: |
          kubectl rollout restart deployment/${APP_NAME} -n ${NAMESPACE} --kubeconfig=${K8S_CONFIG}

      - name: Cleanup unused docker images
        run: |
          docker rmi $IMAGE_REGISTRY/$DOCKER_IMAGE || true
          docker rmi $IMAGE_REGISTRY/$DOCKER_IMAGE:$DOCKER_TAG || true
