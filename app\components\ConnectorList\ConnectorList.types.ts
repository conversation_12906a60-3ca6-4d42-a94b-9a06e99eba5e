export interface Connector {
  id: string;
  name: string;
  displayName: string;
  icon: string;
  enabled: boolean;
  category?: string;
}

export interface ConnectorListProps {
  title: string;
  subtitle?: string;
  docsLink?: string;
  connectors: Connector[];
  onToggle: (id: string, enabled: boolean) => void;
  onRefresh?: () => void;
  onConnectorClick?: (connector: Connector) => void;
  searchPlaceholder?: string;
  className?: string;
}

export interface ConnectorCardProps {
  connector: Connector;
  onToggle: (enabled: boolean) => void;
  onClick?: () => void;
}