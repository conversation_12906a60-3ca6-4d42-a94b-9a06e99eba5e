import { useEffect } from 'react';
import CreateUserFlow from 'sections/advanced/userflow';
import useCustomBreadcrumbs from 'store/custom-breadcrumbs';

export default () => {
   const { update, reset } = useCustomBreadcrumbs()
     useEffect(() => {
           update({
             
              links: [
                 { title: 'Connect UI / Create a Connect UI', to: '/console/connect-UI' },
              ]
           });
     
           return () => {
              reset()
           }
        }, [])
   return (
      <CreateUserFlow />
   )
}