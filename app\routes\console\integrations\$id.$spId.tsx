import { Stack } from "@mui/material";
import { type MetaFunction } from "@remix-run/node";
import { FlowDiagram } from "sections/integration-details/flow-diagram";
import { RootTabs } from "sections/integration-details/tabs";
import { ClientOnly } from 'remix-utils/client-only';
import { DetailPageSkeleton } from 'components/skeletons';


export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
   ];
};


export default function () {
   return (
      <ClientOnly fallback={<DetailPageSkeleton />}>
         {() => {

            return (
               <Stack gap={2}>
                  <FlowDiagram />
                  <RootTabs />
               </Stack>
            )
         }}
      </ClientOnly>
   )
}