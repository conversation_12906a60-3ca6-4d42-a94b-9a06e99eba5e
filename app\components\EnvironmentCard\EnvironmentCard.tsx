import React from 'react';

import { EditOutlined } from '@ant-design/icons';

import { EnvironmentCardProps, EnvironmentStatus } from './EnvironmentCard.types';
import {
  StyledEnvironmentCard,
  CardHeader,
  HeaderContent,
  StatusIndicator,
  StyledTitle,
  StyledDescription,
  StyledIconButton,
} from './EnvironmentCard.styles';

export const EnvironmentCard: React.FC<EnvironmentCardProps> = ({
  name,
  description,
  isProduction = false,
  onEdit,
  className,
}) => {
  const status: EnvironmentStatus = isProduction ? 'production' : 'development';

  return (
    <StyledEnvironmentCard status={status} className={className}>
      <CardHeader>
        <HeaderContent>
          <StatusIndicator status={status} />
          <StyledTitle variant="subtitle1">{name}</StyledTitle>
        </HeaderContent>
        {onEdit && (
          <StyledIconButton
            size="small"
            onClick={onEdit}
            aria-label="Edit environment"
            status={status}
          >
            <EditOutlined style={{ fontSize: 18 }} />
          </StyledIconButton>
        )}
      </CardHeader>
      <StyledDescription variant="body2">{description}</StyledDescription>
    </StyledEnvironmentCard>
  );
};