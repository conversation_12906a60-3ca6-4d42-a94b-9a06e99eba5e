import { useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  MenuItem,
  FormControlLabel,
  Switch,
  Grid,
  FormHelperText
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Webhook, WebhookType, WebhookCategory, CreateWebhookPayload } from 'types/webhook';
import { useWebhooks } from 'hooks/api/webhooks/useWebhooks';
import useUserDetails from 'store/user';

interface WebhookDialogProps {
  open: boolean;
  onClose: () => void;
  webhook?: Webhook | null;
  onSuccess: () => void;
}

const webhookSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  url: z.string().url('Must be a valid URL'),
  type: z.string().min(1, 'Type is required') as z.ZodType<WebhookType>,
  category: z.string().min(1, 'Category is required') as z.ZodType<WebhookCategory>,
  isActive: z.boolean()
});

type WebhookFormData = z.infer<typeof webhookSchema>;

const webhookCategories: { value: WebhookCategory; label: string }[] = [
  { value: 'Communications', label: 'Communications' },
  { value: 'Incident management', label: 'Incident management' },
  { value: 'Identity', label: 'Identity' },
  { value: 'Platform', label: 'Platform' },
  { value: 'Source Code', label: 'Source Code' }
];

const webhookTypes: { value: WebhookType; label: string }[] = [
  { value: 'PLATFORM_WATCH_HOOK', label: 'Platform Watch Hook' },
  { value: 'SCM_WATCH_HOOK', label: 'SCM Watch Hook' },
  { value: 'TICKETING_WATCH_HOOK', label: 'Ticketing Watch Hook' },
  { value: 'ORG_ACTIVITY_HOOK', label: 'Organization Activity Hook' },
  { value: 'CONSUMER_ACTIVITY_HOOK', label: 'Consumer Activity Hook' },
  { value: 'INTEGRATION_ACTIVITY_HOOK', label: 'Integration Activity Hook' },
  { value: 'RESOURCE_ADDED_HOOK', label: 'Resource Added Hook' },
  { value: 'RESOURCE_UPDATED_HOOK', label: 'Resource Updated Hook' },
  { value: 'RESOURCE_DELETED_HOOK', label: 'Resource Deleted Hook' },
  { value: 'CUSTOM_ACTION_TRIGGERED_HOOK', label: 'Custom Action Triggered Hook' }
];

export const WebhookDialog = ({ open, onClose, webhook, onSuccess }: WebhookDialogProps) => {
  const { organizationId } = useUserDetails();
  const { createWebhook, updateWebhook } = useWebhooks();
  const isEdit = !!webhook;

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<WebhookFormData>({
    resolver: zodResolver(webhookSchema),
    defaultValues: {
      name: '',
      url: '',
      type: 'PLATFORM_WATCH_HOOK' as WebhookType,
      category: 'Platform' as WebhookCategory,
      isActive: true
    }
  });

  useEffect(() => {
    if (webhook) {
      reset({
        name: webhook.name,
        url: webhook.url,
        type: webhook.type,
        category: webhook.category,
        isActive: webhook.isActive
      });
    } else {
      reset({
        name: '',
        url: '',
        type: 'PLATFORM_WATCH_HOOK' as WebhookType,
        category: 'Platform' as WebhookCategory,
        isActive: true
      });
    }
  }, [webhook, reset]);

  const onSubmit = async (data: WebhookFormData) => {
    try {
      if (isEdit && webhook) {
        await updateWebhook.mutateAsync({
          organizationId,
          webhookId: webhook.id,
          data: {
            name: data.name,
            url: data.url,
            type: data.type,
            category: data.category,
            isActive: data.isActive
          }
        });
      } else {
        const payload: CreateWebhookPayload = {
          name: data.name,
          url: data.url,
          type: data.type,
          category: data.category,
          isActive: data.isActive
        };
        await createWebhook.mutateAsync({ organizationId, data: payload });
      }
      onSuccess();
    } catch (error) {
      console.error('Failed to save webhook:', error);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogTitle>
          {isEdit ? 'Edit Webhook' : 'Create Callback Url'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Name"
                    fullWidth
                    error={!!errors.name}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="url"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="URL"
                    fullWidth
                    error={!!errors.url}
                    helperText={errors.url?.message}
                    placeholder="https://example.com/webhook"
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="category"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Category"
                    select
                    fullWidth
                    error={!!errors.category}
                    helperText={errors.category?.message}
                  >
                    {webhookCategories.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="type"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Event Type"
                    select
                    fullWidth
                    error={!!errors.type}
                    helperText={errors.type?.message}
                  >
                    {webhookTypes.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="isActive"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={
                      <Switch
                        checked={field.value}
                        onChange={field.onChange}
                      />
                    }
                    label="Active"
                  />
                )}
              />
              <FormHelperText>
                Active webhooks will receive notifications for events
              </FormHelperText>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button 
            type="submit" 
            variant="contained"
            disabled={isSubmitting}
          >
            {isEdit ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};