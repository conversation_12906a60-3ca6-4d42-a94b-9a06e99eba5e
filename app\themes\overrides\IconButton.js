import { alpha } from '@mui/material/styles';

// ==============================|| OVERRIDES - ICON BUTTON ||============================== //

export default function IconButton(theme) {
  return {
    MuiIconButton: {
      styleOverrides: {
        root: {
          borderRadius: 0,
          border: `1px solid ${theme.palette.mode === 'dark' ? theme.palette.grey[700] : theme.palette.grey[300]}`,
          backgroundColor: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#ffffff',
          padding: theme.spacing(1),
          transition: 'all 0.2s ease',
          boxShadow: theme.palette.mode === 'dark' 
            ? '0 1px 2px rgba(0, 0, 0, 0.3)' 
            : '0 1px 2px rgba(0, 0, 0, 0.05)',
          '&:hover': {
            backgroundColor: theme.palette.mode === 'dark' 
              ? theme.palette.grey[800] 
              : theme.palette.grey[50],
            borderColor: theme.palette.primary.main,
            boxShadow: theme.palette.mode === 'dark'
              ? '0 2px 4px rgba(0, 0, 0, 0.4)'
              : '0 2px 4px rgba(0, 0, 0, 0.1)',
          },
          '&:active': {
            transform: 'translateY(1px)',
            boxShadow: 'none',
          }
        },
        colorPrimary: {
          color: theme.palette.primary.main,
          borderColor: theme.palette.primary.main,
          backgroundColor: theme.palette.mode === 'dark' 
            ? alpha(theme.palette.primary.main, 0.08)
            : alpha(theme.palette.primary.main, 0.04),
          '&:hover': {
            backgroundColor: theme.palette.mode === 'dark' 
              ? alpha(theme.palette.primary.main, 0.15)
              : alpha(theme.palette.primary.main, 0.08),
            borderColor: theme.palette.primary.dark,
          }
        },
        sizeLarge: {
          width: 'auto',
          height: 'auto',
          padding: theme.spacing(1.5),
          fontSize: '1.25rem'
        },
        sizeMedium: {
          width: 'auto',
          height: 'auto',
          padding: theme.spacing(1),
          fontSize: '1rem'
        },
        sizeSmall: {
          width: 'auto',
          height: 'auto',
          padding: theme.spacing(0.75),
          fontSize: '0.875rem'
        }
      }
    }
  };
}
