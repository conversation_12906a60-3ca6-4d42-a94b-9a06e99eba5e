import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Stack,
  Chip,
  Link,
  IconButton,
  Divider,
  Tooltip,
  alpha,
  useTheme,
  Skeleton
} from '@mui/material';
import {
  <PERSON><PERSON>,
  Eye,
  EyeOff,
  ExternalLink,
  Trash2,
  X,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface WebhookDetailsPanelProps {
  webhook: any;
  showSecret: boolean;
  showAllEvents: boolean;
  onToggleSecret: () => void;
  onToggleEvents: () => void;
  onEdit: () => void;
  onEditEvents: () => void;
  onDelete: () => void;
  getHookTypeLabel?: (type: string) => string;
  onClose?: () => void;
  showCloseButton?: boolean;
}

const WebhookDetailsPanel: React.FC<WebhookDetailsPanelProps> = ({
  webhook,
  showSecret,
  showAllEvents,
  onToggleSecret,
  onToggleEvents,
  onEdit,
  onEditEvents,
  onDelete,
  getHookTypeLabel,
  onClose,
  showCloseButton = false
}) => {
  const theme = useTheme();

  const [copyLoading, setCopyLoading] = useState<string | null>(null);

  const handleCopy = async (text: string, label: string, fieldId: string) => {
    if (!text) {
      toast.error(`No ${label.toLowerCase()} to copy`);
      return;
    }

    setCopyLoading(fieldId);
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${label} copied to clipboard`);
    } catch (error) {
      console.error('Failed to copy:', error);
      toast.error('Failed to copy to clipboard');
    } finally {
      setTimeout(() => setCopyLoading(null), 500);
    }
  };

  const formatSecret = (secret: string | undefined | null) => {
    if (!secret) return '—';
    if (showSecret) return secret;
    // Show first 8 chars with more dots for optimal visual balance
    return `${secret.substring(0, 8)}••••••••••••`;
  };

  // Helper function to safely get nested values
  const getWebhookValue = (path: string, defaultValue: any = null) => {
    try {
      const keys = path.split('.');
      let value = webhook;
      for (const key of keys) {
        value = value?.[key];
        if (value === undefined) return defaultValue;
      }
      return value || defaultValue;
    } catch {
      return defaultValue;
    }
  };

  // Extract values with proper fallbacks
  const endpointId = webhook?.id || '—';
  const endpointUrl = getWebhookValue('destination.webhookConfig.url') || 
                     getWebhookValue('data.url') || 
                     webhook?.url || '—';
  const description = webhook?.description || '';
  const signingSecret = getWebhookValue('securityConfig.signingSecret') || 
                       getWebhookValue('authentication.signingSecret') || '';
  const signingAlgorithm = getWebhookValue('securityConfig.signingAlgorithm', 'HMAC-SHA256');
  const signingVersion = getWebhookValue('securityConfig.signingVersion', 'v1');
  const apiVersion = webhook?.apiVersion || 'Latest';
  const eventTypes = getWebhookValue('eventSubscriptions.eventTypes', []) || 
                    getWebhookValue('data.events', []) || 
                    webhook?.events || [];
  const sslVerification = getWebhookValue('destination.webhookConfig.sslVerification', true);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
        <Box sx={{ flex: 1, mr: 2 }}>
          <Typography 
            variant="h5" 
            sx={{ 
              fontWeight: 700,
              mb: 0.5,
              wordBreak: 'break-word'
            }}
          >
            {webhook?.name || 'Unnamed Webhook'}
          </Typography>
          {webhook?.description && (
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{ 
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            >
              {webhook.description}
            </Typography>
          )}
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexShrink: 0 }}>
          <Button
            variant="text"
            size="small"
            onClick={onEdit}
            sx={{ textTransform: 'none' }}
          >
            Edit
          </Button>
          {showCloseButton && onClose && (
            <IconButton size="small" onClick={onClose} sx={{ ml: 1 }}>
              <X size={20} />
            </IconButton>
          )}
        </Box>
      </Box>

      {/* Details Grid */}
      <Stack spacing={2.5}>
        {/* Endpoint URL */}
        <Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
            Endpoint URL
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
            <Typography 
              variant="body2" 
              sx={{ 
                wordBreak: 'break-all',
                flex: 1,
                lineHeight: 1.6
              }}
            >
              {endpointUrl}
            </Typography>
            {endpointUrl !== '—' && (
              <Tooltip title="Copy URL">
                <IconButton 
                  size="small" 
                  onClick={() => handleCopy(endpointUrl, 'Endpoint URL', 'endpoint-url')}
                  disabled={copyLoading === 'endpoint-url'}
                  sx={{ flexShrink: 0 }}
                >
                  {copyLoading === 'endpoint-url' ? (
                    <Skeleton variant="circular" width={16} height={16} />
                  ) : (
                    <Copy size={16} />
                  )}
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>


        {/* Signing Secret */}
        <Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
            Signing secret
          </Typography>
          {signingSecret ? (
            <Box sx={{ 
              display: 'flex', 
              alignItems: 'center', 
              gap: 1,
              backgroundColor: alpha(theme.palette.action.hover, 0.04),
              border: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
              borderRadius: 1,
              px: 1.5,
              py: 0.75,
              maxWidth: 'fit-content'
            }}>
              <Typography 
                variant="body2" 
                sx={{ 
                  fontFamily: 'monospace',
                  fontSize: '0.813rem',
                  letterSpacing: showSecret ? 'normal' : '0.05em',
                  color: theme.palette.text.primary,
                  wordBreak: showSecret ? 'break-all' : 'normal'
                }}
              >
                {formatSecret(signingSecret)}
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5, ml: 'auto' }}>
                <Tooltip title={showSecret ? 'Hide secret' : 'Reveal secret'}>
                  <IconButton
                    size="small"
                    onClick={onToggleSecret}
                    sx={{ 
                      p: 0.5,
                      color: theme.palette.text.secondary,
                      '&:hover': {
                        color: theme.palette.primary.main,
                        backgroundColor: alpha(theme.palette.primary.main, 0.08)
                      }
                    }}
                  >
                    {showSecret ? <EyeOff size={16} /> : <Eye size={16} />}
                  </IconButton>
                </Tooltip>
                {showSecret && (
                  <Tooltip title="Copy secret">
                    <IconButton 
                      size="small" 
                      onClick={() => handleCopy(signingSecret, 'Signing secret', 'signing-secret')}
                      disabled={copyLoading === 'signing-secret'}
                      sx={{ 
                        p: 0.5,
                        color: theme.palette.text.secondary,
                        '&:hover': {
                          color: theme.palette.primary.main,
                          backgroundColor: alpha(theme.palette.primary.main, 0.08)
                        }
                      }}
                    >
                      {copyLoading === 'signing-secret' ? (
                        <Skeleton variant="circular" width={16} height={16} />
                      ) : (
                        <Copy size={16} />
                      )}
                    </IconButton>
                  </Tooltip>
                )}
              </Box>
            </Box>
          ) : (
            <Typography variant="body2" color="text.secondary">
              {formatSecret(signingSecret)}
            </Typography>
          )}
          {signingSecret && (
            <Box sx={{ mt: 0.75 }}>
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                {signingAlgorithm} • {signingVersion}
              </Typography>
            </Box>
          )}
        </Box>

        {/* API Version */}
        <Box>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
            API version
          </Typography>
          <Typography variant="body2">
            {apiVersion}
          </Typography>
        </Box>

        {/* SSL Verification */}
        {endpointUrl !== '—' && (
          <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
              SSL verification
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {sslVerification ? (
                <Chip 
                  label="Enabled" 
                  size="small" 
                  color="success" 
                  variant="outlined"
                  sx={{ height: 24 }}
                />
              ) : (
                <>
                  <Chip 
                    label="Disabled" 
                    size="small" 
                    color="warning" 
                    variant="outlined"
                    icon={<AlertCircle size={14} />}
                    sx={{ height: 24 }}
                  />
                  <Tooltip title="SSL verification is disabled. This may pose security risks.">
                    <AlertCircle size={16} color={theme.palette.warning.main} />
                  </Tooltip>
                </>
              )}
            </Box>
          </Box>
        )}

        {/* Events */}
        <Box>
          <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Listening to
            </Typography>
            <Typography variant="body2">
              {eventTypes.length} {eventTypes.length === 1 ? 'event' : 'events'}
            </Typography>
          </Box>

          {/* Events List */}
          {eventTypes.length > 0 && (
            <Box sx={{ mt: 1.5 }}>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {eventTypes
                  .slice(0, showAllEvents ? undefined : 4)
                  .map((event: string, index: number) => {
                    // Extract event category and type
                    const [category, ...typeParts] = event.split(/[:.]/);
                    const eventType = typeParts.join('.');
                    
                    return (
                      <Chip
                        key={`${event}-${index}`}
                        label={event}
                        size="small"
                        sx={{
                          fontFamily: 'monospace',
                          fontSize: '0.75rem',
                          backgroundColor: alpha(theme.palette.primary.main, 0.08),
                          '&:hover': {
                            backgroundColor: alpha(theme.palette.primary.main, 0.12)
                          }
                        }}
                      />
                    );
                  })}
              </Box>
              {eventTypes.length > 4 && (
                <Button
                  size="small"
                  onClick={onToggleEvents}
                  sx={{ 
                    textTransform: 'none', 
                    minWidth: 'auto', 
                    mt: 1,
                    fontSize: '0.813rem'
                  }}
                >
                  {showAllEvents ? 'View less' : `View all ${eventTypes.length} events`}
                </Button>
              )}
            </Box>
          )}
          {eventTypes.length === 0 && (
            <Typography variant="caption" color="text.secondary" sx={{ fontStyle: 'italic' }}>
              No events configured
            </Typography>
          )}
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Resources */}
        <Box>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Resources
          </Typography>
          <Stack spacing={1}>
            <Link
              href="https://docs.unizo.ai/webhooks"
              target="_blank"
              rel="noopener noreferrer"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                color: 'primary.main',
                textDecoration: 'none',
                '&:hover': { textDecoration: 'underline' }
              }}
            >
              <ExternalLink size={16} />
              Webhooks quick start guide
            </Link>
          </Stack>
        </Box>

        {/* Delete Button */}
        <Box sx={{ pt: 3 }}>
          <Button
            variant="outlined"
            color="error"
            fullWidth
            startIcon={<Trash2 size={16} />}
            onClick={onDelete}
            sx={{ textTransform: 'none' }}
          >
            Delete webhook endpoint
          </Button>
        </Box>
      </Stack>
    </Box>
  );
};

export default WebhookDetailsPanel;