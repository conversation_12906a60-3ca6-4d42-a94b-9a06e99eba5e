/* eslint-disable import/no-named-as-default */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { useMemo } from "react";
import {
    DialogContent,
    DialogProps,
    Stack,
} from "@mui/material";
import { alpha } from '@mui/material/styles';

import SimpleBarScroll from 'components/third-party/SimpleBar';

import useSelfRegistration from "store/self-signUp/self-signup";

import { Dialog } from "components/@extended/dialog";
import { TenantMetadataProvider, useTenantMetadata } from './contexts/tenant-metadata.provider';

import ComponentMap from './component-mapper';

import Footer from "./footer";
import Welcome from "./welcome";

interface Props extends DialogProps {
    onConfirm: () => void;
}

const BASE_PATH = '/images/brand';

const SelfSignUp = (props: Props) => {

    return (
        <TenantMetadataProvider value={{ onClose: props?.onClose }}>
            <DialogBox {...props} />
        </TenantMetadataProvider>
    )
};


const showFooter = true;

const DialogBox = (props: Props) => {

    const { open } = props;

    const {
        step,
        isReady,
    } = useSelfRegistration();

    const { tenantMetadata } = useTenantMetadata();

    const onboardingSteps = tenantMetadata?.onboardingSteps ?? [];

    const currentStep = onboardingSteps[step];

    const isLast = onboardingSteps?.length <= step;

    const stepComponent = useMemo(() => {
        const Comp = ComponentMap[currentStep?.type]?.component;

        /**
         * If Component exist Return the Component from mapper
        */
        return !Comp ? null : <Comp {...{ currentStep, steps: onboardingSteps }} />;
    }, [currentStep, onboardingSteps]);

    return (
        <Dialog
            open={open}
            maxWidth={'xl'}
            slotProps={{
                backdrop: {
                    sx: styles.backdrop
                }
            }}
            PaperProps={{ sx: styles.paperProps({ step }) }}
        >
            <DialogContent
                dividers
                sx={styles.dialogContentRoot({ showFooter })}
            >

                {!isReady ? (
                    <Welcome />
                ) : (
                    stepComponent ? <SimpleBarScroll
                        sx={styles.scroll}
                    >
                        <Stack
                            gap={2}
                            sx={styles.stack}
                        >
                            {stepComponent}
                        </Stack>
                    </SimpleBarScroll> : null
                )}

                {isReady && step < onboardingSteps?.length - 1  && (
                    <Footer
                        {...{
                            currentStep,
                            steps: onboardingSteps,
                            hideNext: isLast,
                            hideProgress: isLast
                        }}
                    />
                )}
            </DialogContent>
        </Dialog>
    )
}

const styles = {
    scroll: {
        height: '100%',
        '& .simplebar-content': {
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
        }
    },
    stack: {
        p: 3,
        width: '100%',
        margin: 'auto'
    },
    backdrop: {
        backgroundColor: alpha('#000', 0.7),
        backdropFilter: 'blur(8px)',
    },
    paperProps: ({ step }: { step: number }) => {
        return {
            width: "65%",
            height: "85%",
            m: 2,
            position: 'relative',
            overflow: 'hidden',
            '&::after': step === 0 ? {
                content: '""',
                position: 'absolute',
                bottom: -100,
                right: -100,
                height: 400,
                backgroundImage: `url(${BASE_PATH}/welcomeSelfRegistration.svg)`,
                backgroundSize: 'contain',
                backgroundPosition: 'bottom right',
                backgroundRepeat: 'no-repeat',
                opacity: 10,
                zIndex: 0
            } : {},
            '& .MuiDialogContent-root': {
                height: showFooter ? 'calc(100% - 64px)' : '100%',
                overflow: 'hidden'
            }
        }
    },
    dialogContentRoot: ({ showFooter }: { showFooter: boolean }) => {
        return {
            height: showFooter ? 'calc(100% - 140px)' : '100%',
            p: 0,
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            alignContent: 'center'
        }
    }
}


export default SelfSignUp
