import { useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "utils/api/api-endpoints";
import { tenantsClient } from "services/tenants.service";
import { TenantMetadata } from "types/tenant-metadata";

export const TENANT_METADATA_KEY = 'TENANT_METADATA';

export const useTenantMetadata = (tenantId: string) => {
    return useQuery<TenantMetadata>({
        queryKey: [API_ENDPOINTS.TENANTS, TENANT_METADATA_KEY, tenantId],
        queryFn: async () => {
            const response = await tenantsClient.getTenantMetadata(tenantId);
            return response.data;
        },
        enabled: !!tenantId
    });
};

export default useTenantMetadata;