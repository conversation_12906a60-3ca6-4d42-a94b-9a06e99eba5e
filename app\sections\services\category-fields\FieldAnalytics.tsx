import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Stack,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  LinearProgress,
  useTheme,
  alpha
} from '@mui/material';
import {
  RiseOutlined,
  FallOutlined,
  MinusOutlined
} from '@ant-design/icons';

// Mock analytics data
const fieldUsageData = [
  { 
    model: 'Repository',
    fieldName: 'project_owner', 
    usage: 95, 
    trend: 'up', 
    activeServices: 5,
    totalServices: 6
  },
  { 
    model: 'Repository',
    fieldName: 'compliance_level', 
    usage: 78, 
    trend: 'up', 
    activeServices: 4,
    totalServices: 6
  },
  { 
    model: 'Organization',
    fieldName: 'compliance_tier', 
    usage: 62, 
    trend: 'stable', 
    activeServices: 3,
    totalServices: 6
  },
  { 
    model: 'Pull Request',
    fieldName: 'review_priority', 
    usage: 45, 
    trend: 'down', 
    activeServices: 2,
    totalServices: 6
  },
  { 
    model: 'Branch',
    fieldName: 'protected_by', 
    usage: 12, 
    trend: 'down', 
    activeServices: 1,
    totalServices: 6
  }
];

export default function FieldAnalytics({ category }: { category: string }) {
  const theme = useTheme();

  const getTrendIcon = (trend: string) => {
    if (trend === 'up') return <RiseOutlined style={{ color: theme.palette.success.main, fontSize: 14 }} />;
    if (trend === 'down') return <FallOutlined style={{ color: theme.palette.error.main, fontSize: 14 }} />;
    return <MinusOutlined style={{ color: theme.palette.text.secondary, fontSize: 14 }} />;
  };

  const getUsageColor = (usage: number) => {
    if (usage >= 80) return theme.palette.success.main;
    if (usage >= 50) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  return (
    <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      {/* Simple Summary */}
      <Card variant="outlined" sx={{ mb: 3, boxShadow: 'none', border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : '#e5e5e5'}` }}>
        <CardContent>
          <Typography variant="h6" fontWeight={600} mb={2}>
            Field Usage Summary
          </Typography>
          <Stack direction="row" spacing={4}>
            <Stack>
              <Typography variant="h4" fontWeight={600} color="primary">
                {fieldUsageData.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Additional Fields
              </Typography>
            </Stack>
            <Stack>
              <Typography variant="h4" fontWeight={600} color="success.main">
                {fieldUsageData.filter(f => f.usage >= 50).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Well-Used Fields (≥50%)
              </Typography>
            </Stack>
            <Stack>
              <Typography variant="h4" fontWeight={600} color="error.main">
                {fieldUsageData.filter(f => f.usage < 20).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Rarely Used Fields (&lt;20%)
              </Typography>
            </Stack>
          </Stack>
        </CardContent>
      </Card>

      {/* Usage Table */}
      <Card variant="outlined" sx={{ boxShadow: 'none', border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : '#e5e5e5'}` }}>
        <CardContent>
          <Typography variant="h6" fontWeight={600} mb={2}>
            Field Usage by Model
          </Typography>
          
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Model</TableCell>
                  <TableCell>Field Name</TableCell>
                  <TableCell>Usage Rate</TableCell>
                  <TableCell>Trend</TableCell>
                  <TableCell>Adoption</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {fieldUsageData.map((field, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Chip 
                        label={field.model} 
                        size="small" 
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight={500}>
                        {field.fieldName}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack spacing={0.5}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <LinearProgress
                            variant="determinate"
                            value={field.usage}
                            sx={{ 
                              width: 80, 
                              height: 6, 
                              borderRadius: 1,
                              bgcolor: alpha(getUsageColor(field.usage), 0.1),
                              '& .MuiLinearProgress-bar': {
                                bgcolor: getUsageColor(field.usage)
                              }
                            }}
                          />
                          <Typography variant="caption" fontWeight={600}>
                            {field.usage}%
                          </Typography>
                        </Stack>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      {getTrendIcon(field.trend)}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {field.activeServices} of {field.totalServices} services
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
}