import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { Autocomplete, Chip, Grid, GridProps, TextField } from "@mui/material";
import { Form, FormField, FormItem, FormControl } from "components/@extended/Form";
import MainCard from "components/MainCard";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { CardTitle } from "../card-title";
import { formConfig } from "./form";
import useCreateTenant from "store/create-tenants";
import { useEffect, useState } from "react";
import { FormItemType } from "constants/form";
import { getIsValidEmail } from "lib/utils";
import { userClient } from "services/user.service";



const TITLE = 'Org Admin Details';

const SPANS: GridProps = {
   xs: 12,
   md: 12,
   lg: 12,
   xl: 6,
}

const schema = z.object({
   firstName: z
      .string().nonempty("First Name is required."),
   lastName: z
      .string()
      .nonempty("Last Name is required."),
   email: z.string().nonempty("Email is required.").email({ message: "Invalid email address" }).refine(async (email) => {
      const payload = {
         filter: {
            or: [
               {
                  and: [
                     { property: "/state", operator: "=", values: ["ACTIVE"] },
                     { property: "/email", operator: "=", values: [email] },
                  ],
               },
            ],
         },
         pagination: { offset: 0, limit: 10 },
      };

      try {
         const resp = await userClient.searchUserEmail(payload);
         const { data } = resp.data;
         return !Boolean(data?.length); 
      } catch (error) {
         console.error("API Error:", error);
         return false;
      }
   }, {
      message: "This email already exists.",
   }),
   phone: z.string().nonempty("Phone is required."),
   role: z.string().nonempty("Role is required."),
   emails: z
      .array(z.string()).max(5, { message: 'Cannot be more than 5' }).refine((value: string[]) => {
         return value.every((i) => getIsValidEmail(i))
      }, {
         message: "Email format is invalid",
      })
}).superRefine((values, ctx) => {

   const { email, emails = [] } = values;

   if (emails?.length) {
      const exist = emails?.indexOf(email);
      if (exist >= 0) {
         ctx.addIssue({
            path: ['emails'],
            message: `${emails[exist]} is already given as admin email!`,
            code: z.ZodIssueCode.custom
         });
      }

   }

})

type CustomerDetailsProps = {
   formRef: React.RefObject<HTMLFormElement>
   onSuccess: (values: z.infer<typeof schema>) => void
}

export const OrgAdmin = ({ formRef, onSuccess: onSuccessProp }: CustomerDetailsProps) => {

   const { formData, defaultValues } = useCreateTenant();

   const values = formData?.orgDetails;

   const form = useForm<z.infer<typeof schema>>({
      resolver: zodResolver(schema),
      defaultValues: defaultValues?.formData?.orgDetails,
      mode: "onChange",
   }),
      { setValue, getValues, reset } = form;

   const onSubmit = (values: z.infer<typeof schema>) => {
      if (typeof onSuccessProp === 'function') {
         onSuccessProp(values)
      }
   }

   const [inputValue, setInputValue] = useState('');

   const handleAddEmail = (email: string) => {
      if (!email.trim()) return;
      const currentEmails = getValues('emails') || [];
      if (!currentEmails.includes(email)) {
         setValue('emails', [...currentEmails, email]);
      }
      setInputValue(''); // Clear input value
   };

   useEffect(() => {
      reset(values)
   }, [values])

   return (
      <MainCard
         title={(
            <CardTitle title={TITLE} />
         )}
         className="m-auto"
      >
         <Form {...form}>
            <form
               onSubmit={(...args) => (
                  void form.handleSubmit(onSubmit)(...args)
               )}
               ref={formRef}
               className="flex flex-col gap-5"
            >
               <Grid container columnSpacing={2} rowSpacing={2}>
                  {formConfig.map(({ type, ...item }, index) => {

                     return (
                        <Grid item {...SPANS} key={index}>
                           <FormField
                              control={form.control}
                              name={item.name}
                              render={({ field, }) => {
                                 const { onChange, value } = field;
                                 return (
                                    <FormItem label={item.label} description={item.description}>
                                       <FormControl>
                                          {
                                             type !== FormItemType.MultiText ? (
                                                <TextField
                                                   {...field}
                                                   placeholder={item?.placeholder}
                                                />
                                             ) : (
                                                <Autocomplete
                                                   multiple
                                                   freeSolo
                                                   options={[]}
                                                   value={value}
                                                   onChange={(_, newValue) => {
                                                      const uniqueEmails = Array.from(new Set(newValue));
                                                      onChange(uniqueEmails);
                                                   }}
                                                   inputValue={inputValue}
                                                   onInputChange={(_, newInputValue) => {
                                                      setInputValue(newInputValue);
                                                   }}
                                                   renderTags={(value, getTagProps) =>
                                                      value.map((option, index: number) => (
                                                         <Chip
                                                            label={option}
                                                            {...getTagProps({ index })}
                                                            key={index}
                                                         />
                                                      ))
                                                   }
                                                   renderInput={(params) => {
                                                      return (
                                                         <TextField
                                                            {...params}
                                                            placeholder="Enter email"
                                                            onBlur={() => handleAddEmail(inputValue)}
                                                         />
                                                      )
                                                   }} />
                                             )
                                          }
                                       </FormControl>
                                    </FormItem>
                                 )
                              }}
                           />
                        </Grid>

                     )
                  })}
               </Grid>
            </form>
         </Form>
      </MainCard>
   )
}