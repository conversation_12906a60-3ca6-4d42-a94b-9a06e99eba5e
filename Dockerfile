# Use Node.js as the base image
FROM node:18 AS builder

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json files to install dependencies
COPY package.json package-lock.json* ./

# Install dependencies
RUN npm install --force

# Copy all source files to the working directory
COPY . .

# Build the Remix application
RUN npm run build

# -----

# Start a new stage from a smaller image for production (optional but recommended)
FROM node:18-alpine AS runner

# Set the working directory in the container
WORKDIR /app

# Copy built files and node_modules from the builder stage
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/build ./build
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json

# Expose the port the app runs on
EXPOSE 3000

# Set the command to run the application
# CMD ["npm", "run", "start:$NODE_ENV"]
# Use environment variable directly in the command
CMD ["sh", "-c", "npm run start:$NODE_ENV"]
