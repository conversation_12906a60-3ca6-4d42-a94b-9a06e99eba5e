# Getting Started Page - Requirements Understanding

## Current Understanding

### Problem Statement
When a user logs into Unizo for the first time and has no tenant configured, they should see a full-page onboarding flow (instead of the regular dashboard) that guides them through setting up their Unizo instance.

### Key Requirements

#### 1. **Detection Logic**
- Check if user has any tenants using `tenantsClient.getAllTenants()`
- If no tenants exist, show the Getting Started flow
- This check happens in `useOnboarding` hook which triggers a self-signup modal currently
- We need to replace the modal approach with a full-page experience

#### 2. **Design Inspiration**
- Take inspiration from <PERSON><PERSON><PERSON>'s onboarding (as shown in the screenshot)
- Left sidebar with stepper navigation showing progress and items to be 
  - 1. Title - Select Unified APIs. helpText - Select and enable API categories
    2. Title - Select Connectors. HelpText - Select and enable connectors
    3. Title - Get Started. HelpText - Get started with Unizo
- Main content area on the right with forms/selections
- Clean, elegant, modern design following Unizo's theme

#### 3. **User Flow**
Based on my analysis, the Getting Started page should guide users through:

1. **Integration Categories**
   - Select from available categories (Source Control, Ticketing, etc.) and also have description relevant to Unizo's unified APIs business. use the same dataset with additional description. Call serviceProfiles search API with type such as SCM, Ticketing, etc.. use pagination total for total count and limit 4 to show first four tools' images url to show avatar kinda display, use apideck image for it, repeat this for all categories or find a better way!
   - Multiple selection allowed
   - Show popular/recommended options
   - I want the title to be "Get Started with Unified APIs" and help text to be which <bold>API categories</bold> would you like to start with?

2. **Service Configuration**
   - Use ServiceProfile APIs and create Service, the application has reference in connectors page, take a look at this and implement the same
   - Configure selected services
   - Set up data models/field mappings
   - API endpoint configuration

3. Getting started Page, take the user to dashboard 

### Technical Implementation

#### APIs to Use
1. **Tenant Creation**
   ```typescript
   tenantsClient.create(payload)
   ```

2. **Organization Management**
   ```typescript
   organizationClient.getOrganizationById(id)
   organizationClient.createOrgWatchConfig(id, payload)
   ```

3. **Service Configuration**
   ```typescript
   serviceProfileClient.searchProfiles(payload)
   serviceProfileClient.createService(payload)
   ```

4. **Access Keys**
   ```typescript
   accesskeyClient.createAccessKey(payload)
   ```

#### Current Onboarding Flows to Leverage
1. **Self-Signup Flow** (`/app/sections/self-signup/`)
   - Already has category selection
   - Service selection logic
   - Team invitation
   - Webhook configuration

2. **Onboarding Dialog** (`/app/sections/onboarding/`)
   - Has step management
   - Progress tracking
   - Category/Service selection components

### Key Differences from Existing Flows

1. **Full Page vs Modal**
   - Current flows use modals/dialogs
   - Getting Started needs to be a full-page experience

2. **Comprehensive Setup**
   - More detailed than current onboarding
   - Includes organization setup, access keys, etc.

3. **No Skip Option**
   - Users must complete basic setup before accessing dashboard
   - Can skip optional steps like team invitation

### Design Considerations

1. **Theme Compliance**
   - Follow CLAUDE.md guidelines
   - No hardcoded colors
   - Support dark/light modes

2. **Responsive Design**
   - Mobile-friendly stepper
   - Collapsible navigation on small screens

3. **Progress Persistence**
   - Save progress in localStorage
   - Allow users to resume if they refresh

### Questions for Clarification

1. **Product Types**
   - What exactly are "Unify" and "Ecosystem" products?
   - Do they affect the subsequent steps/configuration?

2. **Mandatory vs Optional Steps**
   - Which steps are absolutely required?
   - Can users skip webhook configuration initially?

3. **Data Models/Field Mapping**
   - How detailed should the service configuration be?
   - Should we include full field mapping UI in onboarding?

4. **Post-Setup Flow**
   - After completion, where should users land?
   - Should there be a quick tour of the dashboard?

5. **Existing User Detection**
   - The current `useOnboarding` hook triggers a modal
   - Should we modify this hook or create a new detection mechanism?

6. **Tenant Types**
   - I see `OnboardingType.SELF_SIGNUP` in the code
   - Are there other tenant types we need to consider?

### Next Steps

1. **Confirm Understanding**
   - Review this document and provide corrections
   - Clarify the questions above

2. **Design Mockups**
   - Create wireframes for each step
   - Get design approval before implementation

3. **Implementation Plan**
   - Start with the routing/detection logic
   - Build step-by-step incrementally
   - Integrate with existing APIs

## Summary

The Getting Started page will be a comprehensive, full-page onboarding experience that replaces the current modal approach for first-time users without tenants. It will guide them through 8 key steps to set up their Unizo instance, leveraging existing APIs and components while providing a more detailed and user-friendly experience.