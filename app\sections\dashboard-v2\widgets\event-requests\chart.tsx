import { useMemo } from 'react';
import { LineChart } from '@mui/x-charts/LineChart';
import { alpha, useTheme } from '@mui/material/styles';

import { getChartData } from '../helper';
import { ID_S } from './constant';

import { useDashboard } from 'hooks/api/dashboard/useDashboard';
import {  getLast7DaysStr } from 'sections/dashboard-v2/layout/helper';
import { GRAPH_CONTAINER_MARGIN_S } from 'sections/dashboard-v2/constant';

const labels = getLast7DaysStr() ?? [];
export default () => {

   const theme: any = useTheme();

   const { eventUtils: stats } = useDashboard()

   const { data1, data2, isEmpty } = useMemo(() => getChartData(stats), [stats?.series])

   const line = theme.palette.divider;

   const visibleSeries = useMemo(() => {
      return (
         [
            {
               data: data1,
               label: 'Success',
               showMark: false,
               area: true,
               id: ID_S.SUCCESS,
               color: theme.palette.success.main || '',
            },
            {
               data: data2,
               label: 'Failure',
               showMark: false,
               area: true,
               id: ID_S.FAILURE,
               color: theme.palette.error.main || '',
            }
         ]
      )
   }, [data1, data2]);

   const axisFontStyle = { fontSize: 10, fill: theme.palette.text.secondary };

   return (
      <LineChart
         grid={{ horizontal: true }}
         xAxis={[{ scaleType: 'point', data: labels, disableLine: !isEmpty, tickLabelStyle: axisFontStyle }]}
         yAxis={[{ disableLine: true, disableTicks: true, tickLabelStyle: axisFontStyle }]}
         height={200}
         margin={GRAPH_CONTAINER_MARGIN_S}
         series={visibleSeries
            .map((series) => ({
               type: 'line',
               data: series.data,
               label: series.label,
               showMark: series.showMark,
               area: series.area,
               id: series.id,
               color: series.color,
               stroke: series.color,
               strokeWidth: 2
            }))}
         slotProps={{ legend: { hidden: true } }}
         sx={{
            [`& .MuiAreaElement-series-${ID_S.SUCCESS}`]: { fill: "url('#myGradient1')", strokeWidth: 2, opacity: 0.8 },
            [`& .MuiAreaElement-series-${ID_S.FAILURE}`]: { fill: "url('#myGradient2')", strokeWidth: 2, opacity: 0.8 },
            '& .MuiChartsAxis-directionX .MuiChartsAxis-tick': { stroke: line }
         }}
      >
         <defs>
            <linearGradient id="myGradient1" gradientTransform="rotate(90)">
               <stop offset="10%" stopColor={alpha(theme.palette.success.main, 0.4)} />
               <stop offset="90%" stopColor={alpha(theme.palette.background.default, 0.4)} />
            </linearGradient>
            <linearGradient id="myGradient2" gradientTransform="rotate(90)">
               <stop offset="10%" stopColor={alpha(theme.palette.error.main, 0.4)} />
               <stop offset="90%" stopColor={alpha(theme.palette.background.default, 0.4)} />
            </linearGradient>
         </defs>
      </LineChart>
   )
}