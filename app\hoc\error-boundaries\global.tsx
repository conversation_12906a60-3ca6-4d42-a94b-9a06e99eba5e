import { ReactNode, useEffect } from 'react';
import { useRouteError } from "@remix-run/react";
import { ErrorBoundary } from 'react-error-boundary';

import Error500 from 'sections/maintenance/500'

type Props = {
   children: ReactNode
}

export default ({ children }: Props) => {
   const error: any = useRouteError();

   return (
      <ErrorBoundary
         FallbackComponent={Error500}
         onError={(error, info) => console.error(error, info)}
         onReset={() => console.log('Error boundary reset')}
      >
         {children}
      </ErrorBoundary>
   )
}