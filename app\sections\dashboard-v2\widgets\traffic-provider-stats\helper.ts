import { Subscription } from "types/subscription";


const getStatsForSelectedCategory = (
   selectedCategory: string,
   providerStats: Array<Record<string, any>>
) => {

   const events: any = {
      data: [],
      label: 'Event',
      meta: []
   };
   const api: any = {
      data: [],
      label: 'API',
      meta: []
   };

   const integrations: number[] = [],
      xAxisLabels: string[] = [];

   const statsRecordForSubscriptions = (
      providerStats?.filter((i) => i?.subscription?.id === selectedCategory)
   ) ?? [];

   statsRecordForSubscriptions?.map(({ providerStats: { metrics, serviceProfile, integration } }: any) => {
      xAxisLabels.push(serviceProfile?.name)
      const apiCount = metrics?.api?.totalCount;
      const eventCount = metrics?.event?.totalCount;

      const total = apiCount + eventCount;
      const apiPercentage = (apiCount / total) * 100;
      const eventPercentage = (eventCount / total) * 100;

      events.data.push(eventPercentage);
      api.data.push(apiPercentage);

      // meta block
      events.meta.push(eventCount);
      api.meta.push(apiCount);

      integrations.push(integration?.totalCount ?? 0);

   })
   return { events, api, xAxisLabels, integrations }
}

const generateLabels = (dynamicLabels: Array<string>, downLG: boolean) => {

   if (!downLG) {
      const counts = new Array(10).fill(null)
      counts.splice(0, dynamicLabels?.length, ...dynamicLabels);
      return counts;
   }

   return dynamicLabels;
}

export { getStatsForSelectedCategory, generateLabels }