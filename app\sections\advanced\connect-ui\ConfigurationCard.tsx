import React from 'react';
import {
  <PERSON>,
  Typography,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
  Menu,
  MenuItem,
  Divider,
} from '@mui/material';
import  PlayArrow  from '@mui/icons-material/PlayArrow';
import  MoreVert  from '@mui/icons-material/MoreVert';
import  Delete  from '@mui/icons-material/Delete';
import  Language  from '@mui/icons-material/Language';
import  Layers  from '@mui/icons-material/Layers';
import  CheckCircle  from '@mui/icons-material/CheckCircle';
import { useNavigate } from '@remix-run/react';
import Code  from '@mui/icons-material/Code';
import Edit  from '@mui/icons-material/Edit';
import WebAsset  from '@mui/icons-material/WebAsset';

interface ConfigurationCardProps {
  configuration: {
    id: string;
    name: string;
    frontendUrl: string;
    pageLayout: 'EMBEDDED' | 'POP_UP';
    state: string;
    customDomain?: string;
  };
  onTestRun: (config: any) => void;
  onDelete: (id: string) => void;
}

export default function ConfigurationCard({
  configuration,
  onTestRun,
  onDelete,
}: ConfigurationCardProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMoreClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleEdit = () => {
    handleClose();
    navigate(`/console/connect-UI/configuration/edit/${configuration.id}`);
  };

  const handleDelete = () => {
    handleClose();
    onDelete(configuration.id);
  };

  const hasCustomDomain = configuration.customDomain && 
    configuration.customDomain !== 'https://dock.unizo.ai';

  const layoutIcon = configuration.pageLayout === 'EMBEDDED' ? <Code /> : <WebAsset />;
  const layoutLabel = configuration.pageLayout === 'EMBEDDED' ? 'Embedded' : 'Pop-up';

  return (
    <Box
      sx={{
        p: 2,
        height: '100%',
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: 1,
        backgroundColor: theme.palette.background.paper,
        transition: 'all 0.2s',
        '&:hover': {
          borderColor: alpha(theme.palette.primary.main, 0.5),
          backgroundColor: alpha(theme.palette.action.hover, 0.02),
        },
      }}
    >
      <Stack spacing={1.5}>
        {/* Header with title and actions */}
        <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
          <Typography variant="subtitle2" fontWeight={600} noWrap sx={{ flex: 1, minWidth: 0 }}>
            {configuration.name}
          </Typography>
          
          {/* Compact action buttons */}
          <Stack direction="row" spacing={0}>
            <Tooltip title="Test Run">
              <IconButton 
                size="small" 
                onClick={() => onTestRun(configuration)}
                sx={{ p: 0.5 }}
              >
                <PlayArrow sx={{ fontSize: 18 }} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Edit">
              <IconButton 
                size="small" 
                onClick={handleEdit}
                sx={{ p: 0.5 }}
              >
                <Edit sx={{ fontSize: 18 }} />
              </IconButton>
            </Tooltip>
            <IconButton 
              size="small" 
              onClick={handleMoreClick}
              sx={{ p: 0.5 }}
            >
              <MoreVert sx={{ fontSize: 18 }} />
            </IconButton>
          </Stack>
        </Stack>
          
        {/* Chips */}
        <Stack direction="row" spacing={0.5} flexWrap="wrap" useFlexGap>
          <Chip
            size="small"
            label={layoutLabel}
            icon={layoutIcon}
            sx={{
              height: 20,
              fontSize: '0.7rem',
              backgroundColor: theme.palette.mode === 'dark' 
                ? alpha(theme.palette.primary.main, 0.15)
                : alpha(theme.palette.primary.main, 0.08),
              color: theme.palette.primary.main,
              border: 'none',
              '& .MuiChip-icon': {
                fontSize: 14,
                marginLeft: 0.25,
                color: theme.palette.primary.main,
              },
              '& .MuiChip-label': {
                px: 0.75,
              },
            }}
          />
          {configuration.state === 'ACTIVE' && (
            <Chip
              size="small"
              label="Active"
              icon={<CheckCircle />}
              sx={{
                height: 20,
                fontSize: '0.7rem',
                backgroundColor: theme.palette.mode === 'dark'
                  ? alpha(theme.palette.success.main, 0.15)
                  : alpha(theme.palette.success.main, 0.08),
                color: theme.palette.success.main,
                border: 'none',
                '& .MuiChip-icon': {
                  fontSize: 14,
                  marginLeft: 0.25,
                  color: theme.palette.success.main,
                },
                '& .MuiChip-label': {
                  px: 0.75,
                },
              }}
            />
          )}
          {hasCustomDomain && (
            <Chip
              size="small"
              label="Custom"
              icon={<Language />}
              sx={{
                height: 20,
                fontSize: '0.7rem',
                backgroundColor: theme.palette.mode === 'dark'
                  ? alpha(theme.palette.info.main, 0.15)
                  : alpha(theme.palette.info.main, 0.08),
                color: theme.palette.info.main,
                border: 'none',
                '& .MuiChip-icon': {
                  fontSize: 14,
                  marginLeft: 0.25,
                  color: theme.palette.info.main,
                },
                '& .MuiChip-label': {
                  px: 0.75,
                },
              }}
            />
          )}
        </Stack>

        {/* Compact Footer */}
        <Typography 
          variant="caption" 
          color="text.secondary"
          sx={{ 
            display: 'flex', 
            alignItems: 'center',
            gap: 0.5,
          }}
        >
          <Layers sx={{ fontSize: 14 }} />
          ID: {configuration.id.substring(0, 8)}...
        </Typography>
      </Stack>

      {/* More Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={handleEdit}>
          <Edit fontSize="small" sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={() => onTestRun(configuration)}>
          <PlayArrow fontSize="small" sx={{ mr: 1 }} />
          Test Run
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleDelete} sx={{ color: theme.palette.error.main }}>
          <Delete fontSize="small" sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Box>
  );
}