import React, { useMemo, useRef, useState } from "react";
import moment from "moment";

import { type MetaFunction } from "@remix-run/node";
import { useNavigate } from "@remix-run/react";
import { ClientOnly } from "remix-utils/client-only";

import {
   Box,
   Button,
   ButtonProps,
   DialogActions,
   DialogContent,
   DialogContentText,
   DialogProps,
   DialogTitle,
   Stack,
   Step,
   StepLabel,
   Stepper,
   Typography
} from "@mui/material";


import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import Image from "remix-image";

import MainCard from "components/MainCard";
import { Dialog } from "components/@extended/dialog";

import { CustomerDetails } from "sections/admin/create-tenant/customer";
import { OrgAdmin } from "sections/admin/create-tenant/org-admin";
import { Subscriptions } from "sections/admin/create-tenant/subscription";
import { Verify } from "sections/admin/create-tenant/verify";

import useCreateTenant from "store/create-tenants";
import { UserRoleEnum } from "hooks/api/permission/usePermission";
import { useDate } from "hooks/useDate";
import { useGetSuperAdmin } from "hooks/api/super-admin/useGetSuperAdmin";
import { State, useStatus } from "hooks/useStatus";


export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
      { name: "description", content: "Welcome to Remix!" },
   ];
};

const mocked = {
   subscription: {
      id: "0dca462d-4cdc-4d3d-bf4d-b1ee90b7f99e",
      startDateTime: "2024-07-26T00:00:00Z",
      expirationDateTime: "2024-08-31T00:00:00Z",
      entitlements: [
         {
            id: "3761507f-a8b4-4fae-8d42-e6aeaf4f2f68",
            type: "SCM_API"
         },
         {
            id: "e2e5f0d9-0dec-48a9-9890-2e4f5bdbd994",
            type: "TICKETING_API"
         },
         {
            id: "5f06578b-7f14-4aa9-978b-f8e183efbca3",
            type: "SCM_2_WAY_API"
         },
         {
            id: "8b2b43b4-6775-4af1-b84c-de2192be5397",
            type: "TICKETING_2_WAY_API"
         },
         {
            id: "a1bd190d-9fa5-44de-80f1-22b9601e8653",
            type: "COMMUNICATIONS_API"
         },
         {
            id: "a19a8e53-34fb-45a5-8c45-f5fa24fd1487",
            type: "PCR_2_WAY_API"
         },
         {
            id: "d3ede079-ff4b-4ed0-83e2-23a756a86848",
            type: "PCR_API"
         },
         {
            id: "afd3acce-844f-4082-ab44-a5f19ba8d7d7",
            type: "PREMIUM_CUSTOMER_SUPPORT"
         },
         {
            id: "e1f9fb0a-5705-471e-aa73-4335b2b88e20",
            type: "COMMUNICATIONS_2_WAY_API"
         }
      ],
      notifications: {
         type: "NOTIFICATION",
         emails: []
      }
   },
}

const Header = () => {
   return (
      <div>
         <Image src="https://unizo.ai/images/unz_dark_logo.svg" alt="logo" className="w-20 py-5 mx-auto block" />
      </div>
   )
}

type FooterProps = {
   submitProps: Partial<ButtonProps>,
   backProps: Partial<ButtonProps>,
   cancelProps: Partial<ButtonProps>,
}

const Footer = (props: FooterProps) => {

   const { submitProps, backProps, cancelProps } = props;

   return (
      <Box className=" w-full py-4">
         <Stack direction={'row'} justifyContent={'space-between'} alignItems={'center'} px={2}>
            <Button color="error" variant='outlined' {...cancelProps}>Cancel</Button>

            <Stack direction={'row'} gap={1}>
               <Button {...backProps}>Back</Button>
               <Button variant="contained" children='Next' {...submitProps} ></Button>
            </Stack>
         </Stack>
      </Box>
   )
}

const steps = ['Customer', 'Org Admin', 'Subscriptions', 'Verify & Create Tenant'];

export default function Index() {

   return (
      <ClientOnly fallback={null}>
         {() => {
            return (
               <Stack height={'100vh'}>
                  <Content />
               </Stack>
            )
         }}
      </ClientOnly>
   );
}



const Content = () => {
   const { step, setValues, move, reset, formData } = useCreateTenant();
   const { resolveOutlinedIcon } = useStatus()

   const { create } = useGetSuperAdmin();

   const { payloadFormat } = useDate();

   const currentHour = moment().hour(),
      currentMinute = moment().minute(),
      currentSecond = moment().second();


   const formRef = useRef<HTMLFormElement>(null);
   const navigate = useNavigate();

   const [open, setOpen] = useState(false)
   const [successModalOpen, setSuccessModalOpen] = useState(false);
   const subscriptions = formData.subscriptions;
   const customer = formData.customer;
   const orgDetails = formData.orgDetails;

   const subsWindow = step === 2;
   const verifyWindow = steps.length - 1 === step;

   const main = useMemo(() => {

      switch (step) {
         case 0:
            return (
               <CustomerDetails
                  formRef={formRef}
                  onSuccess={(values) => {
                     setValues('customer', values);
                     move('next')
                  }}
               />
            );
         case 1:
            return (
               <OrgAdmin
                  formRef={formRef}
                  onSuccess={(values) => {
                     setValues('orgDetails', values);
                     move('next')
                  }}
               />
            );

         case 2:
            return (
               <Subscriptions
               />
            );
         case 3:
            return (
               <Verify />
            );
         default:
            return null;
      }

   }, [step, formRef]);

   const openCancelRequest = () => {
      setOpen(true)
   },
      closeCancelRequest = () => {
         setOpen(false)
      }


   const onCancel = () => {
      reset();
      navigate(-1)
   }

   const createTenants = () => {

      const payload = {
         type: "STANDARD",
         name: customer?.name,
         firstName: orgDetails.firstName,
         lastName: orgDetails.lastName,
         description: customer?.companyUrl,
         companyUrl: customer.companyUrl,
         address: {
            line1: customer?.line1,
            line2: customer?.line2,
            city: customer?.city,
            country: customer?.country,
            state: customer?.state,
            zipcode: parseInt(customer?.zip),
         },
         region: {
            code: 12345,
            name: 'northAmerica'
         },
         ...mocked,
         subscriptions: subscriptions.map((item) => {
            const startDate = moment(item?.startDate)
               .hour(currentHour)   // Current hour
               .minute(currentMinute) // Current minute
               .second(currentSecond).utc().format(payloadFormat);

            const endDate = moment.utc(item?.endDate)
               .hour(currentHour)   // Current hour
               .minute(currentMinute) // Current minute
               .second(currentSecond).utc().format(payloadFormat);

            return {
               type: "PRODUCT",
               product: {
                  id: item?.category?.id,
               },
               charge: {
                  type: "MONTHLY_RECURRING",
                  currency: "USD",
                  pricingTier: {
                     id: item?.tier?.id
                  }
               },
               startDate,
               endDate
            }
         }),
         members: [
            {
               emails: [orgDetails.email, ...orgDetails.emails],
               role: {
                  type: UserRoleEnum.ORG_ADMIN
               }
            }
         ]
      }

      const onSuccessCallback = () => {
         setSuccessModalOpen(true);
         setTimeout(() => {
            setSuccessModalOpen(false);
            onCancel();
         }, 2000);
      };
      create(payload, onSuccessCallback);
   }

   const onSubmit = () => {
      if (subsWindow) {
         move('next')
      } else if (verifyWindow) {
         createTenants()
      } else {
         formRef.current?.requestSubmit();
      }
   }


   return (
      <>
         <Stack px={2} className="flex-1">
            <Header />
            <Stack gap={2}>
               <MainCard>
                  <Box sx={{ width: '100%' }}>
                     <Stepper activeStep={step}>
                        {steps.map((label, index) => {

                           const labelProps: {
                              optional?: React.ReactNode;
                              error?: boolean;
                           } = {};

                           return (
                              <Step key={index}>
                                 <StepLabel {...labelProps}>{label}</StepLabel>
                              </Step>
                           );
                        })}
                     </Stepper>
                  </Box>
               </MainCard>
               {main}
            </Stack>
            {successModalOpen && (

               <Dialog open={successModalOpen} onClose={() => setSuccessModalOpen(false)} aria-labelledby="success-dialog-title" maxWidth="xs" fullWidth>
                  <DialogContent>
                     <Box display="flex" flexDirection="column" alignItems="center" textAlign="center">
                        {resolveOutlinedIcon(State.ACTIVE, { style: { fontSize: 60 } })}
                        <Typography variant="h5" sx={{ mt: 2 }}>
                           Tenant created successfully.
                        </Typography>
                     </Box>
                  </DialogContent>
                  <DialogActions>
                     <Stack direction="row"
                        justifyContent="center"
                        alignItems="center"
                        margin="auto"
                        spacing={2}>
                        <Button onClick={() => setSuccessModalOpen(false)} variant="contained">
                           Ok
                        </Button>
                     </Stack>
                  </DialogActions>
               </Dialog>
            )}
         </Stack>
         <Footer
            submitProps={{
               onClick: onSubmit,
               disabled: subsWindow && (
                  !subscriptions?.length
               ),
               children: verifyWindow ? 'Create Tenant' : 'Next'
            }}
            backProps={{
               onClick: () => {
                  move('prev')
               },
               hidden: step === 0
            }}
            cancelProps={{
               onClick: openCancelRequest
            }}
         />
         <AlertDialog
            open={open}
            onApprove={onCancel}
            onClose={closeCancelRequest}
         />
      </>
   )
}

type AlertDialogProps = {
   onApprove: () => void
} & any

function AlertDialog({ open, onClose, onApprove }: AlertDialogProps) {

   return (
      <Dialog
         open={open}
         onClose={onClose}
         aria-labelledby="alert-dialog-title"
         aria-describedby="alert-dialog-description"
      >
         <DialogTitle id="alert-dialog-title" variant="h5">
            {"Are you sure ?"}
         </DialogTitle>
         <DialogContent dividers>
            <DialogContentText id="alert-dialog-description">
               You have unsaved changes. Are you sure you want to cancel and lose your changes?
            </DialogContentText>
         </DialogContent>
         <DialogActions>
            <Button onClick={onClose}>Disagree</Button>
            <Button onClick={onApprove} autoFocus variant='contained'>
               Agree
            </Button>
         </DialogActions>
      </Dialog>
   );
}