import { ReactNode } from 'react';
import { styled } from '@mui/material/styles';
import { <PERSON>, CardContent, CardHeader, Box, Typography, alpha, useTheme } from '@mui/material';
import TrendingDown from '@mui/icons-material/TrendingDown';
import  TrendingUp from '@mui/icons-material/TrendingUp';

interface ModernChartCardProps {
  title: string;
  subtitle?: string;
  children: ReactNode;
  action?: ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StyledCard = styled(Card)(({ theme }) => ({
  position: 'relative',
  overflow: 'hidden',
  height: '100%',
  background: theme.palette.mode === 'dark'
    ? theme.palette.background.paper
    : '#ffffff',
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: 0,
  boxShadow: theme.palette.mode === 'dark'
    ? '0 1px 3px rgba(0, 0, 0, 0.2)'
    : '0 1px 3px rgba(0, 0, 0, 0.05)',
  transition: 'all 0.2s ease',
  
  '&:hover': {
    boxShadow: theme.palette.mode === 'dark'
      ? '0 4px 12px rgba(0, 0, 0, 0.3)'
      : '0 4px 12px rgba(0, 0, 0, 0.08)',
  },
  
  '& .MuiCardHeader-root': {
    borderBottom: `1px solid ${theme.palette.divider}`,
    background: theme.palette.mode === 'dark'
      ? theme.palette.background.paper
      : theme.palette.grey[50],
  }
}));

const GlowEffect = styled(Box)(({ theme }) => ({
  display: 'none', // Removed for cleaner enterprise look
}));

export default function ModernChartCard({
  title,
  subtitle,
  children,
  action,
  trend
}: ModernChartCardProps) {
  const theme = useTheme();

  return (
    <StyledCard>
      <GlowEffect />
      <CardHeader
        title={
          <Box>
            <Typography 
              variant="h6" 
              sx={{ 
                color: theme.palette.text.primary
              }}
            >
              {title}
            </Typography>
            {subtitle && (
              <Typography 
                variant="body2" 
                sx={{ 
                  color: 'text.secondary',
                  mt: 0.5,
                  display: 'block'
                }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>
        }
        action={
          <Box display="flex" alignItems="center" gap={1}>
            {trend && (
              <Box
                display="flex"
                alignItems="center"
                gap={0.5}
                sx={{
                  px: 1.5,
                  py: 0.5,
                  borderRadius: 0,
                  background: 'transparent',
                  border: `1px solid ${theme.palette.divider}`,
                }}
              >
                {trend.isPositive ? (
                  <TrendingUp sx={{ fontSize: 16, color: 'success.main' }} />
                ) : (
                  <TrendingDown sx={{ fontSize: 16, color: 'error.main' }} />
                )}
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 'medium',
                    color: trend.isPositive ? 'success.main' : 'error.main',
                  }}
                >
                  {trend.value}%
                </Typography>
              </Box>
            )}
            {action}
          </Box>
        }
      />
      <CardContent sx={{ p: 3, pt: 2 }}>
        {children}
      </CardContent>
    </StyledCard>
  );
}