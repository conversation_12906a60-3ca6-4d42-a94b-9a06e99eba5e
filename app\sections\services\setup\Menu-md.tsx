import { useMemo, useState } from "react"

import { Typo<PERSON>, useMediaQuery, Menu, Button, MenuItem, Tooltip, Box, Stack, Chip, ListItemIcon, } from "@mui/material"

import { DownOutlined, LockFilled, } from "@ant-design/icons";


export default function MdDownMenu(
   {
      items = [],
      domain,
      setDomain
   }: Record<string, any>
) {

   const [anchorEl, setAnchorEl] = useState(null);

   const onOpen = (event: any) => {
      setAnchorEl(event?.currentTarget);
   };

   const onClose = () => {
      setAnchorEl(null);
   };

   const onSelect = (selectedDomain: string) => {
      onClose()
      setDomain(selectedDomain);
   }

   const selectedLabel = useMemo(() => {
      return items?.find((i: Record<string, any>) => i?.id == domain)
   }, [domain])

   return (
      <>
         <Button
            onClick={onOpen}
            color='primary'
            variant={('dashed' as any)}
            sx={{ borderStyle: 'none' }}
            endIcon={<DownOutlined />}
         >
            {selectedLabel?.title}
         </Button>
         <Menu
            id="simple-menu"
            anchorEl={anchorEl}

            keepMounted
            open={Boolean(anchorEl)}
            onClose={onClose}
            anchorOrigin={{
               vertical: 'bottom',
               horizontal: 'left'
            }}
            transformOrigin={{
               vertical: 'top',
               horizontal: 'left'
            }}
         >
            {items?.map((item: Record<string, any>, index: number) => {

               const { released = false, ...rest } = item;
               return (
                  <Stack
                     direction={'row'}
                     sx={{
                        position: 'relative',
                        alignItems: 'center',
                     }}
                     key={index}
                     gap={2}
                  >
                     <MenuItem
                        selected={domain === item?.id}
                        onClick={() => {
                           onSelect(item?.id)
                        }}
                        sx={{ width: '100%' }}
                        {...rest}
                        disabled={!released}
                     >
                        <Typography>{item?.title}</Typography>
                     </MenuItem>
                     {item?.isNew && (
                        <Stack marginRight='5px'>
                        <Chip variant={('light') as any}
                           size="small"
                           label='New'
                           color={"info"}
                           sx={{
                              height: '18px',
                              '& .MuiChip-label': {
                                 fontSize: '12px',
                                 padding: '0 6px',
                                 lineHeight: 1.2
                              }
                           }}
                        />
                     </Stack>
                     )}
                     {item?.disabled && (item?.released) && (
                        <Tooltip
                           title='Your plan does not support this category'
                           placement='top'
                        >
                           <ListItemIcon
                           >
                              <LockFilled />
                           </ListItemIcon>
                        </Tooltip>
                     )}
                     {!item?.released && (
                        <Chip label='Coming soon' size='small' color='success' className='ml-2 scale-[.8]' />
                     )}
                     
                  </Stack>

               )
            })}
         </Menu>
      </>
   )
}