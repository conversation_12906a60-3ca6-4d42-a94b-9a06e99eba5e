import React from 'react';
import { Box, Skeleton, Stack, Grid, Card, CardContent } from '@mui/material';
import MainCard from 'components/MainCard';

const DetailPageSkeleton: React.FC = () => {
  return (
    <Stack spacing={3}>
      {/* Page Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <Skeleton variant="circular" width={56} height={56} />
          <Box>
            <Skeleton width={200} height={32} />
            <Skeleton width={300} height={20} sx={{ mt: 1 }} />
          </Box>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Skeleton variant="rectangular" width={100} height={36} />
          <Skeleton variant="rectangular" width={100} height={36} />
        </Box>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={2}>
        {Array.from({ length: 4 }).map((_, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card>
              <CardContent>
                <Skeleton width="60%" height={16} />
                <Skeleton width="40%" height={28} sx={{ mt: 1 }} />
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Main Content */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <MainCard title={<Skeleton width={150} height={24} />}>
            <Stack spacing={2}>
              {Array.from({ length: 5 }).map((_, index) => (
                <Box key={index}>
                  <Skeleton width="30%" height={20} />
                  <Skeleton width="100%" height={16} sx={{ mt: 0.5 }} />
                  <Skeleton width="80%" height={16} sx={{ mt: 0.5 }} />
                </Box>
              ))}
            </Stack>
          </MainCard>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Stack spacing={3}>
            <MainCard title={<Skeleton width={120} height={24} />}>
              <Stack spacing={2}>
                {Array.from({ length: 4 }).map((_, index) => (
                  <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Skeleton width="40%" height={16} />
                    <Skeleton width="40%" height={16} />
                  </Box>
                ))}
              </Stack>
            </MainCard>
            
            <MainCard title={<Skeleton width={120} height={24} />}>
              <Skeleton variant="rectangular" width="100%" height={200} />
            </MainCard>
          </Stack>
        </Grid>
      </Grid>
    </Stack>
  );
};

export default DetailPageSkeleton;