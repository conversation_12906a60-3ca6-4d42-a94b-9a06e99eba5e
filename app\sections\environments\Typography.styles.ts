import { styled } from '@mui/material/styles';
import { Typography } from '@mui/material';

export const StyledTitle = styled(Typography)(({ theme }) => ({
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  fontSize: '1.2rem',
  fontWeight: 600,
  letterSpacing: 0,
  flex: 1,
  minWidth: 0,
  color: theme.palette.text.primary,
}));

export const StyledDescription = styled(Typography)(({ theme }) => ({
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  fontSize: '0.9rem',
  lineHeight: 1.5,
  color: theme.palette.text.secondary,
  wordBreak: 'break-word',
}));

export const StyledTemplateTitle = styled(Typography)(({ theme }) => ({
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitLineClamp: 1,
  WebkitBoxOrient: 'vertical',
  fontSize: '0.875rem',
  fontWeight: 600,
  letterSpacing: 0,
  color: theme.palette.text.primary,
}));

export const StyledTemplateDescription = styled(Typography)(({ theme }) => ({
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: '-webkit-box',
  WebkitLineClamp: 2,
  WebkitBoxOrient: 'vertical',
  lineHeight: 1.4,
  fontSize: '0.75rem',
  color: theme.palette.text.secondary,
}));

export const StyledPlanTitle = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.primary,
  fontSize: '0.875rem',
  fontWeight: 600,
}));

export const StyledPlanDescription = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: '0.75rem',
  lineHeight: 1.5,
}));