name: Deploy - Production

on:
  workflow_dispatch:

env:
  # Define authorized team members (add/remove users as needed)
  AUTHORIZED_USERS: "<PERSON><PERSON>,shruthiv08,kmune<PERSON>,s<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,rkousar"

jobs:
  approval:
    runs-on: dev
    outputs:
      approved: ${{ steps.final-status.outputs.approved }}
    steps:
      - name: Check Authorization
        id: check-approval
        run: |
          TRIGGERED_BY="${{ github.actor }}"
          echo "Workflow triggered by: $TRIGGERED_BY"
          
          # Check if user is in authorized list
          if echo "$AUTHORIZED_USERS" | grep -q "$TRIGGERED_BY"; then
            echo " User $TRIGGERED_BY is authorized - auto-approved"
            echo "needs_manual_approval=false" >> $GITHUB_OUTPUT
          else
            echo " User $TRIGGERED_BY is NOT authorized - manual approval required"
            echo "needs_manual_approval=true" >> $GITHUB_OUTPUT
          fi

      - name: Create Approval Issue and Wait
        if: steps.check-approval.outputs.needs_manual_approval == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GH_PROMOTE_TOKEN }}
          script: |
            const issue = await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: ` Manual Approval Required: Promote Workflow by @${context.actor}`,
              body: `## Promotion Approval Request
              
              **Requested by**: @${context.actor}
              **Workflow**: Promote
              **Run ID**: ${context.runId}
              **Triggered at**: ${new Date().toISOString()}
              
              ### Authorization Status:
              - User \`${context.actor}\` is **NOT** in the authorized team
              - Manual approval is required to proceed with the promotion
              
              ### Authorized Team Members:
              ${process.env.AUTHORIZED_USERS.split(',').map(user => `- @${user.trim()}`).join('\n')}
              
              **To approve this promotion, comment:**
              \`\`\`
              /approve
              \`\`\`
              
              **To reject this promotion, comment:**
              \`\`\`
              /reject
              \`\`\`
              
              ### Links:
              - [Workflow Run](https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId})
              `,
              labels: ['approval-required', 'promotion-review']
            });
            
            // Wait for approval (check every 20 seconds for 10 minutes)
            let approved = false;
            const maxWait = 10 * 60 * 1000; // 10 minutes
            const checkInterval = 20 * 1000; // 20 seconds
            let elapsed = 0;
            
            const authorizedUsers = process.env.AUTHORIZED_USERS.split(',').map(user => user.trim());
            
            while (!approved && elapsed < maxWait) {
              await new Promise(resolve => setTimeout(resolve, checkInterval));
              elapsed += checkInterval;
              
              const comments = await github.rest.issues.listComments({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue.data.number
              });
              
              for (const comment of comments.data) {
                // Only authorized users can approve/reject
                if (authorizedUsers.includes(comment.user.login)) {
                  if (comment.body.includes('/approve')) {
                    approved = true;
                    console.log(' Promotion approved by: ' + comment.user.login);
                    break;
                  } else if (comment.body.includes('/reject')) {
                    throw new Error(' Promotion rejected by: ' + comment.user.login);
                  }
                }
              }
              
              if (!approved) {
                console.log(` Waiting for approval... (${elapsed/1000}s elapsed)`);
              }
            }
            
            if (!approved) {
              throw new Error(' Approval timeout - no response received');
            }
            
            // Close the issue
            await github.rest.issues.update({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: issue.data.number,
              state: 'closed'
            });

      - name: Set Final Approval Status
        id: final-status
        run: |
          if [[ "${{ steps.check-approval.outputs.needs_manual_approval }}" == "false" ]]; then
            echo "approved=true" >> $GITHUB_OUTPUT
            echo " Auto-approved due to authorized user"
          else
            # If we reach here, manual approval was successful (otherwise the previous step would have failed)
            echo "approved=true" >> $GITHUB_OUTPUT
            echo " Manual approval received"
          fi

  deployment:
    runs-on: ui
    needs: approval
    steps:
      - uses: actions/checkout@v3

      - name: Load environment variables from .env file
        run: |
          cat .github/workflows/.env-production | grep -v '^#' | while IFS= read -r line;do
          echo "$line" >> $GITHUB_ENV
          done

      - name: Pull k8s manifests
        uses: actions/checkout@v3
        with:
          repository: Unizo-Inc/unizo-infra-manifests
          path: unizo-infra-manifests
          token: ${{ secrets.PAT_TOKEN }}
          ref: main

      - name: Prepare kubernetes manifest
        run: |
          cat ${K8S_MANIFEST} | envsubst > deployment.yaml

      - name: Set up kubectl
        uses: azure/setup-kubectl@v1
        with:
          version: 'latest'

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -f deployment.yaml -n ${NAMESPACE} --kubeconfig=${K8S_CONFIG}

      - name: Restart Kubernetes Deployment
        run: |
          kubectl rollout restart deployment/${APP_NAME} -n ${NAMESPACE} --kubeconfig=${K8S_CONFIG}
