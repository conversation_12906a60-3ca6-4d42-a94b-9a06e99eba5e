import { alpha, <PERSON>, Chip, <PERSON><PERSON><PERSON><PERSON>on, <PERSON>ack, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Tooltip, Typography, useTheme } from "@mui/material";
import { AdditionalField } from "./type";
import { DeleteOutlined, DownOutlined, EditOutlined, PlusOutlined, RightOutlined } from "@ant-design/icons";
import { Dispatch, useState } from "react";

type DialogConfirmtypes = {
   open: boolean;
   field?: AdditionalField;
}

interface FieldRow extends
   Pick<FieldListProps, 'setDeleteConfirmDialog' | 'toggleFieldExpansion' | 'expandedFields' | "openAddChildDialog" | "openEditDialog"> {
   field: AdditionalField;
   level?: number,
}

const FieldRow: React.FC<FieldRow> = ({
   field,
   level = 0,
   expandedFields,
   setDeleteConfirmDialog,
   toggleFieldExpansion,
   openAddChildDialog,
   openEditDialog
}) => {
   const theme = useTheme();

   const hasChildren = field.children && field.children.length > 0;
   const canHaveChildren = field.type === "object" || field.type === "array";
   const isExpanded = expandedFields.includes(field.id);

   const getTypeColor = (type: string) => {
      const colors: Record<string, string> = {
         string: theme.palette.info.main,
         number: theme.palette.success.main,
         boolean: theme.palette.warning.main,
         date: theme.palette.primary.main,
         array: theme.palette.secondary.main,
         object: theme.palette.error.main,
      };
      return colors[type] || theme.palette.grey[500];
   };

   return (
      <>
         <TableRow
            key={field.id}
            sx={{
               "&:hover": {
                  bgcolor: alpha(theme.palette.action.hover, 0.04),
               },
            }}
         >
            <TableCell>
               <Stack
                  direction="row"
                  spacing={1}
                  alignItems="flex-start"
                  sx={{ pl: level * 4 }}
               >
                  {canHaveChildren && (
                     <IconButton
                        size="small"
                        onClick={() => toggleFieldExpansion(field.id)}
                        sx={{ p: 0.25, mt: -0.5 }}
                     >
                        {isExpanded ? <DownOutlined /> : <RightOutlined />}
                     </IconButton>
                  )}
                  <Box>
                     <Typography variant="body2" fontFamily="monospace">
                        {field.name}
                     </Typography>
                     {field.description && (
                        <Typography
                           variant="caption"
                           color="text.secondary"
                           sx={{
                              display: 'block',
                              mt: 0.5,
                              wordBreak: 'break-word',
                              whiteSpace: 'normal',
                           }}
                        >
                           {field.description}
                        </Typography>
                     )}
                  </Box>
               </Stack>
            </TableCell>
            <TableCell>
               <Chip
                  label={field.type}
                  size="small"
                  sx={{
                     bgcolor: alpha(getTypeColor(field.type), 0.1),
                     color: getTypeColor(field.type),
                     fontWeight: 500,
                  }}
               />
            </TableCell>
            <TableCell>
               <Stack direction="row" spacing={1}>
                  {canHaveChildren && (
                     <Tooltip title="Add child field">
                        <IconButton
                           size="small"
                           onClick={() => openAddChildDialog(field)}
                           sx={{
                              border: "none",
                              color: "primary.main",
                              "&:hover": {
                                 bgcolor: alpha(theme.palette.primary.main, 0.1),
                              },
                           }}
                        >
                           <PlusOutlined />
                        </IconButton>
                     </Tooltip>
                  )}
                  {/* <Tooltip title="Edit field">
                     <IconButton
                        size="small"
                        onClick={() => openEditDialog(field)}
                        sx={{
                           border: "none",
                           "&:hover": {
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: "primary.main",
                           },
                        }}
                     >
                        <EditOutlined />
                     </IconButton>
                  </Tooltip> */}
                  <Tooltip title="Delete field">
                     <IconButton
                        size="small"
                        onClick={() => setDeleteConfirmDialog({ open: true, field })}
                        sx={{
                           border: "none",
                           color: "error.main",
                           "&:hover": {
                              bgcolor: alpha(theme.palette.error.main, 0.1),
                           },
                        }}
                     >
                        <DeleteOutlined />
                     </IconButton>
                  </Tooltip>
               </Stack>
            </TableCell>
         </TableRow>
         {isExpanded && hasChildren &&
            field.children?.map((child) => (
               <FieldRow
                  setDeleteConfirmDialog={setDeleteConfirmDialog}
                  key={child.id}
                  field={child}
                  level={level + 1}
                  toggleFieldExpansion={toggleFieldExpansion}
                  expandedFields={expandedFields}
                  openAddChildDialog={openAddChildDialog}
                  openEditDialog={openEditDialog}
               />
            ))}
      </>
   );
};

interface FieldListProps {
   modelFields: AdditionalField[]
   toggleFieldExpansion: (fieldId: string) => void
   setDeleteConfirmDialog: Dispatch<React.SetStateAction<DialogConfirmtypes>>
   openAddChildDialog: (field: AdditionalField) => void
   openEditDialog: (field: AdditionalField) => void
   expandedFields: string[]
}

const FieldList = ({
   expandedFields,
   modelFields,
   toggleFieldExpansion,
   setDeleteConfirmDialog,
   openAddChildDialog,
   openEditDialog
}: FieldListProps) => {
   const theme = useTheme();

   if (!modelFields?.length) return (
      <Box
         sx={{
            mt: 2,
            p: 3,
            textAlign: "center",
            bgcolor: alpha(theme.palette.grey[500], 0.04),
            borderRadius: 1,
         }}
      >
         <Typography variant="body2" color="text.secondary">
            No additional fields defined for this model
         </Typography>
      </Box>
   )

   return (
      <TableContainer
         sx={{
            border: `1px solid ${theme.palette.mode === "dark" ? "rgba(255, 255, 255, 0.12)" : "#e5e5e5"}`,
            borderRadius: 1,
         }}
      >
         <Table size="small">
            <TableHead>
               <TableRow>
                  <TableCell>Field</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Actions</TableCell>
               </TableRow>
            </TableHead>
            <TableBody>
               {modelFields
                  .filter((field) => !field.parentId)
                  .map((field) => (
                     <FieldRow
                        key={field.id}
                        field={field}
                        expandedFields={expandedFields}
                        setDeleteConfirmDialog={setDeleteConfirmDialog}
                        toggleFieldExpansion={toggleFieldExpansion}
                        openAddChildDialog={openAddChildDialog}
                        openEditDialog={openEditDialog}
                     />
                  ))}
            </TableBody>
         </Table>
      </TableContainer>
   )
}

export default FieldList;