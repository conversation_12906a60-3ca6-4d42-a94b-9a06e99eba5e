export const ENTITLE_MAPPER: any = {
   'CUSTOMER_COUNT': {
      keys: ['customerCount'],
      properties: [
         {
            label: 'Customer Count',
            key: 'customerCount',
            getMessage(externalValue: any) {
               return externalValue?.displayName
            },
            getEntitlementMessage: () => {
               return
            }
         }
      ]
   },
   'API_RATELIMIT': {
      keys: ['apiRateLimit'],
      properties: [
         {
            label: 'API Ratelimit',
            key: 'apiRateLimit',
            getMessage(externalValue: any) {
               return externalValue?.displayName
            }
         }
      ]
   },
   'REMOTE_DATA': {
      keys: ['remoteData'],
      properties: [
         {
            label: 'Remote Data',
            key: 'remoteData',
            getMessage(externalValue: any) {
               return externalValue?.displayName
            }
         }
      ]
   },
   'WEBHOOK_SUPPORT': {
      keys: ['webhookSupport'],
      properties: [
         {
            label: 'Webhook Support',
            key: 'webhookSupport',
            getMessage(externalValue: any) {
               return externalValue?.displayName
            }
         }
      ]
   },
   'FEATURE_SUPPORT': {
      keys: ['featureSupport'],
      properties: [
         {
            label: 'Feature Support',
            key: 'featureSupport',
            getMessage(externalValue: any) {
               return externalValue?.displayName
            }
         }
      ]
   },
   'LOG_RETENTION': {
      keys: ['logRetention'],
      properties: [
         {
            label: 'Log Retention',
            key: 'logRetention',
            getMessage(externalValue: any) {
               return externalValue?.displayName ?? '-'
            }
         }
      ]
   },
   'SERVICE_AGREEMENT': {
      keys: ['serviceAgreement'],
      properties: [
         {
            label: 'Service Agreement',
            key: 'serviceAgreement',
            getMessage(externalValue: any) {
               return externalValue?.displayName
            }
         }
      ]
   },
   'SSO': {
      keys: ['sso'],
      properties: [
         {
            label: 'SSO',
            key: 'sso',
            getMessage(externalValue: any) {
               return externalValue?.displayName
            }
         }
      ]
   }
}