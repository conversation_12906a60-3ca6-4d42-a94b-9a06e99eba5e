import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Stack,
  Button,
  Chip,
  useTheme,
  alpha,
  Skeleton,
  Accordion,
  AccordionSummary,
  AccordionDetails,

  Paper,
  Divider,
} from '@mui/material';
import { ChevronRight, ChevronDown, Check, ArrowLeft } from 'lucide-react';
import { useGettingStarted } from '../context/GettingStartedContext';
import { serviceProfileClient } from 'services/service-profile.service';
import { ServiceProfile } from 'types/service-profile';
import { toast } from 'sonner';
import { useNavigate } from '@remix-run/react';
import { getDomainByValue } from 'data/domains';
import { useServiceProfile } from 'hooks/useServiceProfile';
import { TenantOnboardType } from 'constants/organization';
import useSelfSignUp from 'sections/self-signup/hooks/useSelfSignUp';
import { useMutation } from '@tanstack/react-query';
import { tenantsClient } from 'services/tenants.service';
import { useGetOrganization } from 'hooks/api/organization/useGetOrganization';
import { Routes } from 'constants/route';

interface ServiceConfigurationProps {
  onNext: () => void;
  onBack: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

interface CategoryServices {
  category: string;
  categoryName: string;
  services: ServiceProfile[];
  isLoading: boolean;
  isExpanded: boolean;
}

enum PromotionInfoType {
  MetaData = "METADATA",
}

const SERVICE_PROFILE_KEY = 'SERVICE_PROFILE'

export default function ServiceConfiguration({ onNext, onBack }: ServiceConfigurationProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const { selectedCategories, selectedServices, addService, removeService, isPredefinedTenant, onboardingType } = useGettingStarted();
  const [categoryServices, setCategoryServices] = useState<Record<string, CategoryServices>>({});
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);

  const { loadImage } = useServiceProfile();
  const { tenantMetaDataClient } = useSelfSignUp();

  const tenantMetadata = tenantMetaDataClient?.data;
  const { attemptToUpdateOrgOnboarding } = useGetOrganization({ id: window.authUserOrgId });

  const { mutateAsync: createServicesBulk } = useMutation({
    mutationFn: async (payload: any) => {
      const response = await serviceProfileClient.createServicesBulk(payload);
      return response;
    },
    onSuccess: async () => {
      try {
        await attemptToUpdateOrgOnboarding(() => {
          navigate(Routes.QuickStart);
        });
      } catch (error) {
        console.error("Failed to update organization onboarding:", error);
        toast.error("Services were saved, but there was an error updating the organization status.");
      }
    },
    onError: (error: any) => {
      console.error("Failed to update tenant services:", error);
      toast.error("Failed to save service selections. Please try again.");
    },
  });

  const { mutateAsync: createTenantMutation, isPending: isUpdating } =
    useMutation({
      mutationFn: (payload: any) => {
        return tenantsClient.create(payload);
      },
      onSuccess: () => {
        // Navigate to the next step after successful API call
        navigate(Routes.TenantOnboarding);
      },
      onError: (error: any) => {
        console.error("Failed to update tenant services:", error);
        toast.error("Failed to save service selections. Please try again.");
      },
    });

  // Fetch services for selected categories
  useEffect(() => {
    const fetchServicesForCategories = async () => {
      // 1. Initialize loading state for all categories first
      const initialData: Record<string, CategoryServices> = {};
      for (const categoryCode of selectedCategories) {
        initialData[categoryCode] = {
          category: categoryCode,
          categoryName: getDomainByValue(categoryCode)?.label || categoryCode,
          services: [],
          isLoading: true,
          isExpanded: true, // expand by default
        };
      }
      setCategoryServices(initialData);
      setExpandedCategories(selectedCategories);

      try {
        // 2. Fetch all categories in parallel
        const responses = await Promise.all(
          selectedCategories.map((categoryCode) =>
            serviceProfileClient
              .searchProfiles({
                filter: {
                  property: "/type",
                  operator: "=",
                  values: [categoryCode],
                },
                pagination: { limit: 50, offset: 0 },
              })
              .then((response) => ({
                categoryCode,
                services: response.data?.data || [],
              }))
              .catch(() => ({
                categoryCode,
                services: [],
              }))
          )
        );

        // 3. Merge results into one state update
        const finalData: Record<string, CategoryServices> = {};
        for (const categoryCode of selectedCategories) {
          const result = responses.find((r) => r.categoryCode === categoryCode);
          finalData[categoryCode] = {
            category: categoryCode,
            categoryName: getDomainByValue(categoryCode)?.label || categoryCode,
            services: result?.services ?? [],
            isLoading: false,
            isExpanded: true,
          };
        }

        setCategoryServices(finalData);
      } catch (error) {
        console.error("Failed to fetch services:", error);
      }
    };

    if (selectedCategories.length > 0) {
      fetchServicesForCategories();
    }
  }, [selectedCategories]);

  const handleToggleService = (service: ServiceProfile, category: string) => {
    const isSelected = selectedServices.some(s => s.serviceProfileId === service.id);

    if (isSelected) {
      removeService(service.id);
    } else {
      addService({
        serviceProfileId: service.id,
        category: category,
        name: service.name,
        logo: service.image.small,
      });
    }
  };

  const handleToggleCategory = (categoryCode: string) => {
    setExpandedCategories(prev =>
      prev.includes(categoryCode)
        ? prev.filter(c => c !== categoryCode)
        : [...prev, categoryCode]
    );
  };

const handleNext = async () => {
  if (selectedServices.length === 0) {
    toast.error('Please select at least one connector');
    return;
  }

  if (onboardingType === TenantOnboardType.Self) {
    const categories = selectedCategories.map((category) => {
      const servicesForCategory = selectedServices
        .filter((s) => s.category === category)
        .map((service) => ({
          type: SERVICE_PROFILE_KEY,
          id: service.serviceProfileId,
        }));

      return servicesForCategory.length > 0
        ? { type: category, providers: servicesForCategory }
        : { type: category };
    });

    const tenantPayload: any = {
      categories,
      type: "SELF_SIGNUP",
      promotionInfo: {
        type: PromotionInfoType.MetaData,
        metadata: { id: tenantMetadata?.id as string },
      },
    };

    await createTenantMutation(tenantPayload);
  } else {
    const payload = {
      data: selectedServices.map((service) => ({
        serviceProfile: { id: service.serviceProfileId },
      })),
    };
    await createServicesBulk(payload);
  }
};

  const getSelectedCountForCategory = (categoryCode: string) => {
    return selectedServices.filter(s => s.category === categoryCode).length;
  };

  const renderServiceCard = (service: ServiceProfile, category: string) => {
    const isSelected = selectedServices.some(s => s.serviceProfileId === service.id);

    return (
      <Grid item xs={6} sm={4} md={3} lg={2.4} key={service.id}>
        <Paper
          onClick={() => handleToggleService(service, category)}
          elevation={0}
          sx={{
            cursor: 'pointer',
            p: 1.5,
            height: '100%',
            minHeight: 100,
            border: '1px solid',
            borderColor: isSelected ? theme.palette.primary.main : theme.palette.grey[200],
            borderRadius: 1.5,
            backgroundColor: theme.palette.background.paper,
            transition: 'all 0.2s ease',
            position: 'relative',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: 0.75,
            '&:hover': {
              borderColor: theme.palette.primary.main,
            },
          }}
        >
          {isSelected && (
            <Box
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                width: 20,
                height: 20,
                borderRadius: '50%',
                backgroundColor: theme.palette.primary.main,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Check size={14} color="white" strokeWidth={2.5} />
            </Box>
          )}

          <Box
            sx={{
              height: 36,
              width: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mt: 0.5,
              position: 'relative',
            }}
          >
            {loadImage(service, { size: 'small' })}
          </Box>

          <Typography
            variant="h5"
            sx={{
              textAlign: 'center',
              color: theme.palette.text.primary,
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            {service.name}
          </Typography>
        </Paper>
      </Grid>
    );
  };

  const renderCategorySection = (categoryCode: string) => {
    const data = categoryServices[categoryCode];
    if (!data) return null;

    const selectedCount = getSelectedCountForCategory(categoryCode);
    const isExpanded = expandedCategories.includes(categoryCode);

    return (
      <Box key={categoryCode} sx={{ mb: 3 }}>
        <Accordion
          expanded={isExpanded}
          onChange={() => handleToggleCategory(categoryCode)}
          sx={{
            backgroundColor: "transparent",
            boxShadow: "none",
            border: "none",
            "&:before": { display: "none" },
            "& .MuiAccordionSummary-root": {
              borderBottom: "none",
            },
            "& .MuiAccordionDetails-root": {
              borderTop: "none",
            },
          }}
        >
          <AccordionSummary
            expandIcon={<ChevronDown size={20} />}
            sx={{
              backgroundColor: 'transparent',
              px: 2,
              py: 1,
              minHeight: 48,
              borderRadius: 1,
              borderBottom: 'none',
              '&.Mui-expanded': {
                borderBottom: 'none',
              },
              '&:hover': {
                backgroundColor: alpha(theme.palette.action.hover, 0.04),
              },
              '& .MuiAccordionSummary-content': {
                alignItems: 'center',
                my: 0,
              },
              '& .MuiAccordionSummary-expandIconWrapper': {
                marginRight: 0,
                color: theme.palette.text.secondary,
              },
            }}
          >
            <Stack direction="row" alignItems="center" spacing={2} width="100%" justifyContent="space-between">
              <Stack direction="row" alignItems="center" spacing={2}>
                <Typography
                  variant="h3"
                  sx={{
                    color: theme.palette.text.primary,
                    fontSize: '1rem',
                    fontWeight: 600,
                  }}
                >
                  {data.categoryName}
                </Typography>
                {selectedCount > 0 && (
                  <Chip
                    label={`${selectedCount} selected`}
                    size="small"
                    sx={{
                      fontWeight: 600,
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      color: theme.palette.primary.main,
                      height: 24,
                    }}
                  />
                )}
              </Stack>
              {data.isLoading && (
                <Typography variant="caption" color="text.secondary">
                  Loading...
                </Typography>
              )}
            </Stack>
          </AccordionSummary>
          <AccordionDetails sx={{ px: 2, py: 2 }}>
            {data.isLoading ? (
              <Grid container spacing={2}>
                {[1, 2, 3].map(i => (
                  <Grid item xs={12} sm={6} md={4} key={i}>
                    <Skeleton variant="rectangular" height={120} />
                  </Grid>
                ))}
              </Grid>
            ) : data.services.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ py: 2, textAlign: 'center' }}>
                No connectors available for this category
              </Typography>
            ) : (
              <Grid container spacing={2}>
                {data.services.map(service => renderServiceCard(service, categoryCode))}
              </Grid>
            )}
          </AccordionDetails>
        </Accordion>
      </Box>
    );
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', position: 'relative' }}>
      {/* Header */}
      <Box sx={{ mb: 0 }}>
        {/* Title and Subtitle */}
        <Box sx={{ textAlign: 'center', pb: 3 }}>
          <Typography variant="h1" fontWeight={700} gutterBottom sx={{ fontSize: '2.5rem' }}>
            {isPredefinedTenant ? 'Configure Connectors' : 'Select Connectors'}
          </Typography>
          <Typography variant="h4" color="text.secondary" sx={{ fontSize: '1.25rem', fontWeight: 400 }}>
            {isPredefinedTenant
              ? 'Configure the connectors for your pre-selected API categories'
              : 'Choose the connectors you want to enable for your selected API categories'
            }
          </Typography>
        </Box>
      </Box>

      {/* Category Sections */}
      <Box
        sx={{
          flex: 1,
          overflowY: 'auto',
          mb: '80px',
          pr: 1,
          '&::-webkit-scrollbar': {
            width: 8,
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: alpha(theme.palette.divider, 0.1),
            borderRadius: 4,
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: alpha(theme.palette.divider, 0.3),
            borderRadius: 4,
            '&:hover': {
              backgroundColor: alpha(theme.palette.divider, 0.5),
            },
          },
        }}
      >
        {selectedCategories.map(categoryCode => renderCategorySection(categoryCode))}
      </Box>

      {/* Floating Bottom Bar */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 0,
          left: '280px', // Account for sidebar width
          right: 0,
          backgroundColor: theme.palette.background.paper,
          borderTop: `1px solid ${theme.palette.divider}`,
          zIndex: 100,
        }}
      >
        <Box
          sx={{
            px: 4,
            py: 2,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          {/* Back link */}
          <Stack direction="row" alignItems="center" spacing={1}>
            <ArrowLeft size={20} color={theme.palette.text.secondary} />
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ cursor: 'pointer', '&:hover': { color: theme.palette.text.primary } }}
              onClick={onBack}
            >
              Select categories
            </Typography>
          </Stack>

          {/* Continue button */}
          <Button
            variant="contained"
            onClick={handleNext}
            endIcon={<ChevronRight size={20} />}
            sx={{
              minWidth: 140,
              backgroundColor: theme.palette.primary.main,
              '&:hover': {
                backgroundColor: theme.palette.primary.dark,
              },
            }}
          >
            {isPredefinedTenant ? 'Complete Setup' : 'Continue'}
          </Button>
        </Box>
      </Box>
    </Box>
  );
}