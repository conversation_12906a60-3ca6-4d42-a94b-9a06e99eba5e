import { useEffect, useState } from "react";
import jsonpath from 'jsonpath';

import { CheckCircleTwoTone } from "@ant-design/icons";
import { Upload, UploadProps } from "components/@extended/upload";
import { useGetAccessPoint } from "hooks/api/use-accesspoint-config/useGetAccessPoint";
import { Stack } from "@mui/material";
import useServiceConfig from "store/setup-service";
import { replaceSlashWithDot } from "lib/json-path";

type ExtendedUploadProps = {
   property: string
   selectedAccessPoint: Record<string, any>
   formState: Record<string, any>
} & UploadProps

const ConfigFileUpload = ({
   multiple = false,
   property, value,
   formState = {},
   ...props }: ExtendedUploadProps) => {
   const [fileList, setFileList] = useState<any>(multiple ? value : [value]);

   const {
      selectedTypeConfig,
      updateSelectedTypeConfig
   } = useServiceConfig()

   useEffect(() => {
      setFileList(multiple ? value : [value])
   }, [value]);

   const serviceId = selectedTypeConfig?.accessPoint?.service?.id;


   const { attemptUpdateAccessPoints } = useGetAccessPoint({
      serviceProfileId: selectedTypeConfig?.serviceProfile?.id,
      serviceId
   })

   const customRequest: UploadProps['customRequest'] = (options) => {

      const { file } = options,
         lastPath = property?.split('/');

      const multipart = new FormData();
      const path = !lastPath?.length ? 'not_set' : lastPath?.[lastPath.length - 1];

      multipart.append(path, file);

      attemptUpdateAccessPoints(
         serviceId,
         selectedTypeConfig?.accessPoint?.id,
         multipart,
         (data: Record<string, any>) => {
            // Retrieve the accessPoint configuration, defaulting to an empty object if not present
            const accessPoint = selectedTypeConfig?.accessPoint ?? {};

            // Convert the lastPath array into a dot-separated string for JSONPath
            const path = replaceSlashWithDot(lastPath.join('/'));

            // Query the current value from the data object using JSONPath
            let currentKey = jsonpath.query(data ?? {}, path)?.[0] ?? '';

            // Update the value in the accessPoint object using JSONPath
            // The jsonpath.apply method applies a function to the value at the specified path
            Object.entries(formState).forEach(([key, value]) => {
               key = replaceSlashWithDot(key);
               jsonpath.apply(accessPoint, key, () => value);
            })

            jsonpath.apply(accessPoint, path, () => currentKey);

            // Update the selected type configuration with the modified accessPoint
            updateSelectedTypeConfig('accessPoint', accessPoint);
         }
      )
   };

   const name = fileList?.[0]?.name?.slice(0, 30);

   return (
      <Stack direction={'row'} gap={3} alignItems={'center'}>
         {name ? (
            <Stack>
               {name}
            </Stack>
         ) : null}
         <Upload
            customRequest={customRequest}
            buttonProps={(
               name ? { variant: 'text', className: 'font-semibold link', startIcon: <CheckCircleTwoTone />, color: 'secondary' } : {}
            )}
         >
            {props?.children}
         </Upload>
      </Stack>
   );
};

export default ConfigFileUpload;
