/* eslint-disable @typescript-eslint/no-explicit-any */
import { Stack, TextField, Typography, InputAdornment, IconButton } from "@mui/material";
import { LinkOutlined, CheckCircleFilled, DeleteOutlined, CloseCircleFilled } from '@ant-design/icons';

import { useTheme } from '@mui/material/styles';

import { WebhookData } from "store/self-signUp/self-signup";

interface WebhookRowProps {
  item: WebhookData;
  onUrlChange: (category: string, newUrl: string) => void;
  handleDelete: (category: string) => void;
}

const WebhookRow = ({ item, onUrlChange, handleDelete }: WebhookRowProps) => {
  const theme: any = useTheme();

  return (
    <Stack
      direction="row"
      sx={{
        p: 1.5,
        borderBottom: 1,
        borderColor: 'divider',
        '&:last-child': {
          borderBottom: 0
        },
        '&:hover': {
          bgcolor: 'grey.50'
        }
      }}
    >
      <Typography
        sx={{
          flex: '0 0 200px',
          fontSize: '0.875rem',
          color: theme.palette.grey[700],
          textTransform: 'capitalize',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        {item.category}
      </Typography>
      <Stack sx={{ flex: 1 }}>
        <TextField
          fullWidth
          size="small"
          placeholder="https://your-webhook-url.com"
          value={item.url}
          onChange={(e) => onUrlChange(item.category, e.target.value)}
          sx={{
            '& .MuiOutlinedInput-root': {
              backgroundColor: 'white',
              '& fieldset': {
                border: 'none'
              },
              '&:hover fieldset': {
                border: 'none'
              },
              '&.Mui-focused fieldset': {
                border: 'none'
              },
              '& input': {
                py: 0.75,
                px: 1
              }
            }
          }}
          InputProps={{
            startAdornment: item.url && (
              <InputAdornment position="start">
                <LinkOutlined style={{
                  color: theme.palette.grey[400],
                  fontSize: '14px'
                }} />
              </InputAdornment>
            ),
            endAdornment: item.url && (
              <InputAdornment position="end">
                {item.isValid ? (
                  <CheckCircleFilled style={{
                    color: theme.palette.success.main,
                    fontSize: '14px'
                  }} />
                ) : (
                  <CloseCircleFilled style={{
                    color: theme.palette.error.main,
                    fontSize: '14px'
                  }} />
                )}
                <IconButton
                  size="small"
                  onClick={() => handleDelete(item.category)}
                  sx={{
                    ml: 0.5,
                    p: 0.5,
                    color: theme.palette.grey[400],
                    '&:hover': {
                      color: theme.palette.error.main,
                    }
                  }}
                >
                  <DeleteOutlined style={{ fontSize: '14px' }} />
                </IconButton>
              </InputAdornment>
            )
          }}
        />
      </Stack>
    </Stack>
  );
};

export default WebhookRow;