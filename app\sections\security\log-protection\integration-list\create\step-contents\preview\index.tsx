import { Box, Grid, List, ListItem, Stack, Typography } from "@mui/material"
import { LogProtectionConnectorType } from "constants/log-protection";
import { State, useStatus } from "hooks/useStatus";
import { ReactElement, useEffect } from "react";
import useLogProtection from "store/security/log-protection"

export default () => {

   const { values } = useLogProtection();

   const { renderStatus } = useStatus();

   const preview = values?.integrationDetails;

   if (values?.service?.type !== LogProtectionConnectorType.Webhook) {
      return (
         <Grid container spacing={3}>
            {Object.entries(preview)?.map(([key, value = '-'], index) => {

               let valueBlock: JSX.Element = <Typography>{value?.toString()}</Typography>;

               if (typeof value === 'boolean') {
                  valueBlock = (
                     renderStatus(
                        value ? State.ACTIVE : State.INACTIVE
                     ) as any
                  )
               }

               return (
                  <Grid item xs={12} key={index}>
                     <Stack direction={'row'} alignItems={'end'} justifyContent={'space-between'}>
                        <Stack spacing={0.5} sx={{ width: '100%' }}>
                           <Typography color="secondary" sx={{ textTransform: 'capitalize' }} >
                              {key?.split('/')?.[1]}
                           </Typography>
                           {valueBlock}
                        </Stack>
                     </Stack>
                  </Grid>
               )
            })}
         </Grid>
      )
   }

   return (

      <List sx={{ py: 0 }}>
         <ListItem >
            <Grid container spacing={3}>

               {/* name */}
               <Grid item xs={12}>
                  <Stack direction={'row'} alignItems={'end'} justifyContent={'space-between'}>
                     <Stack spacing={0.5} sx={{ width: '100%' }}>
                        <Typography color="secondary">URL</Typography>
                        <Typography>{preview?.url}</Typography>
                     </Stack>
                  </Stack>
               </Grid>

               {/* contentType */}
               <Grid item xs={12} >
                  <Stack spacing={0.5}>
                     <Typography color="secondary">Content Type</Typography>
                     <Typography>{preview?.contentType}</Typography>
                  </Stack>
               </Grid>

               {/* ssl */}
               <Grid item xs={12}>
                  <Stack spacing={0.5}>
                     <Typography color="secondary">Secured SSL</Typography>
                     {renderStatus(
                        preview?.securedSSLRequired ? State.ACTIVE : State.INACTIVE
                     )}
                  </Stack>
               </Grid>

               {/* apiKey */}
               <Grid item xs={12}>
                  <Stack spacing={0.5}>
                     <Typography color="secondary">API Key</Typography>
                     <Typography>{preview?.apiKey}</Typography>
                  </Stack>
               </Grid>
            </Grid>
         </ListItem>


      </List>
   )
}