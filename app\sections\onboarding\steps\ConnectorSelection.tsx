import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Chip,
  Tabs,
  Tab,
  Stack,
  useTheme,
  alpha,
  Switch,
  Skeleton,
  Paper,
  Grid,
  TextField,
  InputAdornment,
  Fade,
  Grow,
} from '@mui/material';
import { 
  Search,
  X
} from 'lucide-react';
import useOnboardingStore from 'store/onboarding';
import { serviceProfileClient } from 'services/service-profile.service';
import { useQuery } from '@tanstack/react-query';
import { toast } from 'sonner';
import { ServiceProfile } from 'types/service-profile';
import MainCard from 'components/MainCard';
import { useServiceProfile } from 'hooks/useServiceProfile';
import debounce from 'lodash/debounce';
import { getDomainByValue, DOMAINS } from 'data/domains';

// Build category config from centralized domains data
const categoryConfig = DOMAINS.reduce((acc, domain) => {
  acc[domain.value] = {
    name: domain.label,
    icon: domain.icon,
  };
  return acc;
}, {} as Record<string, { name: string; icon: any }>);

export default function ConnectorSelection() {
  const theme = useTheme();
  const { selectedCategories, selectedServices, addSelectedService } = useOnboardingStore();
  const [activeTab, setActiveTab] = useState(0);
  const [servicesByCategory, setServicesByCategory] = useState<Record<string, ServiceProfile[]>>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const { loadImage } = useServiceProfile();

  // Debounce search term
  const debouncedSearch = useMemo(
    () => debounce((value: string) => {
      setDebouncedSearchTerm(value);
    }, 500),
    []
  );

  useEffect(() => {
    debouncedSearch(searchTerm);
  }, [searchTerm, debouncedSearch]);

  const currentCategory = selectedCategories[activeTab];

  // Fetch service profiles for all selected categories with search
  const { data: servicesData, isLoading, error, refetch } = useQuery({
    queryKey: ['serviceProfiles', selectedCategories, debouncedSearchTerm],
    queryFn: async () => {
      if (selectedCategories.length === 0) return {};
      
      const results: Record<string, ServiceProfile[]> = {};
      setIsSearching(!!debouncedSearchTerm);
      
      // Fetch services for each category in parallel
      const promises = selectedCategories.map(async (category) => {
        const filters = [
          { property: "/state", operator: "=", values: ["ACTIVE"] },
          { property: "/type", operator: "=", values: [category] }
        ];
        
        // Add search filter if search term exists
        if (debouncedSearchTerm) {
          filters.push({ property: "/name", operator: "LIKE", values: [debouncedSearchTerm] });
        }
        
        const payload = {
          filter: { and: filters },
          pagination: { limit: 50, offset: 0 }
        };
        
        try {
          const response = await serviceProfileClient.searchProfiles(payload);
          // Access the nested data structure
          const profilesData = response?.data?.data || response?.data?.result || [];
          results[category] = profilesData;
        } catch (err) {
          console.error(`Failed to fetch services for ${category}:`, err);
          results[category] = [];
        }
      });
      
      await Promise.all(promises);
      setIsSearching(false);
      return results;
    },
    enabled: selectedCategories.length > 0,
    staleTime: 5 * 60 * 1000,
  });

  useEffect(() => {
    if (servicesData) {
      setServicesByCategory(servicesData);
    }
  }, [servicesData]);

  useEffect(() => {
    if (error) {
      toast.error('Failed to load connectors. Please try again.');
    }
  }, [error]);

  const toggleConnector = (profile: ServiceProfile) => {
    addSelectedService(profile);
  };

  const isServiceSelected = (profileId: string) => {
    return selectedServices.some(s => s.id === profileId);
  };

  const getSelectedCountForCategory = (category: string) => {
    return selectedServices.filter(s => s.type === category).length;
  };

  if (selectedCategories.length === 0) {
    return (
      <Box textAlign="center" py={4}>
        <Typography variant="body1" color="text.secondary">
          Please select categories first
        </Typography>
      </Box>
    );
  }

  const availableConnectors = servicesByCategory[currentCategory] || [];

  return (
    <Box>
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" mb={3}>
        <Box>
          <Typography variant="h6" gutterBottom fontWeight={600}>
            Choose your connectors
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Enable the services you want to integrate for each category
          </Typography>
        </Box>
        
        <TextField
          size="small"
          placeholder="Search connectors..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          sx={{ 
            minWidth: 250,
            '& .MuiInputBase-root': {
              borderRadius: 1,
            }
          }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search size={18} style={{ opacity: 0.5 }} />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <Box
                  component="button"
                  onClick={() => setSearchTerm('')}
                  sx={{
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    p: 0.5,
                    borderRadius: 0.5,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.action.active, 0.1),
                    }
                  }}
                >
                  <X size={16} />
                </Box>
              </InputAdornment>
            ),
          }}
        />
      </Stack>

      {selectedCategories.length > 0 && (
        <Paper variant="outlined" sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={(_, value) => setActiveTab(value)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                textTransform: 'uppercase',
                fontWeight: 500,
                fontSize: '0.875rem',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.04),
                },
              },
              '& .MuiTabs-indicator': {
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              },
            }}
          >
            {selectedCategories.map((category, index) => {
              const count = getSelectedCountForCategory(category);
              const config = categoryConfig[category];
              const displayName = config?.name || category.toUpperCase();
              const Icon = config?.icon;
              
              return (
                <Tab
                  key={category}
                  label={
                    <Stack direction="row" spacing={1} alignItems="center">
                      {Icon && (
                        <Icon 
                          size={16} 
                          strokeWidth={2}
                          style={{ 
                            opacity: 0.7,
                            marginRight: 4
                          }}
                        />
                      )}
                      <span>{displayName}</span>
                      {count > 0 && (
                        <Chip
                          size="small"
                          label={count}
                          color="primary"
                          sx={{ height: 20, fontSize: '0.75rem' }}
                        />
                      )}
                    </Stack>
                  }
                />
              );
            })}
          </Tabs>
        </Paper>
      )}

      <Box sx={{ position: 'relative', minHeight: 200 }}>
        <Fade in={isLoading || isSearching} unmountOnExit>
          <Grid container spacing={2} sx={{ position: 'absolute', width: '100%' }}>
            {[1, 2, 3, 4].map((index) => (
              <Grid item xs={12} sm={6} key={index}>
                <Skeleton 
                  variant="rectangular" 
                  height={100} 
                  sx={{ 
                    borderRadius: 1,
                    animation: 'pulse 1.5s ease-in-out infinite',
                    '@keyframes pulse': {
                      '0%': { opacity: 0.6 },
                      '50%': { opacity: 1 },
                      '100%': { opacity: 0.6 },
                    },
                  }} 
                />
              </Grid>
            ))}
          </Grid>
        </Fade>
        
        <Fade in={!(isLoading || isSearching)} timeout={400}>
          <Box>
            {availableConnectors.length > 0 ? (
              <Grid container spacing={2}>
                {availableConnectors.map((profile, index) => {
                  const isSelected = isServiceSelected(profile.id);

                  return (
                    <Grow 
                      in={true} 
                      timeout={300 + (index * 50)}
                      key={profile.id}
                      style={{ transformOrigin: '0 0 0' }}
                    >
                      <Grid item xs={12} sm={6} md={6} lg={6}>
                        <MainCard
                          sx={{
                            height: '100%',
                            position: 'relative',
                            display: 'flex',
                            flexDirection: 'column',
                            transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': { 
                              cursor: 'pointer',
                              boxShadow: theme.shadows[1],
                            },
                            ...(isSelected && {
                              backgroundColor: alpha(theme.palette.primary.main, 0.02),
                            }),
                          }}
                          onClick={() => toggleConnector(profile)}
                        >
                    {profile.isBeta && (
                      <Box sx={{
                        position: 'absolute',
                        top: '-2px',
                        right: '0px',
                        zIndex: 1
                      }}>
                        <Chip
                          variant={'light' as any}
                          size="small"
                          label='Beta'
                          color="info"
                          sx={{
                            height: '18px',
                            borderRadius: "0px !important",
                            '& .MuiChip-label': {
                              fontSize: '11px',
                              lineHeight: 1.2,
                              padding: "0 8px",
                            }
                          }}
                        />
                      </Box>
                    )}

                      <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                        <Stack direction="row" gap={2} alignItems="flex-start" sx={{ flex: 1, minWidth: 0 }}>
                          <Box sx={{ flexShrink: 0 }}>
                            {loadImage(profile, { size: 'small' })}
                          </Box>
                          <Stack sx={{ flex: 1, minWidth: 0 }}>
                            <Typography 
                              variant="body1" 
                              fontWeight={600}
                              sx={{ 
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {profile.name}
                            </Typography>
                            {profile.description && (
                              <Typography 
                                variant="caption" 
                                color="text.secondary"
                                sx={{
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                  lineHeight: 1.4,
                                }}
                              >
                                {profile.description}
                              </Typography>
                            )}
                          </Stack>
                        </Stack>

                        <Switch
                          checked={isSelected}
                          onClick={(e) => e.stopPropagation()}
                          onChange={() => toggleConnector(profile)}
                          sx={{ 
                            ml: 1,
                            '& .MuiSwitch-switchBase': {
                              transition: 'transform 0.2s',
                            },
                            '& .MuiSwitch-track': {
                              transition: 'opacity 0.2s, background-color 0.2s',
                            },
                          }}
                        />
                      </Stack>
                      </MainCard>
                    </Grid>
                  </Grow>
                );
                })}
              </Grid>
            ) : (
              <Fade in={true} timeout={500}>
                <Box
              sx={{
                textAlign: 'center',
                py: 6,
                px: 3,
                border: 2,
                borderStyle: 'dashed',
                borderColor: theme.palette.grey[300],
                borderRadius: 2,
                backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[900] : theme.palette.grey[50],
              }}
            >
              <Typography variant="body1" color="text.secondary">
                {searchTerm 
                  ? `No connectors found matching "${searchTerm}" in ${categoryConfig[currentCategory]?.name || currentCategory.toUpperCase()}`
                  : `No connectors available for ${categoryConfig[currentCategory]?.name || currentCategory.toUpperCase()}`
                }
                </Typography>
              </Box>
            </Fade>
            )}
          </Box>
        </Fade>
      </Box>

    </Box>
  );
}