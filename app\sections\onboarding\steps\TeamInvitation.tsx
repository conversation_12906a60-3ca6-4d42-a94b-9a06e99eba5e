import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  TextField,
  Paper,
  Stack,
  Chip,
  Button,
  MenuItem,
  useTheme,
  alpha,
  Divider,
} from '@mui/material';
import Avatar from '@mui/material/Avatar';
import IconButton from '@mui/material/IconButton';
import { UserPlus, X, Mail } from 'lucide-react';
import useOnboardingStore from 'store/onboarding';

const roles = [
  { value: 'ADMIN', label: 'Admin', description: 'Full access to all features' },
  { value: 'MEMBER', label: 'Member', description: 'Manage integrations and API keys' },
  { value: 'VIEWER', label: 'Viewer', description: 'View-only access' },
];

export default function TeamInvitation() {
  const theme = useTheme();
  const { invitedMembers, setInvitedMembers } = useOnboardingStore();
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('MEMBER');
  const [error, setError] = useState('');

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleAddMember = () => {
    if (!email) {
      setError('Email is required');
      return;
    }

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    if (invitedMembers.some(member => member.email === email)) {
      setError('This email has already been invited');
      return;
    }

    setInvitedMembers([...invitedMembers, { email, role }]);
    setEmail('');
    setError('');
  };

  const handleRemoveMember = (emailToRemove: string) => {
    setInvitedMembers(invitedMembers.filter(member => member.email !== emailToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddMember();
    }
  };

  const getRoleInfo = (roleValue: string) => {
    return roles.find(r => r.value === roleValue);
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom fontWeight={600}>
        Invite your team
      </Typography>
      <Typography variant="body2" color="text.secondary" mb={3}>
        Collaborate with your team members on integrations (optional)
      </Typography>

      <Paper variant="outlined" sx={{ p: 3, mb: 3, borderRadius: 2 }}>
        <Stack spacing={2}>
          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
            <TextField
              fullWidth
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                setError('');
              }}
              onKeyPress={handleKeyPress}
              error={!!error}
              helperText={error}
              InputProps={{
                startAdornment: (
                  <Mail size={18} color={theme.palette.text.disabled} style={{ marginRight: 8 }} />
                ),
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1.5,
                },
              }}
            />
            <TextField
              select
              value={role}
              onChange={(e) => setRole(e.target.value)}
              sx={{
                minWidth: 150,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 1.5,
                },
              }}
              SelectProps={{
                renderValue: (value) => {
                  const selectedRole = roles.find(r => r.value === value);
                  return selectedRole?.label || value;
                }
              }}
            >
              {roles.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Box>
                    <Typography variant="body2">{option.label}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {option.description}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </TextField>
            <Button
              variant="contained"
              onClick={handleAddMember}
              startIcon={<UserPlus size={18} />}
              sx={{
                borderRadius: 1.5,
                minWidth: 120,
                textTransform: 'none',
                boxShadow: 'none',
                '&:hover': {
                  boxShadow: 'none',
                },
              }}
            >
              Add
            </Button>
          </Stack>
        </Stack>
      </Paper>

      {invitedMembers.length > 0 && (
        <>
          <Typography variant="body2" fontWeight={500} mb={2}>
            Team members ({invitedMembers.length})
          </Typography>
          <Stack spacing={2}>
            {invitedMembers.map((member, index) => {
              const roleInfo = getRoleInfo(member.role);
              return (
                <Paper
                  key={member.email}
                  elevation={0}
                  sx={{
                    p: 2.5,
                    borderRadius: 1.5,
                    border: `1px solid ${theme.palette.divider}`,
                    backgroundColor: theme.palette.background.paper,
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      borderColor: alpha(theme.palette.primary.main, 0.2),
                      backgroundColor: alpha(theme.palette.grey[50], 0.5),
                    },
                  }}
                >
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <Avatar 
                        sx={{ 
                          width: 40, 
                          height: 40, 
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          fontSize: '1rem',
                          fontWeight: 500,
                        }}
                      >
                        {member.email[0].toUpperCase()}
                      </Avatar>
                      <Box>
                        <Typography variant="body2" fontWeight={500}>
                          {member.email}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {roleInfo?.description}
                        </Typography>
                      </Box>
                    </Stack>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <Chip
                        label={roleInfo?.label}
                        size="small"
                        variant="outlined"
                        sx={{ 
                          borderRadius: 1,
                          borderColor: theme.palette.divider,
                          color: theme.palette.text.secondary,
                          fontSize: '0.75rem',
                          height: 24,
                          '& .MuiChip-label': {
                            px: 1.5,
                          }
                        }}
                      />
                      <IconButton
                        size="small"
                        onClick={() => handleRemoveMember(member.email)}
                        sx={{
                          color: theme.palette.text.secondary,
                          '&:hover': {
                            color: theme.palette.error.main,
                            backgroundColor: alpha(theme.palette.error.main, 0.08),
                          },
                        }}
                      >
                        <X size={18} />
                      </IconButton>
                    </Stack>
                  </Stack>
                </Paper>
              );
            })}
          </Stack>
        </>
      )}

      {invitedMembers.length === 0 && (
        <Box
          sx={{
            textAlign: 'center',
            py: 4,
            px: 3,
            border: 2,
            borderStyle: 'dashed',
            borderColor: theme.palette.grey[300],
            borderRadius: 2,
            backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[900] : theme.palette.grey[50],
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <UserPlus size={32} color={theme.palette.text.disabled} />
          <Typography variant="body1" color="text.secondary" mt={2}>
            No team members invited yet
          </Typography>
          <Typography variant="caption" color="text.secondary">
            Add team members to collaborate on integrations
          </Typography>
        </Box>
      )}
    </Box>
  );
}