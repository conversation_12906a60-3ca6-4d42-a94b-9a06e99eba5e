import React from 'react';

import { Info } from 'lucide-react';
import { CardContent, Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';

import { PlanInfoCardProps } from '../Environment.types';
import {
  StyledPlanCard,
  StyledPlanBadge,
  PlanInfo,
} from '../Environment.styles';
import { StyledPlanTitle, StyledPlanDescription } from '../Typography.styles';

function getEnvLimitMessage(isLaunch: boolean, isEnterprise: boolean) {
  if (isLaunch) {
    return "You can create up to 1 environment with your current subscription.";
  }
  if (isEnterprise) {
    return "You can create up to 5 environments with your current subscription.";
  }
  return "Your subscription doesn't support creating environments. Upgrade to create and manage environments.";
}

export const PlanInfoCard: React.FC<PlanInfoCardProps> = ({
  highestTier,
  isLaunch,
  isEnterprise,
}) => {
  const theme = useTheme();

  return (
    <StyledPlanCard variant="outlined">
      <CardContent sx={{ p: 2 }}>
        <PlanInfo>
          <Box
            sx={{
              color: theme.palette.info.main,
              flexShrink: 0,
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Info size={20} />
          </Box>
          <Box flex={1}>
            <Box display="flex" alignItems="center" gap={1}>
              <StyledPlanTitle variant="subtitle2">
                Environment Limit
              </StyledPlanTitle>
              <StyledPlanBadge component="span">
                {highestTier}
              </StyledPlanBadge>
            </Box>
            <StyledPlanDescription variant="body2">
              {getEnvLimitMessage(isLaunch, isEnterprise)}
            </StyledPlanDescription>
          </Box>
        </PlanInfo>
      </CardContent>
    </StyledPlanCard>
  );
};