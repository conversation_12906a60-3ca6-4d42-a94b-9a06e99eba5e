import { CheckOutlined, ClockCircleOutlined, CloseOutlined } from "@ant-design/icons";
import { Grid, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, Table, List, ListItemButton, ListItemAvatar, Avatar, ListItemText, ListItemSecondaryAction, Stack } from "@mui/material"
import SimpleBarScroll from "components/third-party/SimpleBar"
import { useDashboard } from "hooks/api/dashboard/useDashboard";
import { memo, useMemo } from "react";

const avatarSX = {
   width: 36,
   height: 36,
   fontSize: '1rem'
};

// action style
const actionSX = {
   mt: 0.75,
   ml: 1,
   top: 'auto',
   right: 'auto',
   alignSelf: 'flex-start',
   transform: 'none'
};

export default memo(() => {
   const { errorStats: data = [] } = useDashboard();

   return (
      <Stack>
         <ListItemButton
            sx={(theme) => {
               return {
                  backgroundColor: theme?.palette?.grey?.[50]
               }
            }}
         >
            <ListItemText primary={
               <Typography variant="subtitle1">
                  Message
               </Typography>}
            />
            <ListItemSecondaryAction>
               <Typography variant="subtitle1" noWrap >
                  Count
               </Typography>
            </ListItemSecondaryAction>
         </ListItemButton>
         <SimpleBarScroll
            sx={{
               height: 421
            }}
         >
            <List
               component="nav"
               sx={{
                  p: 0,
                  '& .MuiListItemButton-root': {
                     py: 1.5,
                     '& .MuiAvatar-root': avatarSX,
                     '& .MuiListItemSecondaryAction-root': { ...actionSX, position: 'relative' }
                  }
               }}
            >
               {data?.map((item: Record<string, any>, index: number) => {
                  return (
                     <ListItemButton divider key={index}>
                        <ListItemAvatar>
                           <Avatar sx={{ color: 'error.main', bgcolor: 'error.lighter' }}>
                              <CloseOutlined />
                           </Avatar>
                        </ListItemAvatar>
                        <ListItemText primary={
                           <Typography variant="subtitle1">
                              {item?.errorStats?.message}
                           </Typography>}
                           secondary={item?.errorStats?.code}
                        />
                        <ListItemSecondaryAction>
                           <Typography variant="subtitle1" noWrap mt={1}>
                              {item?.errorStats?.totalCount}
                           </Typography>
                        </ListItemSecondaryAction>
                     </ListItemButton>
                  )
               })}
            </List>
         </SimpleBarScroll>
      </Stack>
   )
})