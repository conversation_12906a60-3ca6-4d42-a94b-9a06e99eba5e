import { useState } from 'react';
import { Dialog, DialogContent, DialogTitle, Typography, Box, Button, Stack } from '@mui/material';
import { styled, useTheme, alpha } from '@mui/material/styles';
import SketchOutlined from '@ant-design/icons/SketchOutlined';
import InfoCircleOutlined from '@ant-design/icons/InfoCircleOutlined';
import { differenceInDays, isAfter, parseISO } from 'date-fns';

const StyledTrialIndicator = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  padding: theme.spacing(0.75, 1.5),
  borderRadius: theme.spacing(1),
  border: `1px solid ${theme.palette.warning.main}`,
  backgroundColor: 'transparent',
  cursor: 'pointer',
  transition: theme.transitions.create(['border-color', 'background-color']),
  '&:hover': {
    backgroundColor: alpha(theme.palette.warning.main, 0.08),
    borderColor: theme.palette.warning.dark,
  }
}));

interface TrialExpirationIndicatorProps {
  cancelledDate: string;
}

export default function TrialExpirationIndicator({ cancelledDate }: TrialExpirationIndicatorProps) {
  const [open, setOpen] = useState(false);
  const theme = useTheme();
  
  const today = new Date();
  const expiryDate = parseISO(cancelledDate);
  const daysRemaining = differenceInDays(expiryDate, today);
  const isExpired = isAfter(today, expiryDate);
  
  const handleClick = () => {
    setOpen(true);
  };
  
  const handleClose = () => {
    setOpen(false);
  };
  
  // Don't show indicator if more than 12 days remaining
  if (daysRemaining > 12) {
    return null;
  }
  
  const getIconColor = () => {
    if (isExpired) return theme.palette.error.main;
    if (daysRemaining <= 7) return theme.palette.error.main;
    if (daysRemaining <= 14) return theme.palette.warning.main;
    return theme.palette.warning.main;
  };
  
  const getBorderColor = () => {
    if (isExpired) return theme.palette.error.main;
    if (daysRemaining <= 7) return theme.palette.error.main;
    return theme.palette.warning.main;
  };
  
  const getLabel = () => {
    if (isExpired) return 'Trial Expired';
    if (daysRemaining === 0) return 'Expires Today';
    if (daysRemaining === 1) return '1 day left';
    return `${daysRemaining} days left`;
  };
  
  return (
    <>
      <StyledTrialIndicator 
        onClick={handleClick}
        sx={{ 
          borderColor: getBorderColor(),
          '&:hover': {
            backgroundColor: alpha(getBorderColor(), 0.08),
            borderColor: getBorderColor(),
          }
        }}
      >
        <SketchOutlined 
          style={{ 
            fontSize: 18,
            color: getIconColor()
          }} 
        />
        <Typography 
          variant="body2" 
          fontWeight={500}
          sx={{ color: getBorderColor() }}
        >
          {getLabel()}
        </Typography>
      </StyledTrialIndicator>
      
      <Dialog 
        open={open} 
        onClose={handleClose} 
        maxWidth="md" 
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
            overflow: 'hidden'
          }
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' } }}>
          {/* Left Column - Illustration */}
          <Box 
            sx={{ 
              flex: 1,
              bgcolor: 'background.default',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              p: 6,
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundImage: 'url(/images/brand/welcomeSelfRegistration.svg)',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
                backgroundSize: 'cover',
                opacity: 0.1
              }}
            />
            <Stack spacing={3} alignItems="center" textAlign="center" sx={{ position: 'relative', zIndex: 1 }}>
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  bgcolor: alpha(isExpired ? theme.palette.error.main : theme.palette.warning.main, 0.1),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <SketchOutlined 
                  style={{ 
                    fontSize: 40,
                    color: isExpired ? theme.palette.error.main : theme.palette.warning.main
                  }} 
                />
              </Box>
              <Typography variant="h4" fontWeight={700}>
                {isExpired ? 'Trial Period Ended' : 'Trial Expiring Soon'}
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 300 }}>
                {isExpired 
                  ? 'Your trial has ended. Upgrade now to continue using Unizo and unlock all features.'
                  : `Your trial expires on ${expiryDate.toLocaleDateString('en-US', { 
                      month: 'long', 
                      day: 'numeric', 
                      year: 'numeric' 
                    })}`}
              </Typography>
            </Stack>
          </Box>

          {/* Right Column - Content */}
          <Box sx={{ flex: 1, p: 6 }}>
            <Stack spacing={4}>
              <Box>
                <Typography variant="h5" fontWeight={600} gutterBottom>
                  Need More Time?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  If you need more time to explore Unizo's capabilities, our sales team can help extend your trial.
                </Typography>
              </Box>

              {/* Trial Extension Message */}
              <Box 
                sx={{ 
                  p: 2, 
                  bgcolor: alpha(theme.palette.warning.main, 0.08),
                  borderRadius: 1.5,
                  border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`
                }}
              >
                <Stack direction="row" alignItems="flex-start" spacing={1.5}>
                  <InfoCircleOutlined style={{ fontSize: 18, color: theme.palette.warning.main, marginTop: 2 }} />
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight={600} gutterBottom>
                      Trial Extension Available
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Need more time to evaluate? Our sales team can extend your trial period.
                    </Typography>
                  </Box>
                </Stack>
              </Box>

              {/* Plan Options */}
              <Box>
                <Typography variant="body2" fontWeight={600} gutterBottom sx={{ mb: 1.5 }}>
                  When you're ready, choose a plan that fits your needs:
                </Typography>
                <Stack spacing={1.5}>
                  <Box 
                    sx={{ 
                      p: 2, 
                      bgcolor: 'background.default',
                      borderRadius: 1,
                      border: `1px solid ${theme.palette.divider}`
                    }}
                  >
                    <Typography variant="subtitle2" fontWeight={600}>
                      Launch Plan
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Perfect for small teams getting started with integrations
                    </Typography>
                  </Box>
                  
                  <Box 
                    sx={{ 
                      p: 2, 
                      bgcolor: alpha(theme.palette.primary.main, 0.04),
                      borderRadius: 1,
                      border: `2px solid ${theme.palette.primary.main}`,
                      position: 'relative'
                    }}
                  >
                    <Box
                      sx={{
                        position: 'absolute',
                        top: -8,
                        right: 12,
                        bgcolor: 'primary.main',
                        color: 'primary.contrastText',
                        px: 1,
                        py: 0.25,
                        borderRadius: 0.5,
                        fontSize: '0.65rem',
                        fontWeight: 600,
                        textTransform: 'uppercase'
                      }}
                    >
                      Popular
                    </Box>
                    <Typography variant="subtitle2" fontWeight={600}>
                      Scale Plan
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      For growing businesses with advanced integration needs
                    </Typography>
                  </Box>
                  
                  <Box 
                    sx={{ 
                      p: 2, 
                      bgcolor: 'background.default',
                      borderRadius: 1,
                      border: `1px solid ${theme.palette.divider}`
                    }}
                  >
                    <Typography variant="subtitle2" fontWeight={600}>
                      Enterprise
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Custom solutions for large organizations with complex requirements
                    </Typography>
                  </Box>
                </Stack>
              </Box>

              <Stack spacing={2}>
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  fullWidth
                  href="mailto:<EMAIL>?subject=Trial%20Extension%20Request"
                  sx={{ py: 1.5 }}
                >
                  Request Trial Extension
                </Button>
                
                <Button
                  variant="outlined"
                  color="primary"
                  fullWidth
                  href="https://unizo.ai/pricing/"
                  target="_blank"
                >
                  View Pricing Details
                </Button>
              </Stack>

              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="caption" color="text.secondary">
                  Need help? Email <NAME_EMAIL>
                </Typography>
              </Box>
            </Stack>
          </Box>
        </Box>
      </Dialog>
    </>
  );
}