import { alpha, Box, Button, ClickAwayListener, Dialog, DialogActions, DialogContent, DialogTitle, FormControl, IconButton, InputAdornment, InputLabel, List, ListItem, ListItemButton, MenuItem, Paper, Popper, Select, Stack, TextField, Typography, useTheme } from "@mui/material";
import { X, ChevronDown, Plus, FileText, Hash, ToggleLeft, Calendar, ListIcon, Braces } from "lucide-react";
import { useState, useRef, useEffect } from "react";

const AutocompleteField = ({
   value,
   onChange,
   options,
   placeholder,
   hasError,
   excludeValues = [],
   onCreateField,
   allowCreate = false,
}: {
   value: string;
   onChange: (value: string) => void;
   options: any[];
   placeholder: string;
   hasError?: boolean;
   excludeValues?: string[];
   onCreateField?: (fieldName: string) => void;
   allowCreate?: boolean;
}) => {
   const theme = useTheme();
   const [isOpen, setIsOpen] = useState(false);
   const [searchValue, setSearchValue] = useState(value);
   const anchorRef = useRef<HTMLDivElement>(null);
   const [showCreateDialog, setShowCreateDialog] = useState(false);
   const [newFieldData, setNewFieldData] = useState({
      name: "",
      type: "string",
      description: "",
   });

   useEffect(() => {
      setSearchValue(value);
   }, [value]);

   const filteredOptions = options.filter(
      (option) =>
         option.label.toLowerCase().includes(searchValue.toLowerCase()) &&
         !excludeValues.includes(option.value) &&
         option.value !== value // Don't exclude the current value
   );

   const handleSelect = (optionValue: string) => {
      onChange(optionValue);
      setSearchValue(optionValue);
      setIsOpen(false);
   };

   return (
      <ClickAwayListener onClickAway={() => setIsOpen(false)}>
         <Box ref={anchorRef} sx={{ position: "relative" }}>
            <TextField
               fullWidth
               size="small"
               value={searchValue}
               onChange={(e) => {
                  setSearchValue(e.target.value);
                  setIsOpen(true);
               }}
               onFocus={() => setIsOpen(true)}
               placeholder={placeholder}
               autoComplete="off"
               inputProps={{
                  autoComplete: "off",
                  "data-form-type": "other",
               }}
               sx={{
                  "& .MuiOutlinedInput-root": {
                     pr: "40px",
                     fontSize: "0.875rem",
                     bgcolor: "background.paper",
                     "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: hasError ? "error.main" : "#d1d5db",
                        borderWidth: 1,
                     },
                     "&:hover": {
                        "& .MuiOutlinedInput-notchedOutline": {
                           borderColor: hasError
                              ? "error.main"
                              : theme.palette.primary.main,
                           borderWidth: 1,
                        },
                     },
                     "&.Mui-focused": {
                        "& .MuiOutlinedInput-notchedOutline": {
                           borderColor: theme.palette.primary.main,
                           borderWidth: 2,
                           boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
                        },
                     },
                  },
                  "& .MuiInputBase-input": {
                     py: 0.75,
                     px: 1.25,
                     "&::placeholder": {
                        color: theme.palette.text.secondary,
                        opacity: 1,
                     },
                  },
               }}
               InputProps={{
                  endAdornment: (
                     <InputAdornment
                        position="end"
                        sx={{ position: "absolute", right: 8 }}
                     >
                        <Stack direction="row" spacing={0.5}>
                           {value && (
                              <IconButton
                                 size="small"
                                 onClick={(e) => {
                                    e.stopPropagation();
                                    onChange("");
                                    setSearchValue("");
                                 }}
                                 sx={{
                                    p: 0.25,
                                    mr: 0.5,
                                    color: "#6b7280",
                                    border: "none",
                                    "&:hover": {
                                       bgcolor: "rgba(107, 114, 128, 0.1)",
                                       color: "#374151",
                                    },
                                    "&:focus": {
                                       outline: "none",
                                       bgcolor: "rgba(107, 114, 128, 0.1)",
                                    },
                                    transition: "all 0.2s ease",
                                 }}
                              >
                                 <X size={14} />
                              </IconButton>
                           )}
                           <IconButton
                              size="small"
                              onClick={() => setIsOpen(!isOpen)}
                              sx={{
                                 p: 0.5,
                                 border: "none",
                                 "&:hover": {
                                    bgcolor: "transparent",
                                 },
                              }}
                           >
                              <ChevronDown size={16} />
                           </IconButton>
                        </Stack>
                     </InputAdornment>
                  ),
               }}
            />
            <Popper
               open={isOpen && filteredOptions.length > 0}
               anchorEl={anchorRef.current}
               placement="bottom-start"
               style={{ width: anchorRef.current?.offsetWidth, zIndex: 1300 }}
            >
               <Paper
                  elevation={0}
                  sx={{
                     mt: 1,
                     maxHeight: 240,
                     overflow: "auto",
                     border: `1px solid ${theme.palette.divider}`,
                     boxShadow:
                        "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
                     borderRadius: 1,
                  }}
               >
                  <List dense sx={{ py: 0 }}>
                     {/* Create new field option */}
                     {allowCreate && searchValue && filteredOptions.length === 0 && (
                        <ListItem disablePadding>
                           <ListItemButton
                              onClick={() => {
                                 setNewFieldData({ ...newFieldData, name: searchValue });
                                 setShowCreateDialog(true);
                                 setIsOpen(false);
                              }}
                              sx={{
                                 py: 1,
                                 px: 1.5,
                                 borderBottom: `1px solid ${theme.palette.divider}`,
                                 bgcolor: alpha(theme.palette.primary.main, 0.04),
                                 "&:hover": {
                                    bgcolor: alpha(theme.palette.primary.main, 0.08),
                                 },
                              }}
                           >
                              <Stack
                                 direction="row"
                                 spacing={1.5}
                                 alignItems="center"
                                 sx={{ width: "100%" }}
                              >
                                 <Box
                                    sx={{
                                       display: "flex",
                                       alignItems: "center",
                                       justifyContent: "center",
                                       width: 24,
                                       height: 24,
                                       borderRadius: 0.5,
                                       bgcolor: alpha(theme.palette.info.main, 0.1),
                                    }}
                                 >
                                    <Plus size={14} color={theme.palette.info.main} />
                                 </Box>
                                 <Box sx={{ flex: 1 }}>
                                    <Typography
                                       variant="body2"
                                       sx={{
                                          fontSize: "0.875rem",
                                          fontWeight: 600,
                                          color: theme.palette.primary.main,
                                       }}
                                    >
                                       Create "{searchValue}"
                                    </Typography>
                                    <Typography
                                       variant="caption"
                                       sx={{ fontSize: "0.75rem", color: "text.secondary" }}
                                    >
                                       Add as a new field
                                    </Typography>
                                 </Box>
                              </Stack>
                           </ListItemButton>
                        </ListItem>
                     )}

                     {/* Always show create field option when dropdown is open */}
                     {allowCreate && filteredOptions.length > 0 && (
                        <>
                           <ListItem
                              disablePadding
                              sx={{
                                 borderBottom: `1px solid ${theme.palette.divider}`,
                                 mb: 0.5,
                              }}
                           >
                              <ListItemButton
                                 onClick={() => {
                                    setNewFieldData({ ...newFieldData, name: searchValue });
                                    setShowCreateDialog(true);
                                    setIsOpen(false);
                                 }}
                                 sx={{
                                    py: 0.75,
                                    px: 1.5,
                                    bgcolor: alpha(theme.palette.primary.main, 0.02),
                                    "&:hover": {
                                       bgcolor: alpha(theme.palette.primary.main, 0.08),
                                    },
                                 }}
                              >
                                 <Stack
                                    direction="row"
                                    spacing={1.5}
                                    alignItems="center"
                                    sx={{ width: "100%" }}
                                 >
                                    <Box
                                       sx={{
                                          display: "flex",
                                          alignItems: "center",
                                          justifyContent: "center",
                                          width: 24,
                                          height: 24,
                                          borderRadius: 0.5,
                                          border: `1px dashed ${theme.palette.primary.main}`,
                                       }}
                                    >
                                       <Plus size={14} color={theme.palette.info.main} />
                                    </Box>
                                    <Typography
                                       variant="body2"
                                       sx={{
                                          fontSize: "0.875rem",
                                          color: theme.palette.primary.main,
                                          fontWeight: 500,
                                       }}
                                    >
                                       Create new field
                                    </Typography>
                                 </Stack>
                              </ListItemButton>
                           </ListItem>
                        </>
                     )}

                     {filteredOptions.map((option) => (
                        <ListItem key={option.value} disablePadding>
                           <ListItemButton
                              onClick={() => handleSelect(option.value)}
                              sx={{
                                 py: 0.75,
                                 px: 1.5,
                                 "&:hover": {
                                    bgcolor: alpha(theme.palette.primary.main, 0.08),
                                 },
                              }}
                           >
                              <Stack
                                 direction="row"
                                 spacing={1.5}
                                 alignItems="center"
                                 sx={{ width: "100%" }}
                              >
                                 <Box
                                    sx={{
                                       display: "flex",
                                       alignItems: "center",
                                       justifyContent: "center",
                                       width: 24,
                                       height: 24,
                                       borderRadius: 0.5,
                                       bgcolor:
                                          option.type === "string"
                                             ? alpha(theme.palette.success.main, 0.1)
                                             : option.type === "number"
                                                ? alpha(theme.palette.info.main, 0.1)
                                                : option.type === "boolean"
                                                   ? alpha(theme.palette.warning.main, 0.1)
                                                   : option.type === "date"
                                                      ? alpha(theme.palette.secondary.main, 0.1)
                                                      : option.type === "array"
                                                         ? alpha(theme.palette.error.light, 0.1)
                                                         : option.type === "object"
                                                            ? alpha(theme.palette.primary.dark, 0.1)
                                                            : alpha(
                                                               theme.palette.text.secondary,
                                                               0.1
                                                            ),
                                    }}
                                 >
                                    {option.type === "string" ? (
                                       <FileText
                                          size={14}
                                          color={theme.palette.success.main}
                                       />
                                    ) : option.type === "number" ? (
                                       <Hash size={14} color={theme.palette.info.main} />
                                    ) : option.type === "boolean" ? (
                                       <ToggleLeft
                                          size={14}
                                          color={theme.palette.warning.main}
                                       />
                                    ) : option.type === "date" ? (
                                       <Calendar
                                          size={14}
                                          color={theme.palette.secondary.main}
                                       />
                                    ) : option.type === "array" ? (
                                       <ListIcon
                                          size={14}
                                          color={theme.palette.error.light}
                                       />
                                    ) : option.type === "object" ? (
                                       <Braces
                                          size={14}
                                          color={theme.palette.primary.dark}
                                       />
                                    ) : (
                                       <FileText
                                          size={14}
                                          color={theme.palette.text.secondary}
                                       />
                                    )}
                                 </Box>
                                 <Box sx={{ flex: 1 }}>
                                    <Typography
                                       variant="body2"
                                       sx={{ fontSize: "0.875rem", fontWeight: 500 }}
                                    >
                                       {option.label}
                                    </Typography>
                                    <Typography
                                       variant="caption"
                                       sx={{ fontSize: "0.75rem", color: "text.secondary" }}
                                    >
                                       {option.type}
                                    </Typography>
                                 </Box>
                              </Stack>
                           </ListItemButton>
                        </ListItem>
                     ))}
                  </List>
               </Paper>
            </Popper>

            {/* Quick Field Creation Dialog */}
            <Dialog
               open={showCreateDialog}
               onClose={() => setShowCreateDialog(false)}
               maxWidth="xs"
               fullWidth
            >
               <DialogTitle>
                  <Stack direction="row" alignItems="center" spacing={1}>
                     <Box
                        sx={{
                           width: 32,
                           height: 32,
                           borderRadius: 1,
                           bgcolor: alpha(theme.palette.info.main, 0.1),
                           display: "flex",
                           alignItems: "center",
                           justifyContent: "center",
                        }}
                     >
                        <Plus size={18} color={theme.palette.info.main} />
                     </Box>
                     <Typography variant="h6">Create New Field</Typography>
                  </Stack>
               </DialogTitle>
               <DialogContent>
                  <Stack spacing={3} sx={{ mt: 1 }}>
                     <TextField
                        label="Field Name"
                        value={newFieldData.name}
                        onChange={(e) =>
                           setNewFieldData({ ...newFieldData, name: e.target.value })
                        }
                        fullWidth
                        size="small"
                        helperText="This will be the field identifier"
                     />

                     <FormControl size="small" fullWidth>
                        <InputLabel>Field Type</InputLabel>
                        <Select
                           value={newFieldData.type}
                           onChange={(e) =>
                              setNewFieldData({ ...newFieldData, type: e.target.value })
                           }
                           label="Field Type"
                        >
                           <MenuItem value="string">
                              <Stack direction="row" spacing={1} alignItems="center">
                                 <FileText size={14} color={theme.palette.success.main} />
                                 <span>String</span>
                              </Stack>
                           </MenuItem>
                           <MenuItem value="number">
                              <Stack direction="row" spacing={1} alignItems="center">
                                 <Hash size={14} color={theme.palette.info.main} />
                                 <span>Number</span>
                              </Stack>
                           </MenuItem>
                           <MenuItem value="boolean">
                              <Stack direction="row" spacing={1} alignItems="center">
                                 <ToggleLeft
                                    size={14}
                                    color={theme.palette.warning.main}
                                 />
                                 <span>Boolean</span>
                              </Stack>
                           </MenuItem>
                           <MenuItem value="date">
                              <Stack direction="row" spacing={1} alignItems="center">
                                 <Calendar
                                    size={14}
                                    color={theme.palette.secondary.main}
                                 />
                                 <span>Date</span>
                              </Stack>
                           </MenuItem>
                           <MenuItem value="object">
                              <Stack direction="row" spacing={1} alignItems="center">
                                 <Braces size={14} color={theme.palette.primary.dark} />
                                 <span>Object</span>
                              </Stack>
                           </MenuItem>
                        </Select>
                     </FormControl>

                     <TextField
                        label="Description (Optional)"
                        value={newFieldData.description}
                        onChange={(e) =>
                           setNewFieldData({
                              ...newFieldData,
                              description: e.target.value,
                           })
                        }
                        fullWidth
                        size="small"
                        multiline
                        rows={2}
                     />
                  </Stack>
               </DialogContent>
               <DialogActions sx={{ px: 3, pb: 2 }}>
                  <Button onClick={() => setShowCreateDialog(false)} color="inherit">
                     Cancel
                  </Button>
                  <Button
                     onClick={() => {
                        // Here you would typically make an API call to create the field
                        console.log("Creating field:", newFieldData);
                        if (onCreateField) {
                           onCreateField(newFieldData.name);
                        }
                        onChange(newFieldData.name);
                        setShowCreateDialog(false);
                        setNewFieldData({ name: "", type: "string", description: "" });
                     }}
                     variant="contained"
                     startIcon={<Plus size={16} />}
                     disabled={!newFieldData.name.trim()}
                  >
                     Create Field
                  </Button>
               </DialogActions>
            </Dialog>
         </Box>
      </ClickAwayListener>
   );
};

export default AutocompleteField;