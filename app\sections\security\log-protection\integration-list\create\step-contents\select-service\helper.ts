import { Service } from "types/service";


export const generateIntegrationValue = (integrations: Record<string, any>[], service: Service) => {
   const integrationForService = integrations?.find((i: Record<string, any>) => i?.service?.id === service?.id);
   return integrationForService
}

export const getNestedValue = (path: string, obj: Record<string, any> | null) => {
   const keys = path.replace(/^\//, "").split(/\/|\\/); // Split path by '/' or '\'
   return keys.reduce((acc: any, key: any) => (acc && acc?.[key] ? acc?.[key] : undefined), obj);
};