// material-ui
import { useTheme, alpha } from '@mui/material/styles';
import Button from '@mui/material/Button';
import Link from '@mui/material/Link';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import AutoStoriesOutlinedIcon from '@mui/icons-material/AutoStoriesOutlined';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';

// project import
import MainCard from 'components/MainCard';
import AnimateButton from 'components/@extended/AnimateButton';
import { UNZ_DOCS_PAGE } from 'config';

// ==============================|| DRAWER CONTENT - NAVIGATION CARD ||============================== //

export default function NavCard() {
  const theme = useTheme();

  return (
    <MainCard
      sx={{
        background:
          theme.palette.mode === "dark"
            ? `linear-gradient(135deg, ${alpha(
                theme.palette.primary.dark,
                0.2
              )} 0%, ${alpha(theme.palette.primary.main, 0.1)} 100%)`
            : `linear-gradient(135deg, ${theme.palette.primary.lighter} 0%, ${alpha(
                theme.palette.primary.light,
                0.3
              )} 100%)`,
        border: "1px solid",
        borderColor:
          theme.palette.mode === "dark"
            ? alpha(theme.palette.primary.main, 0.2)
            : alpha(theme.palette.primary.main, 0.1),
        mx: 2,
        my: 2,
        p: 2,
        position: "relative",
        overflow: "hidden",
        "&::before": {
          content: '""',
          position: "absolute",
          top: -20,
          right: -20,
          width: 80,
          height: 80,
          borderRadius: "50%",
          background: alpha(theme.palette.primary.main, 0.05),
          pointerEvents: "none",
        },
      }}
    >
      <Stack
        spacing={2}
        sx={{ position: "relative", zIndex: 1 }}
        alignItems="center"
        justifyContent="center"
      >
        {/* ICON + TEXT STACK */}
        <Stack direction="row" spacing={1} alignItems="center" >
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 1,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              background:
                theme.palette.mode === "dark"
                  ? alpha(theme.palette.primary.main, 0.2)
                  : theme.palette.primary.lighter,
              color: theme.palette.primary.main,
              flexShrink: 0,
              boxShadow:
                theme.palette.mode === "dark"
                  ? `0 2px 8px ${alpha(theme.palette.primary.main, 0.15)}`
                  : `0 2px 8px ${alpha(theme.palette.primary.main, 0.08)}`,
            }}
          >
            <AutoStoriesOutlinedIcon sx={{ fontSize: 20 }} />
          </Box>

          <Stack spacing={0.25} sx={{ textAlign: "center" }}>
            <Typography
              variant="body2"
              sx={{
                fontWeight: 600,
                color:
                  theme.palette.mode === "dark"
                    ? "text.primary"
                    : theme.palette.primary.darker,
                lineHeight: 1.2,
                fontSize: "0.875rem",
              }}
            >
              Documentation
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: "text.secondary",
                lineHeight: 1.2,
                fontSize: "0.7rem",
              }}
            >
              Guides & references
            </Typography>
          </Stack>
        </Stack>

        {/* BUTTON */}
        <AnimateButton>
          <Button
            fullWidth
            variant="contained"
            size="small"
            component={Link}
            target="_blank"
            href={UNZ_DOCS_PAGE}
            endIcon={<ArrowForwardIcon sx={{ fontSize: 16 }} />}
            sx={{
              bgcolor: theme.palette.primary.main,
              color: "#fff",
              py: 0.75,
              px: 2,
              fontSize: "0.8125rem",
              fontWeight: 500,
              borderRadius: 1,
              textTransform: "none",
              boxShadow: "none",
              "&:hover": {
                bgcolor: theme.palette.primary.dark,
                boxShadow: `0 2px 8px ${alpha(
                  theme.palette.primary.main,
                  0.2
                )}`,
              },
            }}
          >
            View Docs
          </Button>
        </AnimateButton>
      </Stack>
    </MainCard>
  );
}

