import z from "zod";
import _ from "lodash";
import jsonpath from "jsonpath";

import { FormFieldTypeEnum, ValidationType } from "hooks/useServiceProfile";
import { replaceSlashWithDot } from "lib/json-path";
import { AccessPointConfigType } from "hooks/api/use-accesspoint-config/useGetAccessPoint";

const fieldValidator = {
  [ValidationType.Regex]: {
    validate: validateRegexp,
  },
};

function validateRegexp(value, { errorMessage, value: exp }) {
  const regexp = new RegExp(exp);
  return !regexp.test(value) ? errorMessage : "";
}

const parseZODRule = ({ required = false, label, validations }) => {
  return z["string"]()
    [required ? "nonempty" : "optional"](required ? `${label} is required` : "")
    .superRefine((val, ctx) => {
      validations?.forEach(async (validation) => {
        const isError = fieldValidator?.[validation?.type]?.validate(
          val,
          validation
        );

        isError
          ? ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: isError,
            })
          : null;
      });
    });
};

export const getAccessPointTypeContainer = (accessPoint) => {
  switch (accessPoint?.type) {
    case AccessPointConfigType.AppFlow:
      // Return the appConfig configuration if the flow type is AppFlow
      return accessPoint?.appConfig;
    case AccessPointConfigType.OAuthFlow:
      // Return the appConfig configuration if the flow type is AppFlow
      return accessPoint?.oAuthConfig;
    default:
      // Warn if no container is found for the given flow type
      console.warn(`No Container found for ${accessPoint?.type}`);
      return null;
  }
};

export function generateConfigFormSchema(segments, accessPoint) {
  return {
    defaultValues: segments?.reduce((acc, cur) => {
      const key = cur?.key,
        fieldTypeConfigs = cur?.fieldTypeConfigs,
        tempObj = acc;

      fieldTypeConfigs?.forEach((config) => {
        const convertedPath = "$." + replaceSlashWithDot(config?.property);
        let value = jsonpath.query(accessPoint ?? {}, convertedPath)?.[0] ?? "";

        tempObj[key] = {
          ...tempObj[key],
          [config?.property]: value,
        };
      });
      return tempObj;
    }, {}),

    buildValidationSchema() {
      const schema = {},
        filterSchema = {};

      segments.forEach(({ key, fieldTypeConfigs }) => {
        schema[key] = z.object(
          fieldTypeConfigs.reduce((acc, cur) => {
            acc[cur.property] = parseZODRule(cur);
            return acc;
          }, {})
        );
      });

      // filter the file fields
      segments.forEach(({ key, fieldTypeConfigs }) => {
        const excluded = {};
        filterSchema[key] = z
          .object(
            fieldTypeConfigs.reduce((acc, cur) => {
              acc[cur.property] = parseZODRule(cur);
              if (FormFieldTypeEnum.File === cur.type)
                excluded[cur.property] = true;
              return acc;
            }, {})
          )
          .omit(excluded);
      });

      return {
        all: z.object(schema),
        filtered: z.object(filterSchema),
      };
    },

    parsePayload: (data) => {
      const mergedObject = _.merge({}, ..._.values(data));

      return Object.entries(mergedObject).map(([path, value]) => {
        return {
          value,
          path,
          op: "replace",
        };
      });
    },
  };
}
