import { UndefinedInitialDataOptions, UseMutationOptions } from "@tanstack/react-query";
import { State } from "hooks/useStatus";
import { podClient } from "services/enviroinment";
import { ResponseModel } from "types/common";
import Pod from "types/environment";

const LIMIT = 30;

const useEnvironment = () => {

   return {
      getAllEnvironmentQueryConfig: (): UndefinedInitialDataOptions<ResponseModel<Pod.Root>> => {
         return {
            queryKey: ['pods', window.authUserOrgId],
            queryFn: async () => {
               return (await podClient.getAllEnviroinment()).data
            },
            enabled: !!window.authUserOrgId && !!window.authUserId
         }
      },
      getAllEnvironmentInfinateQueryConfig: () => {
         return {
            queryKey: ['pods', 'infinate',window.authUserOrgId],
            queryFn: async ({ pageParam }: any) => {
               const response = await podClient.getAllEnviroinment({ params: { limit: LIMIT, offset: pageParam, state: State.ACTIVE } });
               const { total } = response?.data?.pagination;
               const hasMore = (((pageParam as number) + 1) * LIMIT) < total;
               return { ...response, hasMore };
            },
            initialPageParam: 0,
            getNextPageParam: (lastPage: any) => {
               if (!lastPage?.hasMore) {
                  return;
               }
               const { offset } = lastPage?.data?.pagination;
               return offset + 1;
            },
            enabled: !!window.authUserOrgId && !!window.authUserId
         }
      },
      getEnvironmentByIdQueryConfig: (id: string): UndefinedInitialDataOptions<{}> => {
         return {
            queryKey: ['pods', id],
            queryFn: async () => {
               return await podClient.getEnviroinmentById(id)
            },
            enabled: !!id
         }
      },
      createEnvironmentQueryConfig: (): UseMutationOptions<{}, unknown, { payload: Record<string, any> }> => {
         return {
            mutationFn: async ({ payload }) => {
               return await podClient.createEnviroinment(payload)
            },
         }
      },
      patchEnvironmentQueryConfig: (): UseMutationOptions<{}, unknown, { environmentId: string, payload: Record<string, any> }> => {
         return {
            mutationFn: async ({ environmentId, payload }) => {
               return await podClient.updatEnviroinment(environmentId, payload)
            },
         }
      },
      actionEnvironmentQueryConfig: (): UseMutationOptions<{}, unknown, { environmentId: string, payload: Record<string, any> }> => {
         return {
            mutationFn: async ({ environmentId, payload }) => {
               return await podClient.actionEnviroinment(environmentId, payload)
            },
         }
      },
      deleteEnvironmentQueryConfig: (): UseMutationOptions<{}, unknown, { environmentId: string}> => {
         return {
            mutationFn: async ({ environmentId }) => {
               return await podClient.deleteEnviroinment(environmentId)
            },
         }
      },
   }
};

export default useEnvironment;