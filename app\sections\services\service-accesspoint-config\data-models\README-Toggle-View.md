# Field Mappings Toggle View 🔄

## Overview

The Field Mappings interface now features a toggle between **Table View** and **Visual View**, combining the power of both approaches in a single, integrated experience. The Visual Mapping tab has been removed in favor of this more streamlined approach.

## 🎯 Key Changes

### Before:
- 3 separate tabs: Field Mappings, Visual Mapping, Additional Fields
- Users had to switch tabs to access different mapping modes
- Separate contexts for table and visual mappings

### After:
- 2 tabs: Field Mappings (with toggle), Additional Fields
- Seamless switching between Table and Visual views
- Unified mapping experience with shared data

## 📊 Table View

### Features:
- **Hierarchical dropdown mappings** with source → target selection
- **Nested object support** with expandable parent-child relationships
- **Array mapping options** ([item], [0], [1], [*])
- **Type validation** ensures compatible field mappings
- **Visual indicators** for field types and array structures

### Best For:
- Precise field-by-field mapping
- Complex nested structures
- Array-specific mappings
- Detailed configuration

### UI Elements:
```
Source Field → Target Field [X]
├─ Nested Field → Target Nested Field [X]
└─ Array[0] → Specific Target [X]
```

## 🎨 Visual View

### Features:
- **Drag-and-drop connections** between source and target fields
- **Node-based visualization** with React<PERSON>low
- **Color-coded field types** for quick identification
- **Interactive canvas** with zoom, pan, and minimap
- **Real-time validation** of connections

### Best For:
- Quick visual understanding of mappings
- Bulk mapping operations
- Seeing overall data flow
- Presentations and documentation

### UI Elements:
```
[Source Node] ──────→ [Target Node]
     ↓                      ↓
[Field Type]           [Field Type]
```

## 🔀 Toggle Button

Located in the top-right of the Field Mappings header:

```
Field Mappings                    [📊 Table View] [🔗 Visual View]
```

### Icons:
- **📊 Table View** (Table2 icon) - Structured dropdown interface
- **🔗 Visual View** (GitBranch icon) - Node-based flow diagram

## 💾 Data Synchronization

### Seamless Data Transfer:
1. **Table → Visual**: All mappings appear as connected nodes
2. **Visual → Table**: Connections convert to dropdown selections
3. **Real-time sync**: Changes in one view immediately reflect in the other
4. **Validation**: Both views enforce the same type rules

### Mapping Format:
```typescript
{
  id: string,
  sourceFieldPath: string,
  targetFieldPath: string,
  sourceType: string,
  targetType: string,
  level: number // for nested mappings in table view
}
```

## 🚀 Usage Scenarios

### Scenario 1: Initial Bulk Mapping
1. Start in **Visual View** for quick overview
2. Drag connections between obvious field pairs
3. Switch to **Table View** for fine-tuning
4. Configure nested objects and arrays precisely

### Scenario 2: Complex Nested Structures
1. Use **Table View** to set up parent objects
2. Expand objects to map nested fields
3. Switch to **Visual View** to verify overall structure
4. Return to **Table View** for array configurations

### Scenario 3: Documentation & Review
1. Configure mappings in **Table View**
2. Switch to **Visual View** for screenshots
3. Use visual diagram in documentation
4. Share visual representation with stakeholders

## 🎨 UI/UX Benefits

### 1. **Reduced Complexity**
- Fewer tabs = simpler navigation
- Related features grouped together
- Less context switching

### 2. **Enhanced Flexibility**
- Choose the right tool for the task
- Switch views without losing work
- Accommodate different user preferences

### 3. **Improved Workflow**
- Start with visual overview
- Drill down with table details
- Validate with visual confirmation

### 4. **Better Space Utilization**
- Full height for mapping interface
- No tab switching overhead
- More room for field lists

## 🔧 Technical Implementation

### State Management:
```typescript
const [viewMode, setViewMode] = useState<'table' | 'visual'>('table');
const [mappings, setMappings] = useState<FieldMapping[]>([]);
```

### View Toggle:
```typescript
<ToggleButtonGroup
  value={viewMode}
  exclusive
  onChange={(_, value) => value && setViewMode(value)}
>
  <ToggleButton value="table">Table View</ToggleButton>
  <ToggleButton value="visual">Visual View</ToggleButton>
</ToggleButtonGroup>
```

### Conditional Rendering:
```typescript
{viewMode === 'table' ? (
  <TableMappingView mappings={mappings} />
) : (
  <VisualMappingView 
    mappings={mappings}
    onMappingsChange={setMappings}
  />
)}
```

## 📝 Migration Notes

### For Existing Users:
1. **Visual Mapping tab removed** - Access via toggle in Field Mappings
2. **All features preserved** - No functionality lost
3. **Data compatible** - Existing mappings work in both views
4. **Improved performance** - Single component, less overhead

### For Developers:
1. **VisualFieldMapping** now embedded, not standalone
2. **Shared state** between views via parent component
3. **Simplified routing** - No separate visual mapping route
4. **Unified callbacks** for mapping changes

## 🎯 Best Practices

### 1. **Start Visual, Finish Table**
- Use Visual View for initial connections
- Switch to Table View for detailed configuration
- Validate in Visual View before saving

### 2. **Use Table for Complex Types**
- Arrays with specific indices
- Deeply nested objects
- Conditional mappings

### 3. **Use Visual for Overview**
- Check mapping completeness
- Identify unmapped fields
- Present to stakeholders

### 4. **Toggle Frequently**
- Don't stick to one view
- Use both for verification
- Leverage strengths of each

The new toggle view design provides the best of both worlds, making field mapping more intuitive and efficient! 🚀