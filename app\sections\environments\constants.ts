const Environment_set = {
  "plans": {
    "FREE": {
      "environments": [
        {
          "type": "TEST",
          "name": "Test",
          "description": "Create a secure production environment for live applications",
          "key": "TEST",
          "colorPicker": "#10B981",
          "icon": "secure",
          "locked": false
        },
        {
          "type": "TEST",
          "name": "Environment Creation Disabled",
          "description": "Your subscription doesn't support creating environments. Upgrade your plan to unlock this feature.",
          "key": "TEST",
          "colorPicker": "#BBB8B5",
          "icon": "secure",
          "locked": true,
          "lockIcon": true
        }
      ]
    },
    "LAUNCH": {
      "environments": [
        {
          "type": "LIVE",
          "name": "Production",
          "description": "Create a secure production environment for live applications",
          "key": "PROD",
          "colorPicker": "#10B981",
          "icon": "secure",
          "locked": false
        },
        {
          "type": "TEST",
          "name": "Test",
          "description": "Test changes in a controlled Test environment before going live",
          "key": "TEST",
          "colorPicker": "#3B82F6",
          "icon": "test-tube",
          "locked": false
        }
      ]
    },
    "ENTERPRISE": {
      "environments": [
        {
          "type": "LIVE",
          "name": "Production",
          "description": "Create a secure production environment for live applications",
          "key": "PROD",
          "colorPicker": "#10B981",
          "icon": "secure",
          "locked": false
        },
        {
          "type": "TEST",
          "name": "Test",
          "description": "Test changes in a controlled Test environment before going live",
          "key": "TEST",
          "colorPicker": "#3B82F6",
          "icon": "test-tube",
          "locked": false
        },
        {
          "type": "TEST",
          "name": "Development",
          "description": "Build and experiment in a flexible development space",
          "key": "DEV",
          "colorPicker": "#F59E0B",
          "icon": "code",
          "locked": false
        },
        {
          "type": "TEST",
          "name": "Custom",
          "description": "Configure a specialized environment for your unique needs",
          "key": "CUSTOM",
          "colorPicker": "#8B5CF6",
          "icon": "puzzle",
          "locked": false
        },
         {
          "type": "TEST",
          "name": "Staging",
          "description": "Configure a specialized environment for your unique needs",
          "key": "STAGING",
          "colorPicker": "#8B5CF6",
          "icon": "puzzle",
          "locked": false
        }
      ]
    }
  }
}

export default Environment_set;