/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, useMemo } from 'react';
import { Box, Stack, Skeleton,Typography } from '@mui/material';

import { useGetSuperAdmin } from "hooks/api/super-admin/useGetSuperAdmin";
import useSelfRegistration from 'store/self-signUp/self-signup';


import { serviceProfileClient } from '../../../services/service-profile.service';
import { State } from 'hooks/useStatus';
import Content from './content';
import Header from '../header';
import { StepComponentProps } from '../component-mapper';

const CategorySelection = ({ currentStep}: StepComponentProps) => {

    const { searchProducts } = useGetSuperAdmin();
    const { selectedCategories, currentCategory, setCurrentCategory } = useSelfRegistration();
    const { data: products = [], isPending } = searchProducts();

    const [profiles, setProfiles] = useState([]);
    const [isProfileLoading, setIsProfileLoading] = useState(false);
    const [, setSelectedIndex] = useState<number | null>(null);

    const menu = useMemo(() => {
        return (
            products
                ?.filter((product: any) => selectedCategories.includes(product.code))
                ?.map((product: any) => ({
                    id: product.code,
                    title: product.name,
                    disabled: false,
                    released: true
                })) || []
        );
    }, [products, selectedCategories]);

    const fetchProfiles = async (selectedDomain: string) => {
        setIsProfileLoading(true);
        try {
            const response = await serviceProfileClient.searchProfiles({
                filter: {
                    and: [
                        {
                            property: "/state",
                            operator: "=",
                            values: [State.ACTIVE]
                        },
                        {
                            property: "/type",
                            operator: "=",
                            values: [selectedDomain]
                        }
                    ]
                },
                pagination: {
                    limit: 33,
                    offset: 0
                }
            });
            setProfiles(response?.data?.data || []);
        } catch (error) {
            console.error('Error fetching profiles:', error);
            setProfiles([]);
        } finally {
            setIsProfileLoading(false);
            setSelectedIndex(null);
        }
    };

    useEffect(() => {
        if (menu.length > 0 && !currentCategory) {
            const initialDomain = menu[0].id;
            setCurrentCategory(initialDomain);
        }
    }, [menu]);

    useEffect(() => {
        if (currentCategory) {
            fetchProfiles(currentCategory);
        }
    }, [currentCategory]);

    return (
        <Stack spacing={4} alignItems="center" sx={{ width: '100%', pt: 2 }} marginTop='5rem'>
            {isPending ? (
                <Stack spacing={2} alignItems="center" sx={{ width: '100%', }}>
                    <Skeleton variant="text" width="60%" height={40} />
                    <Skeleton variant="text" width="40%" height={24} />
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, justifyContent: 'center', width: '100%', mt: 2 }}>
                        {[...Array(6)].map((_, idx) => (
                            <Skeleton key={idx} variant="rounded" width={100} height={36} sx={{ borderRadius: '16px' }} />
                        ))}
                    </Box>
                    <Skeleton variant="text" width="30%" height={18} sx={{ mt: 2 }} />
                </Stack>
            ) :
                (
                    <>
                       <Header subTitle={currentStep?.subTitle} title={currentStep?.title} />
                        <Content
                            {...{
                                showLeftNav: selectedCategories?.length > 1,
                                categories: menu,
                                profiles,
                                onSelectCategory: setCurrentCategory,
                                isProfileLoading,
                                currentStep
                            }}
                        />
                         <Typography
                        variant="body1"
                        align="center"
                        color="text.secondary"
                    >
                        {currentStep?.assistanceInfo}
                    </Typography>
                    </>
                )}
        </Stack>
    );
};

export default CategorySelection;