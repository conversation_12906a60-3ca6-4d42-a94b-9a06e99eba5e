import { useState, useEffect, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Stack,
  IconButton,
  <PERSON>per,
  Step,
  StepLabel,
  Grid,
  Paper,
  Chip,
  useTheme,
  alpha,
  Alert,
  AlertTitle
} from '@mui/material';
import { X, AlertTriangle } from 'lucide-react';

import useUserDetails from 'store/user';
import LearnMoreLink from 'components/@extended/LearnMoreLink';
import { DOMAINS } from 'data/domains';
import CategoryFieldManager from 'sections/services/category-fields';
import ServiceFieldMappingStep from './ServiceFieldMappingStep';
import FieldMappingErrorBoundary from './FieldMappingErrorBoundary';

interface FieldMappingDrawerProps {
  open: boolean;
  onClose: () => void;
  onSave?: (_data: any) => void;
  initialCategory?: string | null;
  initialModel?: string | null;
}

const FieldMappingDrawer = ({ open, onClose, onSave, initialCategory, initialModel }: FieldMappingDrawerProps) => {
  const theme = useTheme();
  const { subscriptions } = useUserDetails();
  const [activeStep, setActiveStep] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  // Get subscribed categories from user subscriptions (reusing logic from main page)
  const subscribedCategories = useMemo(() => {
    if (!subscriptions || subscriptions.length === 0) return [];
    
    const categories = new Set<string>();
    
    subscriptions.forEach(sub => {
      const productCode = sub.product?.code;
      
      // Find the domain that matches this subscription
      const matchingDomain = DOMAINS.find(domain => {
        if (domain.subscription?.code === productCode) return true;
        if (domain.key === productCode?.toUpperCase()) return true;
        if (productCode?.includes('SyntacticBasedEvent') && domain.key === 'SCM') return true;
        if (sub.type === domain.key) return true;
        return false;
      });
      
      if (matchingDomain) {
        categories.add(matchingDomain.key);
      }
    });
    
    return Array.from(categories);
  }, [subscriptions]);

  // Get only subscribed domains that are available for field mappings
  const availableDomains = DOMAINS.filter(domain => 
    subscribedCategories.includes(domain.key) &&
    domain.visibility.showInFeatures.includes('integrations-list')
  );

  // Reset state when drawer opens/closes
  useEffect(() => {
    if (!open) {
      // Only reset when drawer is closing
      setActiveStep(0);
      setSelectedCategory(null);
      setIsSaving(false);
    } else if (initialCategory && selectedCategory === null) {
      // If initialCategory is provided and no category is selected yet, skip to step 2
      setSelectedCategory(initialCategory);
      setActiveStep(1);
    }
  }, [open, initialCategory]);

  // Check if SCM and Ticketing are available
  const hasScmAndTicketing = availableDomains.some(domain => 
    ['SCM', 'TICKETING'].includes(domain.key)
  );

  // Check if all categories are coming soon
  const allCategoriesComingSoon = availableDomains.length > 0 && 
    availableDomains.every(domain => !['SCM', 'TICKETING'].includes(domain.key));

  const handleCategorySelect = (categoryKey: string) => {
    setSelectedCategory(categoryKey);
    setActiveStep(1);
  };

  const handleBack = () => {
    if (activeStep === 2) {
      setActiveStep(1);
    } else if (activeStep === 1) {
      setActiveStep(0);
      // Keep the selected category so it appears pre-selected
      // Don't clear it here
    }
  };

  const handleNext = () => {
    if (activeStep < 2) {
      setActiveStep(activeStep + 1);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Since CategoryFieldManager handles its own saving,
      // we just close the drawer here
      onClose();
    } catch (error) {
      console.error('Error closing drawer:', error);
    } finally {
      setIsSaving(false);
    }
  };


  const steps = ['Select Category', 'Add Custom Field', 'Map to Connectors'];

  // Get the selected category details
  const selectedCategoryData = availableDomains.find(domain => domain.key === selectedCategory);
  
  // Debug logging
  console.log('FieldMappingDrawer state:', {
    open,
    initialCategory,
    selectedCategory,
    activeStep,
    selectedCategoryData,
    availableDomains: availableDomains.map(d => d.key)
  });

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: { xs: '100%', sm: '90%', md: '85%', lg: '1200px' },
          maxWidth: '1200px',
        }
      }}
    >
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{ p: 3, borderBottom: `1px solid ${theme.palette.divider}` }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="h5" fontWeight={600}>
              Manage Custom Fields
            </Typography>
            <IconButton onClick={onClose} size="small">
              <X size={20} />
            </IconButton>
          </Stack>
        </Box>

        {/* Stepper */}
        <Box sx={{ px: 3, pt: 3 }}>
          <Stepper activeStep={activeStep}>
            {steps.map((label, index) => (
              <Step key={label} completed={initialCategory && index === 0 ? true : index < activeStep}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        {/* Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          {activeStep === 0 && (
            <>
              {allCategoriesComingSoon ? (
                // Show coming soon message when all categories are coming soon
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: '400px',
                    textAlign: 'center',
                    px: 3,
                  }}
                >
                  <Box
                    sx={{
                      width: 64,
                      height: 64,
                      borderRadius: '50%',
                      backgroundColor: alpha(theme.palette.warning.main, 0.1),
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mb: 3,
                    }}
                  >
                    <AlertTriangle
                      size={32}
                      color={theme.palette.warning.main}
                    />
                  </Box>
                  <Typography variant="h6" fontWeight={600} gutterBottom>
                    Field Mappings Coming Soon
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mb: 4, maxWidth: 400 }}
                  >
                    Field mapping configuration is currently being developed for your subscribed categories. 
                    Please check back later or contact support for more information about availability.
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={onClose}
                    sx={{
                      textTransform: 'none',
                      px: 4,
                    }}
                  >
                    Close
                  </Button>
                </Box>
              ) : (
                // Show normal category selection
                <Stack spacing={3}>
                  <Box>
                    <Typography variant="body1" fontWeight={600} gutterBottom>
                      What type of integrations do you need custom fields for?
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Select a category to configure custom fields. Currently available for Source Code and Ticketing integrations.
                    </Typography>
                  </Box>

                  {!hasScmAndTicketing && (
                    <Alert 
                      severity="warning" 
                      icon={<AlertTriangle size={20} />}
                      sx={{ 
                        borderRadius: 1,
                        '& .MuiAlert-icon': {
                          color: theme.palette.warning.main
                        }
                      }}
                    >
                      <AlertTitle sx={{ fontWeight: 600 }}>
                        Limited Categories Available
                      </AlertTitle>
                      <Typography variant="body2">
                        SCM and Ticketing categories are not available in your current subscription. 
                        Please contact{' '}
                        <LearnMoreLink href="mailto:<EMAIL>">
                          Unizo sales
                        </LearnMoreLink>
                        {' '}to upgrade your plan and access these features.
                      </Typography>
                    </Alert>
                  )}

              <Grid container spacing={2}>
                {availableDomains.map((domain) => {
                  const Icon = domain.icon;
                  const isEnabled = ['SCM', 'TICKETING'].includes(domain.key);
                  const isSelected = selectedCategory === domain.key;
                  
                  return (
                    <Grid item xs={12} sm={6} md={4} key={domain.key}>
                      <Paper
                        elevation={0}
                        onClick={() => isEnabled && handleCategorySelect(domain.key)}
                        sx={{
                          p: 2,
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          cursor: isEnabled ? 'pointer' : 'not-allowed',
                          border: 1.5,
                          borderColor: isSelected
                            ? theme.palette.primary.main
                            : theme.palette.mode === 'dark'
                            ? theme.palette.grey[800]
                            : theme.palette.grey[300],
                          backgroundColor: isSelected
                            ? alpha(theme.palette.primary.main, 0.04)
                            : !isEnabled
                            ? theme.palette.mode === 'dark'
                              ? alpha(theme.palette.grey[900], 0.3)
                              : alpha(theme.palette.grey[100], 0.3)
                            : theme.palette.mode === 'dark'
                            ? theme.palette.background.paper
                            : '#fff',
                          borderRadius: 1,
                          transition: 'all 0.2s ease',
                          position: 'relative',
                          opacity: isEnabled ? 1 : 0.6,
                          '&:hover': isEnabled ? {
                            borderColor: theme.palette.primary.main,
                            backgroundColor: isSelected
                              ? alpha(theme.palette.primary.main, 0.08)
                              : alpha(theme.palette.primary.main, 0.02),
                            boxShadow: theme.shadows[1],
                          } : {},
                        }}
                      >
                        <Stack direction="row" spacing={1.5} alignItems="flex-start" sx={{ height: '100%' }}>
                          <Box
                            sx={{
                              width: 40,
                              height: 40,
                              borderRadius: 1,
                              backgroundColor: isSelected
                                ? alpha(theme.palette.primary.main, 0.1)
                                : !isEnabled
                                ? alpha(theme.palette.grey[500], 0.15)
                                : theme.palette.mode === 'dark'
                                ? alpha(theme.palette.grey[700], 0.3)
                                : alpha(theme.palette.grey[300], 0.2),
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              transition: 'all 0.2s ease',
                              flexShrink: 0,
                            }}
                          >
                            <Icon
                              size={20}
                              color={
                                isSelected
                                  ? theme.palette.primary.main
                                  : !isEnabled
                                  ? theme.palette.text.disabled
                                  : theme.palette.text.secondary
                              }
                              strokeWidth={2}
                            />
                          </Box>
                          <Box sx={{ flex: 1, minWidth: 0 }}>
                            <Typography
                              variant="body2"
                              fontWeight={600}
                              sx={{
                                mb: 0.25,
                                color: !isEnabled 
                                  ? theme.palette.text.disabled 
                                  : theme.palette.text.primary,
                                lineHeight: 1.3,
                              }}
                            >
                              {domain.label}
                            </Typography>
                            <Typography
                              variant="caption"
                              sx={{
                                color: !isEnabled 
                                  ? theme.palette.text.disabled 
                                  : theme.palette.text.secondary,
                                display: 'block',
                                lineHeight: 1.3,
                                fontSize: '0.7rem',
                              }}
                            >
                              {domain.description}
                              {!isEnabled && (
                                <Typography
                                  component="span"
                                  variant="caption"
                                  sx={{
                                    color: theme.palette.warning.main,
                                    fontSize: '0.7rem',
                                    fontWeight: 600,
                                    ml: 0.5,
                                  }}
                                >
                                  · Coming soon
                                </Typography>
                              )}
                            </Typography>
                          </Box>
                        </Stack>
                      </Paper>
                    </Grid>
                  );
                })}
              </Grid>
                </Stack>
              )}
            </>
          )}

          {activeStep === 1 && selectedCategory && selectedCategoryData && (
            <Stack spacing={3}>
              <Box>
                <Typography variant="body1" fontWeight={600} gutterBottom>
                  Configure custom unified fields for {selectedCategoryData.label}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Add custom fields to extend the default data models. These fields will be available across all {selectedCategoryData.label.toLowerCase()} integrations.
                </Typography>
              </Box>
              
              <Box sx={{ 
                height: 'calc(100% - 80px)', // Adjust to account for title/description height
                display: 'flex', 
                flexDirection: 'column',
                mx: -3, // Negative margin to offset parent padding
                overflow: 'hidden'
              }}>
                <CategoryFieldManager
                  category={selectedCategory}
                  categoryLabel={selectedCategoryData.label}
                  initialModel={initialModel}
                />
              </Box>
            </Stack>
          )}

          {activeStep === 2 && selectedCategory && selectedCategoryData && (
            <FieldMappingErrorBoundary>
              <ServiceFieldMappingStep
                category={selectedCategory}
                categoryLabel={selectedCategoryData.label}
                onBack={handleBack}
                onComplete={handleSave}
              />
            </FieldMappingErrorBoundary>
          )}
        </Box>

        {/* Footer */}
        <Box sx={{ 
          p: 3, 
          borderTop: `1px solid ${theme.palette.divider}`,
          backgroundColor: theme.palette.background.default
        }}>
          <Stack direction="row" spacing={2} justifyContent="space-between">
            <Button
              variant="outlined"
              onClick={activeStep === 0 ? onClose : handleBack}
              disabled={isSaving}
            >
              {activeStep === 0 ? 'Cancel' : 'Back'}
            </Button>
            
            {activeStep === 1 && (
              <Button
                variant="contained"
                onClick={handleNext}
              >
                Next
              </Button>
            )}
            
            {activeStep === 2 && (
              <Button
                variant="contained"
                onClick={handleSave}
                disabled={isSaving}
              >
                {isSaving ? 'Closing...' : 'Complete Setup'}
              </Button>
            )}
          </Stack>
        </Box>
      </Box>
    </Drawer>
  );
};

export default FieldMappingDrawer;