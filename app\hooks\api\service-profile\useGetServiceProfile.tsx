import { queryOptions, useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import _ from 'lodash';

import { API_ENDPOINTS } from "utils/api/api-endpoints";
import { useEffect, useState } from "react";
import { ServiceProfile } from "types/service-profile";

import { toast } from "sonner";
import { State } from "hooks/useStatus";
import { parseError } from "lib/utils";
import useInitialSetup from "store/initial-setup";

import { parseFilterPayload, updateServiceProfile, updateToInitialSetupStore } from './helper';
import { serviceProfileClient } from "../../../services/service-profile.service";
import useUserDetails from "store/user";
import { AxiosResponse } from "axios";
import { ServiceProfileSpecificationFieldType } from "constants/service-profile";
import { ServiceProfileDataTypes } from "types/service-profile-datatypes";

type UseGetServiceProfileProps = {
  searchOptions: {
    search: {
      type: string,
      limit: number,
      offset: number,
      search?: string
    },
  }
}

export enum ServiceState {
  Active = 'ACTIVE',
  InActive = 'INACTIVE',
  InProgress = 'IN_PROGRESS',
}
export enum EnrollStateType {
  EnrollRequest = 'ENROLL_REQUEST',
  DeactivateRequest = 'SERVICE_DEACTIVATE_REQUEST',
  ReactivateRequest = 'SERVICE_ACTIVATE_REQUEST',
}
export enum ValidationType {
  Regex = 'regex'
}

export enum ServiceConfigState {
  Configured = 'CONFIGURED',
  ReadyToConfig = 'READY_TO_BE_CONFIGURED',
  Disabled = 'DISABLED',
}

const SERVICE_PROFILE_SANDBOX = "SERVICE_PROFILE_SANDBOX"
const SERVICE_PROFILE_VERSIONS = "SERVICE_PROFILE_VERSIONS"
const UPDATE_SANDBOX = "UPDATE_SANDBOX"


export const useGetServiceProfile = (props?: UseGetServiceProfileProps) => {

  const { user } = useUserDetails();
  const [profiles, setProfiles] = useState<ServiceProfile[]>([]);

  const [enabledServices, setEnabledServices] = useState<ServiceProfile[]>([]),
    { setValues } = useInitialSetup();

  const searchOptions = props?.searchOptions;

  const pagination = {
    limit: searchOptions?.search?.limit, offset: searchOptions?.search?.offset
  }, dependencies = {
    type: searchOptions?.search?.type, search: searchOptions?.search?.search
  };
  const queryClient = useQueryClient();

  const clientConsolidatedQuery = [API_ENDPOINTS.SERVICE_PROFILE, API_ENDPOINTS.SERVICE, dependencies];

  const client = useQuery(
    {
      queryKey: clientConsolidatedQuery,
      queryFn: async () => {

        const criteria = [
          {
            property: "/state",
            operator: "=",
            values: [
              State.ACTIVE,
              State.IN_ACTIVE,
            ]
          }
        ];

        return await (
          Promise.all([
            serviceProfileClient.searchProfiles({
              filter: {
                and: [
                  ...criteria,
                  ...parseFilterPayload(dependencies, 'serviceProfile')
                ]
              },
              pagination
            }),
            serviceProfileClient.searchServices(
              {
                filter: {
                  and: [
                    {
                      property: "/state",
                      operator: "=",
                      values: [
                        State.ACTIVE,
                        State.INACTIVE,
                        State.IN_PROGRESS,
                      ],
                    },
                    {
                      property: "/organization/id",
                      operator: "=",
                      values: [
                        user?.organization?.id
                      ],
                    },
                    ...parseFilterPayload(dependencies, 'service')
                  ]
                },
                pagination
              }
            )
          ])
        )
      },
      select(selectable) {
        const [resp1, resp2] = selectable
        return [resp1?.data?.data, resp2?.data?.data]
      },
      enabled: !!dependencies?.type && !!user?.organization?.id,
      refetchOnWindowFocus: false
    },
  );

  const {
    mutateAsync: createMutation,
  } = useMutation({
    mutationFn: serviceProfileClient.createService,
  })

  const {
    mutateAsync: updateMutation,
  } = useMutation({
    mutationFn: (item: any) => {
      return serviceProfileClient.updateService(item?.service?.id, {
        type: [ServiceState.Active, ServiceState.InProgress].includes(item?.service?.state) ?
          EnrollStateType.DeactivateRequest : EnrollStateType.ReactivateRequest
      })
    },
  })

  const {
    mutateAsync: updateSandboxMutation,
  } = useMutation({
    mutationFn: ({ id, payload }: { id: any; payload: any }) => {
      return serviceProfileClient.updateSandboxConfig(id, payload)
    },
  });

  const {
    mutateAsync: updateVersionsMutation,
  } = useMutation({
    mutationFn: ({ id, payload }: { id: any; payload: any }) => {
      return serviceProfileClient.updateSandboxConfig(id, payload)
    },
  });

  useEffect(() => {
    const [data1 = [], data2 = []]: any = client.data ?? [];
    setProfiles(data1.map((item: any) => (
      { ...item, service: data2?.find((i: any) => i?.serviceProfile?.id === item?.id) }
    )) ?? [])
  }, [client.data])

  useEffect(() => {
    updateToInitialSetupStore(setEnabledServices, profiles)
  }, [profiles]);

  useEffect(() => {
    setValues('services', enabledServices)
  }, [enabledServices]);

  return {
    // create
    createService: (payload: { serviceProfile: { id: string } }, cb: any) => {
      createMutation(payload)
        .then(async ({ data }) => {
          cb && cb(data);
          // Update profiles with the new data
          setProfiles(updateServiceProfile(profiles, data));
        })
        .catch((error: any) => {
          // Handle error without toast
          const errorMessage = parseError(error?.response?.data)?.message;
          console.error(errorMessage); // Log the error or use a custom alert
        });
    },

    updateService: (item: any, cb: any) => {
      // Start the mutation without toast.promise
      updateMutation(item)
        .then(async () => {
          const { data } = await serviceProfileClient.getService(item?.service?.id);

          cb && cb(data);

          setProfiles(updateServiceProfile(profiles, data));
        })
        .catch((error: any) => {
          // Handle error without toast
          const errorMessage = parseError(error?.response?.data)?.message;
          console.error(errorMessage); // You can log the error or show a custom alert
        });
    },

    isEnabledSandboxConfig: ({ id }: Record<string, any>) => {
      return useQuery({
        queryKey: [API_ENDPOINTS.SERVICE_PROFILE, SERVICE_PROFILE_SANDBOX, id],
        queryFn: async () => {
          return await serviceProfileClient.enableUpdateSandboxConfig(id)
        },
        select: (resp) => {
          return resp?.data ?? []
        },
        enabled: !!id
      });
    },

    getAllVersionsServiceProfile: ({ id }: Record<string, any>) => {
      return useQuery({
        queryKey: [API_ENDPOINTS.SERVICE_PROFILE, SERVICE_PROFILE_VERSIONS, id],
        queryFn: async () => {
          return await serviceProfileClient.getVersions(id)
        },
        select: (resp) => {
          return resp?.data ?? []
        },
        enabled: !!id
      })
    },

    isUpdateSandboxConfig: (id: any, payload: any, cb: any) => {
      toast.promise(updateSandboxMutation({ id, payload }), {
        loading: 'updating...',
        success: () => {

          queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.SERVICE, UPDATE_SANDBOX] });

          cb && cb()
          return 'Sandbox configuration updated successfully';
        },
        error: (error: any) => {
          return parseError(error?.response?.data)?.message;
        },
      })
    },
    updateVersions: (id: any, payload: any, cb: any) => {
      toast.promise(
        updateVersionsMutation({ id, payload }), {
        loading: 'updating...',
        success: async ({ data }) => {

          // updating profiles
          setProfiles(
            updateServiceProfile(profiles, data)
          );

          cb && cb(data)
          return 'Selected version updated successfully';
        },
        error: (error: any) => {
          return parseError(error?.response?.data)?.message;
        },
      })
    },
    serviceProfileClient: { ...client, data: profiles },

    serviceProfileSpecificationQueryOptions: ({ id, params }: { id: string, params: object }) => {
      return queryOptions<AxiosResponse<{ data: ServiceProfileSpecificationFieldType[] }>>({
        queryKey: ['serviceProfileSpecification', params],
        queryFn: async () => {
          return await serviceProfileClient.getServiceProfileSpecifications(id, params) as AxiosResponse<{ data: ServiceProfileSpecificationFieldType[] }>
        }
      })
    },
    serviceProfileDataTypesQueryOptions: ({ id }: { id: string }) => {
      return queryOptions<AxiosResponse<{ data: ServiceProfileDataTypes.Root[] }>>({
        queryKey: ['serviceProfileDataTypes', id],
        queryFn: async () => {
          return await serviceProfileClient.getServiceProfileDataTypes(id) as AxiosResponse<{ data: ServiceProfileDataTypes.Root[] }>
        }
      })
    },
    searchServiceProfileDataTypesQueryOptions: ({ id, payload }: { id: string, payload: object | object[] }) => {
      return queryOptions<AxiosResponse<{ data: ServiceProfileDataTypes.Root[] }>>({
        queryKey: ['serviceProfileDataTypes', 'search', payload],
        queryFn: async () => {
          return await serviceProfileClient.searchServiceProfileDataTypes(id, payload) as AxiosResponse<{ data: ServiceProfileDataTypes.Root[] }>
        }
      })
    }
  }
};