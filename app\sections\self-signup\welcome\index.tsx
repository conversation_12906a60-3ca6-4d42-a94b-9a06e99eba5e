import { Box, Button, useTheme, Stack, Typography } from "@mui/material";
import MainCard from "components/MainCard";
import useSelfRegistration from "store/self-signUp/self-signup";


const BASE_PATH = '/images/brand'
const MIN_STYLE = { width: 60, height: 60 };


const Welcome = () => {
    const { palette: { mode } } = useTheme()

    const { setReady } = useSelfRegistration();

    const handleGetStarted = () => {
        setReady(true);
    };

    return (
        <MainCard
            content={false}
            modal
            border={false}
            sx={{ overFlow: 'hidden' }}
        >
            <Stack
                spacing={3}
                alignItems="center"
                sx={{
                    py: 4,
                }}
            >
                <Box
                    sx={{
                        width: 80,
                        height: 80,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',

                        flexShrink: 0,// prevent shrinking to zero
                        position: 'relative', // Add relative positioning
                        zIndex: 1 // Lower z-index for image container

                    }}
                >
                    <img
                        src={`${BASE_PATH}/unz_min_brand_${mode}.svg`}
                        alt="Welcome"
                        style={MIN_STYLE}
                    />
                </Box>

                <Stack alignItems={'center'} gap={1}>
                    <Typography variant="h2" component="h1">
                        Welcome to Unizo!
                    </Typography>

                    {/* Subtitle */}
                    <Typography
                        variant="body1"
                        color="textSecondary"
                    >
                        Let&apos;s find out how we can help you, in less than a minute.
                    </Typography>
                </Stack>

                <Button
                    variant="contained"
                    size="large"
                    fullWidth
                    onClick={handleGetStarted}
                    sx={{
                        mt: 2,
                        py: 1.5,
                        maxWidth: 200,
                        position: 'relative',
                        zIndex: 2, // Higher z-index for button
                        '&:hover': {
                            zIndex: 2 // Maintain z-index on hover
                        }
                    }}
                >
                    Get started
                </Button>
            </Stack>
        </MainCard>
    );
};

export default Welcome;
