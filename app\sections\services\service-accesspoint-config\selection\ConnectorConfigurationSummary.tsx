import React, { useState } from 'react';
import {
  Box,
  Stack,
  Typography,
  Chip,
  useTheme,
  alpha,
  Button,
  Divider,
  Paper,
  Grid,
  Card,
  CardContent,
  Tooltip,
} from '@mui/material';
import {
  Shield,
  Settings,
  ChevronRight,
  Check,
  Key,
  UserCheck,
  Link2,
  Database,
  Code2,
  Circle,
  CheckCircle,
  Info,
} from 'lucide-react';
import { AccessPointConfigType } from 'hooks/api/use-accesspoint-config/useGetAccessPoint';
import useServiceConfig from 'store/setup-service';
import { State } from 'hooks/useStatus';

interface AuthMethod {
  id: string;
  type: AccessPointConfigType;
  label: string;
  description?: string;
  icon: React.ReactNode;
  configured: boolean;
  features?: {
    version?: string;
    sandboxSupported?: boolean;
    productionSupported?: boolean;
    fieldMappings?: number;
    customFields?: number;
    lastUpdated?: string;
  };
}

interface ConnectorConfigurationSummaryProps {
  serviceProfile: any;
  accessPoints: any[];
  onEdit?: () => void;
  onConfigureAuth?: (authMethod: AuthMethod) => void;
  hasVersionSupport?: boolean;
}

export default function ConnectorConfigurationSummary({
  serviceProfile,
  accessPoints,
  onEdit,
  onConfigureAuth,
  hasVersionSupport = false,
}: ConnectorConfigurationSummaryProps) {
  const theme = useTheme();
  const { setSelectedTypeConfig } = useServiceConfig();
  const isServiceActive = serviceProfile?.service?.state === State.ACTIVE;

  // Transform access points to auth methods
  const authMethods: AuthMethod[] = React.useMemo(() => {
    const authTypeIcons = {
      [AccessPointConfigType.OAuthFlow]: <Shield size={16} />,
      [AccessPointConfigType.OAuthPasswordFlow]: <UserCheck size={16} />,
      [AccessPointConfigType.APIKeyFlow]: <Key size={16} />,
      [AccessPointConfigType.AppFlow]: <Link2 size={16} />,
    };

    return accessPoints.map((ap) => ({
      id: ap.id,
      type: ap.type,
      label: ap.label,
      description: ap.description,
      icon: authTypeIcons[(ap.type as AccessPointConfigType)] || <Shield size={16} />,
      configured: !!ap.accessPoint?.id,
      features: {
        version: ap.accessPoint?.version || ap.version || 'v2.0',
        sandboxSupported: ap.sandboxSupported !== false,
        productionSupported: ap.productionSupported !== false,
        fieldMappings: ap.fieldMappings?.length || 0,
        customFields: ap.customFields?.length || 0,
        lastUpdated: ap.accessPoint?.updatedAt || ap.updatedAt,
      },
    }));
  }, [accessPoints]);

  return (
    <Box>
      {/* Header Section */}
      <Stack mb={3}>
        <Typography variant="h6" fontWeight={600}>
          {serviceProfile?.name || 'Service'} Configuration
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
          Manage authentication methods for your {serviceProfile?.name || 'service'} integration
        </Typography>
      </Stack>

      {/* Authentication Methods Section */}
      <Box>
        {authMethods.length >= 2 && (
          <Box display="flex" alignItems="flex-start" gap={1} sx={{ mb: 2 }}>
            <Info size={16} color={theme.palette.info.main} style={{ marginTop: 3 }} />
            <Typography variant="body2" color="text.secondary">
              Unizo supports {authMethods.length} {serviceProfile?.name || 'Service'} authentication methods whichever you want to configure will be available in both API and Connect UI, allowing customers to configure integrations with ease.
            </Typography>
          </Box>
        )}
        
        <Grid container spacing={2}>
          {authMethods.map((method) => (
            <Grid item xs={12} md={6} key={method.id}>
              <Card
                elevation={0}
                sx={{
                  height: '100%',
                  borderRadius: 1,
                  backgroundColor: theme.palette.background.paper,
                  '&:hover': {
                    backgroundColor: theme.palette.mode === 'dark'
                      ? theme.palette.grey[800]
                      : theme.palette.grey[50],
                  },
                }}
              >
                <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                  <Stack spacing={1.5}>
                    <Stack direction="row" spacing={1.5} alignItems="flex-start">
                      <Box sx={{ color: theme.palette.text.secondary, mt: 0.25 }}>
                        {method.icon}
                      </Box>
                      <Box flex={1}>
                        <Stack direction="row" alignItems="center" justifyContent="space-between">
                          <Typography variant="body2" fontWeight={500}>
                            {method.label}
                          </Typography>
                          {method.configured && isServiceActive && (
                            <Tooltip title="Authentication method is configured and ready to use">
                              <CheckCircle size={16} color={theme.palette.success.main} />
                            </Tooltip>
                          )}
                        </Stack>
                        {method.description && (
                          <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 0.5 }}>
                            {method.description}
                          </Typography>
                        )}
                      </Box>
                    </Stack>

                    <Stack direction="row" alignItems="center" justifyContent="flex-end">
                      {!method.configured && (
                        <Button
                          size="small"
                          variant="text"
                          endIcon={<ChevronRight size={14} />}
                          onClick={() => {
                            if (method.type === AccessPointConfigType.AppFlow || method.type === AccessPointConfigType.OAuthFlow) {
                              const accessPoint = accessPoints.find(ap => ap.id === method.id);
                              if (accessPoint) {
                                setSelectedTypeConfig(accessPoint);
                              }
                            } else {
                              onConfigureAuth?.(method);
                            }
                          }}
                          sx={{
                            textTransform: 'none',
                            fontSize: '0.75rem',
                            color: theme.palette.primary.main,
                            '&:hover': {
                              backgroundColor: alpha(theme.palette.primary.main, 0.08),
                            },
                          }}
                        >
                          Configure
                        </Button>
                      )}
                      {method.configured && (method.type === AccessPointConfigType.AppFlow || method.type === AccessPointConfigType.OAuthFlow) && (
                        <Button
                          size="small"
                          variant="outlined"
                          endIcon={<Settings size={14} />}
                          onClick={() => {
                            const accessPoint = accessPoints.find(ap => ap.id === method.id);
                            if (accessPoint) {
                              setSelectedTypeConfig(accessPoint);
                            }
                          }}
                          sx={{
                            textTransform: 'none',
                            fontSize: '0.75rem',
                            borderColor: theme.palette.primary.main,
                            color: theme.palette.primary.main,
                            '&:hover': {
                              borderColor: theme.palette.primary.dark,
                              backgroundColor: alpha(theme.palette.primary.main, 0.08),
                            },
                          }}
                        >
                          Configure
                        </Button>
                      )}
                    </Stack>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
}