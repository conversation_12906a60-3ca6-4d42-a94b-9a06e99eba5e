export interface EnvironmentCardProps {
  name: string;
  description: string;
  isProduction?: boolean;
  onEdit?: () => void;
  className?: string;
}

export type EnvironmentStatus = 'production' | 'staging' | 'development' | 'test';

export interface EnvironmentTheme {
  production: {
    primary: string;
    background: string;
    border: string;
  };
  staging: {
    primary: string;
    background: string;
    border: string;
  };
  development: {
    primary: string;
    background: string;
    border: string;
  };
  test: {
    primary: string;
    background: string;
    border: string;
  };
}