# OpenAPI Field Mapping Framework

This framework processes OpenAPI specifications to automatically generate field structures for data mapping between service providers and unified data models.

## Architecture Overview

```
OpenAPI Spec (JSON) → OpenApiProcessor → Field Structures → Mapping Interface
```

## Core Components

### 1. OpenApiProcessor (`openapi-processor.ts`)
- **Purpose**: Processes OpenAPI JSON schemas and converts them to our field structure
- **Key Features**:
  - Handles complex nested objects and arrays
  - Resolves `$ref` references with circular reference protection
  - Supports `allOf`, `oneOf`, `anyOf` compositions
  - Maps OpenAPI types to our standardized types
  - Generates hierarchical field structures

### 2. GitHub OpenAPI Spec (`github-openapi-spec.ts`)
- **Purpose**: Complete OpenAPI specification for GitHub's Repository API
- **Features**:
  - Based on GitHub's actual REST API v3
  - Includes all repository fields and nested structures
  - Proper type definitions with formats and constraints
  - References for related entities (User, Organization, License, etc.)

### 3. Field Mapping Integration (`openapi-field-mapping.ts`)
- **Purpose**: Generates mapping data from OpenAPI specs
- **Features**:
  - Processes both source (provider) and target (unified) schemas
  - Creates hierarchical field structures for the UI
  - Supports multiple providers (extensible architecture)
  - Fallback to mock data if OpenAPI processing fails

## Generated Field Structure

### Source Fields (GitHub Repository)
```typescript
{
  id: 'repository',
  name: 'repository',
  displayName: 'Repository',
  type: 'object',
  children: [
    {
      id: 'id',
      name: 'id',
      type: 'number',
      description: 'Repository ID'
    },
    {
      id: 'owner',
      name: 'owner',
      type: 'object',
      children: [
        { id: 'owner.login', name: 'login', type: 'string' },
        { id: 'owner.id', name: 'id', type: 'number' },
        // ... more nested fields
      ]
    },
    // ... more fields
  ]
}
```

### Target Fields (Unified Repository)
```typescript
{
  id: 'unified_repository',
  name: 'unified_repository',
  type: 'object',
  children: [
    {
      id: 'repo_id',
      name: 'repo_id',
      type: 'string',
      required: true
    },
    {
      id: 'owner_info',
      name: 'owner_info',
      type: 'object',
      children: [
        { id: 'owner_info.username', name: 'username', type: 'string' },
        // ... more unified fields
      ]
    }
  ]
}
```

## Mapping Examples

### Simple Field Mapping
```
GitHub: repository.name (string) → Unified: repo_name (string)
GitHub: repository.id (number) → Unified: repo_id (string)
```

### Nested Object Mapping
```
GitHub: repository.owner.login (string) → Unified: owner_info.username (string)
GitHub: repository.owner.avatar_url (string) → Unified: owner_info.avatar_url (string)
```

### Array Mapping
```
GitHub: repository.topics (array<string>) → Unified: topics (array<string>)
```

### Complex Nested Structure
```
GitHub: repository.security_and_analysis.secret_scanning.status
→ Unified: security.secret_scanning_enabled
```

## Testing the Framework

### Console Testing
```typescript
// Import the test function
import { runOpenApiTests } from './test-openapi-mapping';

// Run tests in browser console
runOpenApiTests();
```

### Expected Output
```
==========================================================
OpenAPI Field Mapping Test Results
==========================================================

📁 GitHub Repository → Unified Repository Mapping
--------------------------------------------------

🔍 Source Schema Analysis:
- Schema: GitHub Repository
- Total fields: 85
- Nested objects: 12
- Array fields: 3

🎯 Target Schema Analysis:
- Schema: Unified Repository
- Total fields: 45
- Nested objects: 8
- Array fields: 1

📋 Sample Field Mappings:
  ✅ id → repo_id (Direct ID mapping)
  ✅ name → repo_name (Direct name mapping)
  ✅ owner.login → owner_info.username (Nested field mapping)
  ✅ permissions.admin → permissions.admin (Object-to-object mapping)
  ✅ topics → topics (Array mapping)

🔧 Complex Nested Structures:
  📂 owner (object)
    📂 plan (object)
  📂 permissions (object)
  📂 license (object)
  📂 organization (object)
  📂 security_and_analysis (object)
    📂 advanced_security (object)
    📂 secret_scanning (object)
```

## Integration with UI Component

The `HierarchicalFieldMapping` component automatically uses OpenAPI-generated data:

```typescript
// Automatically tries OpenAPI first, falls back to mock data
const openApiModels = getOpenApiBasedDataModels();
const sourceFields = openApiModels.PROVIDER_DATA_MODELS[provider]?.[dataModel.id];
const targetFields = openApiModels.UNIFIED_DATA_MODELS[dataModel.id];
```

## Supported Mapping Scenarios

### 1. Direct Field Mapping
- `repository.name` → `repo_name`
- Type validation ensures compatibility

### 2. Nested Object Mapping
- `repository.owner` → `owner_info`
- Child fields automatically available after parent selection

### 3. Object-within-Array Mapping
- `repository.topics[].item` → `topics[].item`
- Array item structures preserved

### 4. Deep Nesting
- `repository.security_and_analysis.secret_scanning.status`
- Up to 4+ levels of nesting supported

### 5. Type Conversion
- `number` → `string` (with validation warning)
- `date-time` → `date` format normalization

## Benefits

1. **Automatic Schema Processing**: No manual field definition required
2. **Real API Compliance**: Based on actual OpenAPI specifications
3. **Type Safety**: Proper type validation and conversion
4. **Extensible**: Easy to add new providers and schemas
5. **Fallback Support**: Graceful degradation to mock data
6. **Complex Structure Support**: Handles any level of nesting

## Adding New Providers

1. Create OpenAPI spec file (e.g., `gitlab-openapi-spec.ts`)
2. Add provider case in `generateOpenApiFieldMapping()`
3. Define unified schema for the new models
4. Update schema mapping in `getSourceSchemaName()`

## Future Enhancements

- **Auto-mapping suggestions** based on field name similarity
- **Custom transformation functions** for type conversions
- **Field validation rules** from OpenAPI constraints
- **Multiple schema version support**
- **Interactive schema explorer** for large APIs