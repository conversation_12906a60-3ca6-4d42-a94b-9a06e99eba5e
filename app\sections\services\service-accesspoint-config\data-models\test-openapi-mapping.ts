/**
 * Test OpenAPI Field Mapping
 * Demonstrates the OpenAPI-based field mapping functionality
 */

import { testOpenApiFieldMapping, generateOpenApiFieldMapping } from './openapi-field-mapping';

// Test the OpenAPI field mapping
export function runOpenApiTests() {
  console.log('='.repeat(60));
  console.log('OpenAPI Field Mapping Test Results');
  console.log('='.repeat(60));

  // Test GitHub Repository mapping
  try {
    const githubRepo = generateOpenApiFieldMapping('github', 'repository');
    
    console.log('\n📁 GitHub Repository → Unified Repository Mapping');
    console.log('-'.repeat(50));
    
    console.log('\n🔍 Source Schema Analysis:');
    console.log(`- Schema: GitHub Repository`);
    console.log(`- Total fields: ${countFields(githubRepo.sourceFields)}`);
    console.log(`- Nested objects: ${countObjectFields(githubRepo.sourceFields)}`);
    console.log(`- Array fields: ${countArrayFields(githubRepo.sourceFields)}`);
    
    console.log('\n🎯 Target Schema Analysis:');
    console.log(`- Schema: Unified Repository`);
    console.log(`- Total fields: ${countFields(githubRepo.targetFields)}`);
    console.log(`- Nested objects: ${countObjectFields(githubRepo.targetFields)}`);
    console.log(`- Array fields: ${countArrayFields(githubRepo.targetFields)}`);
    
    console.log('\n📋 Sample Field Mappings:');
    displaySampleMappings(githubRepo.sourceFields, githubRepo.targetFields);
    
    console.log('\n🔧 Complex Nested Structures:');
    displayNestedStructures(githubRepo.sourceFields);
    
    return githubRepo;
  } catch (error) {
    console.error('❌ Test failed:', error);
    return null;
  }
}

function countFields(schema: any): number {
  if (!schema.children) return 1;
  return schema.children.reduce((count: number, child: any) => 
    count + countFields(child), 1);
}

function countObjectFields(schema: any): number {
  if (!schema.children) return schema.type === 'object' ? 1 : 0;
  return (schema.type === 'object' ? 1 : 0) + 
    schema.children.reduce((count: number, child: any) => 
      count + countObjectFields(child), 0);
}

function countArrayFields(schema: any): number {
  if (!schema.children) return schema.type === 'array' ? 1 : 0;
  return (schema.type === 'array' ? 1 : 0) + 
    schema.children.reduce((count: number, child: any) => 
      count + countArrayFields(child), 0);
}

function displaySampleMappings(sourceSchema: any, targetSchema: any) {
  const sourcePaths = extractFieldPaths(sourceSchema);
  const targetPaths = extractFieldPaths(targetSchema);
  
  const potentialMappings = [
    { source: 'id', target: 'repo_id', type: 'Direct ID mapping' },
    { source: 'name', target: 'repo_name', type: 'Direct name mapping' },
    { source: 'full_name', target: 'repo_full_name', type: 'Direct full name mapping' },
    { source: 'owner.login', target: 'owner_info.username', type: 'Nested field mapping' },
    { source: 'owner.id', target: 'owner_info.id', type: 'Nested ID mapping' },
    { source: 'owner.avatar_url', target: 'owner_info.avatar_url', type: 'Nested URL mapping' },
    { source: 'permissions.admin', target: 'permissions.admin', type: 'Object-to-object mapping' },
    { source: 'license.key', target: 'license_info.key', type: 'License mapping' },
    { source: 'topics', target: 'topics', type: 'Array mapping' },
    { source: 'created_at', target: 'created_at', type: 'Date mapping' }
  ];
  
  potentialMappings.forEach(mapping => {
    const sourceExists = sourcePaths.includes(mapping.source);
    const targetExists = targetPaths.includes(mapping.target);
    const status = sourceExists && targetExists ? '✅' : sourceExists ? '⚠️' : '❌';
    
    console.log(`  ${status} ${mapping.source} → ${mapping.target} (${mapping.type})`);
  });
}

function extractFieldPaths(schema: any, parentPath = ''): string[] {
  const paths: string[] = [];
  const currentPath = parentPath ? `${parentPath}.${schema.name}` : schema.name;
  
  if (schema.type !== 'object' || !schema.children) {
    paths.push(currentPath);
  }
  
  if (schema.children) {
    schema.children.forEach((child: any) => {
      paths.push(...extractFieldPaths(child, currentPath));
    });
  }
  
  return paths;
}

function displayNestedStructures(schema: any, depth = 0) {
  if (depth > 3) return; // Limit depth for readability
  
  const indent = '  '.repeat(depth);
  
  if (schema.type === 'object' && schema.children) {
    console.log(`${indent}📂 ${schema.name} (${schema.type})`);
    schema.children.forEach((child: any) => {
      if (child.type === 'object' || child.type === 'array') {
        displayNestedStructures(child, depth + 1);
      }
    });
  } else if (schema.type === 'array' && schema.children) {
    console.log(`${indent}📊 ${schema.name} (${schema.type})`);
    schema.children.forEach((child: any) => {
      if (child.type === 'object' || child.type === 'array') {
        displayNestedStructures(child, depth + 1);
      }
    });
  }
}

// Export for use in console
if (typeof window !== 'undefined') {
  (window as any).testOpenApiMapping = runOpenApiTests;
}

export { runOpenApiTests };