import { styled } from '@mui/material/styles';
import { Card, Box, IconButton, Typography } from '@mui/material';
import { EnvironmentStatus } from './EnvironmentCard.types';

interface StyledCardProps {
  status?: EnvironmentStatus;
}

const getStatusColors = (theme: any, status: EnvironmentStatus = 'development') => {
  const statusTheme = {
    production: {
      primary: '#ef4444',
      background: 'rgba(239, 68, 68, 0.08)',
      border: 'rgba(239, 68, 68, 0.2)',
    },
    staging: {
      primary: '#f59e0b',
      background: 'rgba(245, 158, 11, 0.08)',
      border: 'rgba(245, 158, 11, 0.2)',
    },
    development: {
      primary: '#3b82f6',
      background: 'rgba(59, 130, 246, 0.08)',
      border: 'rgba(59, 130, 246, 0.2)',
    },
    test: {
      primary: '#10b981',
      background: 'rgba(16, 185, 129, 0.08)',
      border: 'rgba(16, 185, 129, 0.2)',
    },
  };

  return statusTheme[status];
};

export const StyledEnvironmentCard = styled(Card)<StyledCardProps>(({ theme, status = 'development' }) => {
  const colors = getStatusColors(theme, status);
  
  return {
    position: 'relative',
    borderRadius: theme.shape.borderRadius * 2,
    border: `1px solid ${colors.border}`,
    backgroundColor: colors.background,
    padding: theme.spacing(2.5),
    transition: theme.transitions.create(['box-shadow', 'border-color', 'transform']),
    '&:hover': {
      borderColor: colors.primary,
      boxShadow: `0 4px 20px rgba(0, 0, 0, 0.08)`,
      transform: 'translateY(-2px)',
    },
  };
});

export const CardHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  marginBottom: theme.spacing(1.5),
}));

export const HeaderContent = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1.5),
}));

export const StatusIndicator = styled(Box)<{ status?: EnvironmentStatus }>(({ theme, status = 'development' }) => {
  const colors = getStatusColors(theme, status);
  
  return {
    width: 8,
    height: 8,
    borderRadius: '50%',
    backgroundColor: colors.primary,
    flexShrink: 0,
  };
});

export const StyledTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1rem',
  fontWeight: 600,
  color: theme.palette.text.primary,
  margin: 0,
}));

export const StyledDescription = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  margin: 0,
}));

export const StyledIconButton = styled(IconButton)<{ status?: EnvironmentStatus }>(({ theme, status = 'development' }) => {
  const colors = getStatusColors(theme, status);
  
  return {
    padding: theme.spacing(0.75),
    color: colors.primary,
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.04)',
    },
  };
});