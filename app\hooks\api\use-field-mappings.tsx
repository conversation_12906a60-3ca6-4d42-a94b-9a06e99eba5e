import { queryOptions, UseMutationOptions } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { fieldMappingsClient } from "services/field-mappings.service";
import { FieldMappings } from "types/field-mappings";



type CreateFieldMappingsOptions = {
   serviceId: string
   payload: FieldMappings.CreateFieldMappingsPayload
}

type UpdateFieldMappingsOptions = {
   serviceId: string
   fieldMappingId: string
   payload: FieldMappings.UpdateFieldMappingsPayload[]
}

const useGetFieldMappings = () => {

   return {
      createFieldMappingsQuery: (): UseMutationOptions<AxiosResponse<FieldMappings.Root>, {}, CreateFieldMappingsOptions> => {
         return {
            mutationFn: ({ serviceId, payload }) => {
               return fieldMappingsClient.createFieldMappings(serviceId, payload)
            }
         }
      },
      updateFieldMappingsQuery: (): UseMutationOptions<AxiosResponse<FieldMappings.Root>, {}, UpdateFieldMappingsOptions> => {
         return {
            mutationFn: ({ serviceId, fieldMappingId, payload }) => {
               return fieldMappingsClient.updateFieldMappings(serviceId, fieldMappingId, payload)
            }
         }
      },
      searchFieldMappings: ({ serviceId, payload }: { serviceId: string, payload: object }) => {
         return queryOptions<AxiosResponse<{ data: FieldMappings.Root[] }>>({
            queryKey: ['field-mappings-search', payload],
            queryFn: () => {
               return fieldMappingsClient.searchFieldMappings(serviceId, payload)
            },
         })
      },
      getAllFieldMappings: ({ serviceId, params }: { serviceId: string, params: object }) => {
         return queryOptions<AxiosResponse<{ data: FieldMappings.Root[] }>>({
            queryKey: ['field-mappings-gat-all', params],
            queryFn: () => {
               return fieldMappingsClient.getFieldMappings(serviceId, params)
            },
         })
      }
   }
}

export default useGetFieldMappings;