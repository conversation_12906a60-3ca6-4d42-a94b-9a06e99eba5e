import React from 'react';

import { Edit2, <PERSON>, Rocket } from 'lucide-react';
import { Typography, Tooltip, Box, Chip, alpha } from '@mui/material';

import Dot from 'components/@extended/Dot';

import { EnvironmentCardProps } from '../Environment.types';
import {
  StyledEnvironmentCard,
  CardHeader,
  CardContent,
  ActionButtons,
  StyledEditButton,
} from '../Environment.styles';
import { StyledTitle, StyledDescription } from '../Typography.styles';

export const EnvironmentCard: React.FC<EnvironmentCardProps> = ({
  env,
  onEdit,
  onDelete,
  onStar,
  isDefault,
}) => {
  const isProduction = env.key === 'PROD' || env.name?.toLowerCase().includes('production');

  return (
    <StyledEnvironmentCard variant="outlined" envColor={env.color}>
      <Box position="relative">
        {isProduction && (
          <Tooltip title="Production Environment">
            <Box 
              sx={{ 
                position: 'absolute', 
                top: -8, 
                right: -8,
                bgcolor: (theme) => theme.palette.mode === 'dark' 
                  ? alpha(env.color, 0.9)
                  : env.color,
                borderRadius: '50%',
                width: 28,
                height: 28,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: (theme) => theme.shadows[2],
                border: (theme) => `1px solid ${theme.palette.background.paper}`,
              }}
            >
              <Rocket size={14} color="#fff" strokeWidth={2.5} />
            </Box>
          </Tooltip>
        )}
        <CardHeader>
          <Box sx={{ width: 8, height: 8, bgcolor: env.color, flexShrink: 0, borderRadius: '50%' }} />
          <StyledTitle variant="subtitle1">
            {env.name}
          </StyledTitle>
          {isDefault && (
            <Chip 
              label="DEFAULT" 
              size="small" 
              sx={{ 
                height: 20,
                fontSize: '0.625rem',
                fontWeight: 600,
                borderRadius: 0,
                bgcolor: (theme) => theme.palette.mode === 'dark' 
                  ? 'rgba(255, 255, 255, 0.1)' 
                  : 'rgba(0, 0, 0, 0.08)',
                color: 'text.secondary',
              }} 
            />
          )}
        </CardHeader>
        
        <CardContent>
          <StyledDescription variant="body2">
            {env.description || "No description"}
          </StyledDescription>
        </CardContent>
      </Box>

      <ActionButtons>
        <Tooltip title={isDefault ? "Default environment" : "Set as default"}>
          <StyledEditButton
            size="small"
            onClick={() => !isDefault && onStar(env)}
            aria-label="Set as default environment"
            iconColor={env.color}
            disabled={isDefault}
          >
            <Star size={16} fill={isDefault ? 'currentColor' : 'none'} />
          </StyledEditButton>
        </Tooltip>
        <Tooltip title="Edit">
          <StyledEditButton
            size="small"
            onClick={() => onEdit(env)}
            aria-label="Edit environment"
            iconColor={env.color}
          >
            <Edit2 size={16} />
          </StyledEditButton>
        </Tooltip>
      </ActionButtons>
    </StyledEnvironmentCard>
  );
};