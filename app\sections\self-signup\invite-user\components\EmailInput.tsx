import { TextField, useTheme, alpha, Select, MenuItem, InputAdornment, Theme, Tooltip, IconButton,Box } from "@mui/material";
import { Stack } from "@mui/material";
import { CloseCircleFilled } from '@ant-design/icons';
import AnimateButton from 'components/@extended/AnimateButton';
import { UserRoleEnum, ROLE_MAPPER } from "hooks/api/permission/usePermission";
import { ROLE_ICONS } from '../constants';

interface EmailInputProps {
   inputValue: string;
   selectedRole: UserRoleEnum;
   error: string;
   onInputChange: (value: string) => void;
   onKeyDown: (event: React.KeyboardEvent<HTMLInputElement>) => void;
   onRoleChange: (role: UserRoleEnum) => void;
}

// const EmailInput = ({
//    inputValue,
//    selectedRole,
//    error,
//    onInputChange,
//    onKeyDown,
//    onRoleChange
// }: EmailInputProps) => {
//    const theme = useTheme();

//    return (
//       <TextField
//          fullWidth
//          value={inputValue}
//          onChange={(e) => onInputChange(e.target.value)}
//          onKeyDown={onKeyDown}
//          placeholder="Enter email address"
//          variant="outlined"
//          error={Boolean(error)}
//          InputProps={{
//             startAdornment: (
//                <InputAdornment position="start">
//                   <span style={{ color: alpha(theme.palette.common.black, 0.75) }}>
//                      {ROLE_ICONS[selectedRole]}
//                   </span>
//                </InputAdornment>
//             ),
//             endAdornment: (
//                <InputAdornment position="end">
//                   <Stack direction="row" spacing={0.5} alignItems="center">
//                      {error && (
//                         <Tooltip title={error} arrow placement="top">
//                            <AnimateButton type="shake">
//                               <IconButton
//                                  size="small"
//                                  sx={{
//                                     color: 'error.main',
//                                     p: 0.5,
//                                     '&:hover': {
//                                        backgroundColor: alpha(theme.palette.error.main, 0.08)
//                                     }
//                                  }}
//                               >
//                                  <CloseCircleFilled style={{ fontSize: '16px' }} />
//                               </IconButton>
//                            </AnimateButton>
//                         </Tooltip>
//                      )}
//                      <Select
//                         value={selectedRole}
//                         onChange={(e) => onRoleChange(e.target.value as UserRoleEnum)}
//                         variant="standard"
//                         disableUnderline
//                         sx={style.select({ theme })}
//                      >
//                         {Object.entries(ROLE_MAPPER).map(([role, label]) => (
//                            <MenuItem
//                               key={role}
//                               value={role}
//                               sx={style.menuItem({ theme })}
//                            >
//                               {label}
//                            </MenuItem>
//                         ))}
//                      </Select>
//                   </Stack>
//                </InputAdornment>
//             )
//          }}
//          sx={style.textField({ theme })}
//       />
//    );
// };
const EmailInput = ({
   inputValue,
   selectedRole,
   error,
   onInputChange,
   onKeyDown,
   onRoleChange
}: EmailInputProps) => {
   const theme = useTheme();

   return (
      <Stack spacing={0.5}>
         <TextField
            fullWidth
            value={inputValue}
            onChange={(e) => onInputChange(e.target.value)}
            onKeyDown={onKeyDown}
            placeholder="Enter email address and press Enter"
            variant="outlined"
            error={Boolean(error)}
            // Only show error message as helper text
            helperText={error}
            FormHelperTextProps={{
               sx: {
                  minHeight: error ? '1.25rem' : '0',
                  margin: 0,
                  transition: 'all 0.2s ease-in-out'
               }
            }}
            InputProps={{
               sx: {
                  minHeight: '56px', // Fixed height for input
               },
               startAdornment: (
                  <InputAdornment position="start">
                     <span style={{ 
                        color: alpha(theme.palette.common.black, 0.75),
                        display: 'flex',
                        alignItems: 'center'
                     }}>
                        {ROLE_ICONS[selectedRole]}
                     </span>
                  </InputAdornment>
               ),
               endAdornment: (
                  <InputAdornment position="end">
                     <Stack 
                        direction="row" 
                        spacing={0.5} 
                        alignItems="center"
                        sx={{ 
                           minWidth: '200px',
                           justifyContent: 'flex-end' 
                        }}
                     >
                        <Box sx={{ 
                           width: '24px',
                           display: 'flex',
                           justifyContent: 'center'
                        }}>
                           {error && (
                              <Tooltip title={error} arrow placement="top">
                                 <AnimateButton type="shake">
                                    <IconButton
                                       size="small"
                                       sx={{
                                          color: 'error.main',
                                          p: 0.5,
                                          '&:hover': {
                                             backgroundColor: alpha(theme.palette.error.main, 0.08)
                                          }
                                       }}
                                    >
                                       <CloseCircleFilled style={{ fontSize: '16px' }} />
                                    </IconButton>
                                 </AnimateButton>
                              </Tooltip>
                           )}
                        </Box>
                        {/* <Box sx={{ 
                           width: '80px',
                           display: 'flex',
                           justifyContent: 'center'
                        }}>
                           {inputValue && !error && (
                              <Tooltip title="Press Enter to Add Email" arrow placement="top">
                                 <span style={{ 
                                    fontSize: '12px', 
                                    color: theme.palette.text.secondary,
                                 }}>
                                    Press Enter↵
                                 </span>
                              </Tooltip>
                           )}
                        </Box> */}
                        <Select
                           value={selectedRole}
                           onChange={(e) => onRoleChange(e.target.value as UserRoleEnum)}
                           variant="standard"
                           disableUnderline
                           sx={{
                              ...style.select({ theme }),
                              minWidth: '120px',
                           }}
                        >
                           {Object.entries(ROLE_MAPPER).map(([role, label]) => (
                              <MenuItem
                                 key={role}
                                 value={role}
                                 sx={style.menuItem({ theme })}
                              >
                                 {label}
                              </MenuItem>
                           ))}
                        </Select>
                     </Stack>
                  </InputAdornment>
               )
            }}
            sx={{
               ...style.textField({ theme }),
               '& .MuiInputBase-root': {
                  transition: 'all 0.2s ease-in-out',
               }
            }}
         />
      </Stack>
   );
};

const style = {
   textField: ({ theme }: { theme: Theme }) => ({
      '& .MuiInputAdornment-root': {
         color: alpha(theme.palette.common.black, 0.75),
      },
      '& .MuiOutlinedInput-root': {
         bgcolor: theme.palette.background.paper,
         borderRadius: 2,
         transition: 'all 0.2s ease-in-out',
         boxShadow: `0 2px 8px ${alpha(theme.palette.grey[500], 0.08)}`,
         border: `1px solid ${alpha(theme.palette.grey[500], 0.2)}`,
         '& fieldset': {
            border: 'none'
         },
         '&:hover': {
            boxShadow: `0 4px 12px ${alpha(theme.palette.grey[500], 0.12)}`,
            border: `1px solid ${alpha(theme.palette.primary.main, 0.3)}`,
         },
         '&.Mui-focused': {
            boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.12)}`,
            border: `1px solid ${theme.palette.primary.main}`,
         }
      },
      '& .MuiInputAdornment-root.MuiInputAdornment-positionEnd': {
         mr: 0,
         '& .MuiStack-root': {
            pl: 1,
            '& .MuiIconButton-root': {
               pr: 0.5,  // Reduced padding
               mr: 0.5,  // Reduced margin
               borderRadius: 0
            }
         }
      }
   }),
   menuItem: ({ theme }: { theme: Theme }) => ({
      fontSize: '0.875rem',
      py: 1,
      px: 2,
      color: alpha(theme.palette.common.black, 0.75),
      display: 'flex',
      alignItems: 'center',
      gap: 1,
      '&:hover': {
         color: alpha(theme.palette.common.black, 0.95),
         backgroundColor: alpha(theme.palette.common.black, 0.08)
      },
      '&.Mui-selected': {
         color: alpha(theme.palette.common.black, 0.95),
         backgroundColor: 'transparent',
         fontWeight: 600,
         '&:hover': {
            backgroundColor: alpha(theme.palette.common.black, 0.08)
         }
      }
   }),
   select: ({ theme }: { theme: Theme }) => ({
      minWidth: 120,
      '& .MuiSelect-select': {
         color: alpha(theme.palette.common.black, 0.75),
         fontSize: '0.875rem',
         fontWeight: 600,
         pl: 1.5,  // Reduced padding
         pr: 3,
         '&:focus': {
            backgroundColor: 'transparent'
         }
      },
      '& .MuiSelect-icon': {
         color: alpha(theme.palette.common.black, 0.75),
         opacity: 0.8,
         width: 20,
         height: 20,
         right: 0,
         top: 'calc(50% - 10px)'
      }
   })
};

export default EmailInput;
