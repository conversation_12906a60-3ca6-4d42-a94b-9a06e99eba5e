import { 
  Grid, 
  Skeleton, 
  Box, 
  Stack,
  Paper,
  useTheme,
  useMediaQuery 
} from '@mui/material';

const FieldMappingsSkeleton = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          {/* Tabs skeleton */}
          <Stack 
            direction="row" 
            spacing={2} 
            sx={{ 
              p: 2,
              overflowX: isMobile ? 'auto' : 'visible',
              '&::-webkit-scrollbar': {
                height: 6,
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: theme.palette.action.disabled,
                borderRadius: 3,
              }
            }}
          >
            {[1, 2, 3].map((i) => (
              <Skeleton 
                key={i} 
                variant="rounded" 
                width={120} 
                height={40}
                sx={{ flexShrink: 0 }}
              />
            ))}
          </Stack>
        </Box>

        <Paper sx={{ p: 3, mt: 3 }}>
          {/* Search bar skeleton */}
          <Paper
            elevation={0}
            sx={{
              p: 2,
              mb: 3,
              backgroundColor: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 1
            }}
          >
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="space-between">
              <Skeleton variant="rounded" width={isMobile ? '100%' : 320} height={40} />
              <Skeleton variant="rounded" width={isMobile ? '100%' : 180} height={40} />
            </Stack>
          </Paper>

          {/* Model sections skeleton */}
          <Stack spacing={2}>
            {[1, 2, 3].map((i) => (
              <Box
                key={i}
                sx={{
                  backgroundColor: theme.palette.background.paper,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 1,
                  p: 3
                }}
              >
                <Stack direction="row" alignItems="center" justifyContent="space-between">
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Skeleton variant="circular" width={24} height={24} />
                    <Box>
                      <Skeleton variant="text" width={150} height={24} />
                      <Skeleton variant="text" width={200} height={16} />
                    </Box>
                  </Stack>
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Skeleton variant="text" width={80} height={20} />
                    <Skeleton variant="rounded" width={120} height={32} />
                  </Stack>
                </Stack>
              </Box>
            ))}
          </Stack>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default FieldMappingsSkeleton;