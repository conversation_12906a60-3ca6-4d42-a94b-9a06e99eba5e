import ModernChartCard from "components/cards/ModernChartCard"
import { CONTAINER_STYLE_S } from "sections/dashboard-v2/constant";

import { Line } from 'react-chartjs-2';
import {
   Chart as ChartJS,
   CategoryScale,
   LinearScale,
   PointElement,
   LineElement,
   Title,
   Tooltip,
   Legend
} from 'chart.js';

import { useDashboard } from "hooks/api/dashboard/useDashboard";

import { GadgetConfig } from "../../layout/grid-type";
// chart
import Chart from './chart';
import { Box } from "@mui/material";
import { memo } from "react";

ChartJS.register(
   CategoryScale,
   LinearScale,
   PointElement,
   LineElement,
   Title,
   Tooltip,
   Legend
);

export default memo(({ gadget }: GadgetConfig) => {


   return (
      <ModernChartCard
         title={gadget.name || "API Requests"}
         subtitle="Real-time API usage metrics"
      >
         <Box sx={{ pr: 2 }}>
            <Chart />
         </Box>
      </ModernChartCard>
   )
})