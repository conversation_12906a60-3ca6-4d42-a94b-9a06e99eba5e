// ==============================|| OVERRIDES - TYPOGRAPHY ||============================== //

export default function Typography(theme) {
  return {
    MuiTypography: {
      defaultProps: {
        variantMapping: {
          h1: 'h1',
          h2: 'h2',
          h3: 'h3',
          h4: 'h4',
          h5: 'h5',
          h6: 'h6',
          subtitle1: 'h6',
          subtitle2: 'h6',
          body1: 'p',
          body2: 'p',
          inherit: 'p'
        }
      },
      styleOverrides: {
        root: {
          color: theme.palette.text.primary,
          WebkitFontSmoothing: 'antialiased',
          MozOsxFontSmoothing: 'grayscale',
        },
        h1: {
          color: theme.palette.text.primary,
          marginBottom: theme.spacing(2),
        },
        h2: {
          color: theme.palette.text.primary,
          marginBottom: theme.spacing(1.5),
        },
        h3: {
          color: theme.palette.text.primary,
          marginBottom: theme.spacing(1.5),
        },
        h4: {
          color: theme.palette.text.primary,
          marginBottom: theme.spacing(1),
        },
        h5: {
          color: theme.palette.text.primary,
          marginBottom: theme.spacing(1),
        },
        h6: {
          color: theme.palette.text.primary,
          marginBottom: theme.spacing(1),
        },
        subtitle1: {
          color: theme.palette.text.primary,
          fontWeight: theme.typography.fontWeightMedium,
        },
        subtitle2: {
          color: theme.palette.text.secondary,
          fontWeight: theme.typography.fontWeightMedium,
        },
        body1: {
          color: theme.palette.text.primary,
        },
        body2: {
          color: theme.palette.text.primary,
        },
        caption: {
          color: theme.palette.text.secondary,
        },
        overline: {
          color: theme.palette.text.secondary,
        },
        button: {
          textTransform: 'none',
          fontWeight: theme.typography.fontWeightMedium,
        },
        gutterBottom: {
          marginBottom: theme.spacing(1)
        }
      }
    }
  };
}
