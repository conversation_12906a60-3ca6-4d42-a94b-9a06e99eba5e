import { <PERSON>ton, <PERSON>alog<PERSON><PERSON>, DialogContent, DialogTitle, Grid, IconButton, ListItem, ListItemAvatar, ListItemText, Skeleton, Stack, Tooltip, Typography, useMediaQuery } from "@mui/material";
import loadable from '@loadable/component';

import MainCard from "components/MainCard";
import useLogProtection from "store/security/log-protection";

import { useDate } from "hooks/useDate";
import { useServiceProfile } from "hooks/useServiceProfile";
import { DeleteOutlined } from "@ant-design/icons";
import { useIntegrationCreateProvider } from "../integration-create-provider";

import customIntegration from "./create/step-contents/select-service/custom-integration-options";
import { LogProtectionConnectorType } from "constants/log-protection";
import { useEffect, useMemo, useState } from "react";
import { Dialog, DialogClose } from "components/@extended/dialog";
import Empty from "components/@extended/List/Empty";
import { List } from "components/@extended/List";


const CreateIntegration = loadable(() => import('./create'), {
   fallback: <div>Loading...</div>, // Custom loading UI
});

const TITLE = 'Key Management Options';
const DESCRIPTION = (
   `Manage integrations to securely store and access encryption keys within your systems, supporting seamless encryption and decryption for you and your customers.`)
const CREATE_BTN_TEXT = 'Create Integration'

export default () => {

   const downMd = useMediaQuery((theme: any) => theme.breakpoints.down('md'));

   const {
      reset,
      setOpenCreate,
      openCreate
   } = useLogProtection();

   const {
      getIsWebhookConfigExist,
      searchKeyProtections,
      attemptDeleteIntegration,
   } = useIntegrationCreateProvider();

   const { loadImage } = useServiceProfile();

   const { data: keyProtection, isPending } = searchKeyProtections()

   const [open, setOpen] = useState(false);
   const [selected, setSelected] = useState<Record<string, any> | null>(null);
   const [selectedIndex, setSelectedIndex] = useState<number | null>(null);

// console.log(keyProtection,"keyProtectionkeyProtectionkeyProtection")
   const onOpen = () => {
      setOpenCreate(true);
   }

   const onClose = () => {
      setOpenCreate(false);

      // make sure to clean all the log protection state
      reset();
   }

   const onDeleteRequest = (newSelected: Record<string, any>, newSelectedIndex: number) => {
      setOpen(true)
      setSelected(newSelected);
      setSelectedIndex(newSelectedIndex)
   }

   const onDeleteCancel = () => {
      setOpen(false)
      setSelected(null)
      setSelectedIndex(null)
   }


   const onDelete = () => {
      attemptDeleteIntegration(keyProtection?.id, [
         {
            op: "remove",
            path: `/vaultConfigs/${selectedIndex}`,
            value: [
               selected
            ]
         }
      ],
         () => {
            onDeleteCancel()
         })

   }

   const connectorTypeConfigs = useMemo(() => {
      return keyProtection?.vaultConfigs ?? []
   }, [keyProtection?.vaultConfigs])

   return (
      <MainCard>
         <Grid
            container
            alignItems={'center'}
            spacing={downMd ? 1 : 0}
         >
            <Grid item xs={12} md={8} >
               <Stack gap={2} alignItems={'start'} sx={{ maxWidth: '50%' }} >
                  <Typography variant="h5" className="truncate">
                     {TITLE}
                  </Typography>
                  <Typography color={'secondary.600'} mt={1} >
                     {DESCRIPTION}
                  </Typography>
               </Stack>
            </Grid>
            <Grid
               item
               xs={12}
               md={4}
               sx={{
                  textAlign: !downMd ? 'end' : 'start',
                  width: '100%'
               }}
            >
               <Button
                  variant='contained'
                  onClick={onOpen}
               >
                  {CREATE_BTN_TEXT}
               </Button>
            </Grid>
         </Grid>


         <List
            sx={{ mt: 2 }}
            bordered={true}
         >
            {(isPending) ? (
               Array.from({ length: 3 }, () => {
                  return (
                     <ListItem button key={Math.random()}>
                        <ListItemAvatar sx={{ mr: 2 }}>
                           <Skeleton variant="circular" height={30} />
                        </ListItemAvatar>
                        <ListItemText
                           primary={(
                              <Skeleton width={'40%'} height={20} />
                           )}
                           sx={{ gap: 2 }}
                           secondary={(
                              <Skeleton width={'20%'} height={20} />
                           )}
                        />
                     </ListItem>
                  )
               })
            ) : (
               connectorTypeConfigs?.length ? (
                  connectorTypeConfigs?.map((item: Record<string, any>, index: number) => {
                     return (
                        <ListItemComp
                           onDeleteRequest={onDeleteRequest}
                           key={index}
                           index={index}
                           item={item}
                        />
                     )
                  })
               ) : (
                  <Empty
                     title="No Integrations Found"
                     description="When you have Integrations available, they will show up here."
                  />
               )
            )}
         </List>

         <CreateIntegration
            open={openCreate}
            onClose={onClose}
         />

         <DeleteConfirmation
            open={open}
            onClose={onDeleteCancel}
            onConfirm={onDelete}
         />
      </MainCard>
   )
}

type Props = {
   onDeleteRequest: any
   item: any
   index: any
}

const ListItemComp = ({
   onDeleteRequest,
   item,
   index,
}: Props) => {

   const { loadDate } = useDate();
   const { loadImage } = useServiceProfile();

   const { getIntegration, searchKeyProtections } = useIntegrationCreateProvider();

   const { data } = searchKeyProtections()

   const { data: integration = {} } = getIntegration({
      logProtectionId: data?.id,
      id: item?.integration?.id as string
   })

   return (
      <ListItem
         button
      >
         <ListItemAvatar>
            {integration?.serviceProfile && (
               loadImage(integration?.serviceProfile, { size: 'small' })
            )}
         </ListItemAvatar>
         <ListItemText
            primary={(
               integration?.name && (
                  <Typography variant="subtitle1" color="text.primary">
                     {integration?.name}
                  </Typography>
               )
            )}
            secondary={loadDate(integration?.changeLog?.lastUpdatedDateTime)}
         />
         <Tooltip title={`Delete ${integration?.name}`} placement="top">
            <IconButton color="error"
               onClick={() => onDeleteRequest(item, index)}
            >
               <DeleteOutlined />
            </IconButton>
         </Tooltip>
      </ListItem>
   )
}

const DeleteConfirmation = ({ onConfirm, onClose, ...props }: any) => {
   return (
      <Dialog maxWidth={'xs'} {...props} onClose={onClose}>
         <DialogTitle>Delete Integration</DialogTitle>
         <DialogClose onClose={onClose} />
         <DialogContent dividers>
            Are you sure you want to delete this integration?
         </DialogContent>
         <DialogActions>
            <Button onClick={onClose} color="primary">
               Cancel
            </Button>
            <Button onClick={onConfirm} variant="contained" color="error">
               Delete
            </Button>
         </DialogActions>
      </Dialog>
   )
}