.card-select {
    display: flex;
    flex-wrap: wrap;
    /* flex-direction: row; */
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
}
@media (max-width: 720px) {
    .card-select {
        grid-template-columns: repeat(4, 1fr);
    }
}

.card-input {
    border: 2px solid transparent;
    background-color: rgba(248, 248, 248, 0.329);
    width: 120px;

    user-select: none;

    margin: auto;
}

.card-input-md {
    width: 134px !important;
}

.card-input  {
   padding: 1rem 1rem !important;
}

.card-select [aria-selected=true] {
    border: 1px solid rgb(240, 134, 64);
    cursor: pointer;
}



:where(.css-dev-only-do-not-override-kghr11).ant-card .ant-card-head {
    min-height: 0px !important;
}

.ant-card-body{
    min-width: 90px !important;
}