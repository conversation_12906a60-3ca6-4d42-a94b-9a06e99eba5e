import { Dispatch, SetStateAction, useMemo, useState } from "react";

import {
   Button,
   CheckboxProps,
   DialogActions,
   DialogContent,
   DialogTitle,
   FormControlLabel,
   Grid,
   ListItem,
   ListItemText,
   Skeleton,
   Stack,
   Tooltip,
   Typography,
   Radio,
   useTheme
} from "@mui/material";
import IconButton from '@mui/material/IconButton';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import useLogProtection from "store/security/log-protection";
import loadable from '@loadable/component';
import MainCard from "components/MainCard";
import { BRAND_NAME } from "config";
import { useServiceProfile } from "hooks/useServiceProfile";
import { useIntegrationCreateProvider } from "./integration-create-provider";
import { DeleteOutlined } from "@ant-design/icons";
import { Dialog, DialogClose } from "components/@extended/dialog";
import { useDate } from "hooks/useDate";
import { List } from "components/@extended/List";
import { getHighlightedStyles, behaviorStyles } from './helper'
import InfoIcon from '@mui/icons-material/Info';

const TITLE = 'Key Storage Policy';
const DESCRIPTION = (
   `Protect your data and customer data by ensuring encryption keys and sensitive cryptographic activities are not stored within Unizo.`
);


type Props = {
   enabled: boolean; // Initial enabled state for the log storage policy checkbox
   setEnabled: Dispatch<SetStateAction<boolean>>

   keyId: string | null
   isLoading: boolean

   hasEnterpriseTier: boolean
}

const CreateIntegration = loadable(() => import('./integration-list/create'), {
   fallback: <div>Loading...</div>, // Custom loading UI
});


export default ({
   enabled,
   keyId = null,
   isLoading,
   setEnabled
}: Props) => {

   const { palette }: any = useTheme();

   const [open, setOpen] = useState(false);
   const [selected, setSelected] = useState<Record<string, any> | null>(null);
   const [selectedIndex, setSelectedIndex] = useState<number | null>(null);

   const {
      attemptDeleteIntegration,
      attemptUpdateKeyProtection,
      searchKeyProtections,
   } = useIntegrationCreateProvider()

   const { data: keyProtection } = searchKeyProtections()
   const {
      reset,
      setOpenCreate,
      openCreate
   } = useLogProtection();

   const onOpen = () => {
      setOpenCreate(true);
   }

   const onClose = () => {
      setOpenCreate(false);

      // make sure to clean all the log protection state
      reset();

   }

   const onDeleteRequest = (newSelected: Record<string, any>, newSelectedIndex: number) => {
      setOpen(true)
      setSelected(newSelected);
      setSelectedIndex(newSelectedIndex)
   }
   const onDeleteCancel = () => {
      setOpen(false)
      setSelected(null)
      setSelectedIndex(null)
   }


   const onDelete = () => {
      attemptDeleteIntegration(keyProtection?.id, [
         {
            op: "remove",
            path: `/vaultConfigs/${selectedIndex}`,
            value: [
               selected
            ]
         }
      ],
         () => {
            onDeleteCancel()
            onConfirmed()
         })
   }

   const onCheckChange: CheckboxProps['onChange'] = (event, checked) => {
      onOpen(); // Open the modal when the checkbox value changes
   };


   const onConfirmed = () => {
      attemptUpdateKeyProtection(
         [{
            op: "replace",
            path: "/unizoManagedVault",
            value: true,
         }],
         keyId,
         () => {
            setEnabled(!enabled);
         }
      )
   };

   const onConfirm = () => {
      attemptUpdateKeyProtection(
         [{
            op: "replace",
            path: "/unizoManagedVault",
            value: false,
         }],
         keyId,
         () => {
            setEnabled(!enabled);
         }
      )
   };

   // Handler for Unizo-managed KMS card click
   const handleUnizoManagedClick = () => {
      if (!unizoManaged) {
         onConfirmed(); // Switch to Unizo-managed
      }
   };

   // Handler for BYOK card click
   const handleBYOKClick = () => {
      if (unizoManaged) {
         // Simulate a change event and checked value for the handler
         onCheckChange(
            { target: { checked: true } } as React.ChangeEvent<HTMLInputElement>,
            true
         );
      }
   };

   const connectorTypeConfigs = useMemo(() => {
      return keyProtection?.vaultConfigs ?? []
   }, [keyProtection?.vaultConfigs])

   const unizoManaged = useMemo(() => {
      return keyProtection?.unizoManagedVault
   }, [keyProtection?.unizoManagedVault])


   return (
      <MainCard >
         <Grid container>
            <Grid item xs={12} lg={10} xl={8}>
               <Stack gap={2} alignItems={'start'}>
                  {/* <Stack>
                     <Typography variant="h5">
                        {TITLE}
                     </Typography>
                     <Typography color={'secondary.600'} mt={1} >
                        {DESCRIPTION}
                     </Typography>
                  </Stack> */}

                  {
                     isLoading ? (
                        <Skeleton sx={{ width: '20%', height: '1rem' }} />
                     ) : (
                        <Grid container spacing={2}>
                           <Grid item xs={12} md={6} lg={6} xl={6}>
                              <MainCard 
                                 onClick={handleUnizoManagedClick}
                                 sx={{
                                    borderRadius: '5px',
                                    borderWidth: .5,
                                    opacity: unizoManaged ? 1 : 0.6,
                                    transition: 'all 0.3s ease',
                                    userSelect: 'none',
                                    cursor: !unizoManaged ? 'pointer' : 'default',

                                    ...(!unizoManaged ? {} : behaviorStyles(palette)),
                                    ...((unizoManaged ? getHighlightedStyles(palette) : {})),

                                 }}
                              >
                                 <Stack display="flex" flexDirection="row" alignItems="start" gap="0.6rem">
                                    <FormControlLabel
                                       control={
                                          <Radio
                                             // onChange={onCheckChange}
                                             checked={unizoManaged}
                                             disabled={!unizoManaged}
                                          />
                                       }
                                       label=""
                                    // sx={{ alignItems: "start", marginTop: 0 }}
                                    />


                                    <Stack display="flex" flexDirection="column" gap="0.6rem">
                                       <Typography variant="h5">
                                          Use Unizo-Managed KMS
                                       </Typography>
                                       <Typography variant="body1" color="textSecondary">
                                          {'Let Unizo handle secure storage of your integration credentials (API keys, OAuth tokens, secrets) using our enterprise-grade KMS infrastructure.​ This option is fully managed by Unizo and is ideal for teams that want to reduce operational overhead while maintaining strong security.​ All credentials are encrypted at rest and in transit, with strict access controls and audit logging built in.'}
                                       </Typography>
                                    </Stack>
                                    {!unizoManaged && <Tooltip title="This capability cannot be enabled while an integration to your key management system is active. Please remove it to proceed." placement="top">
                                       <IconButton
                                          onClick={(e) => e.stopPropagation()} // Prevent card click when clicking info icon
                                       >
                                          <InfoIcon sx={{ fontSize: "medium" }} />
                                       </IconButton>
                                    </Tooltip>}
                                 </Stack>
                              </MainCard>
                           </Grid>
                           <Grid item xs={12} md={6} lg={6} xl={6}>
                              <MainCard 
                                 onClick={handleBYOKClick}
                                 sx={{
                                    borderRadius: '5px',
                                    borderWidth: .5,
                                    transition: 'all 0.3s ease',
                                    userSelect: 'none',
                                    cursor: unizoManaged ? 'pointer' : 'default',
                                    ...(unizoManaged ? {} : behaviorStyles(palette)),
                                    ...((!unizoManaged ? getHighlightedStyles(palette) : {})),
                                 }}
                              >
                                 <Stack display='flex' flexDirection='row' alignItems='start' justifyItems='start'>
                                    <FormControlLabel
                                       control={(
                                          <Radio
                                             onChange={onCheckChange}
                                             checked={!unizoManaged}

                                          />
                                       )}
                                       label=""
                                    />
                                    <Stack display='flex' flexDirection='column' gap='.6rem'>
                                       <Typography variant="h5">
                                          Bring Your Own Key Management (BYOK)
                                       </Typography>
                                       <Typography variant="body1" color="textSecondary">
                                          {'Use your own Key Management System to store and manage integration credentials. This option gives you full control over encryption, key lifecycle management, and compliance. '}
                                          <em>Note: When using BYOK, Unizo will delegate key storage and access responsibilities to your KMS. You must ensure your system is properly configured and available to support integration workflows.</em>
                                       </Typography>
                                    </Stack>

                                 </Stack>

                              </MainCard>
                           </Grid>

                        </Grid>
                     )
                  }

               </Stack>
               {connectorTypeConfigs?.length > 0 && (
                  <List
                     sx={{ mt: 2 }}
                     bordered={true}
                  >
                     {connectorTypeConfigs.map((item: Record<string, any>, index: number) => (
                        <ListItemComp
                           onDeleteRequest={onDeleteRequest}
                           key={index}
                           index={index}
                           item={item}
                        />
                     ))}
                  </List>
               )}

            </Grid>
         </Grid>
         <CreateIntegration
            open={openCreate}
            onClose={onClose}
            onConfirm={onConfirm}
         />
         <DeleteConfirmation
            open={open}
            onClose={onDeleteCancel}
            onConfirm={onDelete}
         />
      </MainCard>
   )
}

type Propss = {
   onDeleteRequest: any
   item: any
   index: any
}

const ListItemComp = ({
   onDeleteRequest,
   item,
   index,
}: Propss) => {

   const { loadDate } = useDate();
   const { loadImage } = useServiceProfile();

   const { getIntegration, searchKeyProtections } = useIntegrationCreateProvider();

   const { data } = searchKeyProtections()

   const { data: integration = {} } = getIntegration({
      logProtectionId: data?.id,
      id: item?.integration?.id as string
   })

   return (
      <ListItem
         button
      >
         <ListItemAvatar>
            {integration?.serviceProfile && (
               loadImage(integration?.serviceProfile, { size: 'small' })
            )}
         </ListItemAvatar>
         <ListItemText
            primary={(
               integration?.name && (
                  <Typography variant="subtitle1" color="text.primary">
                     {integration?.name}
                  </Typography>
               )
            )}
            secondary={loadDate(integration?.changeLog?.lastUpdatedDateTime)}
         />
         <Tooltip title={`Delete ${integration?.name}`} placement="top">
            <IconButton color="error"
               onClick={() => onDeleteRequest(item, index)}
            >
               <DeleteOutlined />
            </IconButton>
         </Tooltip>
      </ListItem>
   )
}


const DeleteConfirmation = ({ onConfirm, onClose, ...props }: any) => {
   return (
      <Dialog maxWidth={'xs'} {...props} onClose={onClose}>
         <DialogTitle>Delete Integration</DialogTitle>
         <DialogClose onClose={onClose} />
         <DialogContent dividers>
            Are you sure you want to delete this integration?
         </DialogContent>
         <DialogActions>
            <Button onClick={onClose} color="primary">
               Cancel
            </Button>
            <Button onClick={onConfirm} variant="contained" color="error">
               Delete
            </Button>
         </DialogActions>
      </Dialog>
   )
}