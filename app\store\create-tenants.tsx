import { create } from "zustand";
import { persist } from 'zustand/middleware';

type WindowTypes = 'customer' | 'orgDetails' | 'subscriptions'

type Subscriptions = {
   category: Record<string, any>
   tier: Record<string, any>
   startDate?: string
   endDate?: string
}

interface CreateTenants {
   move: (type: 'next' | 'prev') => void
   jump: (step: number) => void
   step: number
   formData: {
      customer: {
         name: string
         companyUrl: string
         industry: string
         line1: string
         line2?: string
         city: string
         zip: string
         state: string
         country: string

      },
      orgDetails: {
         firstName: string,
         lastName: string,
         email: string,
         phone: string,
         role: string
         emails: Array<string>
      },
      subscriptions: Subscriptions[]
   },
   defaultValues?: CreateTenants
   setValues: (window: WindowTypes, values: Record<string, any>) => void

   setSubscriptions: (item: Subscriptions) => void
   deleteSubscription: (item: Subscriptions) => void

   reset: () => void;
}

const STORE_KEY: string = 'create-tenant';

const DEFAULT_VALUES: any = {
   step: 0,
   formData: {
      customer: {
         name: '',
         companyUrl: '',
         industry: '',
         line1: '',
         line2: '',
         city: '',
         zip: '',
         state: '',
         country: '',
      },
      orgDetails: {
         firstName: '',
         lastName: '',
         email: '',
         phone: '',
         role: '',
         emails: []
      },
      subscriptions: []
   }
}

const useCreateTenant = create(
   persist<CreateTenants>(
      (set) => ({
         reset: () => {
            set(DEFAULT_VALUES)
         },
         step: DEFAULT_VALUES.step,
         move: (type) => {
            set(({ step }) => {
               return ({ step: type === 'next' ? step + 1 : step - 1 })
            })
         },
         jump: (step) => {
            set((prevState) => (
               // hard move, setting the index what you are passing
               { ...prevState, step }
            ))
         },

         formData: DEFAULT_VALUES.formData,
         defaultValues: DEFAULT_VALUES,
         setValues: (type, values) => {
            set((prevState: any) => ({
               ...prevState,
               formData: { ...prevState.formData, [type]: values }
            }));
         },

         setSubscriptions: (item: Subscriptions) => {
            set((prevState) => ({
               ...prevState,
               formData: {
                  ...prevState.formData,
                  subscriptions: [
                     ...prevState.formData.subscriptions,
                     item
                  ]
               }
            }));
         },

         deleteSubscription: (item: Subscriptions) => {

            set((prevState) => ({
               ...prevState,
               formData: {
                  ...prevState.formData,
                  subscriptions: (() => {
                     return prevState.formData?.subscriptions?.filter(({ category }) => category?.id !== item.category?.id)
                  })()
               }
            }));
         }
      }),
      {
         name: `${STORE_KEY}`,
      }
   )
);

export default useCreateTenant;