import { createContext, useContext, useEffect, useMemo, useState } from "react";

import { CopyOutlined } from "@ant-design/icons";
import {
  Chip,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  ModalProps,
  Stack,
  TableCell,
  TableRow,
  Typography,
  Tooltip,
} from "@mui/material";
import moment from "moment";
import CopyToClipboard from "react-copy-to-clipboard";
import { useGetLog } from "hooks/api/log/useGetLog";
import { useDate } from "hooks/useDate";
import { useStatus } from "hooks/useStatus";

import Collapse from "components/@extended/Accordian";
import { CodeBlock } from "components/@extended/Codeblock";
import ExpandedTable from "components/@extended/Table/expanded-table";
import { DialogClose } from "components/@extended/dialog";

interface APILogsDetailsProps extends Omit<ModalProps, "children"> {
  selected: Record<string, any>;
}

const LogDetailsContext = createContext<Record<string, any> | null>(null);

const LogDetailsProvider = ({ children, value }: Record<string, any>) => {
  return (
    <LogDetailsContext.Provider value={value}>
      {children}
    </LogDetailsContext.Provider>
  );
};

const useLogDetails = (): any => {
  try {
    return useContext(LogDetailsContext);
  } catch (error) {
    console.error("should wrapped under the <LogDetailsProvider />");
  }
};

export const APILogsDetails = ({ onClose, ...props }: APILogsDetailsProps) => {
  const { selected } = props;

  const requestId = selected?.id;
  const [tooltipText, setTooltipText] = useState("Copy");

  const handleCopy = () => {
    setTooltipText("Copied!");
    setTimeout(() => setTooltipText("Copy"), 2000);
  };

  const { resolveStatusColor, resolveStatus } = useStatus();

  return (
    <Dialog fullScreen onClose={onClose} {...props}>
      <DialogTitle component={"div"}>
        <Stack justifyContent={"space-between"} direction={"row"}>
          <Stack alignItems={"flex-start"} gap={1}>
            <Stack direction="row" spacing={1} alignItems="center">
              <Typography variant="h6" sx={{ fontWeight: "bold" }}>
                {"Request Id:"}
              </Typography>

              <CopyToClipboard text={requestId} onCopy={handleCopy}>
                <Tooltip title={tooltipText} placement="right">
                  <Stack
                    direction={"row"}
                    spacing={1}
                    alignItems="center"
                    sx={{ cursor: "pointer" }}
                  >
                    <Typography variant="h6">{requestId ?? "-"}</Typography>
                    {requestId && <CopyOutlined />}
                  </Stack>
                </Tooltip>
              </CopyToClipboard>
            </Stack>

            <Chip
              size="small"
              variant="outlined"
              color={resolveStatusColor(selected?.state)}
              label={resolveStatus(selected?.state)}
            />
          </Stack>
        </Stack>
      </DialogTitle>
      <DialogClose onClose={onClose} />
      <DialogContent dividers>
        <Stack>
          <LogDetailsProvider value={{ logDetails: selected }}>
            <Overview />
          </LogDetailsProvider>
        </Stack>
      </DialogContent>
    </Dialog>
  );
};

const Summary = () => {
  const { logDetails } = useLogDetails();
  const { loadDate } = useDate();
  let durationInSeconds;

  // The getDateTime object contains startDateTime, endDateTime, and durationInSeconds values
  const getDateTime: {
    startDateTime: string;
    endDateTime: string;
    durationInSeconds: any;
  } = useMemo(() => {
    let time: any;

    if (
      logDetails &&
      logDetails.changeLog &&
      Object.keys(logDetails.changeLog).length === 2
    ) {
      // Destructure startDateTime and endDateTime from logDetails.changeLog
      let {
        changeLog: { startDateTime, endDateTime },
      } = logDetails;

      // Calculate the duration between the start and end times in milliseconds
      durationInSeconds = moment
        .duration(
          moment(endDateTime).diff(moment(startDateTime), "milliseconds")
        )
        .asMilliseconds();

      // Convert start and end date times to UTC format in a human-readable format
      startDateTime = moment(startDateTime).format("LLL") as any;
      endDateTime = moment(endDateTime).format("LLL") as any;

      // Build the final time object with formatted date-times and the calculated duration
      time = {
        ...time,
        startDateTime,
        endDateTime,
        durationInSeconds,
      };

      // Return the time object
      return time;
    }

    // If the condition is not met, return undefined or an empty object (based on your needs)
  }, [logDetails]); // Recompute when logDetails changes

  return (
    <Grid container columnSpacing={3}>
      <Grid item xs={6}>
        <Typography variant="h6" className="font-semibold">
          Resource Details
        </Typography>
        <Stack>
          <Grid container mt={2} rowGap={2}>
            <Grid item xs={3}>
              <Typography variant="body1">Name:</Typography>
            </Grid>
            <Grid item xs={9}>
              <Typography variant="body1">
                {logDetails?.resource?.type}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body1">Action:</Typography>
            </Grid>
            <Grid item xs={9}>
              <Typography variant="body1">
                {logDetails?.resource?.action?.type}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body1">End Point:</Typography>
            </Grid>
            <Grid item xs={9}>
              <Typography variant="body1" sx={{ wordWrap: "break-word" }}>
                {logDetails?.resource?.action?.apiDetails?.url}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body1">Method:</Typography>
            </Grid>
            <Grid item xs={9}>
              <Chip
                label={logDetails?.resource?.action?.apiDetails?.method}
                size="small"
                variant="outlined"
              />
            </Grid>
          </Grid>
        </Stack>
      </Grid>
      <Grid item xs={6}>
        <Typography variant="h6" className="font-semibold">
          Start Date Time & End Date Time
        </Typography>
        <Stack>
          <Grid container mt={2} rowGap={2}>
            <Grid item xs={3}>
              <Typography variant="body1">Start Date Time:</Typography>
            </Grid>
            <Grid item xs={9}>
              <Typography variant="body1">
                {loadDate(getDateTime?.startDateTime) ?? "-"}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body1">End Date Time:</Typography>
            </Grid>
            <Grid item xs={9}>
              <Typography variant="body1">
                {loadDate(getDateTime?.endDateTime) ?? "-"}
              </Typography>
            </Grid>
            <Grid item xs={3}>
              <Typography variant="body1">Duration:</Typography>
            </Grid>
            <Grid item xs={9}>
              <Typography variant="body1">
                {getDateTime?.durationInSeconds
                  ? `${getDateTime.durationInSeconds}ms`
                  : "-"}
              </Typography>
            </Grid>
          </Grid>
        </Stack>
      </Grid>
    </Grid>
  );
};

const InputResults = () => {
  const { logDetails } = useLogDetails(),
    { apiActivities } = useGetLog({ logDetails });

  return (
    <Grid container className="overflow-hidden" columnSpacing={2}>
      {apiActivities?.input.map((item: any) => {
        const { payloads = [] } =
          item?.activityTaskStartedEventAttributes?.input ?? {};
        return payloads?.map(({ data }: any, index: number) => {
          return (
            <Grid item xs={6} key={index}>
              <CodeBlock>
                {JSON.stringify(JSON.parse(atob(data)), null, 2)}
              </CodeBlock>
            </Grid>
          );
        });
      })}
      {apiActivities?.output.map((item: any) => {
        const { payloads = [] } =
          item?.activityTaskCompletedEventAttributes?.result ?? {};
        return payloads?.map(({ data }: any, index: number) => {
          return (
            <Grid item xs={6} key={index}>
              <CodeBlock>
                {JSON.stringify(JSON.parse(atob(data)), null, 2)}
              </CodeBlock>
            </Grid>
          );
        });
      })}
    </Grid>
  );
};

const TargetServerPayload = () => {
  const { logDetails } = useLogDetails(),
    { loadDate } = useDate(),
    { targetServerPayload: data } = useGetLog({ logDetails });

  const columns: any = useMemo<any[]>(
    () => [
      {
        accessorKey: "datetime",
        header: "Date & Time",
        cell({ row: { original: row } }: any) {
          return loadDate(row?.changeLog?.endDateTime);
        },
      },
      {
        accessorKey: "activityTaskStartedEventAttributes.activityType.name",
        header: "Activity Type",
      },
    ],
    []
  );

  const dataWithChildId: any = useMemo(() => {
    const withChildOrderId =
      Array.isArray(data) &&
      data.map((item, key) => {
        if (item.orderId % 2 === 1) {
          const child = data?.[key + 1] ?? undefined;
          return { ...item, child, key };
        } else {
          return { ...item, ["parent"]: item.orderId - 1, key };
        }
      });
    return withChildOrderId ?? [];
  }, [data]);

  const onlyOdd: any = useMemo(() => {
    return (
      Array.isArray(dataWithChildId) &&
      dataWithChildId.filter(({ orderId }) => {
        return orderId === 3;
      })
    );
  }, [dataWithChildId]);

  return (
    <ExpandedTable
      disablePagination
      data={onlyOdd}
      title="Target Inputs & Results"
      columns={columns}
      {...{
        getRowCanExpand: () => true,
        renderSubRow({ original }) {
          return (
            <TableRow>
              <TableCell colSpan={3}>
                <Activity data={original} activities={dataWithChildId} />
              </TableCell>
            </TableRow>
          );
        },
      }}
    />
  );
};

interface ActivityProps {
  data?: any;
  activities: any[];
}

const Activity = ({ data }: ActivityProps) => {
  const pairData = useMemo(() => data?.child, [data]);

  const [startedPayload, setStartedPayload] = useState([]);
  const [completedPayload, setCompletedPayload] = useState([]);

  useEffect(() => {
    const startedEvent = data?.activityTaskStartedEventAttributes?.input;

    if (startedEvent?.payloads) {
      const decodedStartedPayload = startedEvent.payloads.map(
        ({ metadata, data }: any) => ({
          metadata,
          data,
          decoded: atob(data.toString()),
        })
      );
      setStartedPayload(decodedStartedPayload);
    } else {
      setStartedPayload([]);
    }

    if (pairData) {
      const completedEvent =
        pairData?.activityTaskCompletedEventAttributes?.result;
      if (completedEvent && completedEvent?.payloads) {
        const decodedCompletedPayload = completedEvent.payloads.map(
          ({ metadata, data }: any) => ({
            metadata,
            data,
            decoded: atob(data.toString()),
          })
        );
        setCompletedPayload(decodedCompletedPayload);
      } else {
        setCompletedPayload([]);
      }
    }
  }, [data, pairData]);

  const renderPayload = (payload: any) => {
    return payload.length > 0
      ? payload.map(({ decoded }: any, key: number) => (
          <Grid xs={12} item key={key}>
            <CodeBlock>
              {JSON.stringify(JSON.parse(decoded), null, 2)}
            </CodeBlock>
          </Grid>
        ))
      : null;
  };

  return (
    <Grid container className="overflow-hidden" columnSpacing={2}>
      <Grid item xs={6}>
        <Typography variant="h6" sx={{ fontWeight: "bold" }}>
          Activity Task Started
        </Typography>
        {renderPayload(startedPayload)}
      </Grid>
      <Grid item xs={6}>
        <Grid container>
          <Typography variant="h6" sx={{ fontWeight: "bold" }}>
            Activity Task Completed
          </Typography>
          {renderPayload(completedPayload)}
        </Grid>
      </Grid>
    </Grid>
  );
};

const Overview = () => {
  return (
    <Stack gap={2}>
      <Collapse
        defaultExpanded={["summary", "input_results"]}
        items={[
          {
            title: "Summary",
            value: "summary",
            children: <Summary />,
          },
          {
            title: "Input and Results",
            value: "input_results",
            children: <InputResults />,
          },
        ]}
      />
      <TargetServerPayload />
    </Stack>
  );
};
