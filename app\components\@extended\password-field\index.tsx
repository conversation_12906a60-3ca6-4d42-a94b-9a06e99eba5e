import { RefObject, forwardRef, useState } from "react";

import {
   IconButtonProps,
   InputAdornment,
   InputAdornmentProps,
   OutlinedInput,
   OutlinedInputProps
} from "@mui/material";

import IconButton from "../IconButton";
import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";

type OutlinedInputExtendedProps = {
   inputAdornment?: InputAdornmentProps
   iconButtonButton?: IconButtonProps
} & OutlinedInputProps

const PasswordField = forwardRef((props: OutlinedInputExtendedProps, ref: any) => {

   const {
      inputAdornment = {},
      iconButtonButton = {},
   } = props;

   const [showPassword, setShowPassword] = useState<boolean>(false);

   const handleClickShowNewPassword = () => {
      setShowPassword(!showPassword);
   };

   const handleMouseDownPassword = (event: any) => {
      event.preventDefault();
   };

   return (
      <OutlinedInput
         type={showPassword ? 'text' : 'password'}
         endAdornment={
            <InputAdornment position="end" {...inputAdornment}>
               <IconButton
                  aria-label="toggle password visibility"
                  onClick={handleClickShowNewPassword}
                  onMouseDown={handleMouseDownPassword}
                  edge="end"
                  size="large"
                  color="secondary"
                  {...iconButtonButton}
               >
                  {showPassword ? <EyeOutlined /> : <EyeInvisibleOutlined />}
               </IconButton>
            </InputAdornment>
         }
         ref={ref}
         {...props}
      />
   )
});

export default PasswordField;