// Idle duration until warning dialog is shown: 15 minutes (in milliseconds)
export const IDLE_SESSION_DURATION_MS = 15 * 60 * 1000;

// Duration of warning dialog display: 5 minutes (in seconds)
export const SHOW_WARNING_DURATION_S = 5 * 60;

const IGNORED_EVENT_S = ['wheel', 'mousewheel', 'mousemove', 'visibilitychange'];

// Events to be tracked for user activity
export const ACTIVITY_EVENTS = [
	'mousemove',
	'keydown',
	'wheel',
	'mousewheel',
	'mousedown',
	'touchstart',
	'touchmove',
	'visibilitychange',
].filter(action => !IGNORED_EVENT_S.includes(action));

// What's the most often that the activity events should be tracked at?
export const ACTIVITY_DEBOUNCE_MS = 500;

// We should hit this api to keep user's session alive
export const REFRESH_API = '/api/console/session/refresh';