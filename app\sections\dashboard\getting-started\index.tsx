import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import {
  Box,
  Grid,
  Typography,
  Button,
  useTheme,
  useMediaQuery,
  Alert,
  AlertTitle,
  Skeleton 
} from '@mui/material';
import { State } from 'hooks/useStatus';
import { organizationClient } from 'services/organization.service';
import { serviceProfileClient } from 'services/service-profile.service';
import useUserDetails from 'store/user';
import { Service } from 'types/service';
import PageCard from 'components/cards/PageCard';
import CustomTabs, { TabItem } from 'components/getting-started/CustomTabs';
import MobileCustomTabs from 'components/getting-started/MobileCustomTabs';
import EnableConnectorsTab, { Connector } from 'components/getting-started/tabs/EnableConnectorsTab';
import MakeApiCallTab from 'components/getting-started/tabs/MakeApiCallTab';
import SetupCompleteTab from 'components/getting-started/tabs/SetupCompleteTab';




export default function GettingStartedDashboard() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [activeTab, setActiveTab] = useState(0);
  const [connectorData, setConnectorData] = useState<Connector[]>([]);
  const [connectorPagination, setConnectorPagination] = useState({
    total: 10, // In production, this comes from API
    limit: 4,
    offset: 0
  });

  // Track completion status for sections
  const [connectUIEnabled, setConnectUIEnabled] = useState(false);
  const [apiKeyGenerated, setApiKeyGenerated] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [dataLoadStatus, setDataLoadStatus] = useState({
    connectors: { loading: true, error: null as string | null },
    dockProfiles: { loading: true, error: null as string | null }
  });
  
  // Use ref to track if data has been loaded to prevent duplicate calls
  const hasInitializedRef = useRef(false);
  const loadingRef = useRef(false);
  
  // Storage keys - use constants to avoid typos
  const STORAGE_KEYS = {
    API_KEY: 'unizo_api_key',
    API_KEY_TIMESTAMP: 'unizo_api_key_timestamp',
    CONNECT_UI_CONFIG: 'unizo_connect_ui_config',
    CONNECT_UI_TIMESTAMP: 'unizo_connect_ui_timestamp'
  } as const;
  
  // Cache duration - 24 hours for API key validation
  const CACHE_DURATION = 24 * 60 * 60 * 1000;

  const { user } = useUserDetails();
  
  // Debug logging utility - disabled in production
  const debugLog = useCallback((message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[QuickStart] ${message}`, data);
    }
  }, []);

  // Helper function to check if cached data is still valid
  const isCacheValid = (timestampKey: string): boolean => {
    const timestamp = localStorage.getItem(timestampKey);
    if (!timestamp) return false;
    
    const age = Date.now() - parseInt(timestamp, 10);
    return age < CACHE_DURATION;
  };
  
  // Helper function to handle API errors with proper typing
  const handleApiError = (error: unknown, context: string): string => {
    const err = error as any;
    debugLog(`API Error in ${context}:`, error);
    
    if (err?.response?.status === 401) {
      return 'Authentication failed. Please log in again.';
    } else if (err?.response?.status === 403) {
      return 'You don\'t have permission to access this resource.';
    } else if (err?.response?.status >= 500) {
      return 'Server error. Please try again later.';
    } else if (err?.code === 'ECONNABORTED' || err?.message?.includes('timeout')) {
      return 'Request timed out. Please check your connection.';
    } else if (!navigator.onLine) {
      return 'No internet connection. Please check your network.';
    }
    
    return 'An unexpected error occurred. Please try again.';
  };
  
  // Fetch connectors with error handling
  const fetchConnectors = async (): Promise<{
    data: Connector[];
    total: number;
    error: string | null;
  }> => {
    try {
      if (!user?.organization?.id) {
        throw new Error('Organization ID not available');
      }
      
      const response = await serviceProfileClient.searchServices({
        filter: {
          and: [
            {
              property: "/state",
              operator: "=",
              values: [State.ACTIVE, State.IN_PROGRESS],
            },
            {
              property: "/organization/id",
              operator: "=",
              values: [user.organization.id],
            },
          ]
        },
        pagination: { limit: 50, offset: 0 }
      });
      
      const transformedData = response.data?.data?.map((item: Service) => {
        // Handle edge cases and determine configuration status
        const accessPointsSummary = item.accessPointsSummary;
        let isReadyToUse = true; // Default to ready if no access points summary
        
        if (accessPointsSummary) {
          // readyForConfiguration actually means "number of access points that NEED configuration"
          const needsConfigCount = accessPointsSummary.readyForConfiguration ?? 0;
          const totalCount = accessPointsSummary.totalAccessPointCount ?? 0;
          
          // Logic:
          // - Ready to use: when needsConfigCount === 0 (all access points are configured)
          // - Ready to use: when at least one is configured (totalCount - needsConfigCount > 0)
          // - Needs configuration: when needsConfigCount === totalCount (none are configured)
          
          if (totalCount === 0) {
            // No access points, ready to use
            isReadyToUse = true;
          } else if (needsConfigCount === totalCount) {
            // All access points need configuration
            isReadyToUse = false;
          } else {
            // At least one access point is configured (totalCount - needsConfigCount > 0)
            isReadyToUse = true;
          }
        }
        
        return {
          id: item.serviceProfile.id,
          serviceId: item.id, // Add the actual service ID
          name: item.name,
          icon: item.serviceProfile.image.small,
          type: item.type,
          typeLabel: item.type,
          category: item.type,
          requiresConfiguration: !isReadyToUse,
          isConfigured: isReadyToUse
        };
      }) || [];
      
      return {
        data: transformedData,
        total: response?.data?.pagination?.total || transformedData.length,
        error: null
      };
    } catch (error) {
      const errorMessage = handleApiError(error, 'fetchConnectors');
      return { data: [], total: 0, error: errorMessage };
    }
  };
  
  
  // Check Connect UI configuration
  const checkConnectUIConfig = async (): Promise<{
    isEnabled: boolean;
    error: string | null;
  }> => {
    try {
      // First check localStorage cache
      const cachedConfig = localStorage.getItem(STORAGE_KEYS.CONNECT_UI_CONFIG);
      if (cachedConfig && isCacheValid(STORAGE_KEYS.CONNECT_UI_TIMESTAMP)) {
        return { isEnabled: true, error: null };
      }
      
      if (!user?.organization?.id) {
        throw new Error('Organization ID not available');
      }
      
      // Fetch dock profiles to check if Connect UI is configured
      const response = await organizationClient.searchOrganizationWatches({
        filter: {
          and: [
            {
              property: `/organization/id`,
              operator: "=",
              values: [user.organization.id]
            }
          ]
        },
        pagination: { limit: 50, offset: 0 }
      });
      
      const dockProfiles = response?.data?.data || [];
      const isEnabled = dockProfiles.length > 0;
      
      if (isEnabled) {
        localStorage.setItem(STORAGE_KEYS.CONNECT_UI_CONFIG, 'true');
        localStorage.setItem(STORAGE_KEYS.CONNECT_UI_TIMESTAMP, Date.now().toString());
      }
      
      return { isEnabled, error: null };
    } catch (error) {
      const errorMessage = handleApiError(error, 'checkConnectUIConfig');
      return { isEnabled: false, error: errorMessage };
    }
  };
  
  // Main data loading function with parallel execution
  const preloadAllData = React.useCallback(async () => {
    // Prevent concurrent executions
    if (loadingRef.current) {
      return;
    }
    
    try {
      if (!user?.organization?.id) {
        if (debugLog) {
          debugLog('Skipping data load - no organization ID');
        }
        setIsInitialLoading(false);
        return;
      }
    } catch (error) {
      console.error('Error in preloadAllData:', error);
      setIsInitialLoading(false);
      return;
    }
    
    // Set loading flag
    loadingRef.current = true;
    
    if (debugLog) {
      debugLog('Starting parallel data fetch', { orgId: user.organization.id });
    }
    setLoadingError(null);
    setIsInitialLoading(true);
  
    try {
      // Execute all API calls in parallel for better performance
      const [connectorsResult, connectUIResult] = await Promise.allSettled([
        fetchConnectors(),
        checkConnectUIConfig()
      ]);
      
      // Process connectors result
      if (connectorsResult.status === 'fulfilled') {
        const { data, total, error } = connectorsResult.value;
        if (error) {
          setDataLoadStatus(prev => ({ ...prev, connectors: { loading: false, error } }));
        } else {
          setConnectorData(data);
          setConnectorPagination(prev => ({ ...prev, total }));
          setDataLoadStatus(prev => ({ ...prev, connectors: { loading: false, error: null } }));
        }
      } else {
        setDataLoadStatus(prev => ({ 
          ...prev, 
          connectors: { loading: false, error: 'Failed to load connectors' } 
        }));
      }
      
      
      // Process Connect UI result
      if (connectUIResult.status === 'fulfilled') {
        const { isEnabled, error } = connectUIResult.value;
        if (error) {
          setDataLoadStatus(prev => ({ ...prev, dockProfiles: { loading: false, error } }));
        } else {
          setConnectUIEnabled(isEnabled);
          setDataLoadStatus(prev => ({ ...prev, dockProfiles: { loading: false, error: null } }));
        }
      } else {
        setDataLoadStatus(prev => ({ 
          ...prev, 
          dockProfiles: { loading: false, error: 'Failed to check Connect UI status' } 
        }));
      }
      
      // Check for stored API key with cache validation
      const storedApiKey = localStorage.getItem(STORAGE_KEYS.API_KEY);
      if (storedApiKey && isCacheValid(STORAGE_KEYS.API_KEY_TIMESTAMP)) {
        setApiKeyGenerated(true);
      } else if (storedApiKey) {
        // Key exists but cache expired - you might want to validate with API
        if (debugLog) {
          debugLog('API key cache expired, consider revalidation');
        }
        setApiKeyGenerated(true); // For now, assume it's still valid
      }
      
      // Don't set loading error for non-critical failures
      // Users can still use the app with partial data
      
    } catch (error) {
      console.error('Critical error in preloadAllData:', error);
      setLoadingError('Failed to load initial data. Please refresh the page.');
    } finally {
      setIsInitialLoading(false);
      loadingRef.current = false;
      // Save successful load time
      if (!loadingError) {
        localStorage.setItem('unizo_quickstart_last_load', Date.now().toString());
      }
    }
  }, [user?.organization?.id, debugLog, STORAGE_KEYS.API_KEY, STORAGE_KEYS.API_KEY_TIMESTAMP, checkConnectUIConfig, fetchConnectors, isCacheValid, loadingError]); // Added missing dependencies

  // Load data only once on mount when user is available
  useEffect(() => {
    if (user?.organization?.id && !hasInitializedRef.current) {
      hasInitializedRef.current = true;
      try {
        preloadAllData();
      } catch (error) {
        console.error('Error loading initial data:', error);
        setIsInitialLoading(false);
        setLoadingError('Failed to load data. Please refresh the page.');
      }
    }
  }, [user?.organization?.id]); // Remove preloadAllData from deps to prevent loops
  
  // Refresh data when user navigates back to this page
  useEffect(() => {
    const handleFocus = () => {
      // Only refresh if it's been more than 5 minutes since last load and not currently loading
      if (!loadingRef.current) {
        try {
          const lastLoad = localStorage.getItem('unizo_quickstart_last_load');
          if (lastLoad) {
            const timeSinceLastLoad = Date.now() - parseInt(lastLoad, 10);
            if (timeSinceLastLoad > 5 * 60 * 1000) {
              if (debugLog) {
                debugLog('Refreshing data after 5 minutes');
              }
              hasInitializedRef.current = false; // Allow refresh
              preloadAllData();
            }
          }
        } catch (error) {
          console.error('Error checking last load time:', error);
        }
      }
    };
    
    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [preloadAllData, debugLog]);
  


  // Create dynamic tabs based on configuration status
  const tabs: TabItem[] = useMemo(() => {
    const hasConnectors = connectorData.length > 0;
    const isIntegrationComplete = apiKeyGenerated && connectUIEnabled;
    const allComplete = hasConnectors && isIntegrationComplete;

    return [
      {
        id: 0,
        number: "1",
        title: "Setup Connectors",
        helpText: "Configure connectors for workflows in your product​.",
        status: hasConnectors ? 'completed' : 'active',
      },
      {
        id: 1,
        number: "2",
        title: "Connect Unizo to your Product",
        helpText: "Enable secure API access and decide how authentication is handled in your application.",
        status: isIntegrationComplete ? 'completed' : (hasConnectors ? 'active' : 'pending'),
      },
      {
        id: 2,
        number: "3",
        title: "Setup Complete",
        helpText: "Your product is now ready to build workflow with Unizo.",
        status: allComplete ? 'active' : 'pending',
      }
    ];
  }, [connectUIEnabled, apiKeyGenerated, connectorData.length]);



  const handleLoadMoreConnectors = () => {
    // In production, this would make an API call with pagination params
    // For demo, we'll simulate loading more data
    setTimeout(() => {
      const newOffset = connectorPagination.offset + connectorPagination.limit;
      setConnectorPagination(prev => ({
        ...prev,
        offset: newOffset
      }));
      // In real app, append new data to connectorData
      // setConnectorData(prev => [...prev, ...newData]);
    }, 1000);
  };

  const handleNext = () => {
    if (activeTab < tabs.length - 1) {
      setActiveTab(activeTab + 1);
    }
  };

  const handlePrevious = () => {
    if (activeTab > 0) {
      setActiveTab(activeTab - 1);
    }
  };

  const renderTabContent = () => {
    try {
    const navigationProps = {
      onNext: handleNext,
      onPrevious: handlePrevious,
      isFirstTab: activeTab === 0,
      isLastTab: activeTab === tabs.length - 1,
    };

    switch (activeTab) {
      case 0:
        return (
          <EnableConnectorsTab
            connectors={connectorData}
            pagination={connectorPagination}
            onLoadMore={handleLoadMoreConnectors}
            isLoading={false} // In production, track loading state
            {...navigationProps}
            nextLabel="Continue"
            onRefreshConnectors={async () => {
              debugLog('Refreshing connectors after configuration update');
              const result = await fetchConnectors();
              if (!result.error) {
                setConnectorData(result.data);
                setConnectorPagination(prev => ({ ...prev, total: result.total }));
              }
            }}
          />
        );
      case 1:
        return (
          <MakeApiCallTab 
            onConnectUIStatusChange={(status) => {
              setConnectUIEnabled(status);
              if (status) {
                localStorage.setItem(STORAGE_KEYS.CONNECT_UI_CONFIG, 'true');
                localStorage.setItem(STORAGE_KEYS.CONNECT_UI_TIMESTAMP, Date.now().toString());
              } else {
                localStorage.removeItem(STORAGE_KEYS.CONNECT_UI_CONFIG);
                localStorage.removeItem(STORAGE_KEYS.CONNECT_UI_TIMESTAMP);
              }
            }}
            onApiKeyGenerated={(hasKey) => {
              setApiKeyGenerated(hasKey);
              if (hasKey) {
                localStorage.setItem(STORAGE_KEYS.API_KEY, 'true');
                localStorage.setItem(STORAGE_KEYS.API_KEY_TIMESTAMP, Date.now().toString());
              } else {
                localStorage.removeItem(STORAGE_KEYS.API_KEY);
                localStorage.removeItem(STORAGE_KEYS.API_KEY_TIMESTAMP);
              }
            }}
            {...navigationProps}
            nextLabel="Continue"
            previousLabel="Back"
          />
        );
      case 2:
        return (
          <SetupCompleteTab
            connectorsEnabled={connectorData.length > 0}
            apiKeyGenerated={apiKeyGenerated}
            connectUIEnabled={connectUIEnabled}
            {...navigationProps}
            previousLabel="Back"
          />
        );
      default:
        return null;
    }
    } catch (error) {
      console.error('Error rendering tab content:', error);
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography color="error">
            Error loading content. Please refresh the page.
          </Typography>
        </Box>
      );
    }
  };

  // Show skeleton loader during initial load
  if (isInitialLoading) {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Skeleton variant="text" width={200} height={40} />
          <Skeleton variant="text" width={400} height={20} sx={{ mt: 1 }} />
        </Grid>
        <Grid item xs={12}>
          <PageCard sx={{ p: 0 }}>
            <Box sx={{ p: 3 }}>
              <Skeleton variant="rectangular" height={100} sx={{ mb: 3 }} />
              <Grid container spacing={2}>
                {[1, 2, 3, 4].map(i => (
                  <Grid key={i} item xs={12} sm={6} md={3}>
                    <Skeleton variant="rectangular" height={160} />
                  </Grid>
                ))}
              </Grid>
            </Box>
          </PageCard>
        </Grid>
      </Grid>
    );
  }
  
  // Show error state if critical error occurred
  if (loadingError && !isInitialLoading) {
    return (
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Grid container>
            <Grid item xs={12}>
              <Typography variant="h4">Quick Start</Typography>
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12}>
          <PageCard>
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="h6" color="error" gutterBottom>
                {loadingError}
              </Typography>
              <Button 
                variant="contained" 
                onClick={() => window.location.reload()}
                sx={{ mt: 2 }}
              >
                Refresh Page
              </Button>
            </Box>
          </PageCard>
        </Grid>
      </Grid>
    );
  }

  return (
    <Grid container spacing={3}>
      {/* Header */}
      <Grid item xs={12}>
        <Grid container>
          <Grid item xs={12}>
            <Typography variant="h4">
              Quick Start
            </Typography>
          </Grid>
          <Grid item xs={12} lg={10}>
            <Typography variant="body1" className="font-normal">
              Follow these steps to quick start building integrations with Unizo.
            </Typography>
          </Grid>
        </Grid>
      </Grid>

      {/* Custom Tabs wrapped in PageCard */}
      <Grid item xs={12}>
        <PageCard sx={{ p: 0 }}>
          <Box sx={{ px: { xs: 2, sm: 3, md: 4 }, pb: { xs: 3, md: 4 }, pt: 0 }}>
            {/* Show non-critical errors as alerts */}
            {!isInitialLoading && Object.values(dataLoadStatus).some(s => s.error) && (
              <Alert severity="warning" sx={{ mb: 2 }}>
                <AlertTitle>Some data could not be loaded</AlertTitle>
                {dataLoadStatus.connectors.error && (
                  <Typography variant="body2">Connectors: {dataLoadStatus.connectors.error}</Typography>
                )}
                {dataLoadStatus.dockProfiles.error && (
                  <Typography variant="body2">Connect UI: {dataLoadStatus.dockProfiles.error}</Typography>
                )}
              </Alert>
            )}
            
            {isMobile ? (
              <MobileCustomTabs
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
              >
                {renderTabContent()}
              </MobileCustomTabs>
            ) : (
              <CustomTabs
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
              >
                {renderTabContent()}
              </CustomTabs>
            )}
          </Box>
        </PageCard>
      </Grid>
    </Grid>
  );
}

