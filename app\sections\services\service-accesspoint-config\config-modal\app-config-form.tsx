import { RefObject, useEffect, useMemo, useState } from "react";

import { <PERSON><PERSON>, Step, Step<PERSON><PERSON><PERSON>, Stepper } from "@mui/material";
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod";

import { Form, FormField, FormItem, FormControl } from 'components/@extended/Form'

import { useGetAccessPoint } from "hooks/api/use-accesspoint-config/useGetAccessPoint";
import useServiceConfig from "store/setup-service";

import { generateConfigFormSchema } from '../helper.js';
import fieldMap from './field-map.js'


type AppConfigFormProps = {
   segments: Record<string, any>[]
   formRef?: RefObject<HTMLFormElement>
}

export const AppConfigForm = (props: AppConfigFormProps) => {

   const { segments, formRef } = props;

   const {
      selectedService,
      selectedTypeConfig,
      resetSelectedTypeConfig
   } = useServiceConfig();

   const [formState, setFormState] = useState();

   const { attemptUpdateAccessPoints } = useGetAccessPoint({
      serviceId: selectedService?.service?.id,
      serviceProfileId: selectedService?.id
   });

   const {
      defaultValues,
      buildValidationSchema,
      parsePayload
   } = useMemo(() => {
      return generateConfigFormSchema(
         segments, selectedTypeConfig?.accessPoint
      );
   }, [selectedTypeConfig, segments])

   const form = useForm<typeof defaultValues>({
      defaultValues,
      resolver: zodResolver(buildValidationSchema().all)
   });

   // console.log(segments,"selectedAppTypeConfigselectedAppTypeConfig")
   const { reset, watch } = form;

   useEffect(() => {
      reset(defaultValues)
   }, [defaultValues])

   const onSubmit = (data: any) => {
      const filtered = buildValidationSchema().filtered.parse(data)
      const payload: Record<string, any>[] = parsePayload(filtered);

      const formData = new FormData();
      formData.append('operations', JSON.stringify(payload)?.toString());

      if (selectedTypeConfig) {
         attemptUpdateAccessPoints(
            selectedTypeConfig.accessPoint.service.id,
            selectedTypeConfig.accessPoint.id,
            formData,
            () => {
               // closing the modal box
               resetSelectedTypeConfig()
            }
         )
      } else {
         console.error('Missing selected service-access-type id')
      }
   }

   useEffect(() => {
      let subscription: any;

      subscription = watch((values) => {
         setFormState(values);
      });

      return () => {
         subscription?.unsubscribe();
      };

   }, [watch])
   return (
      <Stack>
         <Form {...form}>
            <form
               ref={formRef}
               onSubmit={(...args) => (
                  void form.handleSubmit(onSubmit)(...args)
               )}
            >
               <Stepper activeStep={0} alternativeLabel>
                  {segments.map((item: any, index: number) => {
                     return (
                        <Step key={index} >
                           {segments?.length >= 2 ? (
                              <StepLabel>{item?.label}</StepLabel>
                           ) : null}
                           <FormWindow
                              form={form}
                              item={item}
                              formState={formState?.[item?.key] ?? {}}
                           />
                        </Step>
                     )
                  })}
               </Stepper>
            </form>
         </Form>
      </Stack>
   )
}

const FormWindow = ({ item, form, formState }: any) => {

   const {
      selectedTypeConfig,
   } = useServiceConfig()

   const key = item?.key;
   const fieldTypeConfigs = item?.fieldTypeConfigs ?? [];

   return (
      <Stack gap={4} mt={2}>

         <Stack gap={3}>
            {fieldTypeConfigs.map((config: any, index: number) => {
               const fieldName = `${key}.${config?.property}`;

               return (
                  <FormField
                     control={form.control}
                     name={fieldName}
                     key={index}
                     render={({ field }) => {

                        const inputProps = {
                           placeholder: config?.placeholder || config?.label,
                           fullWidth: true,
                           property: config?.property,
                           formState,
                           ...field,
                        },
                           Field = fieldMap({ selectedTypeConfig })[config?.type].getField(inputProps);

                        return (
                           <FormItem
                              className="w-full"
                              label={config?.label}
                              description={config?.description}
                           >
                              <FormControl>
                                 {Field}
                              </FormControl>
                           </FormItem>
                        )
                     }}
                  />
               )
            })}
         </Stack>
      </Stack>
   )
}