# Additional Fields Tab 📋

## Overview

The Additional Fields tab provides a powerful interface for creating custom field definitions that can be used in field mappings. This feature allows you to define any data structure you need, with full support for complex types, nested objects, arrays, and validation rules.

## 🎯 Key Features

### 1. **Comprehensive Data Type Support**
- **String** 📄 - Text values with format options (email, URL, UUID, date-time, password)
- **Number** 🔢 - Numeric values (integer, float, decimal) with min/max validation
- **Boolean** 🔀 - True/false values with default options
- **Date** 📅 - Date and time values with various formats
- **Array** 📊 - Lists of values with configurable item types
- **Object** 📦 - Complex nested structures for hierarchical data

### 2. **Advanced Field Configuration**
- **Display Names** - Human-readable labels separate from field names
- **Descriptions** - Document field purpose and usage
- **Required Fields** - Mark fields as mandatory
- **Default Values** - Set fallback values when field is not provided
- **Enum Values** - Define allowed values for string fields
- **Validation Rules** - Set constraints like min/max length, patterns, and ranges

### 3. **Hierarchical Structure Support**
- **Nested Objects** - Create complex data structures with multiple levels
- **Object Arrays** - Define arrays containing objects with their own fields
- **Parent-Child Relationships** - Visual hierarchy with indentation and connection lines
- **Expandable/Collapsible** - Clean interface for managing complex structures

### 4. **Visual Field Management**
- **Interactive Cards** - Each field displayed as an interactive card
- **Type Icons & Colors** - Visual indicators for quick field type identification
- **Inline Actions** - Edit, delete, and add child fields with one click
- **Drag & Drop** - (Future) Reorder fields easily

### 5. **Export & Import**
- **JSON Schema Export** - Download field definitions as JSON
- **Schema Import** - (Future) Load previously saved field configurations
- **Version Control** - Track changes with timestamps

## 🚀 Use Cases

### 1. **E-commerce Integration**
```javascript
// Custom product structure
{
  product_id: string (required),
  sku: string,
  pricing: object {
    base_price: number (min: 0),
    discount_percentage: number (min: 0, max: 100),
    tax_rate: number,
    currency: string (enum: ["USD", "EUR", "GBP"])
  },
  inventory: object {
    quantity: number (min: 0),
    warehouse_locations: array of string,
    reorder_level: number
  },
  attributes: array of object {
    name: string,
    value: string,
    display_order: number
  }
}
```

### 2. **User Management System**
```javascript
// Custom user profile
{
  user_id: string (format: uuid, required),
  email: string (format: email, required),
  profile: object {
    first_name: string (required),
    last_name: string (required),
    phone: string (pattern: "^\+?[1-9]\d{1,14}$"),
    date_of_birth: date,
    preferences: object {
      language: string (enum: ["en", "es", "fr"]),
      timezone: string,
      notifications: object {
        email: boolean (default: true),
        sms: boolean (default: false),
        push: boolean (default: true)
      }
    }
  },
  roles: array of string (enum: ["admin", "user", "moderator"]),
  metadata: object // Flexible key-value pairs
}
```

### 3. **Event Tracking**
```javascript
// Custom event structure
{
  event_id: string (format: uuid),
  event_type: string (required, enum: ["click", "view", "purchase", "signup"]),
  timestamp: date (format: date-time, required),
  user_id: string,
  session_id: string,
  properties: object {
    page_url: string (format: url),
    referrer: string (format: url),
    device_type: string (enum: ["mobile", "tablet", "desktop"]),
    browser: string,
    location: object {
      country: string (maxLength: 2),
      city: string,
      latitude: number (min: -90, max: 90),
      longitude: number (min: -180, max: 180)
    }
  },
  custom_data: object // Flexible for any additional data
}
```

## 🎨 How It Works

### Creating a Field

1. **Click "Add Field"** button in the top-right corner
2. **Fill Basic Information**:
   - Field Name: `customer_score` (snake_case)
   - Display Name: `Customer Score`
   - Description: `Calculated score based on purchase history`

3. **Configure Type**:
   - Select `Number` from type dropdown
   - Set format to `integer`
   - Add validation: min: 0, max: 100

4. **Set Additional Options**:
   - Mark as required: ✓
   - Default value: 50
   - Save the field

### Creating Nested Structures

1. **Create Parent Object**:
   - Name: `shipping_address`
   - Type: `Object`

2. **Add Child Fields**:
   - Click `+` button on the parent object card
   - Add fields: `street`, `city`, `zip_code`, `country`

3. **Configure Each Child**:
   - Set appropriate types and validation
   - Mark required fields

### Using Fields in Mappings

Once created, these additional fields become available in the Field Mappings tab:

1. **Source Selection**: Choose from provider fields
2. **Target Selection**: Your additional fields appear alongside predefined fields
3. **Custom Badge**: Additional fields are marked with a special indicator
4. **Type Validation**: Ensures compatible field type mapping

## 🔧 Technical Implementation

### Field Structure
```typescript
interface AdditionalField {
  id: string;
  name: string;
  displayName: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  description?: string;
  required: boolean;
  defaultValue?: any;
  children?: AdditionalField[];
  parentId?: string;
  arrayItemType?: string;
  format?: string;
  enum?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    minLength?: number;
    maxLength?: number;
  };
}
```

### Integration with Field Mappings

The Additional Fields are integrated with the mapping system:

```typescript
// In HierarchicalFieldMapping component
const allTargetFields = [
  ...predefinedTargetFields,
  ...additionalFields, // Fields created in Additional Fields tab
  ...customFields      // Fields created inline during mapping
];
```

## 💡 Best Practices

### 1. **Naming Conventions**
- Use `snake_case` for field names
- Use clear, descriptive display names
- Add descriptions for complex fields

### 2. **Type Selection**
- Choose the most specific type for your data
- Use enums for fields with limited options
- Consider using objects for grouped related data

### 3. **Validation Rules**
- Set appropriate min/max values for numbers
- Use regex patterns for formatted strings
- Always validate critical business fields

### 4. **Structure Design**
- Keep nesting levels reasonable (max 3-4 levels)
- Use arrays for repeating data structures
- Create reusable object structures

### 5. **Documentation**
- Document field purpose in descriptions
- Explain validation rules and constraints
- Provide examples in field descriptions

## 🚦 Field Type Guidelines

### String Fields
- Use for: IDs, names, descriptions, codes
- Formats: email, URL, UUID, date-time, password
- Validation: length, pattern matching

### Number Fields
- Use for: quantities, prices, scores, measurements
- Formats: integer, float, decimal
- Validation: min/max range

### Boolean Fields
- Use for: flags, toggles, yes/no decisions
- Default: typically false for safety
- Examples: is_active, has_permission

### Date Fields
- Use for: timestamps, deadlines, birthdates
- Formats: date, time, date-time
- Consider timezone requirements

### Array Fields
- Use for: lists, collections, multiple values
- Define item type (string, number, object)
- Consider max items if needed

### Object Fields
- Use for: grouped data, nested structures
- Can contain any other field types
- Support unlimited nesting

## 🎯 Benefits

1. **Flexibility** - Create any field structure your business needs
2. **Reusability** - Define once, use in multiple mappings
3. **Validation** - Ensure data quality with built-in constraints
4. **Documentation** - Self-documenting with descriptions and examples
5. **Export/Import** - Share field definitions across projects
6. **Type Safety** - Prevent invalid mappings with type checking

The Additional Fields tab empowers you to define exactly the data structures you need for your integration, ensuring perfect alignment with your business requirements! 🚀