/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
// import Welcome from './welcome';
import CategorySelection from './category';
import ServiceSelection from './service';
import Webhook from './web-hook';
import { OnboardingStep } from 'types/tenant-metadata';
import InviteUser from './invite-user';
import StartupProgram from './startup-program';
import GetStarted from './get-started';

type ComponentMap = {
   label: string
   description?: string,
   component: any
   meta: {
      custom: boolean
   },
   ref?: React.RefObject<HTMLFormElement>
}

interface StepComponentProps { currentStep: OnboardingStep }

enum OnboardingStepType {
   SelectCategory = 'SELECT_CATEGORY',
   SelectProviders = 'SELECT_PROVIDERS',
   SetupWebhooks = 'SETUP_WEBHOOKS',
   SelectMembers = 'SELECT_MEMBERS',
   StartupProgram = 'STARTUP_PROGRAM',
   General='GENERAL',
   Success = 'SUCCESS',
}

const ComponentMap = {
   [OnboardingStepType.SelectCategory]: {
      component: CategorySelection
   },
   [OnboardingStepType.SelectProviders]: {
      component: ServiceSelection
   },
   [OnboardingStepType.SetupWebhooks]: {
      component: Webhook
   },
   [OnboardingStepType.SelectMembers]: {
      component: InviteUser
   },
   [OnboardingStepType.StartupProgram]: {
      component: StartupProgram
   },
   [OnboardingStepType.General]: {
      component: StartupProgram
   },
   [OnboardingStepType.Success]: {
      component: GetStarted
   },
}

export default ComponentMap;

export type { ComponentMap, StepComponentProps }
