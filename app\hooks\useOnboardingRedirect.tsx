import { useEffect } from 'react';
import { useNavigate } from '@remix-run/react';
import useOnboardingStatus from './useOnboardingStatus';
import useOnboardingStore from 'store/onboarding';
import { toast } from 'sonner';

interface UseOnboardingRedirectOptions {
  orgId?: string;
  redirectPath?: string;
  showToast?: boolean;
  autoOpenDialog?: boolean;
}

/**
 * Hook that checks onboarding completion status and can redirect or open dialog
 * Use this hook on pages where you want to ensure users have completed onboarding
 */
export const useOnboardingRedirect = ({
  orgId,
  redirectPath = '/console/dashboard',
  showToast = true,
  autoOpenDialog = false,
}: UseOnboardingRedirectOptions = {}) => {
  const navigate = useNavigate();
  const { openOnboarding } = useOnboardingStore();
  
  const { 
    isLoading, 
    isComplete, 
    pendingSteps,
    hasCategories,
    hasServices,
    hasWebhooks 
  } = useOnboardingStatus(orgId);

  useEffect(() => {
    if (!isLoading && !isComplete) {
      // Core onboarding steps are not complete
      if (!hasCategories || !hasServices || !hasWebhooks) {
        if (showToast) {
          const missingSteps = [];
          if (!hasCategories) missingSteps.push('categories');
          if (!hasServices) missingSteps.push('services');
          if (!hasWebhooks) missingSteps.push('webhooks');
          
          toast.info(
            `Please complete your setup: ${missingSteps.join(', ')}`,
            {
              duration: 5000,
              action: {
                label: 'Complete Setup',
                onClick: () => openOnboarding(),
              },
            }
          );
        }

        if (autoOpenDialog) {
          // Auto-open onboarding dialog after a short delay
          setTimeout(() => {
            openOnboarding();
          }, 500);
        } else if (redirectPath) {
          // Redirect to specified path
          navigate(redirectPath);
        }
      }
    }
  }, [
    isLoading, 
    isComplete, 
    hasCategories, 
    hasServices, 
    hasWebhooks,
    showToast,
    autoOpenDialog,
    redirectPath,
    navigate,
    openOnboarding
  ]);

  return {
    isOnboardingComplete: isComplete,
    isCheckingOnboarding: isLoading,
    pendingOnboardingSteps: pendingSteps,
  };
};

export default useOnboardingRedirect;