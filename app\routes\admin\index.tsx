import { type MetaFunction } from "@remix-run/node";
import { ClientOnly } from 'remix-utils/client-only';
import {
   Outlet, json,
} from "@remix-run/react";
import DashboardLayout from "layout/Dashboard";
import { ConfigProvider } from "contexts/ConfigContext";
import ThemeCustomization from "themes";

export const meta: MetaFunction = () => {
   return [
      { title: "Unizo" },
      { name: "description", content: "Welcome to Remix!" },
   ];
};

export const loader = async ({ request }: Record<string, any>) => {
   // TODO: handle admin
   return json({});
};


export default function Index() {

   return (
      <ClientOnly fallback={null}>
         {() => (
            <ConfigProvider>
               <ThemeCustomization>
                  <DashboardLayout>
                     <Outlet />
                  </DashboardLayout>
               </ThemeCustomization>
            </ConfigProvider>
         )
         }
      </ClientOnly >
   );
}
