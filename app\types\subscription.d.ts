import { ChargeRecurringTypeEnum } from "constants/subscription.constant"
import { Organization } from "./service"

export interface Subscription {
   href: string
   type: string
   id: string
   name: string
   description: string
   state: string
   startDate: string
   cancelledDate: string
   productType: string
   productKey: string
   product: Product
   charge: Charge
   entitlements: Entitlement[]
   organization: Partial<Organization>
   changeLog: ChangeLog
 }

 export interface Product {
   code: string
   id: string
   name: string
 }

 export interface Charge {
   pricingTier: PricingTier
   type: ChargeRecurringTypeEnum
   price: number
   currency: string
 }

 export interface PricingTier {
   type: string
   id: string
   name: string
 }

 export interface Entitlement {
   id: string
   type: string
   assistanceInfo: AssistanceInfo
   displayName: string
   apiRateLimit?: ApiRateLimit
   customerCount?: CustomerCount
   webhookSupport?: WebhookSupport
   serviceAgreement?: ServiceAgreement
   logRetention?: LogRetention
 }

 export interface AssistanceInfo {
   promotionEligibility: boolean
 }

 export interface ApiRateLimit {
   type: string
   displayName: string
   callsPerMonth: number
 }

 export interface CustomerCount {
   type: string
   value: number
   displayName: string
 }

 export interface WebhookSupport {
   isEnabled: string
   displayName: string
 }

 export interface ServiceAgreement {
   type: string
   displayName: string
 }

 export interface LogRetention {
   type: string
   displayName: string
   countInDays: number
 }


 export interface ChangeLog {
   createdDateTime: string
   lastUpdatedDateTime: string
 }
