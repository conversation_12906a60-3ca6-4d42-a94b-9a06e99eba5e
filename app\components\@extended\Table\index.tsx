
// material-ui
import Paper from '@mui/material/Paper';
import TablePrimitive from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { Box, Divider } from '@mui/material';

// third-party
import { flexRender, useReactTable, getCoreRowModel, getPaginationRowModel } from '@tanstack/react-table';
import { TablePagination } from 'components/third-party/react-table';

// project import
import ScrollX from 'components/ScrollX';
import MainCard from 'components/MainCard';

import Empty from './Empty';
import { extractColumnMeta, extractHeaderColumnMeta, getPaginationTotal } from './table.helper.js'
import { TableProps } from './type.js';
import SimpleBarScroll from 'components/third-party/SimpleBar';

export default function Table<TData>(props: TableProps<TData>) {

   const {
      striped = false,
      data = [],
      columns = [],
      title = null,

      // pagination
      totalData = 0,
      paginationPosition = 'bottom',
      manualPagination = true,
      disablePagination = false,

      // row-props
      rowClickable = false,
      onRowClick,
      ...rest
   } = props;

   const table = useReactTable({
      data,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      manualPagination,
      pageCount: getPaginationTotal(totalData, rest?.state?.pagination?.pageSize),
      ...rest,
   });

   return (
      <MainCard
         content={false}
         title={title}
      >
         <ScrollX>
            <TableContainer component={Paper}>
               <SimpleBarScroll
                  sx={{
                     '& .simplebar-content':
                        { display: 'flex', flexDirection: 'column' },
                  }}
               >
                  <TablePrimitive>
                     <TableHead>
                        {table.getHeaderGroups().map((headerGroup) => (
                           <TableRow key={headerGroup.id}>
                              {headerGroup?.headers.map((header) => (
                                 <TableCell
                                    key={header.id}
                                    {...extractHeaderColumnMeta(header)}
                                 >
                                    {header?.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                                 </TableCell>
                              ))}
                           </TableRow>
                        ))}
                     </TableHead>
                     <TableBody {...(striped && { className: 'striped' })}>
                        {data?.length ? (
                           table.getRowModel().rows.map((row) => (
                              <TableRow
                                 onClick={() => (
                                    onRowClick && onRowClick(row)
                                 )}
                                 className={`${rowClickable ? 'cursor-pointer' : ''}`}
                                 key={row.id}
                              >
                                 {row.getVisibleCells().map((cell: any) => {
                                    return (
                                       <TableCell
                                          key={cell.id}
                                          {...extractColumnMeta(
                                             cell,
                                          )}
                                       >
                                          {flexRender(cell?.column?.columnDef?.cell, cell?.getContext())}
                                       </TableCell>
                                    )
                                 })}
                              </TableRow>
                           ))
                        ) : <Empty colSpan={columns?.length} />}
                     </TableBody>
                  </TablePrimitive>
               </SimpleBarScroll>
            </TableContainer>

            {/* pagination block */}
            {!disablePagination ? (
               paginationPosition !== 'top' && (
                  <>
                     <Divider />
                     <Box sx={{ p: 2 }}>
                        <TablePagination
                           {...{
                              setPageSize: table.setPageSize,
                              setPageIndex: table.setPageIndex,
                              getState: table.getState,
                              getPageCount: table.getPageCount
                           }}
                        />
                     </Box>
                  </>
               )
            ) : null}

         </ScrollX>
      </MainCard>
   )
}