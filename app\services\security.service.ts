import { API_ENDPOINTS } from "utils/api/api-endpoints";
import fetchInstance from "utils/api/fetchinstance";



export const logProtectionClient = {

   searchLogProtections: (orgId: string) => {
      return fetchInstance.get(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.LOG_PROTECTION_S}`);
   },

   searchMonitoringServices: (orgId: string, logProtectionId: string, payload: any) => {
      return (
         fetchInstance.post((
            `${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.LOG_PROTECTION_S}/${logProtectionId}/${API_ENDPOINTS.SERVICE}s/search`
         ),
            payload,
         )
      );
   },

   // create
   createLogProtection: (orgId: string, payload: Record<string, any>) => {
      return fetchInstance.post(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.LOG_PROTECTION_S}`, payload);
   },

   // update
   updateLogProtection: (orgId: string, logProtectionId: string, payload: Record<string, any>) => {
      return fetchInstance.patch(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.LOG_PROTECTION_S}/${logProtectionId}`, payload);
   },

   // create integration
   createIntegration: (orgId: string, logProtectionId: string, payload: Record<string, any>) => {
      return fetchInstance.post(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.LOG_PROTECTION_S}/${logProtectionId}/${API_ENDPOINTS.INTEGRATIONS}`, payload);
   },

   getIntegration: (orgId: string, logProtectionId: string, id: string) => {
      return fetchInstance.get(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.LOG_PROTECTION_S}/${logProtectionId}/${API_ENDPOINTS.INTEGRATIONS}/${id}`);
   },

   updateIntegration: (orgId: string, logProtectionId: string, id: string, payload: Record<string, any>) => {
      return fetchInstance.patch(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.LOG_PROTECTION_S}/${logProtectionId}/${API_ENDPOINTS.INTEGRATIONS}/${id}`, payload);
   },

   // test integration
   testIntegration: (payload: Record<string, any>[]) => {
      return fetchInstance.post(`${API_ENDPOINTS.INTEGRATIONS}/validate`, payload)
   }
};

export const keyProtectionClient = {

   searchKeyProtections: (orgId: string) => {
      return fetchInstance.get(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.KEY_PROTECTION_S}`);
   },

   searchMonitoringServices: (orgId: string, keyProtectionId: string, payload: any) => {
      return (
         fetchInstance.post((
            `${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.KEY_PROTECTION_S}/${keyProtectionId}/${API_ENDPOINTS.SERVICE}s/search`
         ),
            payload,
         )
      );
   },

   // create
   createKeyProtection: (orgId: string, payload: Record<string, any>) => {
      return fetchInstance.post(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.KEY_PROTECTION_S}`, payload);
   },

   // update
   updateKeyProtection: (orgId: string, keyProtectionId: string, payload: Record<string, any>) => {
      return fetchInstance.patch(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.KEY_PROTECTION_S}/${keyProtectionId}`, payload);
   },

   // create integration
   createIntegration: (orgId: string, keyProtectionId: string, payload: Record<string, any>) => {
      return fetchInstance.post(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.KEY_PROTECTION_S}/${keyProtectionId}/${API_ENDPOINTS.INTEGRATIONS}`, payload);
   },

   getIntegration: (orgId: string, keyProtectionId: string, id: string) => {
      return fetchInstance.get(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.KEY_PROTECTION_S}/${keyProtectionId}/${API_ENDPOINTS.INTEGRATIONS}/${id}`);
   },

   updateIntegration: (orgId: string, keyProtectionId: string, id: string, payload: Record<string, any>) => {
      return fetchInstance.patch(`${API_ENDPOINTS.ORGANIZATION}s/${orgId}/${API_ENDPOINTS.KEY_PROTECTION_S}/${keyProtectionId}/${API_ENDPOINTS.INTEGRATIONS}/${id}`, payload);
   },

   // test integration
   testIntegration: (payload: Record<string, any>[]) => {
      return fetchInstance.post(`${API_ENDPOINTS.INTEGRATIONS}/validate`, payload)
   }
};