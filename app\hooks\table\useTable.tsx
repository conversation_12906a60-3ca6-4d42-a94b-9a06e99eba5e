import { GridClasses, useTheme } from "@mui/material";
import { GridCallbackDetails, GridColDef, GridPaginationModel } from "@mui/x-data-grid";
import { DEFAULT_PAGINATION } from "constants/common";

import { useState } from "react";
import { ExtendedTablePagination } from "types/utils";

type UseTableReturnType = {
   generateHeaderRows: (items: GridColDef[]) => GridColDef[]
   baseStyles: (classes?: Partial<GridClasses>) => Partial<GridClasses>

   onPaginationModelChange?: ((model: GridPaginationModel, details: GridCallbackDetails<any>) => void) | undefined
   extendedProps?: any
   applyColumnSize: any,

   paginationModal: {
      pagination: ExtendedTablePagination,
      onPaginationChange: any
      setTotal: (total: number) => void
   }
}

const COLUMN_SIZE = {
   SM: {
      height: 50,
      py: 0,
   },
   MD: {
      height: 60,
      py: 0,
   },
   LG: {
      height: 70,
      py: 0,
   }
}


export function useTable<T>(): UseTableReturnType {

   const theme = useTheme();

   const [pagination, setPagination] = useState<any>(DEFAULT_PAGINATION)

   return {
      generateHeaderRows(items: GridColDef[]): GridColDef[] {
         const extendedRow = items.map((item) => {
            if (!item.width && !item.flex) {
               item['flex'] = 1;
            }
            return item;
         })
         return extendedRow;
      },
      baseStyles: (classes?: Partial<GridClasses>) => {
         return {
            ...classes,
         }
      },
      applyColumnSize() {
         return {
            SM: {
               muiTableBodyCellProps: () => {
                  return {
                     sx: {
                        ...COLUMN_SIZE.SM
                     },
                  }
               },
            },
            MD: {
               muiTableBodyCellProps: () => {
                  return {
                     sx: {
                        ...COLUMN_SIZE.MD
                     },
                  }
               },
            },
            LG: {
               muiTableBodyCellProps: () => {
                  return {
                     sx: {
                        ...COLUMN_SIZE.LG
                     },
                  }
               },
            }
         }
      },
      extendedProps: {
         state: {
            // showLoadingOverlay: false
         },
         muiTableBodyCellProps: () => {
            return {
               sx: {
                  ...COLUMN_SIZE.SM
               },
            }
         },
         enableColumnActions: false,
         enableSorting: false,
         enableTopToolbar: false,
         muiTableHeadCellProps: () => {
            return {
               sx: { padding: '12px', backgroundColor: `rgb(250, 250, 250) !important`, }
            }
         },
         muiBottomToolbarProps() {
            return ({
               sx: {
                  background: 'white',
                  boxShadow: 'none',
               }
            })
         },
         muiTableBodyRowProps() {
            return ({
               sx: {
                  backgroundColor: `white`,
                  '&:hover': {
                     backgroundColor: `${theme.palette.grey[100]} !important`, // Set your desired hover color
                     '& td::after': {
                        content: '""',
                        display: 'block',
                        position: 'absolute',
                        width: '100%',
                        height: '0px', // Adjust height as needed
                        bottom: 0,
                        left: 0,
                     },
                  }
               }
            })
         },
         muiTableBodyProps() {
            return {
               sx: {
                  backgroundColor: 'white'
               }
            }
         }
      },

      // PAGINATION CONTROLS
      paginationModal: {
         pagination,
         onPaginationChange: (updated: any) => {

         },
         setTotal: (total) => {
            setPagination((prev: any) => ({ ...prev, total }))
         }
      }
   };

}