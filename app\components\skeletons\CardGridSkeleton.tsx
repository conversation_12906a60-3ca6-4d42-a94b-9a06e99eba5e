import React from 'react';
import { <PERSON><PERSON>, Card, CardContent, Skeleton, Box } from '@mui/material';

interface CardGridSkeletonProps {
  count?: number;
  columns?: { xs: number; sm: number; md: number; lg: number };
  cardHeight?: number;
  showActions?: boolean;
}

const CardGridSkeleton: React.FC<CardGridSkeletonProps> = ({
  count = 6,
  columns = { xs: 12, sm: 6, md: 4, lg: 3 },
  cardHeight = 200,
  showActions = false
}) => {
  return (
    <Grid container spacing={3}>
      {Array.from({ length: count }).map((_, index) => (
        <Grid item key={index} {...columns}>
          <Card>
            <CardContent>
              {/* Card Header */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                  <Skeleton variant="circular" width={48} height={48} />
                  <Box>
                    <Skeleton width={120} height={24} />
                    <Skeleton width={80} height={16} sx={{ mt: 0.5 }} />
                  </Box>
                </Box>
                {showActions && <Skeleton variant="rectangular" width={40} height={40} />}
              </Box>
              
              {/* Card Body */}
              <Box sx={{ mt: 3 }}>
                <Skeleton width="100%" height={20} />
                <Skeleton width="80%" height={20} sx={{ mt: 1 }} />
                <Skeleton width="60%" height={20} sx={{ mt: 1 }} />
              </Box>

              {/* Card Footer */}
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Skeleton width={60} height={24} />
                <Skeleton variant="rectangular" width={80} height={32} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default CardGridSkeleton;