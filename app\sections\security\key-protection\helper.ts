import { SubscriptionTiersType } from "constants/subscription.constant";
import { Subscription } from "types/subscription";

const hasTiers = (subscription: Array<Subscription>, tier: Array<SubscriptionTiersType>) => {
   return subscription?.some((i) => tier.includes(i?.charge?.pricingTier?.name as SubscriptionTiersType))
};

export { hasTiers }

export const getHighlightedStyles = (palette:any) => {
   return {
      borderColor: palette.primary.main, // Change border color on hover
      background: palette.primary.lighter, // Change border color on hover
   }
}

export const getHoverStyles = (palette:any) => {
   return {
      borderColor: palette.primary[100], // Change border color on hover
      background: palette.primary.lighter, // Change border color on hover
   }
}

export const getActiveStyles = (palette:any) => {
   return {
      borderColor: palette.primary[200], // Change border color on hover
      background: palette.primary[100], // Change border color on hover
   }
}

export const getValuesInArr = (items:any) => {
   return items.map((item:any) => item?.value) ?? [];
}

export const behaviorStyles = (palette:any) => {
  return {
   '&:hover': {
      ...getHoverStyles(palette)
   },
   '&:active': {
      ...getActiveStyles(palette)
   },
  }
}