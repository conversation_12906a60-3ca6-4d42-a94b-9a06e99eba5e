// React imports
import { useEffect, useState } from "react";

// Third-party library imports
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>le, <PERSON>rid, Stack, Typography } from "@mui/material";

// Absolute imports
import IntegrationCreationCreateProvider from './integration-create-provider';

import { useGetSecurity } from "hooks/api/security/useGetKeyProtection";

// Relative imports
import PolicyAcceptance from "./policy-acceptance";
import IntegrationList from "./integration-list/index";
import useUserDetails from "store/user";
import { hasTiers } from "./helper";
import { AVAILABLE_SUBSCRIPTION_TIERS } from "./constant";

const TITLE = ('Choose Where Your Integration Credentials Are Stored.'
)
const defaultVale = true;


export default () => {
   const { subscriptions = [], isSubscriptionLoading } = useUserDetails()

   const hasEnterpriseTier = hasTiers(subscriptions, AVAILABLE_SUBSCRIPTION_TIERS);

   // State to manage whether the log protection is enabled
   const { searchKeyProtections } = useGetSecurity({ hasEnterpriseTier });


   // Fetch log protection data
   const { data, isPending } = searchKeyProtections();
   const [enabled, setEnabled] = useState(data?.unizoManagedLog); 

   useEffect(() => {
      setEnabled(data?.unizoManagedLog ?? defaultVale);
   }, [data?.unizoManagedLog]);

   return (
      <Stack gap={3}>
         {!isSubscriptionLoading && !hasEnterpriseTier && (
            <Alert severity="info"  >

               This feature is available on <b>Enterprise Plan</b>
            </Alert>
         )}

         <Stack
            gap={2}
            sx={{
               ...(!hasEnterpriseTier ? {
                  opacity: .4,
                  pointerEvents: 'none',
               } : {})
            }}
         >
            <Grid container>
               <Grid item xs={12} lg={12}>
                  <Typography variant='button' color={'secondary.600'}>
                     {TITLE}
                  </Typography>
               </Grid>
            </Grid>

            <IntegrationCreationCreateProvider
               value={{ hasEnterpriseTier }}
            >
               <>
                  {/* policy acceptance */}
                  <PolicyAcceptance
                     enabled={enabled}
                     setEnabled={setEnabled}
                     keyId={data?.id ?? null}
                     isLoading={isPending}
                     hasEnterpriseTier={hasEnterpriseTier}
                  />

                  {/* policy acceptance */}
                  {/* <IntegrationList /> */}
               </>
            </IntegrationCreationCreateProvider>
         </Stack>
      </Stack>
   )
}