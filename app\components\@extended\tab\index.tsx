
import { ReactElement, useEffect, useState } from 'react';

import { Box, Tab, Tabs as MuiTabs, useTheme } from '@mui/material';
import { selectFirstTab } from './helper'
   ;
function TabPanel({ children, value, index, ...other }: any) {
   return (
      <div role="tabpanel" hidden={value !== index} id={`profile-tabpanel-${index}`} aria-labelledby={`profile-tab-${index}`} {...other}>
         {value === index && children}
      </div>
   );
}

function a11yProps(index: any) {
   return {
      id: `profile-tab-${index}`,
      'aria-controls': `profile-tabpanel-${index}`
   };
}

export type TabItemsTypes = {
   title: string
   label?: string
   key: number
   children: ReactElement
   className?: string
   disabled?: boolean;
}

export type TabsProps = {
   items?: TabItemsTypes[]
   value?: number
   onTabChange?: (event: React.SyntheticEvent<Element, Event>, value: any) => void;
   classNames?: {
      tab?: string
      panel?: string
   },
   disabled?: boolean;
}

export default function Tabs(props: TabsProps) {

   const {
      value: valueProp,
      items = [],
      onTabChange: onChangeProp,
      classNames,
   } = props;

   const { palette, ...theme } = useTheme();
   const [selectedLocalValue, setSelectedLocalValue] = useState<number | null>(selectFirstTab(items))

   const value = valueProp ?? selectedLocalValue;

   const onChange: ((event: React.SyntheticEvent<Element, Event>, value: any) => void) | undefined = (e, newVal) => {
      if (typeof onChangeProp === 'function') {
         onChangeProp(e, newVal)
      }
      setSelectedLocalValue(newVal)
   }

   useEffect(() => {
      setSelectedLocalValue(selectFirstTab(items))
   }, [items])

   return (
      <>
         <Box sx={{ borderBottom: 1, borderColor: 'divider', marginBottom: 2 }}>
            <MuiTabs variant="fullWidth" value={value} onChange={onChange} aria-label="profile tabs">
               {items.map((item, index) => {
                  const { key, title, label, className, disabled } = item;
                  return (
                     <Tab

                        sx={{
                           display: 'flex',
                           flexDirection: 'row',
                           justifyContent: 'center',
                           alignItems: 'center',
                           textTransform: 'capitalize'
                        }}
                        className={['!p-0', classNames?.tab, className,].join(' ')}
                        key={index}
                        label={title || label}
                        disabled={disabled}
                        {...a11yProps(key)}
                     />
                  )
               })}

            </MuiTabs>
         </Box>
         {items.map((item, index) => {
            const { children, key } = item;
            return (
               <TabPanel
                  className={classNames?.panel}
                  value={value}
                  key={index}
                  index={index}
                  dir={theme.direction}
               >
                  {children ? children : null}
               </TabPanel>
            )
         })}
      </>
   );
}
