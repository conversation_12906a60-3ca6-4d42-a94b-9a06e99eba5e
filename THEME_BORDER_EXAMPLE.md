# Adding Theme-Based Border

To add a theme-based border to your component with the CSS class `.css-1qpr7h8`, update the styling as follows:

## Option 1: Using sx prop (Recommended)

```javascript
<Box
  sx={{
    backgroundColor: (theme) => alpha(theme.palette.background.paper, 0.5),
    borderRadius: 1, // theme.shape.borderRadius
    padding: 3, // theme.spacing(3) = 24px
    position: 'relative',
    border: 1,
    borderColor: 'divider', // This will use theme.palette.divider
    // OR use a custom border:
    // border: (theme) => `1px solid ${theme.palette.divider}`,
    // OR for a more subtle border:
    // border: (theme) => `1px solid ${alpha(theme.palette.divider, 0.5)}`,
  }}
>
  {/* Content */}
</Box>
```

## Option 2: Using styled component

```javascript
import { styled, alpha } from '@mui/material/styles';
import { Box } from '@mui/material';

const StyledContainer = styled(Box)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.background.paper, 0.5),
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(3), // 24px
  position: 'relative',
  border: `1px solid ${theme.palette.divider}`,
  // For dark mode support, you can also do:
  // borderColor: theme.palette.mode === 'dark' 
  //   ? alpha(theme.palette.divider, 0.3)
  //   : theme.palette.divider,
}));
```

## Option 3: Update existing component

If you find the component using this class, update it like this:

```javascript
// From this:
sx={{
  backgroundColor: 'rgba(245, 245, 245, 0.5)',
  borderRadius: '8px',
  padding: '24px',
  position: 'relative',
}}

// To this:
sx={{
  backgroundColor: (theme) => 
    theme.palette.mode === 'dark' 
      ? alpha(theme.palette.background.paper, 0.5)
      : alpha(theme.palette.grey[50], 0.5),
  borderRadius: 1,
  padding: 3,
  position: 'relative',
  border: 1,
  borderColor: 'divider',
}}
```

## Available Border Options

1. **Simple border with theme divider**:
   ```javascript
   border: 1,
   borderColor: 'divider',
   ```

2. **Custom border with alpha**:
   ```javascript
   border: (theme) => `1px solid ${alpha(theme.palette.divider, 0.5)}`,
   ```

3. **Mode-specific border**:
   ```javascript
   border: 1,
   borderColor: (theme) => 
     theme.palette.mode === 'dark' ? 'grey.800' : 'grey.300',
   ```

4. **Colored border based on state**:
   ```javascript
   border: 1,
   borderColor: isSelected ? 'primary.main' : 'divider',
   ```

## Theme Palette Options for Borders

- `theme.palette.divider` - Standard divider color
- `theme.palette.grey[300]` - Light grey border
- `theme.palette.grey[800]` - Dark grey border
- `theme.palette.primary.main` - Primary color border
- `theme.palette.action.disabled` - Disabled state border
- `alpha(color, 0.5)` - Semi-transparent border