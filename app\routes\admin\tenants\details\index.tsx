import { Stack } from "@mui/material"

import Tabs from "components/@extended/tab";

import { Subscriptions } from "sections/admin/tenant-details/subscritions"
import { Users } from "sections/admin/tenant-details/users"


export default () => {
   return (
      <Stack>
         <Tabs
            classNames={{
               tab: 'max-w-[150px]'
            }}
            items={[
               {
                  title: 'Subscriptions',
                  key: 0,
                  children: (
                     <Subscriptions />
                  )
               },
               {
                  title: 'Users',
                  key: 1,
                  children:<Users />
               },
            ]}
         />
      </Stack>
   )
}