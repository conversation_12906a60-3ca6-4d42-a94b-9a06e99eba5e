import { Chip, ChipProps } from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';

interface WebhookStatusChipProps {
  status: string;
  size?: ChipProps['size'];
}

export default function WebhookStatusChip({ status, size = 'medium' }: WebhookStatusChipProps) {
  const theme = useTheme();
  
  const getStatusConfig = (status: string) => {
    const normalizedStatus = status?.toLowerCase() || '';
    
    switch (normalizedStatus) {
      case 'active':
      case 'enabled':
        return {
          label: 'Active',
          color: theme.palette.success.main,
          bgColor: alpha(theme.palette.success.main, 0.1),
          borderColor: alpha(theme.palette.success.main, 0.3)
        };
      case 'inactive':
      case 'disabled':
        return {
          label: 'Inactive',
          color: theme.palette.text.secondary,
          bgColor: alpha(theme.palette.text.secondary, 0.1),
          borderColor: alpha(theme.palette.text.secondary, 0.3)
        };
      case 'error':
      case 'failed':
        return {
          label: 'Error',
          color: theme.palette.error.main,
          bgColor: alpha(theme.palette.error.main, 0.1),
          borderColor: alpha(theme.palette.error.main, 0.3)
        };
      case 'pending':
      case 'processing':
        return {
          label: 'Pending',
          color: theme.palette.warning.main,
          bgColor: alpha(theme.palette.warning.main, 0.1),
          borderColor: alpha(theme.palette.warning.main, 0.3)
        };
      default:
        return {
          label: status || 'Unknown',
          color: theme.palette.text.secondary,
          bgColor: alpha(theme.palette.text.secondary, 0.1),
          borderColor: alpha(theme.palette.text.secondary, 0.3)
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Chip
      label={config.label}
      size={size}
      sx={{
        color: config.color,
        backgroundColor: config.bgColor,
        border: `1px solid ${config.borderColor}`,
        fontWeight: 500,
        fontSize: size === 'small' ? '0.75rem' : '0.813rem',
        height: size === 'small' ? 24 : 28,
        '& .MuiChip-label': {
          px: 1.5,
        }
      }}
    />
  );
}