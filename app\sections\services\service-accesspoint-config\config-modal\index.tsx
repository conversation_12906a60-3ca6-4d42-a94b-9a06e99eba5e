import { RefObject, useEffect, useMemo, useRef, useState } from "react";
import { Button, DialogActions, DialogContent, DialogTitle, Radio, Stack, Typography } from "@mui/material"

import { DialogClose, Dialog } from "components/@extended/dialog"
import MainCard from "components/MainCard";

import { useServiceProfile } from "hooks/useServiceProfile";
import useServiceConfig from "store/setup-service";
import { AppConfigForm } from "./app-config-form";
import { getAccessPointTypeContainer } from "../helper";

const ConfigForm = () => {
   const {
      selectedTypeConfig,
      resetSelectedTypeConfig
   } = useServiceConfig();

   const { loadImage } = useServiceProfile();
   const formRef = useRef<HTMLFormElement>(null)

   const open = !!selectedTypeConfig

   const onClose = () => {
      resetSelectedTypeConfig();
   }

   const [selectedAppTypeConfig, seSelectedAppTypeConfig] = useState<Record<string, any> | null>(null)

   // const onSelectAppConfig = (selectedAppConfig: Record<string, any>) => {
   //    seSelectedAppTypeConfig(selectedAppConfig)


   // }

   const configName = selectedTypeConfig?.serviceProfile?.name;

   const onSubmit = () => {
      formRef.current?.requestSubmit()
   }

   useEffect(() => {
      const config =
         selectedTypeConfig?.accessPoint?.appConfig ??
         selectedTypeConfig?.accessPoint?.oAuthConfig ??
         null;

      seSelectedAppTypeConfig(config);
   }, [selectedTypeConfig]);


   return (
      <Dialog open={open} onClose={onClose}>

         <DialogTitle variant='h5'>
            <Stack direction={'row'} spacing={2} alignItems={'center'}>
               {loadImage(selectedTypeConfig?.serviceProfile, { size: 'small' })}
               <Typography variant='h5'>
                  Configure {configName}
               </Typography>

            </Stack>
         </DialogTitle>

         <DialogClose onClose={onClose} />

         <DialogContent dividers>
            <Stack gap={selectedTypeConfig?.type==="APP_FLW"?4:0}>
               {/* <Stack spacing={3}> */}

                  {/* <Stack direction={'row'} spacing={2} alignItems={'center'}>
                     {loadImage(selectedTypeConfig?.serviceProfile, { size: 'small' })}
                     <Typography variant='h5'>
                        {configName}
                     </Typography>
                  </Stack> */}
                  {/* {
                     selectedTypeConfig?.type==="APP_FLW" && selectedTypeConfig?.assistanceInfo?.processSteps?.map((step: any, index: any) => {
                        return (
                           <ProcessSteps
                              index={index}
                              step={step}
                              key={index}
                           />
                        )
                     })
                  } */}

                  {/* selection config block */}
                  {/* {selectedTypeConfig?.type==="APP_FLW" &&  <AppConfigSelection
                     onSelectConfig={onSelectAppConfig}
                     selectedAppTypeConfig={selectedAppTypeConfig}
                  /> } */}
                 
               {/* </Stack> */}

               {/* app type config block */}
               {selectedAppTypeConfig ? (
                  <AppTypeConfig
                     selectedAppTypeConfig={selectedAppTypeConfig}
                     formRef={formRef}
                  />
               ) : null}

            </Stack>
         </DialogContent>

         <DialogActions>
            <Stack direction={'row'} gap={2} justifyContent={'flex-end'}>
               <Button onClick={onClose} >
                  Cancel
               </Button>
               <Button
                  variant='contained'
                  color='primary'
                  onClick={onSubmit}
               >
                  Submit
               </Button>

            </Stack>
         </DialogActions>
      </Dialog>
   )
}



type AppTypeConfigProps = {
   selectedAppTypeConfig: Record<string, any> | null
   formRef?: RefObject<HTMLFormElement>
}

const AppTypeConfig = (props: AppTypeConfigProps) => {

   const { selectedAppTypeConfig, formRef } = props;

   const segments = useMemo(() => (
      selectedAppTypeConfig?.segments ?? []
   ), []);


   return (
<Stack gap={3}>
  <Stack direction={'row'} spacing={2} alignItems={'center'}>
    {selectedAppTypeConfig?.segments?.map((step: any, index: any) => {
      const headers = step.assistanceInfo?.processSteps.map((i: any) => i.title);
      const subheaders = step.assistanceInfo?.processSteps.map((i: any) => i.description);
      const linkerNames = step.links.map((i: any) => i.name);
      const linkerLinks = step.links.map((i: any) => i.href);
      console.log(headers,"hhhhhhh")
      return (
        <Stack direction={'column'} className="gap-1" key={index}>
          {headers?.map((header: string, hIndex: number) => {
            const isGoogleWorkspace = header === "Google Workspace Details";
             const isGitHubApp = header === "GitHub App Details";
            const linkHref = isGoogleWorkspace
              ? "https://docs.unizo.ai/docs/connectors/google-workspace-auth-guide"
              : linkerLinks[hIndex];

            return (
              <>
                <Typography variant="h5">{header}</Typography>
                <Typography variant="h6" color={'secondary.600'}>
                  {subheaders[hIndex]}
                  {!isGitHubApp && linkerNames[hIndex] && (
                    <Typography
                      component={'a'}
                      className="link ml-1"
                      href={linkHref}
                      target="_blank"
                    >
                      {linkerNames[hIndex]}
                    </Typography>
                  )}
                </Typography>
              </>
            );
          })}
        </Stack>
      );
    })}
  </Stack>
  <AppConfigForm segments={segments} formRef={formRef} />
</Stack>

   )
}

// process steps
type ProcessStepsProps = {
   step: any
   index: number
}
const ProcessSteps = ({ step, index }: ProcessStepsProps) => {

   const {
      selectedTypeConfig,
   } = useServiceConfig();
console.log(selectedTypeConfig,"step")
   return (
      <Stack direction={'column'} className="gap-1">
         <Typography variant='h5'>{step?.title}</Typography>
         <Typography
            variant="h6"
            color={'secondary.600'}
         >
            {step?.description}
            <Typography
               component={'a'}
               className="link ml-1"
               href={selectedTypeConfig?.links?.[index]?.href}
               target="_blank"
            >
               {selectedTypeConfig?.links?.[index]?.name}
            </Typography>
         </Typography>
      </Stack>
   )
}

// app selection
type AppConfigSelectionProps = {
   onSelectConfig: (config: Record<string, any>) => void
   selectedAppTypeConfig: Record<string, any> | null
}
const AppConfigSelection = ({ onSelectConfig, selectedAppTypeConfig }: AppConfigSelectionProps) => {

   const {
      selectedTypeConfig,
   } = useServiceConfig();

   return (
      <Stack direction={'row'} spacing={2} alignItems={'center'}>
         {getAccessPointTypeContainer(selectedTypeConfig)?.map((item: any, index: number) => {
            const active = item?.type === selectedAppTypeConfig?.type;


            return (
               <MainCard
                  key={index}
                  className='cursor-pointer'
                  onClick={() => (
                     void onSelectConfig(item)
                  )}
               >
                  <Stack direction={'row'} spacing={1} alignItems={'start'}>
                     <Radio size='small' checked={active} className="p-0" />
                     <Stack className="gap-1">
                        <Typography variant='h5'>
                           {item?.label}
                        </Typography>
                        <Typography variant='h6'>
                           {item?.description}
                        </Typography>
                     </Stack>
                  </Stack>
               </MainCard>
            )
         })}
      </Stack>
   )
}

export default ConfigForm;