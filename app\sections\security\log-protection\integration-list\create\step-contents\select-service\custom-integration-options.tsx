import { LogProtectionConnectorType } from "constants/log-protection";
import { ServiceProfile } from "types/service-profile";

// MIGHT NOT BE USE_FULL, BUT FOR STANDARD FLOW
const WEB_HOOK_UUID = 'b23b47c1-89b5-4559-ab17-66af3a208593';

export default [
   // {
   //    name: 'Webhook',
   //    id: WEB_HOOK_UUID,
   //    type: LogProtectionConnectorType.Webhook,
   //    serviceProfile: {
   //       image: {
   //          medium: `https://unizopublicpaas.blob.core.windows.net/imgs/webhook/webhook_128.png`,
   //          xSmall: 'https://unizopublicpaas.blob.core.windows.net/imgs/webhook/webhook_32.png',
   //          small: `https://unizopublicpaas.blob.core.windows.net/imgs/webhook/webhook_64.png`
   //       },
   //       name: 'Webhook'
   //    }
   // }
] as StaticOption[];

export interface StaticOption {
   name: string
   id: string,
   type: LogProtectionConnectorType.Webhook,
   serviceProfile: Partial<ServiceProfile>
}