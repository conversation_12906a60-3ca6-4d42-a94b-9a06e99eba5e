name: Promote

on:
  workflow_dispatch:

jobs:
  promote:
    runs-on: dev
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT_TOKEN }}

      - name: Load environment variables
        run: |
          cat .github/workflows/.promote | grep -v '^#' | while IFS= read -r line; do
            echo "$line" >> $GITHUB_ENV
          done

      - name: Set promote tag
        run: |
          # Extract base version from TAG_TO_PROMOTE by removing -SNAPSHOT
          PROMOTE_TAG=$(echo $TAG_TO_PROMOTE | sed 's/-SNAPSHOT//')
          echo "PROMOTE_TAG=${PROMOTE_TAG}" >> $GITHUB_ENV

          echo "Will promote $TAG_TO_PROMOTE to $PROMOTE_TAG"

      # - name: Login to Azure Container Registry
      #   run: |
      #     docker login -u push-user -p ${{ secrets.UNIZO_AI_ACR_CRED }} $IMAGE_REGISTRY

      - name: Promote Docker image
        run: |
          # Pull the development image
          docker pull $IMAGE_REGISTRY/$DOCKER_IMAGE:$TAG_TO_PROMOTE
          
          # Tag for promotion
          docker tag $IMAGE_REGISTRY/$DOCKER_IMAGE:$TAG_TO_PROMOTE $IMAGE_REGISTRY/$DOCKER_IMAGE:$PROMOTE_TAG
          
          # Push to registry
          docker push $IMAGE_REGISTRY/$DOCKER_IMAGE:$PROMOTE_TAG

      - name: Update next development version
        run: |
          # Get current year and month
          CURRENT_YEAR=$(date +%Y)
          CURRENT_MONTH=$(date +%m)

          # Extract the current version parts
          VERSION_BASE=$(echo $TAG_TO_PROMOTE | cut -d'.' -f1-2)
          PATCH=$(echo $TAG_TO_PROMOTE | cut -d'.' -f3 | cut -d'-' -f1)
          NEXT_PATCH=$((PATCH + 1))

          # If we're in a new month, reset patch version
          PROMOTED_VERSION_BASE=$(echo $PROMOTE_TAG | cut -d'.' -f1-2)
          NEW_VERSION_BASE="${CURRENT_YEAR}.${CURRENT_MONTH}"

          if [ "$NEW_VERSION_BASE" != "$PROMOTED_VERSION_BASE" ]; then
            NEXT_PATCH=1
          fi

          NEW_DEV_TAG="${NEW_VERSION_BASE}.${NEXT_PATCH}-SNAPSHOT"

          echo "Creating next development version: $NEW_DEV_TAG"

          # First update PROMOTE_TAG in .promote file to match what we're promoting
          sed -i "s/^PROMOTE_TAG=.*/PROMOTE_TAG=$PROMOTE_TAG/" .github/workflows/.promote

          # Then update other files
          sed -i "s/^TAG_TO_PROMOTE=.*/TAG_TO_PROMOTE=$NEW_DEV_TAG/" .github/workflows/.promote
          sed -i "s/^NEXT_TAG=.*/NEXT_TAG=$NEW_DEV_TAG/" .github/workflows/.promote
          sed -i "s/^DOCKER_TAG=.*/DOCKER_TAG=$NEW_DEV_TAG/" .github/workflows/.env-dev
          sed -i "s/^DOCKER_TAG=.*/DOCKER_TAG=$PROMOTE_TAG/" .github/workflows/.env-production

      # Set Git configurations at the system level with stronger enforcement
      - name: Configure Git for GitHub Actions bot
        run: |
          git config --system --unset-all user.name || true
          git config --system --unset-all user.email || true
          git config --global --unset-all user.name || true
          git config --global --unset-all user.email || true
          git config --local --unset-all user.name || true
          git config --local --unset-all user.email || true
          
          # Now set the GitHub Actions bot identity at all levels
          git config --system user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --system user.name "github-actions[bot]"
          git config --global user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"
          git config --local user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          
          # Verify the configuration
          echo "Current Git user configuration:"
          git config --get user.name
          git config --get user.email

      - name: Tag repository with promoted version
        run: |
          git tag -f $PROMOTE_TAG
          git push -f origin $PROMOTE_TAG

      - name: Commit and push changes
        run: |
          # Double-check Git identity before committing
          git config --get user.name
          git config --get user.email
          
          git add .github/workflows/.promote
          git add .github/workflows/.env-dev
          git add .github/workflows/.env-production
          
          # Force the committer identity with environment variables
          export GIT_COMMITTER_NAME="github-actions[bot]"
          export GIT_COMMITTER_EMAIL="41898282+github-actions[bot]@users.noreply.github.com"
          export GIT_AUTHOR_NAME="github-actions[bot]"
          export GIT_AUTHOR_EMAIL="41898282+github-actions[bot]@users.noreply.github.com"
          
          git commit -m "chore(release): Update versions from $TAG_TO_PROMOTE to $PROMOTE_TAG" || echo "No changes to commit"
          git push origin HEAD:main || echo "No changes to push"
      
      - name: Cleanup unused docker images
        run: |
          docker rmi $IMAGE_REGISTRY/$DOCKER_IMAGE:$PROMOTE_TAG || true
          docker rmi $IMAGE_REGISTRY/$DOCKER_IMAGE:$TAG_TO_PROMOTE || true
