import { DeploymentUnitOutlined } from "@ant-design/icons"

import { GadgetConfig } from "../layout/grid-type"
import useUserDetails from "store/user"
import { useGetOrganization } from "hooks/api/organization/useGetOrganization"
import ModernStatCard from "components/cards/statistics/ModernStatCard"


export default ({ gadget }: GadgetConfig) => {
   const { user } = useUserDetails()
   const { watches } = useGetOrganization({ id: user?.organization?.id });

   return (
      <ModernStatCard
         title={gadget?.name || "Webhooks"}
         value={watches?.pagination?.total ?? 0}
         icon={DeploymentUnitOutlined}
         color="info"
         subtitle="Active webhooks"
      />
   )
}