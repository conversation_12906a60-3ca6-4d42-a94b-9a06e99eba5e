import { useEffect, useState } from 'react';
import { useNavigate } from '@remix-run/react';
import { AxiosResponse } from 'axios';
import useUserDetails from 'store/user';
import { tenantsClient } from 'services/tenants.service';
import { API_ENDPOINTS } from 'utils/api/api-endpoints';
import platformFetchInstance from 'utils/api/fetchinstance/platform-fetch-Instance';

interface UseQuickStartRedirectOptions {
  enabled?: boolean;
  redirectPath?: string;
}

/**
 * Hook to check if user needs to complete quick-start onboarding
 * Redirects to /quick-start if tenant metadata is empty or incomplete
 */
export const useQuickStartRedirect = (options: UseQuickStartRedirectOptions = {}) => {
  const { enabled = true, redirectPath = '/quick-start' } = options;
  const navigate = useNavigate();
  const { user, isLoading: userLoading } = useUserDetails();
  const [isChecking, setIsChecking] = useState(true);
  const [needsQuickStart, setNeedsQuickStart] = useState(false);

  useEffect(() => {
    if (!enabled || userLoading || !user?.organization?.id) {
      setIsChecking(false);
      return;
    }

    const checkQuickStartStatus = async () => {
      try {
        const orgId = user.organization.id;
        
        // Check tenant metadata
        const response = await platformFetchInstance.get(
          `${API_ENDPOINTS.TENANT_META_DATA}/${orgId}`
        );

        const metadata = response.data;
        
        // Check if onboarding is completed
        const isOnboardingCompleted = 
          metadata?.onboardingCompleted === true ||
          metadata?.state === 'ONBOARDING_COMPLETED_REQUEST' ||
          metadata?.quickStartCompleted === true;

        // Check if essential data is present
        const hasSelectedCategories = 
          metadata?.onboardingData?.selectedCategories?.length > 0 ||
          metadata?.selectedCategories?.length > 0;

        const hasSelectedServices = 
          metadata?.onboardingData?.selectedServices?.length > 0 ||
          metadata?.selectedServices?.length > 0;

        // Determine if quick start is needed
        const needsOnboarding = !isOnboardingCompleted || (!hasSelectedCategories && !hasSelectedServices);

        if (needsOnboarding) {
          console.log('Quick start needed - redirecting to:', redirectPath);
          setNeedsQuickStart(true);
          navigate(redirectPath);
        } else {
          console.log('Quick start completed - no redirect needed');
          setNeedsQuickStart(false);
        }
      } catch (error: any) {
        // If metadata doesn't exist (404) or any error, assume quick start is needed
        if (error?.response?.status === 404) {
          console.log('No tenant metadata found - redirecting to quick start');
          setNeedsQuickStart(true);
          navigate(redirectPath);
        } else {
          console.error('Error checking quick start status:', error);
          // On other errors, don't redirect to avoid loops
          setNeedsQuickStart(false);
        }
      } finally {
        setIsChecking(false);
      }
    };

    checkQuickStartStatus();
  }, [enabled, user, userLoading, navigate, redirectPath]);

  return {
    isChecking,
    needsQuickStart,
    organizationId: user?.organization?.id
  };
};

/**
 * Alternative implementation using getAllTenants similar to useOnboarding
 */
export const useQuickStartRedirectV2 = (options: UseQuickStartRedirectOptions = {}) => {
  const { enabled = true, redirectPath = '/quick-start' } = options;
  const navigate = useNavigate();
  const { user, isLoading: userLoading, subscriptions } = useUserDetails();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    if (!enabled || userLoading || !user) {
      setIsChecking(false);
      return;
    }

    const checkTenantData = async () => {
      try {
        // Get all tenants
        const response: AxiosResponse = await tenantsClient.getAllTenants();
        const tenants = response?.data?.data || [];

        console.log('Tenant data check:', {
          tenantCount: tenants.length,
          subscriptionCount: subscriptions?.length || 0,
          hasOrganization: !!user?.organization?.id
        });

        // Check if tenant data is empty or incomplete
        if (!tenants.length || tenants.length === 0) {
          console.log('No tenant data found - redirecting to quick start');
          navigate(redirectPath);
          return;
        }

        // Additional checks for incomplete setup
        const firstTenant = tenants[0];
        const needsSetup = 
          !firstTenant?.metadata?.onboardingCompleted ||
          (!subscriptions?.length && !firstTenant?.metadata?.skipQuickStart);

        if (needsSetup) {
          console.log('Tenant setup incomplete - redirecting to quick start');
          navigate(redirectPath);
        }
      } catch (error) {
        console.error('Error checking tenant data:', error);
        // On error, assume setup is needed
        navigate(redirectPath);
      } finally {
        setIsChecking(false);
      }
    };

    // Small delay to ensure other components are ready
    const timeoutId = setTimeout(checkTenantData, 100);
    
    return () => clearTimeout(timeoutId);
  }, [enabled, user, userLoading, subscriptions, navigate, redirectPath]);

  return { isChecking };
};