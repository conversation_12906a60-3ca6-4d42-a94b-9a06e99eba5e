import { Grid, Skeleton } from '@mui/material';
import TableSkeleton from './TableSkeleton';

const WebhooksSkeleton = () => {
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item>
            <Skeleton width={200} height={40} />
            <Skeleton width={400} height={24} />
          </Grid>
          <Grid item>
            <Skeleton variant="rectangular" width={150} height={40} />
          </Grid>
        </Grid>
      </Grid>
      <Grid item xs={12}>
        <TableSkeleton 
          rows={5} 
          columns={5} 
          showActions={true}
          showPagination={true}
        />
      </Grid>
    </Grid>
  );
};

export default WebhooksSkeleton;