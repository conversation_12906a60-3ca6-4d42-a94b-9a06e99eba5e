import { Suspense, lazy, useEffect, useState } from "react";
import { Button, <PERSON>alogActions, DialogContent, DialogProps, DialogTitle, GridProps, Stack } from "@mui/material"
import { Dialog, DialogClose } from "components/@extended/dialog";
import useManageProfile from "sections/services/service-context/use-manage-profile";

const Selection = lazy(() => import('../selection'));

type Props = {
   selected: string[]
} & DialogProps;

const TITLE = 'Select Authorization Methods';
const GRID: GridProps = {
   xs: 12,
   sm: 6,
   md: 6,
   xl: 6
}

export default (props: Props) => {

   const { open, onClose, selected: selectedProp } = props;

   const { updateAccessPointsSelection, selectedService } = useManageProfile()

   const [localSelected, selectedLocalSelected] = useState<string[]>([]);
   const [localUnSelected, selectedUnLocalSelected] = useState<string[]>([]);

   const restState = () => {
      selectedLocalSelected([]);
      selectedUnLocalSelected([]);
   }

   const onUpdate = (e: any) => {
      const data: Array<{ id: string, isEnabled: boolean }> = []

      localSelected.forEach((id) => {
         data.push({ id, isEnabled: true })
      });

      localUnSelected.forEach((id) => {
         data.push({ id, isEnabled: false })
      })

      if (data?.length) {
         updateAccessPointsSelection(selectedService?.service?.id, { data }, () => {
            typeof onClose === 'function' && onClose(e, 'escapeKeyDown');
            restState();
         });

      } else {
         typeof onClose === 'function' && onClose(e, 'escapeKeyDown');
      }
   }

   const onSelect = (newSelected: string[], unSelected: string | null) => {
      selectedLocalSelected(newSelected)

      if (unSelected && selectedProp.includes(unSelected)) {
         selectedUnLocalSelected([...localUnSelected, unSelected]);
      }

   }

   useEffect(() => {
      selectedLocalSelected(selectedProp)
   }, [selectedProp])

   return (
      <Dialog
         open={open}
         onClose={onClose}
      >
         <DialogTitle>
            {TITLE}
         </DialogTitle>
         <DialogClose onClose={onClose} />
         <DialogContent dividers>

            {/* selection box */}
            <Suspense fallback={null}>
               <Selection
                  gridItemProps={GRID}
                  mode='edit'
                  selected={localSelected}
                  onSelect={onSelect}
               />
            </Suspense>
            {/* selection box */}

         </DialogContent>
         <DialogActions>
            <Stack direction={'row'} justifyContent={'flex-end'}>
               <Stack direction={'row'} gap={1}>
                  <Button onClick={onClose as any}>Cancel</Button>
                  <Button variant="contained" onClick={onUpdate} >Update</Button>
               </Stack>
            </Stack>
         </DialogActions>
      </Dialog>
   )
}