import { FormItemType } from "constants/form"

export const formConfig: FormConfigTypes[] = [
   {
      name: 'firstName',
      type: FormItemType.Text,
      label: 'First Name',
      placeholder: 'Enter first name',
      description: 'Please enter the first name in this field.',
   },
   {
      name: 'lastName',
      type: FormItemType.Text,
      label: 'Last Name',
      placeholder: 'Enter last name',
      description: `Please enter the last name in this field.`,
   },
   {
      name: 'email',
      type: FormItemType.Text,
      label: 'Email',
      placeholder: 'Enter email',
      description: `Please provide the email address for communication purposes.`,
   },
   {
      name: 'phone',
      type: FormItemType.Text,
      label: 'Phone Number',
      placeholder: 'Enter phone number',
      description: 'lease enter the phone number',
   },
   {
      name: 'role',
      type: FormItemType.Text,
      label: 'Role',
      placeholder: 'Enter role',
      description: 'Please enter the current role or position of Org Admin within the organization.',
   },
   {
      name: 'emails',
      type: FormItemType.MultiText,
      label: 'Add Additional Org Admins',
      placeholder: 'Enter email',
      description: 'Please add additional Org Admins to this organization. Enter up to five email addresses.',
   },
]