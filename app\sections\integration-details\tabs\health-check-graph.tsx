import { useEffect, useMemo, useRef, useState } from 'react';
import { Chart, registerables } from 'chart.js';
import moment from 'moment';
import 'chartjs-adapter-date-fns';
import { format } from 'date-fns';
import { <PERSON><PERSON>, <PERSON>ack, ToggleButton, ToggleButtonGroup } from '@mui/material';
import { AreaChartOutlined, TableOutlined } from '@ant-design/icons';
import Table from 'components/@extended/Table';

import { ColumnDef } from '@tanstack/react-table'
import { useStatus } from 'hooks/useStatus';
import { useDate } from 'hooks/useDate';

Chart.register(...registerables);
enum HealthEnums {
   Healthy = 'HEALTHY',
   UnHealthy = 'UNHEALTHY'
}

const processData = (data: any, status: any) => {
   const result = data
      .filter((item: any) => item.status === status)
      .map((item: any) => {
         const date = moment(item?.intervalEndDateTime).local();

         // Get the date in local time zone
         const day = date.format('YYYY-MM-DD'); // Format as YYYY-MM-DD in local time

         // Get the time in local time zone and convert to minutes since midnight
         const time = date.hours() * 60 + date.minutes();

         return { x: day, y: time };
      });

   return result;
};

const parseTime = (val: any) => {
   return moment().startOf('day').add(val, 'minutes').format('h:mm A');
}

export const ScatterChart = ({ data }: any) => {

   // Prepare the data for the chart
   const series = {
      datasets: [
         {
            label: 'Healthy',
            data: processData(data, HealthEnums.Healthy),
            backgroundColor: 'green',
            borderWidth: 1,
            pointRadius: 5,
         },
         {
            label: 'Un Healthy',
            data: processData(data, HealthEnums.UnHealthy),
            backgroundColor: 'red',
            borderWidth: 1,
            pointRadius: 5,
         },
      ],
   };

   // Define options for the chart
   const options = {
      scales: {
         x: {
            type: 'time', // Use category for string x-axis
            title: {
               display: true,
               text: 'Date',
            },
            time: {
               unit: 'day',
               displayFormats: {
                  day: 'MMM d', // Define format for X-axis labels (Oct 12, etc.)
               },
            },
            ticks: {
               callback: (value: any) => {
                  const formattedDate = format(new Date(value), 'MMM d'); // Use date-fns to format
                  return formattedDate; // Return formatted date, e.g., "Oct 12"
               },
            },
         },
         y: {
            title: {
               display: true,
               text: 'Time',
            },
            ticks: {
               callback: parseTime
            },
         },
      },
      plugins: {
         zoom: {
            pan: {
               enabled: true, // Enable panning
               mode: 'y', // Allow panning in both directions
            },
            zoom: {
               wheel: {
                  enabled: true,
               },
               pinch: {
                  enabled: true
               },
               mode: 'y',
            },
         },
         datalabels: {
            display: false, // Ensure data labels are not showing
         },
         tooltip: {
            callbacks: {
               label: function (tooltipItem: any) {
                  const x = tooltipItem?.raw?.x,
                     y = tooltipItem?.raw?.y;
                  return `(${x},${parseTime(y)})`;
               },
            }
         }
      }
   };

   const ref = useRef<any>(null);

   const onReset = () => {
      ref.current.resetZoom()
   }

   const [Scatter, setScatter] = useState<any>(null)

   useEffect(() => {
      import("react-chartjs-2").then((module: any) => {
         setScatter(module.Scatter);
      });
      import("chartjs-plugin-zoom").then((module) => {
         Chart.register(module.default as any);
      });
   }, []);

   const [view, setView] = useState<'graph' | 'grid'>('graph');

   const onChangeView = (_: any, updatedView: any) => (
      void (updatedView && setView(updatedView))
   )

   const isGraphView = view === 'graph';

   return (
      <Stack gap={2} >

         <Stack direction={'row'} justifyContent='flex-end'>
            <Stack direction={'row'} gap={2}>
               {isGraphView && (
                  <Button onClick={onReset}
                     size='small'
                     color='primary'
                     variant='contained'
                  >
                     Reset
                  </Button>
               )}
               <ToggleButtonGroup exclusive value={view} onChange={onChangeView} color='primary'>
                  <ToggleButton value="graph" >
                     <AreaChartOutlined />
                  </ToggleButton>
                  <ToggleButton value="grid">
                     <TableOutlined />
                  </ToggleButton>
               </ToggleButtonGroup>
            </Stack>
         </Stack>

         {isGraphView ? (
            Scatter ? (
               <Scatter ref={ref} data={series} options={options as any} height={75} />
            ) : null
         ) : (
            <TableView data={data} />
         )}

      </Stack>
   );
};

type TableViewProps = {
   data: Array<any>;
}

const TableView = ({ data }: TableViewProps) => {

   const { resolveIcon } = useStatus(),
      { loadDate } = useDate()

   const columns = useMemo<ColumnDef<{
      intervalEndDateTime: string;
      status: string;
   }, any>[] | undefined>(
      () => [
         {
            header: 'Status',
            accessorKey: 'status',
            cell: (status) => resolveIcon(status.getValue()) ?? '',
         },
         {
            header: 'Date',
            accessorKey: 'intervalEndDateTime',
            cell: (date) => loadDate(date.getValue()) ?? '',
            meta: {
               className: 'cell-start'
            }
         }
      ],
      []
   );


   return (
      <>
         <Table
            columns={columns}
            data={data}
            manualPagination={false}
            totalData={data?.length}
         />
      </>
   )
}

export default ScatterChart;
