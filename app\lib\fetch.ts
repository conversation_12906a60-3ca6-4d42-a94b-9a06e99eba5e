const apiPlatformBaseUrl = window?.ENV?.API_PLATFORM_BASE_URL;
const apiPodBaseUrl = window?.ENV?.API_POD_BASE_URL;

export const getFetchConfigsHeaders = () => {
   return {
      'organizationid': window.authUserOrgId,
      'authuserid': window.authUserId,
      'sourcechannel': 'PORTAL',
      'Content-Type': 'application/json',
   }
}

const mergeUrl = (urls: (string | undefined)[]) => [...urls].filter(Boolean).join('')

export const BASE_URL_S = {
   PLATFORM: mergeUrl([apiPlatformBaseUrl, '/v1']),
   POD: mergeUrl([apiPodBaseUrl, '/api/v1'])
}

export const withBaseUrl = (endPoint: string) => {
   return `${BASE_URL_S.PLATFORM}${endPoint}`;
}