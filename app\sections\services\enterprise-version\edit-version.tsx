import { useState } from "react"

import { <PERSON>, <PERSON><PERSON>, <PERSON>alogActions, DialogContent, DialogProps, DialogTitle, GridProps, Stack, Typography } from "@mui/material"
import { Dialog, DialogClose } from "components/@extended/dialog"

import { useGetServiceProfile } from "hooks/api/service-profile/useGetServiceProfile"
import useServiceConfig from "store/setup-service"
import { OptionTypes } from "components/@extended/CardSelect"

import { Service } from "types/service"

import Selection from "./selected"

// hook for manage the profile details
import useManageProfile from "../service-context/use-manage-profile";

type Props = {
   versions: OptionTypes[]
   selected: string[]
   setSelectedVersions: React.Dispatch<React.SetStateAction<never[]>>
} & DialogProps;

const PAYLOAD_TYPE = "SP_VERSION";
const SPANS: GridProps = {
   xs: 12,
   md: 6,
   xl: 3,
}

export default (props: Props) => {

   const {
      versions,
      selected,
      open,
      onClose: onCloseProp,
      ...rest
   } = props;

   const { selectedService, setSelectedService } = useServiceConfig();

   const [localSelected, setLocalSelected] = useState<string[]>(selected);

   const { updateVersions } = useManageProfile()

   const onValueSelect = (arg: string[]) => {
      setLocalSelected(arg as any)
   }

   const onClose: DialogProps['onClose'] = (e) => {
      typeof onCloseProp === 'function' && (
         onCloseProp(e, 'backdropClick')
      )
   }

   const onUpdate = (e: any) => {

      // getting (name and id) from version
      const value = localSelected?.map((item) => {
         const serviceVersion = versions.find((i) => i?.value === item);
         if (serviceVersion) {
            return {
               type: PAYLOAD_TYPE,
               serviceProfileVersion: { id: serviceVersion?.value, name: serviceVersion?.title },
            };
         }
      })

      const payload = [{ path: "/versions", op: "replace", value }];

      updateVersions(selectedService?.service?.id, payload, (service: Service) => {
         onClose(e as any, 'escapeKeyDown');
         setSelectedService({ ...selectedService, service } as any)
      })
   }

   return (
      <Dialog open={open} onClose={onClose} {...rest}>
         <DialogTitle >
            Select Versions
         </DialogTitle>
         <DialogClose onClose={onClose} />
         <DialogContent dividers>
            <Box>
               <Stack gap={1}>
                  <Stack>
                     <Typography >Choose the specific versions you'd like to make available for your users.</Typography>
                  </Stack>
                  <Selection
                     selected={localSelected}
                     versions={versions}
                     onSelect={onValueSelect as any}
                     mode="edit"
                     gridProp={{
                        ...SPANS
                     }}
                  />
               </Stack>
            </Box>
         </DialogContent>
         <DialogActions>
            <Stack direction={'row'} justifyContent={'flex-end'}>
               <Stack direction={'row'} gap={1}>
                  <Button onClick={onClose as any}>Cancel</Button>
                  <Button variant="contained" onClick={onUpdate} >Update</Button>
               </Stack>
            </Stack>
         </DialogActions>
      </Dialog>
   )
}
