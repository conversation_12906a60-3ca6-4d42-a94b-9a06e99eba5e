import { Changelog } from "./common";
import { ServiceProfile } from "./service-profile";
import { ServiceProfileSpecifications } from "./service-profile-specification";

export declare namespace ServiceProfileDataTypes {
  interface Root {
    id: string;
    name: string;
    key: string;
    description: string;
    serviceProfile: Partial<ServiceProfile>;
    specification: Partial<ServiceProfileSpecifications>;
    changeLog: Changelog;
    mappedTo: {
      entity: string;
      path: string;
    };
  }
}
