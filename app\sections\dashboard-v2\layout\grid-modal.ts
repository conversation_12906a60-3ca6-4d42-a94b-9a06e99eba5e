import { GridModal } from "./grid-type";

export enum GadgetType {
   SparkLine = 'SPARKLINE',
   PieChart = 'PIE_CHART',

   AllCategories = 'ALL_CATEGORIES',
   AllService = 'ALL_SERVICE',
   Integrations = 'INTEGRATIONS',
   BiDirectional = 'BI_DIRECTIONAL',
   APIRequest = 'API_REQUEST',
   EventRequest = 'EVENT_REQUEST',
   IntegrationsStats = 'INTEGRATIONS_STATUS',
   ServicesStats = 'SERVICES_STATUS',
   ErrorStats = 'ERROR_STATS',
   TrafficProviderStats = 'TRAFFIC_PROVIDER_STATS',
}

const gridModal: GridModal = {
   href: "https://api.platform.com/api/v1/dashboards/988cd906-2072-4e83-a4e1-09f4f8a46a68",
   type: "DASHBOARD",
   id: 12345,
   name: "My Dashboard",
   description: "My Portfolio Dashboard",
   state: "ACTIVE",
   layout: {
      Type: "GRID"
   },
   gadgetConfigs: [
      // stats
      {
         gadget: {
            type: GadgetType.AllCategories,
            id: "b2cd3ef5-a789-41b9-9f12-57a143ed9875",
            name: "Subscriptions",
            description: "Visualizes data as a pie chart"
         },
         layoutPosition: {
            xs: {
               gridX: 0,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            sm: {
               gridX: 0,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            md: {
               gridX: 0,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            lg: {
               gridX: 0,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            xl: {
               gridX: 0,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
         },
         settings: {
            title: "Top API Requests",
            description: "Blah blah",
            dataSource: {
               href: "https://api.platform.com/api/v1/dataSources/1234",
               type: "DATA_SOURCE",
               id: "1234",
               name: "Metrics Data"
            },
            refreshInterval: "5m"
         }
      },
      {
         gadget: {
            type: GadgetType.AllService,
            id: "b2cd3ef5-a789-41b9-9f12-57a143ed9876",
            name: "Connectors",
            description: "Visualizes data as a pie chart"
         },
         layoutPosition: {
            xs: {
               gridX: 0,
               gridY: 1,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            sm: {
               gridX: 1,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            md: {
               gridX: 1,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            lg: {
               gridX: 1,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            xl: {
               gridX: 1,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
         },
         settings: {
            title: "Top API Requests",
            description: "Blah blah",
            dataSource: {
               href: "https://api.platform.com/api/v1/dataSources/1234",
               type: "DATA_SOURCE",
               id: "1234",
               name: "Metrics Data"
            },
            refreshInterval: "5m"
         }
      },
      {
         gadget: {
            type: GadgetType.Integrations,
            id: "b2cd3ef5-a789-41b9-9f12-57a143ed9877",
            name: "Integrations",
            description: "Visualizes data as a pie chart"
         },
         layoutPosition: {
            xs: {
               gridX: 0,
               gridY: 2,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            sm: {
               gridX: 0,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            md: {
               gridX: 2,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            lg: {
               gridX: 2,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            xl: {
               gridX: 2,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
         },
         settings: {
            title: "Top API Requests",
            description: "Blah blah",
            dataSource: {
               href: "https://api.platform.com/api/v1/dataSources/1234",
               type: "DATA_SOURCE",
               id: "1234",
               name: "Metrics Data"
            },
            refreshInterval: "5m"
         }
      },
      {
         gadget: {
            type: GadgetType.BiDirectional,
            id: "b2cd3ef5-a789-41b9-9f12-57a143ed9878",
            name: "Webhooks",
            description: "Visualizes data as a pie chart"
         },
         layoutPosition: {
            xs: {
               gridX: 0,
               gridY: 3,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            sm: {
               gridX: 2,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            md: {
               gridX: 3,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            lg: {
               gridX: 3,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
            xl: {
               gridX: 3,
               gridY: 0,
               gridWidth: 1,
               gridHeight: 1,
               isFixedPosition: true
            },
         },
         settings: {
            title: "Webhooks",
            description: "Blah blah",
            dataSource: {
               href: "https://api.platform.com/api/v1/dataSources/1234",
               type: "DATA_SOURCE",
               id: "1234",
               name: "Metrics Data"
            },
            refreshInterval: "5m"
         }
      },

      //  graphs
      {
         gadget: {
            type: GadgetType.APIRequest,
            id: "b2cd3ef5-a789-41b9-9f12-57a143ed9879",
            name: "Total API Requests",
            description: "Visualizes data as a pie chart"
         },
         layoutPosition: {
            xs: {
               gridX: 0,
               gridY: 4,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            sm: {
               gridX: 0,
               gridY: 2,
               gridWidth: 1,
               gridHeight: 2,
               isFixedPosition: true
            },
            md: {
               gridX: 0,
               gridY: 1,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            lg: {
               gridX: 0,
               gridY: 1,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            xl: {
               gridX: 0,
               gridY: 1,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
         },
         settings: {
            title: "Top API Requests",
            description: "Blah blah",
            dataSource: {
               href: "https://api.platform.com/api/v1/dataSources/1234",
               type: "DATA_SOURCE",
               id: "1234",
               name: "Metrics Data"
            },
            refreshInterval: "5m"
         }
      },
      {
         gadget: {
            type: GadgetType.EventRequest,
            id: "b2cd3ef5-a789-41b9-9f12-57a143ed9879",
            name: "Total Event Requests",
            description: "Visualizes data as a pie chart"
         },
         layoutPosition: {
            xs: {
               gridX: 0,
               gridY: 4,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            sm: {
               gridX: 1,
               gridY: 2,
               gridWidth: 1,
               gridHeight: 2,
               isFixedPosition: true
            },
            md: {
               gridX: 2,
               gridY: 1,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            lg: {
               gridX: 2,
               gridY: 1,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            xl: {
               gridX: 2,
               gridY: 1,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
         },
         settings: {
            title: "Top Event Requests",
            description: "Blah blah",
            dataSource: {
               href: "https://api.platform.com/api/v1/dataSources/1234",
               type: "DATA_SOURCE",
               id: "1234",
               name: "Metrics Data"
            },
            refreshInterval: "5m"
         }
      },

      //  integration stats
      {
         gadget: {
            type: GadgetType.IntegrationsStats,
            id: "b2cd3ef5-a789-41b9-9f12-57a143ed9880",
            name: "Integrations by category",
            description: "Visualizes data as a pie chart"
         },
         layoutPosition: {
            xs: {
               gridX: 0,
               gridY: 8,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            sm: {
               gridX: 0,
               gridY: 4,
               gridWidth: 2,
               gridHeight: 3,
               isFixedPosition: true
            },
            md: {
               gridX: 0,
               gridY: 3,
               gridWidth: 2,
               gridHeight: 3,
               isFixedPosition: true
            },
            lg: {
               gridX: 0,
               gridY: 3,
               gridWidth: 1,
               gridHeight: 3,
               isFixedPosition: true
            },
            xl: {
               gridX: 0,
               gridY: 3,
               gridWidth: 1,
               gridHeight: 3,
               isFixedPosition: true
            },
         },
         settings: {
            title: "Integrations by category",
            description: "Blah blah",
            dataSource: {
               href: "https://api.platform.com/api/v1/dataSources/1234",
               type: "DATA_SOURCE",
               id: "1234",
               name: "Metrics Data"
            },
            refreshInterval: "5m"
         }
      },

      //  service stats
      {
         gadget: {
            type: GadgetType.ServicesStats,
            id: "b2cd3ef5-a789-41b9-9f12-57a143ed981",
            name: "Connectors by category",
            description: "Visualizes data as a pie chart"
         },
         layoutPosition: {
            xs: {
               gridX: 0,
               gridY: 11,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            sm: {
               gridX: 2,
               gridY: 6,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            md: {
               gridX: 2,
               gridY: 3,
               gridWidth: 2,
               gridHeight: 3,
               isFixedPosition: true
            },
            lg: {
               gridX: 1,
               gridY: 3,
               gridWidth: 1,
               gridHeight: 3,
               isFixedPosition: true
            },
            xl: {
               gridX: 1,
               gridY: 3,
               gridWidth: 1,
               gridHeight: 3,
               isFixedPosition: true
            },
         },
         settings: {
            title: "Services by category",
            description: "Blah blah",
            dataSource: {
               href: "https://api.platform.com/api/v1/dataSources/1234",
               type: "DATA_SOURCE",
               id: "1234",
               name: "Metrics Data"
            },
            refreshInterval: "5m"
         }
      },

      // error stats
      {
         gadget: {
            type: GadgetType.ErrorStats,
            id: "b2cd3ef5-a789-41b9-9f12-57a143ed9882",
            name: "Errors",
            description: "Visualizes data as a pie chart"
         },
         layoutPosition: {
            xs: {
               gridX: 0,
               gridY: 13,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            sm: {
               gridX: 0,
               gridY: 9,
               gridWidth: 4,
               gridHeight: 2,
               isFixedPosition: true
            },
            md: {
               gridX: 0,
               gridY: 6,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            lg: {
               gridX: 3,
               gridY: 3,
               gridWidth: 2,
               gridHeight: 2,
               isFixedPosition: true
            },
            xl: {
               gridX: 3,
               gridY: 3,
               gridWidth: 2,
               gridHeight: 3,
               isFixedPosition: true
            },
         },
         settings: {
            title: "Linked Accounts by category",
            description: "Blah blah",
            dataSource: {
               href: "https://api.platform.com/api/v1/dataSources/1234",
               type: "DATA_SOURCE",
               id: "1234",
               name: "Errors"
            },
            refreshInterval: "5m"
         }
      },

      // traffic provider stats
      {
         gadget: {
            type: GadgetType.TrafficProviderStats,
            id: "b2cd3ef5-a789-41b9-9f12-57a143ed9882",
            name: "Traffic usage by connectors",
            description: "Visualizes data as a pie chart"
         },
         layoutPosition: {
            xs: {
               gridX: 0,
               gridY: 15,
               gridWidth: 2,
               gridHeight: 2.7,
               isFixedPosition: true
            },
            sm: {
               gridX: 0,
               gridY: 11,
               gridWidth: 2,
               gridHeight: 3,
               isFixedPosition: true
            },
            md: {
               gridX: 4,
               gridY: 6,
               gridWidth: 4,
               gridHeight: 3,
               isFixedPosition: true
            },
            lg: {
               gridX: 0,
               gridY: 6,
               gridWidth: 4,
               gridHeight: 3,
               isFixedPosition: true
            },
            xl: {
               gridX: 0,
               gridY: 6,
               gridWidth: 4,
               gridHeight: 3.8,
               isFixedPosition: true
            },
         },
         settings: {
            title: "Traffic usage by providers",
            description: "Blah blah",
            dataSource: {
               href: "https://api.platform.com/api/v1/dataSources/1234",
               type: "DATA_SOURCE",
               id: "1234",
               name: "Errors"
            },
            refreshInterval: "5m"
         }
      },
   ],
   tags: [
      {
         key1: "value1"
      },
      {
         key2: "value2"
      }
   ],
   owner: {
      id: 90732584,
      username: "Kiri me",
      email: "<EMAIL>",
      color: "",
      initials: "KM",
      profilePicture: null
   },
   organization: {
      href: "https://api.platform.com/api/v1/organizations/8e2d7aef-a411-42a4-8e00-8c5f299a5c5f",
      type: "STANDARD",
      id: "8e2d7aef-a411-42a4-8e00-8c5f299a5c5f",
      name: "Acme Corporation",
      description: "Organization responsible for this dashboard"
   },
   changeLog: {
      createdBy: "adminUser",
      updatedBy: "editorUser",
      createdDateTime: "2024-01-01T10:00:00Z",
      updatedDateTime: "2024-01-15T12:00:00Z"
   },
   changeRequest: {
      type: "DASHBOARD_UPDATE_REQUEST",
      description: "Update dashboard layout",
      status: "PENDING",
      createdDateTime: "2024-12-01T08:30:00Z",
      updatedDateTime: "2024-12-02T10:00:00Z"
   }
};

export default gridModal;