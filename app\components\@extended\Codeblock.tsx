import React from "react";
import SimpleBarScroll from "components/third-party/SimpleBar"

import Highlight from "prism-react-renderer";
import * as pkg from "prism-react-renderer";

import { styled } from "@mui/material";
import theme from "prism-react-renderer/themes/palenight";

interface CodeBlock {
   children: any
   height?: React.CSSProperties['height']
}
export const Pre = styled('pre')`
  text-align: left;
  margin: 1em 0;
  padding: 0.5em;
  white-space: pre-wrap; 
  overflow-wrap: normal;
  word-break: normal; 
  box-sizing: border-box;
  & .token-line {
    line-height: 1.3em;
  }
`;




export const CodeBlock = ({ children, height = 500 }: CodeBlock) => {

   return (
      <Highlight {...pkg.defaultProps} theme={theme} code={children} language="jsx">
         {({ style, tokens, getLineProps, getTokenProps }: any) => (
            <Pre
               style={{ ...style, height }} className="overflow-auto"
            >
               <SimpleBarScroll
                  sx={{
                     '& .simplebar-content':
                        { display: 'flex', flexDirection: 'column' },
                  }}
               >
                  {tokens.map((line: any, i: number) => (
                     <div {...getLineProps({ line })} key={i}>
                        {line.map((token: any, key: number) => (
                           <span {...getTokenProps({ token })} key={key} />
                        ))}
                     </div>
                  ))}
               </SimpleBarScroll>
            </Pre>
         )}
      </Highlight>
   )
}