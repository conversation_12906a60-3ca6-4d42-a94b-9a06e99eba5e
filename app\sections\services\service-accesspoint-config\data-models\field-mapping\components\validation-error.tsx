import { Alert } from "@mui/material"
import { AlertCircle } from "lucide-react"

const ValidationError = ({ validationError }: { validationError: string | null }) => {

   if (!validationError) return

   return (
      <Alert
         severity="error"
         icon={<AlertCircle size={16} />}
         sx={{
            mt: 2,
            py: 0.5,
            "& .MuiAlert-message": { fontSize: "0.875rem" },
         }}
      >
         {validationError}
      </Alert>
   )
}

export default ValidationError;