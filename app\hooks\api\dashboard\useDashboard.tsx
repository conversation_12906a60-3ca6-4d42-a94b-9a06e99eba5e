import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { statsClient } from "services/stats.service";
import useUserDetails from "store/user";
import { API_ENDPOINTS } from "utils/api/api-endpoints";

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { organizationClient } from "services/organization.service";

import { getPromisesDataByState, getServiceStatsPayload } from './helper';
import moment from "moment";

dayjs.extend(utc);
dayjs.extend(timezone);

enum STATS_ENUMS {
   API_UTILIZATION = 'API_UTILIZATION',
   EVENT_UTILIZATION = 'EVENT_UTILIZATION',
   INTEGRATION_STATS = 'INTEGRATION_STATS',
   ERROR_STATS = 'ERROR_STATS',
   PROVIDER_STATS = 'PROVIDER_STATS',
   SERVICE_STATS = 'SERVICE_STATS',
}

export enum GraphType {
   API = 'API_UTILIZATION',
   EVENTS = 'EVENT_UTILIZATION',
   PROVIDER_STATS = 'PROVIDER_STATS',
}

type UseDashboard = {
   subsIds?: string[]
}

const getContainer = (item: Record<string, any>, type: GraphType) => {
   switch (type) {
      case GraphType.API:
         return item?.apiUtilization?.metrics
      case GraphType.EVENTS:
         return item?.eventUtilization?.metrics
      default:
         return {};
   }
}

const LABEL_MAP: any = {
   succeed: {
      label: 'Success'
   },
   failed: {
      label: 'Failed'
   },
}

const getProcessed = (data: any[], type: any) => {
   const tempArr: any = { succeed: [], failed: [] },
      dates: any = [],
      series: any = []
   let max = 0, min = 0;

   data?.forEach((item: any) => {
      const utilized = getContainer(item, type) ?? [];

      utilized?.forEach((i: any) => {
         const {
            intervalEndDateTime,
            successCount,
            totalCount,
            failureCount
         } = i,
            day = dayjs(intervalEndDateTime).format('MMM D');
         if (dates.includes(day)) {
            try {
               const existedIndex = dates.indexOf(day);
               tempArr.succeed[existedIndex] += successCount;
               tempArr.failed[existedIndex] += failureCount;
            } catch (error) {
               console.error(error);
            }
         } else {
            tempArr.succeed.push(successCount);
            tempArr.failed.push(failureCount);
            dates.push(day)
         };
         if (totalCount > max) max = totalCount;
      })

   });

   Object.keys(tempArr).forEach((key) => {
      series.push({
         name: LABEL_MAP?.[key]?.label,
         data: tempArr[key]
      })
   })


   return { series, max, min, dates }
}


export const useDashboard = () => {

   const { subscriptions } = useUserDetails();

   const subsIds = useMemo(() => {
      return subscriptions?.map((i: Record<string, any>) => i?.id) || [];
   }, [subscriptions]);

   const productKeys = useMemo(() => {
      return subscriptions?.map((i: Record<string, any>) => i?.productKey) || [];
   }, [subscriptions]);


   const { data: apiUtils } = useQuery({
      queryKey: [API_ENDPOINTS.STATS, STATS_ENUMS.API_UTILIZATION],
      queryFn: async () => {
         return await statsClient.getUtilization(STATS_ENUMS.API_UTILIZATION)
      },
      select: (resp) => {
         return getProcessed(resp?.data?.data, STATS_ENUMS.API_UTILIZATION) ?? [];
      },
      enabled: !!subsIds?.length
   });

   const { data: eventUtils } = useQuery({
      queryKey: [API_ENDPOINTS.STATS, STATS_ENUMS.EVENT_UTILIZATION],
      queryFn: async () => {
         return await statsClient.getUtilization(STATS_ENUMS.EVENT_UTILIZATION)
      },
      select: (resp) => {
         return getProcessed(resp?.data?.data, STATS_ENUMS.EVENT_UTILIZATION) ?? [];
      },
      enabled: !!subsIds?.length
   });

   const { data: integrationStats } = useQuery({
      queryKey: [API_ENDPOINTS.STATS, STATS_ENUMS.INTEGRATION_STATS],
      queryFn: async () => {
         return await statsClient.getIntegrationStats()
      },
      select: (resp) => {
         const data = resp?.data?.data || [];
         return data
      }
   });

   const { data: errorStats } = useQuery({
      queryKey: [API_ENDPOINTS.STATS, STATS_ENUMS.ERROR_STATS],
      queryFn: async () => {
         return await statsClient.getUtilization(STATS_ENUMS.ERROR_STATS)
      },
      select: (resp) => {
         return resp?.data?.data ?? []
      },
      enabled: !!subsIds?.length
   });

   const { data: providerStats } = useQuery({
      queryKey: [API_ENDPOINTS.STATS, STATS_ENUMS.PROVIDER_STATS],
      queryFn: async () => {
         return await statsClient.getProviderStats(STATS_ENUMS.PROVIDER_STATS)
      },
      select: (resp) => {
         return resp?.data?.data ?? []
      },
      enabled: !!subscriptions?.length
   });

   const { data: serviceStats } = useQuery({
      queryKey: [API_ENDPOINTS.STATS, STATS_ENUMS.SERVICE_STATS],
      queryFn: async () => {
         return await Promise.allSettled(
            productKeys.map((id) => {
               return organizationClient.searchOrgServices(getServiceStatsPayload(id))
            })
         )
      },
      select: (resp) => {

         const data = getPromisesDataByState(resp)?.map(({ data }) => data);

         const stats = productKeys?.reduce((acc: Record<string, any>, cur, index) => {
            const { pagination } = data[index] ?? {};
            const totalCount = pagination?.total ?? 0;
            acc[index] = { type: cur, stats: { type: 'count', totalCount } }
            return acc;
         }, []) ?? {};

         return stats
      },
      enabled: !!productKeys?.length
   });

   return {
      apiUtils,
      eventUtils,
      integrationStats: integrationStats ?? [],
      errorStats: errorStats ?? [],
      providerStats: providerStats ?? [],
      serviceStats: serviceStats ?? []
   }
}