/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/display-name */
import { useMemo, useState, useCallback } from "react";
import useMediaQuery from "@mui/material/useMediaQuery";
import { Button, Stack, Typography, Grid } from "@mui/material";
import LearnMoreLink from 'components/@extended/LearnMoreLink';

import _ from "lodash";

import Table from "components/@extended/Table";
import { useGetOrganization } from "hooks/api/organization/useGetOrganization";
import { useTable } from "hooks/table/useTable";
import { useServiceProfile } from "hooks/useServiceProfile";
import useUserDetails from "store/user";

import Create from './create';

const CUSTOM_OPTIONS = [{ label: 'Platform', value: 'PLATFORM_WATCH_HOOK', key: 'PLATFORM' }]

export default () => {
   const downSM = useMediaQuery((theme: any) => theme.breakpoints.down('md'));
   const [isOpen, setIsOpen] = useState<boolean>(false)

   const { user, subscriptions: subscriptionsData } = useUserDetails(),
      { getAllDomains } = useServiceProfile(),
      { configs } = useGetOrganization({ id: user?.organization?.id });



   const data = configs?.data ?? [];

   const [isEditMode, setIsEditMode] = useState<boolean>(false);
   const [selected, setSelected] = useState<Record<string, any> | null>(null);
   const [paginationState, setPaginationState] = useState<any>({
      pageIndex: 0,
      pageSize: 10,
   })

   function extractProperties<T extends Record<string, any>>(arr: Array<T>, property: keyof T): Array<T[keyof T]> {
      return arr.reduce((acc: Array<T[keyof T]>, cur) => {
         if (cur && property in cur) {
            acc.push(cur[property]);
         }
         return acc;
      }, []);
   }

   const getHookTypeLabel = useCallback((hook: string) => {
      const extra = {
        name: 'Platform',
        value: 'PLATFORM',
        label: 'Platform',
        key: 'PLATFORM',
        hook: 'PLATFORM_WATCH_HOOK',
        released: false,
        getUpgraded: function () {
          return this.externalKey[this.externalKey.length - 1];
        },
        externalKey: ['PLATFORM_WATCH_HOOK', 'PLATFORM_WATCH_2_WAY_HOOK'],
        color: 'teal',
        description: 'Core platform services and integration capabilities supporting other modules.'
      };
    
      const all = [...(getAllDomains() ?? []), extra];
      return all.find((i) => i?.hook === hook)?.label ?? '';
    }, []);
    

   const columns: any = useMemo(
      () => [
         {
            accessorKey: 'state', //access nested data with dot notation
            header: 'Category',
            cell({ row: { original: { type: hook } } }: any) {
               return getHookTypeLabel(hook)
            }
         },
         {
            accessorKey: 'data.url',
            header: 'Callback Url',

         },
         {
            accessorKey: 'action', //normal accessorKey
            header: 'Actions',
            cell({ row: { original } }: any) {
               return (
                  <Button
                     onClick={() => {
                        onOpen(true, true,
                           {
                              ...original,
                              name: getHookTypeLabel(original?.type)
                           })
                     }}
                     size="small"
                  >
                     Edit
                  </Button>
               )
            }
         },
      ],
      [],
   );

   const getOptions = useCallback(() => {

      const options = getAllDomains()
         .map(({ hook: value, key, label }) => ({ label, value, key }));

      const filteredOptions = options.filter(option =>
         subscriptionsData.map(({ product }) => product.code?.toUpperCase()).includes(option?.key)
      );
      return filteredOptions.concat(CUSTOM_OPTIONS).filter(({ value, ...rest }: any) => {
         return (
            !extractProperties(data, 'type')?.includes(value) &&
            !rest.disabled
         );
      });

   }, [data, subscriptionsData]);


   const onOpen = (arg: boolean,
      mode: boolean = false,
      selected: Record<string, any> | null = null
   ) => {
      setIsOpen(arg);
      setIsEditMode(mode)
      mode && setSelected(selected)
   }

   const onClose = () => {
      setIsOpen(false);
   }

   const {
      paginationModal: { pagination }
   } = useTable();

   return (
      <Stack gap={2}>
         <Grid container spacing={downSM ? 2 : 0} >
            <Grid item md={8} xs={12}>
               <Typography variant='body2' sx={{ color: 'text.secondary' }}>
               Get notified when events occur with HTTP POST requests to your configured URLs. <LearnMoreLink href="https://docs.unizo.ai/webhooks" target="_blank" rel="noopener noreferrer">
                  Learn more
                  </LearnMoreLink>
               </Typography>

            </Grid>
            <Grid item md={4} xs={12} sx={!downSM ? { textAlign: 'end' } : {}}>
               <Button variant='contained' color='primary' sx={{ ml: 'auto' }} onClick={() => onOpen(true, false, null)}>
                  Create Callback Url
               </Button>
            </Grid>
         </Grid>
         <Stack gap={2}>
            <Table
               data={data}
               columns={columns}
               totalData={pagination?.total}
               {...{
                  onPaginationChange: setPaginationState,
                  state: {
                     pagination: {
                        pageIndex: paginationState.pageIndex,
                        pageSize: paginationState?.pageSize,
                     }
                  } as any,
               }}
            />
         </Stack>
         <Create isEditMode={isEditMode} selected={selected} isOpen={isOpen} onClose={onClose} options={getOptions()} />
      </Stack>
   )
}
