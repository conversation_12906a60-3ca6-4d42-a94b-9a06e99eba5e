# Quick Start Configuration Guide

## Overview

This guide covers how to configure the Quick Start flow for different tenant types and customize the experience based on business requirements.

## Tenant Types

### Predefined Configurations

The system includes several predefined tenant configurations:

#### Trial
```typescript
{
  type: 'trial',
  isPredefined: false,
  allowCustomization: true,
  skipAllowed: true,
  predefinedCategories: undefined
}
```
- Full flexibility to explore all categories
- Can skip the quick start
- No pre-selected categories

#### Basic
```typescript
{
  type: 'basic',
  predefinedCategories: ['SCM', 'TICKETING'],
  isPredefined: true,
  allowCustomization: true,
  skipAllowed: false
}
```
- Pre-selected: Source Control Management and Ticketing
- Can add additional categories
- Cannot skip quick start

#### Enterprise
```typescript
{
  type: 'enterprise',
  predefinedCategories: ['SCM', 'TICKETING', 'PCR', 'INCIDENT'],
  isPredefined: true,
  allowCustomization: true,
  skipAllowed: false
}
```
- Comprehensive set of categories
- Can add more categories
- Must complete quick start

#### Security
```typescript
{
  type: 'security',
  predefinedCategories: ['VMS', 'IDENTITY', 'EDR'],
  isPredefined: true,
  allowCustomization: false,
  skipAllowed: false
}
```
- Security-focused categories only
- Cannot modify selection
- Must complete quick start

#### Infrastructure
```typescript
{
  type: 'infra',
  predefinedCategories: ['INFRA', 'MONITORING', 'INCIDENT'],
  isPredefined: true,
  allowCustomization: true,
  skipAllowed: false
}
```
- Infrastructure management focused
- Can add additional categories
- Must complete quick start

## Custom Configuration

### Creating Custom Tenant Types

Add new tenant types by extending the configuration:

```typescript
// In your configuration file
export const CUSTOM_TENANT_CONFIGS = {
  healthcare: {
    type: 'healthcare',
    predefinedCategories: ['EMR', 'BILLING', 'COMPLIANCE'],
    isPredefined: true,
    allowCustomization: false,
    skipAllowed: false,
    customMessage: 'Set up your healthcare integrations'
  },
  retail: {
    type: 'retail',
    predefinedCategories: ['POS', 'INVENTORY', 'CRM'],
    isPredefined: true,
    allowCustomization: true,
    skipAllowed: false,
    customMessage: 'Configure your retail systems'
  }
};
```

### Dynamic Configuration

Configure tenant settings at runtime:

```typescript
// API endpoint to update tenant configuration
async function updateTenantQuickStartConfig(
  tenantId: string, 
  config: Partial<TenantConfiguration>
) {
  const currentConfig = await getTenantConfig(tenantId);
  
  const updatedConfig = {
    ...currentConfig,
    ...config,
    // Ensure required fields
    type: config.type || currentConfig.type || 'custom',
    isPredefined: config.isPredefined ?? currentConfig.isPredefined,
    allowCustomization: config.allowCustomization ?? true,
    skipAllowed: config.skipAllowed ?? false
  };
  
  await saveTenantConfig(tenantId, updatedConfig);
  return updatedConfig;
}
```

## Category Configuration

### Available Categories

Default categories available in the system:

```typescript
const CATEGORIES = {
  // Development Tools
  SCM: {
    id: 'SCM',
    name: 'Source Control Management',
    description: 'Git, GitHub, GitLab, Bitbucket',
    icon: 'git-branch',
    color: '#FF6B6B'
  },
  CI_CD: {
    id: 'CI_CD',
    name: 'CI/CD',
    description: 'Jenkins, CircleCI, GitHub Actions',
    icon: 'play-circle',
    color: '#4ECDC4'
  },
  
  // Project Management
  TICKETING: {
    id: 'TICKETING',
    name: 'Ticketing',
    description: 'Jira, Linear, Asana',
    icon: 'ticket',
    color: '#45B7D1'
  },
  PCR: {
    id: 'PCR',
    name: 'Project Collaboration',
    description: 'Slack, Teams, Discord',
    icon: 'users',
    color: '#96CEB4'
  },
  
  // Security
  VMS: {
    id: 'VMS',
    name: 'Vulnerability Management',
    description: 'Snyk, SonarQube, Veracode',
    icon: 'shield',
    color: '#FECA57'
  },
  IDENTITY: {
    id: 'IDENTITY',
    name: 'Identity Management',
    description: 'Okta, Auth0, Azure AD',
    icon: 'user-check',
    color: '#FF9FF3'
  },
  EDR: {
    id: 'EDR',
    name: 'Endpoint Detection',
    description: 'CrowdStrike, SentinelOne',
    icon: 'monitor',
    color: '#54A0FF'
  },
  
  // Infrastructure
  INFRA: {
    id: 'INFRA',
    name: 'Infrastructure',
    description: 'AWS, Azure, GCP, Terraform',
    icon: 'server',
    color: '#48DBFB'
  },
  MONITORING: {
    id: 'MONITORING',
    name: 'Monitoring',
    description: 'Datadog, New Relic, Prometheus',
    icon: 'activity',
    color: '#FF6B9D'
  },
  INCIDENT: {
    id: 'INCIDENT',
    name: 'Incident Management',
    description: 'PagerDuty, Opsgenie',
    icon: 'alert-circle',
    color: '#C44569'
  }
};
```

### Adding Custom Categories

Register new categories for your organization:

```typescript
// Register custom category
function registerCustomCategory(category: CategoryConfig) {
  // Validate category
  if (!category.id || !category.name) {
    throw new Error('Category must have id and name');
  }
  
  // Add to registry
  CUSTOM_CATEGORIES[category.id] = {
    ...category,
    isCustom: true,
    createdAt: new Date().toISOString()
  };
  
  // Update available categories for tenants
  updateAvailableCategories();
}

// Example usage
registerCustomCategory({
  id: 'CRM',
  name: 'Customer Relationship Management',
  description: 'Salesforce, HubSpot, Pipedrive',
  icon: 'users',
  color: '#00D2D3',
  connectors: ['salesforce', 'hubspot', 'pipedrive']
});
```

## Service Configuration

### Predefined Service Mappings

Map categories to available services:

```typescript
const CATEGORY_SERVICES = {
  SCM: [
    { id: 'github', name: 'GitHub', authType: 'oauth' },
    { id: 'gitlab', name: 'GitLab', authType: 'oauth' },
    { id: 'bitbucket', name: 'Bitbucket', authType: 'oauth' }
  ],
  TICKETING: [
    { id: 'jira', name: 'Jira', authType: 'oauth' },
    { id: 'linear', name: 'Linear', authType: 'api_key' },
    { id: 'asana', name: 'Asana', authType: 'oauth' }
  ],
  // ... more mappings
};
```

### Service Priority and Recommendations

Configure which services to show first:

```typescript
const SERVICE_PRIORITY = {
  SCM: ['github', 'gitlab', 'bitbucket'],
  TICKETING: ['jira', 'linear', 'asana'],
  MONITORING: ['datadog', 'newrelic', 'prometheus']
};

// Get recommended services for a category
function getRecommendedServices(category: string, tenantType: string) {
  const priority = SERVICE_PRIORITY[category] || [];
  const allServices = CATEGORY_SERVICES[category] || [];
  
  // Sort by priority
  return allServices.sort((a, b) => {
    const aIndex = priority.indexOf(a.id);
    const bIndex = priority.indexOf(b.id);
    
    if (aIndex === -1 && bIndex === -1) return 0;
    if (aIndex === -1) return 1;
    if (bIndex === -1) return -1;
    
    return aIndex - bIndex;
  });
}
```

## Quick Start Settings

### Global Settings

Configure global quick start behavior:

```typescript
const QUICK_START_SETTINGS = {
  // Display settings
  showProgressBar: true,
  showStepNumbers: true,
  showHelpLinks: true,
  
  // Behavior settings
  autoSaveInterval: 30000, // 30 seconds
  sessionTimeout: 1800000, // 30 minutes
  maxRetries: 3,
  
  // Validation settings
  minCategories: 1,
  maxCategories: 10,
  requireServiceConfig: false,
  
  // UI settings
  theme: 'light',
  compactMode: false,
  showAnimations: true
};
```

### Per-Tenant Settings

Override settings for specific tenants:

```typescript
interface TenantQuickStartSettings {
  tenantId: string;
  settings: {
    forceCompletion?: boolean;
    customWelcomeMessage?: string;
    skipSteps?: string[];
    requiredServices?: string[];
    customValidation?: (progress: QuickStartProgress) => boolean;
  };
}

// Apply tenant-specific settings
function applyTenantSettings(
  tenantId: string, 
  defaultSettings: QuickStartSettings
): QuickStartSettings {
  const tenantOverrides = getTenantSettingsOverrides(tenantId);
  
  return {
    ...defaultSettings,
    ...tenantOverrides,
    // Merge nested objects
    validation: {
      ...defaultSettings.validation,
      ...tenantOverrides.validation
    }
  };
}
```

## Validation Rules

### Category Selection Validation

```typescript
const CATEGORY_VALIDATION_RULES = {
  trial: {
    minSelection: 1,
    maxSelection: 5,
    allowedCategories: 'all'
  },
  basic: {
    minSelection: 2,
    maxSelection: 4,
    requiredCategories: ['SCM', 'TICKETING'],
    allowedCategories: ['SCM', 'TICKETING', 'MONITORING', 'CI_CD']
  },
  enterprise: {
    minSelection: 4,
    maxSelection: 10,
    requiredCategories: ['SCM', 'TICKETING'],
    allowedCategories: 'all'
  }
};

function validateCategorySelection(
  selected: string[], 
  tenantType: string
): ValidationResult {
  const rules = CATEGORY_VALIDATION_RULES[tenantType];
  
  if (!rules) {
    return { valid: true };
  }
  
  // Check minimum selection
  if (selected.length < rules.minSelection) {
    return {
      valid: false,
      message: `Please select at least ${rules.minSelection} categories`
    };
  }
  
  // Check maximum selection
  if (selected.length > rules.maxSelection) {
    return {
      valid: false,
      message: `Please select no more than ${rules.maxSelection} categories`
    };
  }
  
  // Check required categories
  if (rules.requiredCategories) {
    const missing = rules.requiredCategories.filter(
      cat => !selected.includes(cat)
    );
    
    if (missing.length > 0) {
      return {
        valid: false,
        message: `Required categories: ${missing.join(', ')}`
      };
    }
  }
  
  return { valid: true };
}
```

## Environment-Specific Configuration

### Development
```typescript
const DEV_CONFIG = {
  skipAuthCheck: true,
  mockApiResponses: true,
  showDebugInfo: true,
  defaultTenantType: 'trial',
  autoCompleteSteps: true
};
```

### Staging
```typescript
const STAGING_CONFIG = {
  skipAuthCheck: false,
  mockApiResponses: false,
  showDebugInfo: true,
  defaultTenantType: 'trial',
  enableAnalytics: true
};
```

### Production
```typescript
const PROD_CONFIG = {
  skipAuthCheck: false,
  mockApiResponses: false,
  showDebugInfo: false,
  defaultTenantType: null,
  enableAnalytics: true,
  sentryDsn: process.env.SENTRY_DSN
};
```

## Localization

### Multi-language Support

```typescript
const QUICK_START_TRANSLATIONS = {
  en: {
    welcome: 'Welcome to Unizo',
    selectCategories: 'Select API Categories',
    configureServices: 'Configure Services',
    complete: 'Complete Setup'
  },
  es: {
    welcome: 'Bienvenido a Unizo',
    selectCategories: 'Seleccionar Categorías de API',
    configureServices: 'Configurar Servicios',
    complete: 'Completar Configuración'
  },
  // ... more languages
};

// Usage
function getTranslation(key: string, language: string = 'en') {
  return QUICK_START_TRANSLATIONS[language]?.[key] || 
         QUICK_START_TRANSLATIONS.en[key];
}
```

## Advanced Configuration

### Conditional Steps

Show/hide steps based on selections:

```typescript
const CONDITIONAL_STEPS = {
  webhookConfig: {
    showIf: (progress) => progress.selectedCategories.includes('INCIDENT'),
    required: true
  },
  advancedSettings: {
    showIf: (progress) => progress.selectedServices.length > 3,
    required: false
  }
};
```

### Custom Validators

Add custom validation logic:

```typescript
const CUSTOM_VALIDATORS = {
  enterpriseCompliance: (progress: QuickStartProgress) => {
    // Ensure enterprise tenants have security categories
    if (progress.tenantType === 'enterprise') {
      const hasSecurityCategory = progress.selectedCategories.some(
        cat => ['VMS', 'IDENTITY', 'EDR'].includes(cat)
      );
      
      if (!hasSecurityCategory) {
        return {
          valid: false,
          message: 'Enterprise plans require at least one security category'
        };
      }
    }
    
    return { valid: true };
  }
};