import { useState } from 'react';

import { EventsType, useIdleTimer } from 'react-idle-timer';

import { TimerWorker } from './timer.worker';
import { ACTIVITY_DEBOUNCE_MS, ACTIVITY_EVENTS, IDLE_SESSION_DURATION_MS, SHOW_WARNING_DURATION_S } from './constant';
import { Button, DialogActions, DialogContent, DialogContentText, DialogTitle, Stack, Typography } from '@mui/material';
import { Dialog } from 'components/@extended/dialog';
import { useGetUser } from 'hooks/api/user/useUser';
import { ExclamationCircleFilled, LogoutOutlined } from '@ant-design/icons';


const MODAL_TITLE = 'Session Timeout';
const MODAL_MESSAGE = `You will be logged out in five minutes due to inactivity on the portal. Use the 'Stay' button to remain logged in.`;

let portalTitleUpdated = false;
const PORTAL_TITLE = document.title;
let timerWorker: any;


const getDisplayTime = (duration: number) => {
   let minutes: string | number = parseInt((duration / 60) as any, 10);
   let seconds: string | number = parseInt((duration % 60) as any, 10);
   minutes = minutes < 10 ? `0${minutes}` : minutes;
   seconds = seconds < 10 ? `0${seconds}` : seconds;
   return `${minutes}:${seconds}`;
};

const updatePortalTitle = (displayTime: string) => {
   document.title = `(${displayTime}) ${PORTAL_TITLE}`;
   portalTitleUpdated = true;
};

const resetPortalTitle = () => {
   if (portalTitleUpdated) {
      document.title = PORTAL_TITLE;
      portalTitleUpdated = false;
   }
};

export function useSessionTimeout() {

   const [idle, setIdle] = useState<boolean>(false);
   const [countdown, setCountdown] = useState<string | null>(null);

   const { attemptUserLogout } = useGetUser({})

   const stopTimer = () => {
      if (timerWorker) {
         timerWorker.stop();
      }
   }

   const onIdle = () => {
      const displayName = getDisplayTime(SHOW_WARNING_DURATION_S);
      setIdle(true);
      updatePortalTitle(displayName);
      setCountdown(displayName);

      stopTimer();

      timerWorker = new TimerWorker(
         (timeLeft: any) => {
            const displayTime = getDisplayTime(timeLeft);
            updatePortalTitle(displayTime);
            setCountdown(displayTime);
         },
         () => onLeave()
      );
      timerWorker.start(SHOW_WARNING_DURATION_S)
   };

   const onActive = () => {
      if (timerWorker) {
         timerWorker.stop();
      }

      resetPortalTitle();
      setIdle(false);
   };


   useIdleTimer({
      crossTab: true,
      timeout: IDLE_SESSION_DURATION_MS,
      debounce: ACTIVITY_DEBOUNCE_MS,
      events: ACTIVITY_EVENTS as EventsType[],
      stopOnIdle: true,
      onIdle,
      onActive,
   });

   const onLeave = () => {
      stopTimer();
      resetPortalTitle();
      attemptUserLogout(() => { });
   };

   const onResume = async () => {
      window.location.reload();
   };

   return (
      <Dialog
         open={idle}
         maxWidth={'xs'}
      >
         <DialogTitle variant='h5' >
                  {MODAL_TITLE}
         </DialogTitle>
         <DialogContent>
            <DialogContentText color={'secondary.600'} >
               {MODAL_MESSAGE}
            </DialogContentText>
         </DialogContent>

         <DialogActions>
            <Button color='error' onClick={onLeave} startIcon={<LogoutOutlined />} variant='outlined' >Cancel</Button>
            <Button color='primary' onClick={onResume} variant='contained'  >Stay</Button>
         </DialogActions>

      </Dialog>
   )
}