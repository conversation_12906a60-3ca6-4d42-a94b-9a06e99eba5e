/* eslint-disable @typescript-eslint/no-explicit-any */
import { API_ENDPOINTS } from "utils/api/api-endpoints";
import fetchInstance from "utils/api/fetchinstance";
import platformfetchInstance from "utils/api/fetchinstance/platform-fetch-Instance";


export const tenantsClient = {
  search: (payload: Record<string, any>) => {
    return platformfetchInstance.post(`${API_ENDPOINTS.TENANTS}/search`, payload);
  },
  searchProspects: (payload: Record<string, any>) => {
    return platformfetchInstance.post(`prospects/search`, payload);
  },
  searchProducts: () => {
    return platformfetchInstance.get(`products?sort=displayOrder&limit=20`);
  },
  getPricingTier: (productId: string) => {
    return platformfetchInstance.get(`products/${productId}/pricingTiers`);
  },
  getPricingTierEntitlements: (productId: string, pricingTierId: string) => {
    return platformfetchInstance.get(`products/${productId}/pricingTiers/${pricingTierId}`);
  },
  getOrganizationSubscription: (id: string) => {
    return platformfetchInstance.get(`${API_ENDPOINTS.ORGANIZATION}s/${id}/${API_ENDPOINTS.SUBSCRIPTION}s`);
  },

  getSubscriptionById: (id: string, subscriptionId: string) => {
    return platformfetchInstance.get(`${API_ENDPOINTS.ORGANIZATION}s/${id}/${API_ENDPOINTS.SUBSCRIPTION}s/${subscriptionId}`);
  },

  create: (payload: any) => {
    return platformfetchInstance.post(`${API_ENDPOINTS.TENANTS}`, payload);
  },
  delete: (id: string) => {
    return platformfetchInstance.delete(`${API_ENDPOINTS.TENANTS}/${id}?force=true`)
  },
  createOrganizationSubscription: (id: string, payload: Record<string, any>) => {
    return platformfetchInstance.post(`${API_ENDPOINTS.ORGANIZATION}s/${id}/${API_ENDPOINTS.SUBSCRIPTION}s`, payload);
  },

  inviteBulkUsers: (payload: Record<string, any>, orgId: string) => {
    return platformfetchInstance.post(`${API_ENDPOINTS.USERS}/bulk`, payload, {
      headers: {
        organizationid: orgId
      }
    });
  },
  getTenantMetadata: (tenantId: string) => {
    return platformfetchInstance.get(`${API_ENDPOINTS.TENANT_META_DATA}/${tenantId}`);
  },
  getAllTenantMetadata: (config?: { headers?: Record<string, string> }) => {
    return platformfetchInstance.get(`${API_ENDPOINTS.TENANT_META_DATA}`, config);
  },
  /**get all tenants */
  getAllTenants: (params: object = {}) => {
    return platformfetchInstance.get(`${API_ENDPOINTS.TENANTS}`, { params });
  }
};
