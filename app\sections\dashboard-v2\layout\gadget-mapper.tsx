import { GadgetType } from "./grid-modal"
import { GadgetConfig } from "./grid-type";


import AllCategories from '../widgets/all-categories';
import AllServices from '../widgets/all-services';
import AllIntegrations from '../widgets/all-integrations';
import AllBidirectional from '../widgets/all-bidirectionals';

// graphs
import APIRequests from '../widgets/api-requests';
import EventRequest from '../widgets/event-requests';
import IntegrationStats from '../widgets/integration-stats';
import ErrorStats from '../widgets/error-stats';
import ServiceStats from '../widgets/service-stats';
import TrafficProviderStats from '../widgets/traffic-provider-stats';


export default (props: GadgetConfig): any => {
   return {
      [GadgetType.AllCategories]: (
         <AllCategories {...props} />
      ),
      [GadgetType.AllService]: (
         <AllServices {...props} />
      ),
      [GadgetType.Integrations]: (
         <AllIntegrations {...props} />
      ),
      [GadgetType.BiDirectional]: (
         <AllBidirectional {...props} />
      ),

      // graphs
      [GadgetType.APIRequest]: (
         <APIRequests {...props} />
      ),
      [GadgetType.EventRequest]: (
         <EventRequest {...props} />
      ),
      [GadgetType.IntegrationsStats]: (
         <IntegrationStats {...props} />
      ),
      [GadgetType.ErrorStats]: (
         <ErrorStats {...props} />
      ),
      [GadgetType.ServicesStats]: (
         <ServiceStats {...props} />
      ),
      [GadgetType.TrafficProviderStats]: (
         <TrafficProviderStats {...props} />
      ),
   }
}