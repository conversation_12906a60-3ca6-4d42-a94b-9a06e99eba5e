
export function extractColumnMeta(cell) {
   let meta = { ...(cell?.column?.columnDef?.meta || {}) };
   delete meta?.filterType

   if (typeof meta?.injectHeaderCellProps === 'function') {
      delete meta.injectHeaderCellProps
   }

   if (typeof meta?.injectCellProps === 'function') {
      // extended props injection layer for each column
      meta = { ...meta, ...meta.injectCellProps(cell) }
      delete meta.injectCellProps
   }

   return meta
}

export function extractHeaderColumnMeta(header) {
   let meta = { ... (header?.column?.columnDef?.meta || {}) };
   delete meta?.filterType;

   if (typeof meta?.injectCellProps === 'function') {
      delete meta.injectCellProps
   }

   if (typeof meta?.injectHeaderCellProps === 'function') {
      // extended props injection layer for each column
      meta = { ...meta, ...meta.injectHeaderCellProps(header) }
      delete meta.injectHeaderCellProps
   }

   return meta
}

// pagination block
export function getPaginationTotal(total, size = 10) {
   return Math.round(total / size)
}