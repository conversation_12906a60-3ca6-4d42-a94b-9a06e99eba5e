import { useQuery } from '@tanstack/react-query';
import { getAuthUserId, getAuthUserOrgId } from 'utils/auth';

interface AuthUser {
  id: string;
  orgId: string;
  email?: string;
  name?: string;
  role?: string;
  tenantType?: string;
}

/**
 * Hook to get authenticated user information
 */
export const useAuthUser = () => {
  const userId = getAuthUserId();
  const orgId = getAuthUserOrgId();

  const { data: user, isLoading, error } = useQuery({
    queryKey: ['auth', 'user', userId],
    queryFn: async (): Promise<AuthUser> => {
      // For now, return basic user info from window object
      // In production, this would fetch full user details from API
      return {
        id: userId,
        orgId: orgId,
        // Additional fields would come from API
      };
    },
    enabled: !!userId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    user,
    isLoading,
    error,
    isAuthenticated: !!userId,
  };
};