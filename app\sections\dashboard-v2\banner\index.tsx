// material-ui
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project import
import MainCard from 'components/MainCard';
import useUserDetails from 'store/user';


// ==============================|| DASHBOARD - TOP CARD ||============================== //

export default function Banner() {
  const theme = useTheme();
  const downSM = useMediaQuery(theme.breakpoints.down('sm'));

  const { user } = useUserDetails()

  return (
    <MainCard border={false} content={false}
      sx={{
        position: 'relative',
        backgroundColor: "transparent",

        
      }}>
      <Grid container justifyContent="space-between" alignItems="center" sx={{ position: 'relative', zIndex: 5 }}>
        <Grid item>
          <Stack direction="row" spacing={{ xs: 1, sm: 2 }} alignItems="center">
            <Box sx={{ ml: { xs: 0, sm: 1 } }}>
            </Box>
            <Stack spacing={0.75}>
              <Typography variant="h3">
                Hey, {user?.firstName}! 👋
              </Typography>
              <Stack direction={'row'} gap={1} alignItems={'center'}>
                <Typography variant='h5' color="secondary.600">
                  You have
                </Typography>
                <Typography variant="h5" className='link' >0 Pending Actions</Typography>
              </Stack>
            </Stack>
          </Stack>
        </Grid>
      </Grid>
    </MainCard>
  );
}
