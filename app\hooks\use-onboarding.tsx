/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect } from "react";
import { AxiosResponse } from "axios";

import { useNavigate } from "@remix-run/react";

import { tenantsClient } from "services/tenants.service";

import useUserDetails from "store/user";

import { ResponseModel } from "types/common";
import Tenant from "types/tenant";

import { State } from "./useStatus";

import { getIsInValidOrganization } from "utils/organizaton";
import { Routes } from "constants/route";
import { TenantOnboardType } from "constants/organization";

interface IUseOnboarding {
   orgId: string | null,
   userId: string | null
}

const useOnboarding = ({ userId }: IUseOnboarding) => {
   const { isLoading, fetchUser, user, isSupeAdmin } = useUserDetails();
   const navigate = useNavigate();

   const navigateToGettingStart = (type: TenantOnboardType) => {
      navigate(`${Routes.GettingStarted}?type=${type}`, { replace: true });
   }

   useEffect(() => {
      if (userId) {
         fetchUser(userId);
      }
   }, []);

   useEffect(() => {
      if (!isLoading && user) {
         if (!isSupeAdmin) {
            const validateOrganization = async () => {
               /**
                * check for the organization id is valid, if not strictly navigate to getting started page
                */
               if (getIsInValidOrganization(user?.organization?.id)) {
                  navigateToGettingStart(TenantOnboardType.Self);
                  return;
               }
               try {
                  const response: AxiosResponse<ResponseModel<Tenant>> = await tenantsClient.getAllTenants({ scope: 'organization' }),
                     tenants = response?.data?.data;
                  /**
                   * if tenant is empty or state is not provisioned, navigate to
                   */
                  if (!tenants?.length || tenants?.at(0)?.state !== State.PROVISIONED) {
                     navigateToGettingStart(TenantOnboardType.Self)
                  } else if (user?.organization?.state === State.SUBMITTED) {
                     /**
                      * if organization state is submitted, just throw into getting started with type invitation
                      */
                     navigateToGettingStart(TenantOnboardType.Invitation)
                  }
               } catch (error) { }
            }
            validateOrganization();
         }
      }
   }, [user, isLoading, isSupeAdmin])
   return { isReady: true }
}

export default useOnboarding