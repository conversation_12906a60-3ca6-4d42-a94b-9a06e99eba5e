import { useState } from "react"

import { DeleteOutlined } from "@ant-design/icons"
import { <PERSON><PERSON>, Button, DialogActions, DialogContent, DialogTitle, Stack, TextField, TextFieldProps, Typography } from "@mui/material"
import { Dialog, DialogClose } from "components/@extended/dialog";

import { useGetIntegrationDetails } from "hooks/api/integration/useIntegration-details";
import { useNavigate } from "@remix-run/react";

export const DeleteIntegration = () => {
   const [open, setOpen] = useState<boolean>(false);
   const [entered, setEntered] = useState<string>('');
   const navigate = useNavigate();

   const { integration, attemptDeleteIntegration } = useGetIntegrationDetails();
   const trapName = integration?.name;

   const onOpen = () => {
      setOpen(true)
   },
      onClose = () => {
         setOpen(false)
      };

   const onSubmit = () => {
      attemptDeleteIntegration(integration?.id, () => {
         navigate(-1)
      })
   }

   const onType: TextFieldProps['onChange'] = (e) => {
      const { value } = e.target;
      setEntered(value)
   }

   return (
      <>
         <Button
            color='error'
            variant='contained'
            startIcon={<DeleteOutlined />}
            onClick={onOpen}
         >
            Delete Integration
         </Button>
         <Dialog open={open} onClose={onClose}>
            <DialogTitle variant="h4">Delete Integration</DialogTitle>
            <DialogClose onClose={onClose} />
            <DialogContent dividers>
               <Stack gap={2}>
                  <Typography>
                     Force delete integration <b>{trapName}</b> permanently? This action cannot be undone.
                  </Typography>
                  <Alert color='info'>
                     Proceeding with this action will delete the integration with all content and can impact related resources.
                  </Alert>
                  <Typography>
                     To avoid accidental changes, we ask you to provide additional written consent.
                  </Typography>

                  <Typography>
                     Type <b>{trapName}</b> to agree.
                  </Typography>
                  <TextField value={entered} placeholder={trapName} onChange={onType} />
               </Stack>
            </DialogContent>
            <DialogActions>
               <Button onClick={onClose} color='secondary'>
                  Cancel
               </Button>
               <Button
                  onClick={onSubmit}
                  variant="contained"
                  color='error'
                  startIcon={<DeleteOutlined />}
                  disabled={entered.trim() !== trapName}
               >
                  Delete Integration
               </Button>
            </DialogActions>
         </Dialog>
      </>
   )
}