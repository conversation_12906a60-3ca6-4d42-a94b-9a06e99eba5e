/**
 * OpenAPI Specification Processor
 * Processes OpenAPI JSON schemas to extract field structures for field mapping
 */

export interface OpenApiField {
  id: string;
  name: string;
  displayName?: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'array' | 'object';
  required?: boolean;
  description?: string;
  path?: string;
  children?: OpenApiField[];
  format?: string;
  example?: any;
  enum?: string[];
  isArrayItem?: boolean;
  arrayParent?: string;
  arrayIndex?: number;
  isDynamicIndex?: boolean;
}

export interface OpenApiSchema {
  type: string;
  properties?: Record<string, any>;
  items?: any;
  required?: string[];
  description?: string;
  example?: any;
  enum?: string[];
  format?: string;
  allOf?: any[];
  oneOf?: any[];
  anyOf?: any[];
  $ref?: string;
}

export class OpenApiProcessor {
  private schemas: Record<string, OpenApiSchema> = {};
  private resolvedRefs: Map<string, any> = new Map();

  constructor(private openApiSpec: any) {
    this.schemas = openApiSpec?.components?.schemas || {};
  }

  /**
   * Process a schema definition and convert to our field structure
   */
  processSchema(schemaName: string, parentPath = ''): OpenApiField | null {
    const schema = this.schemas[schemaName];
    if (!schema) return null;

    // Process the schema but skip the root schema name in paths for UI compatibility
    const processed = this.processSchemaDefinition(schema, schemaName, parentPath);
    
    // If this is a root schema, we need to return the children directly
    // to avoid having "Repository" as the root path
    if (!parentPath && processed.children) {
      return {
        ...processed,
        id: schemaName.toLowerCase(),
        name: schemaName.toLowerCase(),
        path: schemaName.toLowerCase(),
        children: processed.children.map(child => this.adjustChildPaths(child, schemaName))
      };
    }
    
    return processed;
  }

  /**
   * Adjust child paths to remove the root schema name for UI compatibility
   */
  private adjustChildPaths(field: OpenApiField, rootSchemaName: string): OpenApiField {
    const newPath = field.path?.replace(`${rootSchemaName}.`, '') || field.name;
    
    return {
      ...field,
      id: newPath,
      path: newPath,
      children: field.children?.map(child => this.adjustChildPaths(child, rootSchemaName))
    };
  }

  /**
   * Process schema definition recursively
   */
  private processSchemaDefinition(
    schema: OpenApiSchema,
    fieldName: string,
    parentPath = '',
    processedRefs = new Set<string>()
  ): OpenApiField {
    const currentPath = parentPath ? `${parentPath}.${fieldName}` : fieldName;

    // Handle $ref
    if (schema.$ref) {
      const refName = this.extractRefName(schema.$ref);
      if (processedRefs.has(refName)) {
        // Circular reference - return a simplified object
        return {
          id: currentPath,
          name: fieldName,
          displayName: this.formatDisplayName(fieldName),
          type: 'object',
          description: `Reference to ${refName} (circular)`,
          path: currentPath,
        };
      }
      
      processedRefs.add(refName);
      const referencedSchema = this.schemas[refName];
      if (referencedSchema) {
        const result = this.processSchemaDefinition(referencedSchema, fieldName, parentPath, processedRefs);
        processedRefs.delete(refName);
        return result;
      }
    }

    // Handle allOf, oneOf, anyOf
    if (schema.allOf || schema.oneOf || schema.anyOf) {
      return this.processCompositeSchema(schema, fieldName, parentPath, processedRefs);
    }

    const field: OpenApiField = {
      id: currentPath,
      name: fieldName,
      displayName: this.formatDisplayName(fieldName),
      type: this.mapOpenApiType(schema),
      description: schema.description,
      path: currentPath,
      format: schema.format,
      example: schema.example,
      enum: schema.enum,
    };

    // Process object properties
    if (schema.type === 'object' && schema.properties) {
      field.children = [];
      const requiredFields = schema.required || [];

      Object.entries(schema.properties).forEach(([propName, propSchema]) => {
        const childField = this.processSchemaDefinition(
          propSchema as OpenApiSchema,
          propName,
          currentPath,
          processedRefs
        );
        childField.required = requiredFields.includes(propName);
        field.children!.push(childField);
      });
    }

    // Process array items - enhanced for creative array-object handling
    if (schema.type === 'array' && schema.items) {
      field.children = [];
      const itemSchema = schema.items as OpenApiSchema;
      
      // Handle array of objects creatively
      if (itemSchema.type === 'object' && (itemSchema.properties || itemSchema.$ref)) {
        // Create a virtual "item" container for the array element
        const itemField = this.processSchemaDefinition(
          itemSchema,
          'item',
          currentPath,
          processedRefs
        );
        
        // Enhance the item field with array-specific metadata
        itemField.isArrayItem = true;
        itemField.arrayParent = fieldName;
        itemField.displayName = `${this.formatDisplayName(fieldName)} Item`;
        itemField.description = `Individual ${fieldName} object in the array`;
        
        field.children.push(itemField);
        
        // Also create indexed examples for better UX (first 3 items)
        for (let i = 0; i < 3; i++) {
          const indexedField = this.processSchemaDefinition(
            itemSchema,
            `[${i}]`,
            currentPath,
            processedRefs
          );
          
          indexedField.isArrayItem = true;
          indexedField.arrayIndex = i;
          indexedField.arrayParent = fieldName;
          indexedField.displayName = `${this.formatDisplayName(fieldName)}[${i}]`;
          indexedField.description = `${fieldName}[${i}] - Array item at index ${i}`;
          
          field.children.push(indexedField);
        }
        
        // Add a dynamic "any index" option
        const dynamicField = this.processSchemaDefinition(
          itemSchema,
          '[*]',
          currentPath,
          processedRefs
        );
        
        dynamicField.isArrayItem = true;
        dynamicField.isDynamicIndex = true;
        dynamicField.arrayParent = fieldName;
        dynamicField.displayName = `${this.formatDisplayName(fieldName)}[*]`;
        dynamicField.description = `${fieldName}[*] - Any array item (dynamic index)`;
        
        field.children.push(dynamicField);
      } else {
        // Handle array of primitives normally
        const itemField = this.processSchemaDefinition(
          itemSchema,
          'item',
          currentPath,
          processedRefs
        );
        field.children.push(itemField);
      }
    }

    return field;
  }

  /**
   * Process composite schemas (allOf, oneOf, anyOf)
   */
  private processCompositeSchema(
    schema: OpenApiSchema,
    fieldName: string,
    parentPath: string,
    processedRefs: Set<string>
  ): OpenApiField {
    const currentPath = parentPath ? `${parentPath}.${fieldName}` : fieldName;
    const field: OpenApiField = {
      id: currentPath,
      name: fieldName,
      displayName: this.formatDisplayName(fieldName),
      type: 'object',
      description: schema.description || 'Composite schema',
      path: currentPath,
      children: [],
    };

    // Merge properties from all schemas
    const allProperties: Record<string, any> = {};
    const allRequired: string[] = [];

    const schemasToProcess = schema.allOf || schema.oneOf || schema.anyOf || [];
    
    schemasToProcess.forEach((subSchema: any) => {
      if (subSchema.$ref) {
        const refName = this.extractRefName(subSchema.$ref);
        const referencedSchema = this.schemas[refName];
        if (referencedSchema?.properties) {
          Object.assign(allProperties, referencedSchema.properties);
          if (referencedSchema.required) {
            allRequired.push(...referencedSchema.required);
          }
        }
      } else if (subSchema.properties) {
        Object.assign(allProperties, subSchema.properties);
        if (subSchema.required) {
          allRequired.push(...subSchema.required);
        }
      }
    });

    // Process merged properties
    Object.entries(allProperties).forEach(([propName, propSchema]) => {
      const childField = this.processSchemaDefinition(
        propSchema as OpenApiSchema,
        propName,
        currentPath,
        processedRefs
      );
      childField.required = allRequired.includes(propName);
      field.children!.push(childField);
    });

    return field;
  }

  /**
   * Map OpenAPI types to our field types
   */
  private mapOpenApiType(schema: OpenApiSchema): OpenApiField['type'] {
    switch (schema.type) {
      case 'string':
        if (schema.format === 'date' || schema.format === 'date-time') {
          return 'date';
        }
        return 'string';
      case 'integer':
      case 'number':
        return 'number';
      case 'boolean':
        return 'boolean';
      case 'array':
        return 'array';
      case 'object':
        return 'object';
      default:
        return 'string';
    }
  }

  /**
   * Extract reference name from $ref path
   */
  private extractRefName(ref: string): string {
    return ref.split('/').pop() || ref;
  }

  /**
   * Format field name for display
   */
  private formatDisplayName(name: string): string {
    return name
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Generate mapping data for a specific endpoint
   */
  generateMappingData(schemaName: string): {
    sourceModel: OpenApiField;
    targetModel: OpenApiField;
  } {
    const sourceModel = this.processSchema(schemaName);
    
    if (!sourceModel) {
      throw new Error(`Schema ${schemaName} not found`);
    }

    // For demo purposes, we'll create a simplified target model
    // In real implementation, this would come from your unified schema
    const targetModel = this.generateUnifiedModel(schemaName);

    return {
      sourceModel,
      targetModel,
    };
  }

  /**
   * Generate a simplified unified model for demonstration
   */
  private generateUnifiedModel(schemaName: string): OpenApiField {
    // This is a simplified version - in practice, you'd have your actual unified schema
    const baseFields: OpenApiField[] = [
      {
        id: 'id',
        name: 'id',
        displayName: 'ID',
        type: 'string',
        required: true,
        description: 'Unique identifier',
        path: 'id',
      },
      {
        id: 'name',
        name: 'name',
        displayName: 'Name',
        type: 'string',
        required: true,
        description: 'Display name',
        path: 'name',
      },
      {
        id: 'description',
        name: 'description',
        displayName: 'Description',
        type: 'string',
        description: 'Description',
        path: 'description',
      },
      {
        id: 'created_at',
        name: 'created_at',
        displayName: 'Created At',
        type: 'date',
        description: 'Creation timestamp',
        path: 'created_at',
      },
      {
        id: 'updated_at',
        name: 'updated_at',
        displayName: 'Updated At',
        type: 'date',
        description: 'Last update timestamp',
        path: 'updated_at',
      },
    ];

    return {
      id: `unified_${schemaName}`,
      name: `unified_${schemaName}`,
      displayName: `Unified ${this.formatDisplayName(schemaName)}`,
      type: 'object',
      description: `Unified model for ${schemaName}`,
      path: `unified_${schemaName}`,
      children: baseFields,
    };
  }
}

/**
 * Helper function to convert OpenAPI field to our hierarchical structure
 */
export function convertToHierarchicalField(openApiField: OpenApiField): any {
  return {
    id: openApiField.id,
    name: openApiField.name,
    displayName: openApiField.displayName,
    type: openApiField.type,
    required: openApiField.required,
    description: openApiField.description,
    path: openApiField.path,
    children: openApiField.children?.map(convertToHierarchicalField),
  };
}