import { List, ListItemButton, <PERSON><PERSON><PERSON><PERSON>ex<PERSON>, Stack, Typography } from "@mui/material";

type Props = {
   stats: Record<string, any>
}

export default ({
   stats,
}: Props) => {

   return (
      <Stack gap={1}>
         <Typography variant="h5">Integrations by connector</Typography>
         {stats?.xAxisLabels?.length ? (
            <List sx={{ p: 0, }}>
               {stats?.xAxisLabels?.map((item: Record<string, any>, index: number) => {
                  const { integrations } = stats;

                  return (
                     <ListItemButton divider key={index}>
                        <ListItemText
                           primary={item?.toString()}
                           primaryTypographyProps={{ variant: 'subtitle1' }}
                           secondaryTypographyProps={{ variant: 'body1', color: 'text.secondary', sx: { display: 'inline' } }}
                        />
                        <Stack direction={'row'} gap={2} >
                           <Typography variant="h5" >
                              {integrations?.[index]}
                           </Typography>
                        </Stack>
                     </ListItemButton>
                  )
               })}
            </List>
         ) : (
            <Stack gap={1} alignItems={'center'} mt={4} sx={{ height: '100%' }} >
               <Typography variant='subtitle1' color={'secondary.800'} >No Record Found</Typography>
               <Typography variant='body1' color={'secondary.600'} >When you have records available, they will show up here.</Typography>
            </Stack>
         )}

      </Stack>
   );
}