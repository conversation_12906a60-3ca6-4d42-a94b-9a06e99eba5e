import { getAuthUserId, getAuthUserOrgId } from './auth';

interface ApiRequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: string;
}

interface ApiResponse<T = any> {
  data: T;
  status: number;
}

/**
 * Client-side API request utility
 */
export async function apiRequest<T = any>(
  endpoint: string,
  options: ApiRequestOptions = {}
): Promise<ApiResponse<T>> {
  const userId = getAuthUserId();
  const orgId = getAuthUserOrgId();

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // Add auth headers if available
  if (userId) {
    headers['X-User-Id'] = userId;
  }
  if (orgId) {
    headers['X-Org-Id'] = orgId;
  }

  try {
    const response = await fetch(endpoint, {
      method: options.method || 'GET',
      headers,
      body: options.body,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `Request failed: ${response.status}`);
    }

    return {
      data,
      status: response.status,
    };
  } catch (error) {
    console.error(`API request failed: ${endpoint}`, error);
    throw error;
  }
}