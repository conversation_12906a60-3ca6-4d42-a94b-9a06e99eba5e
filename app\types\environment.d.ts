import { EnvironmentOperationState } from "constants/environment"
import { Changelog } from "./common"

declare namespace Environment {

   export interface Root {
      href: string
      type: string
      state: string
      id: string
      key: string
      name: string
      description: string
      colorPicker?: string
      settings: Settings
      operation: Operation
      notifications: Notification[]
      organization: Organization
      pod: Pod
      changeLog: Changelog
    }

    export interface Settings {
      isDefault: boolean
      region: string
      active: boolean
      configOverrides: ConfigOverrides
    }

    export interface ConfigOverrides {
      apiRateLimitPerMinute: number
      enableAuditLogging: boolean
      logRetentionDays: number
    }

    export interface Operation {
      status: EnvironmentOperationState
    }

    export interface Notification {
      type: string
    }

    export interface Organization {
      id: string
    }

    export interface Pod {
      id: string
      key: string
    }

}

export default Environment;