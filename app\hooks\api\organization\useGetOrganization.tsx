import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { organizationClient } from "services/organization.service";

import { API_ENDPOINTS } from "utils/api/api-endpoints";
import { toast } from "sonner"
import { parseError } from "lib/utils";
import { useEffect } from "react";
import useInitialSetup from "store/initial-setup";
import useUserDetails from "store/user";

export type SPQueryOptionsType = {
   offset?: number;
   limit?: number;
   orderBy?: string;
};

enum OrgOnboardingRequestState {
   Complete = 'ONBOARDING_COMPLETED_REQUEST'
}

const ORG_SERVICES = 'services';
const ORG_WATCHES = 'ORG_WATCHES';
const PROFILE_EVENTS = 'PROFILE_EVENTS'

export const useGetOrganization = ({ id }: { id: string | undefined }) => {

   const queryClient = useQueryClient(),
      { setValues } = useInitialSetup();

   const {
      mutateAsync: createWatchMutation,
      isPending: isCreating,
   } = useMutation({
      mutationFn: (payload: any) => {
         return organizationClient.createOrgWatchConfig(id as string, payload)
      },
      mutationKey: [API_ENDPOINTS.ORGANIZATION],
   });

   const {
      mutateAsync: updateWatchMutation,
      isPending: isUpdating,
   } = useMutation({
      mutationFn: ({ payload, watchId }: any) => {
         return organizationClient.updateOrgWatchConfig(id as string, watchId, payload)
      },
      mutationKey: [API_ENDPOINTS.ORGANIZATION],
   });

   const {
      mutateAsync: updateState,
      isPending: isStateUpdating,
   } = useMutation({
      mutationFn: () => {
         return organizationClient.updateOrganizationState(id as string, {
            type: OrgOnboardingRequestState.Complete
         })
      },
      mutationKey: [API_ENDPOINTS.ORGANIZATION],
   });

   const { data: configData, isLoading: isConfigLoading } = useQuery({
      queryKey: [API_ENDPOINTS.ORGANIZATION, id],
      queryFn: async () => {
         return await organizationClient.getOrgConfig(id as string)
      },
      enabled: !!id
   });

   const { data: watches, isLoading: isWatchesLoading } = useQuery({
      queryKey: [API_ENDPOINTS.ORGANIZATION, id, ORG_WATCHES],
      queryFn: async () => {

         const payload = {
            "filter": {
               "and": [
                  {
                     "property": `/organization/id`,
                     "operator": "=",
                     "values": [id]
                  }
               ]
            },
            "pagination": {
               "limit": 1,
               "offset": 0,
            }
         };

         return await organizationClient.searchOrganizationWatches(payload)
      },
      enabled: !!id
   });

   const searchEventProfiles = (payload: any, isEnabled: boolean = true) => {
      return useQuery({
         queryKey: [API_ENDPOINTS.ORGANIZATION, PROFILE_EVENTS, payload],
         queryFn: () => organizationClient.searchCategoryProfileEvents(payload),
         enabled: !!id && !!isEnabled,
      });
   };

   const { data: services, isLoading: isServicesLoading } = useQuery({
      queryKey: [API_ENDPOINTS.ORGANIZATION, id, ORG_SERVICES],
      queryFn: async () => {

         const payload = {
            "filter": {
               "and": [
                  {
                     "property": "/state",
                     "operator": "=",
                     "values": [
                        "ACTIVE"
                     ]
                  },
                  {
                     "property": "/organization/id",
                     "operator": "=",
                     "values": [
                        id
                     ]
                  }
               ]
            },
            "pagination": {
               "limit": 1,
               "offset": 0
            }
         }
         return await organizationClient.searchOrgServices(payload)
      },
      select: (resp) => {
         return resp?.data
      },
      enabled: !!id
   });

   const attemptToCreateWatch = async (payload: Record<string, any>, cb: any) => {
      toast.promise(createWatchMutation(payload), {
         loading: 'Creating...',
         success: () => {
            cb && cb();
            queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.ORGANIZATION, id] })
            return <b>Webhook created successfully.</b>;
         },
         error: (error: any) => {
            return <b>{parseError(error?.response?.data)?.message}</b>;
         },
      })
   }

   const attemptToUpdateWatch = async (payload: Record<string, any>, watchId: string, cb: any) => {

      toast.promise(updateWatchMutation({ payload, watchId }), {
         loading: 'Updating...',
         success: () => {
            cb && cb();
            queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.ORGANIZATION, id] })
            return <b>Updated successfully.</b>;
         },
         error: (error: any) => {
            return <b>{parseError(error?.response?.data)?.message}</b>;
         },
      })
   }

   const attemptToUpdateOrgOnboarding = async (cb: any) => {
      toast.promise(updateState(), {
         loading: 'Updating...',
         success: () => {
            queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.ORGANIZATION, id] })
            cb && cb()
            return <b>Updated successfully.</b>;
         },
         error: (error: any) => {
            return <b>{parseError(error?.response?.data)?.message}</b>;
         },
      })
   }

   useEffect(() => {
      setValues('watches', configData?.data?.data ?? [])
   }, [configData?.data?.data])

   return {
      isConfigLoading,
      isCreating,
      isUpdating,
      isStateUpdating,
      configs: configData?.data,
      watches: watches?.data,
      isWatchesLoading,
      searchEventProfiles,
      attemptToCreateWatch,
      attemptToUpdateWatch,
      attemptToUpdateOrgOnboarding,

      services,
      isServicesLoading
   }
};
