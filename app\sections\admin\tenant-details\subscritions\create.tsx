import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON>, <PERSON>ton, Dialog, DialogActions, DialogContent, DialogProps, DialogTitle, FormControl, Grid, GridProps, InputLabel, MenuItem, Select, Stack, TextField, Typography } from "@mui/material"
import { useForm } from "react-hook-form";
import { z } from "zod";
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

import { Form, FormField, FormItem } from "components/@extended/Form"
import { useMemo, useRef } from "react";
import { useGetSuperAdmin } from "hooks/api/super-admin/useGetSuperAdmin";
import { CardSelect } from "components/@extended/CardSelect";
import { useParams } from "@remix-run/react";
import { useDate } from "hooks/useDate";
import moment from "moment";
import nameChecker from "constants/categoryMapper";
type CreateProps = {

} & DialogProps;


const SPANS: GridProps = {
   xs: 12,
};

const FORMAT = "MM-DD-YYYY"

export const Create = (props: CreateProps) => {

   const { open, onClose: onCloseProp } = props;

   const formRef = useRef<HTMLFormElement>(null)

   const onClose: DialogProps['onClose'] = (e) => {
      typeof onCloseProp === 'function' && (
         onCloseProp(e, 'backdropClick')
      )
   }

   return (
      <Dialog open={open} onClose={onClose}>
         <DialogTitle variant="h5">Add Subscription</DialogTitle>
         <DialogContent dividers>
            <FormComp formRef={formRef} onClose={onClose} />
         </DialogContent>
         <DialogActions>
            <Stack direction={'row'} justifyContent={'flex-end'} gap={1}>
               <Button onClick={onClose as any} >Cancel</Button>
               <Button
                  variant='contained'
                  color='primary'
                  onClick={() => {
                     formRef.current?.requestSubmit()
                  }}
               >Submit</Button>
            </Stack>
         </DialogActions>
      </Dialog>
   )
}

const defaultValues: any = {
   category: '',
   tier: '',
   dateRange: [null, null]
}

const schema = z.object({
   category: z.string().nonempty("Category is required."),
   tier: z.string().nonempty("Tier is required."),
   startDate: z.string()
      .nonempty({ message: "Start date is required." })
      .refine(val => moment(val, FORMAT, true).isValid(), {
         message: `Invalid start date format. Use ${FORMAT}.`,
      }),
   endDate: z.string()
      .nonempty({ message: "End date is required." })
      .refine(val => moment(val, FORMAT, true).isValid(), {
         message: `Invalid end date format. Use ${FORMAT}.`,
      }),
}).superRefine(({ startDate, endDate }, ctx) => {

   if (startDate && endDate && moment(startDate).isAfter(moment(endDate))) {
      ctx.addIssue({ path: ["endDate"], message: "Start date cannot be after end date." } as any)
   }

})

const getDateFieldValue = (value: any): any => {
   return moment(value, FORMAT, true).isValid() ? moment(value, FORMAT) : null
}

type FormValues = z.infer<typeof schema>;

type FormProps = {
   formRef: React.LegacyRef<HTMLFormElement>
} & Pick<DialogProps, 'onClose'>

const FormComp = ({ formRef, onClose }: FormProps) => {
   const { id } = useParams()

   const form = useForm<FormValues>({
      resolver: zodResolver(schema),
      defaultValues,
      mode: "onChange",
   }),
      { watch, setValue, reset } = form,
      category = watch('category'),
      tier = watch('tier');

   const startDate = watch('startDate');

   const onFieldValueChange = (fieldName: "category" | "tier" | "startDate" | "endDate", value?: string | undefined) => {
      setValue(fieldName, value as any, { shouldValidate: true })
   }

   const {
      searchProducts,
      getProductPricingTier,
      getPricingTierEntitlements,
      getOrgSubscription,
      createSubscription
   } = useGetSuperAdmin(),
      { data: existing } = getOrgSubscription({ orgId: id as string }),
      subscriptions = existing?.data ?? []

   const { data: products = [] } = searchProducts();
   const { data: pricingTiers = [] } = getProductPricingTier({ productId: category })
   const { data: entitlements } = getPricingTierEntitlements({ productId: category, tierId: tier });

   const categoryOptions = useMemo(() => {
      const productId = subscriptions?.map((i: any) => i?.product?.id)

      return (
         products
            ?.filter((i: any) => !productId.includes(i?.id))
            ?.map((tier: any) => {
               return {
                  name: nameChecker(tier.name),
                  value: tier?.id,
               }
            })
      )
   }, [products, subscriptions])

   const onSubmit = (values: FormValues) => {
      const { startDate, endDate } = values;

      const payload = {
         "type": "PRODUCT",
         "product": {
            "id": values?.category
         },
         "charge": {
            "type": "MONTHLY_RECURRING",
            "currency": "USD",
            "pricingTier": {
               "id": values?.tier
            }
         },
         startDate: moment.utc(startDate),
         endDate: moment.utc(endDate)
      }
      createSubscription(id as string, payload, () => {
         reset(defaultValues)
         typeof onClose === 'function' && (
            onClose({}, 'backdropClick')
         )
      })
   }

   return (
      <Form {...form}>
         <form
            onSubmit={(...args) => (
               void form.handleSubmit(onSubmit)(...args)
            )}
            ref={formRef}
            className="flex flex-col gap-5"
         >
            <FormField
               control={form.control}
               name='category'
               render={({ field }) => (
                  <FormItem label='Select Category'>
                     <FormControl>
                        <InputLabel id="select-label">Category</InputLabel>
                        <Select  {...field} labelId="select-label">
                           {categoryOptions.map((domain: any, index: number) => (
                              <MenuItem value={domain?.value} key={index}  >{domain?.name}</MenuItem>
                           ))}
                        </Select>
                     </FormControl>
                  </FormItem>
               )}
            />
            {category && (
               <Stack gap={1}>
                  <Typography variant='h6' className="font-semibold" sx={{ color: "secondary.600" }}>Select the Tier</Typography>
                  <FormField
                     control={form.control}
                     name={'tier'}
                     render={({ field: { onChange } }) => (
                        <FormItem>
                           <FormControl>
                              <CardSelect
                                 options={(
                                    pricingTiers.map((tier: any) => {
                                       return {
                                          title: tier?.name,
                                          value: tier?.id,
                                       }
                                    })
                                 )}
                                 onValueSelect={(e: any) => {
                                    onChange(e.value)
                                 }}
                              />
                           </FormControl>
                        </FormItem>
                     )}
                  />
               </Stack>
            )}

            {tier && (
               <Alert>
                  <Stack gap={1}>
                     <Typography variant={'h6'} className="font-semibold" >
                        {`Entitlements for selected tier`}
                     </Typography>
                     <Grid container columnSpacing={2} rowSpacing={2} >
                        {entitlements?.entitlements?.map((item: any, index: number) => {
                           return (
                              <Grid item key={index} xs={6}>
                                 {item?.displayName && <Typography key={index} style={{ fontSize: "13px" }}>  ✔ &nbsp; &nbsp;{item.displayName}</Typography>}
                              </Grid>
                           )
                        })}
                     </Grid>
                  </Stack>
               </Alert>
            )}

            <Grid container item  {...SPANS} spacing={2}>
               <Grid item xs={6}>
                  <FormField
                     control={form.control}
                     name={'startDate'}
                     render={({ field }: any) => (
                        <FormItem label={'Start Date'} >
                           <FormControl>
                              <DatePicker
                                 slotProps={{
                                    inputAdornment: {
                                       position: 'start',
                                    },
                                 }}
                                 onChange={(e) => {
                                    onFieldValueChange(field?.name, e?.format(FORMAT))
                                 }}
                                 format={FORMAT} // Set the format here
                                 value={getDateFieldValue(field?.value)}
                                 shouldDisableDate={(date) => date < moment().startOf('day')}
                              />
                           </FormControl>
                        </FormItem>
                     )}
                  />
               </Grid>
               <Grid item xs={6}>
                  <FormField
                     control={form.control}
                     name={'endDate'}
                     render={({ field }: any) => (
                        <FormItem label={'End Date'} >
                           <FormControl>
                              <DatePicker
                                 slotProps={{
                                    inputAdornment: {
                                       position: 'start',
                                    },
                                 }}
                                 onChange={(e) => {
                                    onFieldValueChange(field?.name, e?.format(FORMAT))
                                 }}
                                 value={getDateFieldValue(field?.value)}
                                 format={FORMAT} // Set the format here
                                 shouldDisableDate={(date) => {
                                    return date < moment().startOf('day') || date < moment(startDate).startOf('day')
                                 }}
                              />
                           </FormControl>
                        </FormItem>
                     )}
                  />
               </Grid>
            </Grid>


         </form>
      </Form>
   )
}