import React, { useEffect, useMemo, useRef, useState } from "react";
import { Grid, Stack } from "@mui/material";
import { useInfiniteQuery, useMutation } from "@tanstack/react-query";
import { AxiosResponse } from "axios";
import { toast } from "sonner";
import { CardGridSkeleton } from "components/skeletons";

import useEnvironment from "hooks/api/useEnvironment";
import Environment from "types/environment";
import { getIsEnvDefault } from "./utils";
import { EnvironmentSwitchRequestState } from "constants/environment";
import { parseError } from "lib/utils";
import { ResponseModel } from "types/common";
import useUserDetails from "store/user";
import { getSubscriptionFlags } from "utils/subscription";
import Environment_set from "./constants";

import CreateEnv from "./create-env";
import DeleteConfirmation from "./delete-confirmation";
import { EnvironmentCard } from "./components/EnvironmentCard";
import { LaunchStyleEnvironmentCard } from "./components/LaunchStyleEnvironmentCard";
import { PlanInfoCard } from "./components/PlanInfoCard";

const getPlanEnvironments = (plan: string) => {
  const planKey = plan?.toUpperCase() as keyof typeof Environment_set.plans;
  return Environment_set?.plans[planKey]?.environments || [];
};

const Environments = () => {
  const { subscriptions } = useUserDetails();
  const { actionEnvironmentQueryConfig, getAllEnvironmentInfinateQueryConfig } =
    useEnvironment();

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selected, setSelected] = useState<Environment.Root | null>(null);
  const [selectedKey, setSelectedKey] = useState<string | null>(null);
  const [currenttype, setCurrentType] = useState<string | null>(null);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState<boolean>(false);
  const [defailtEnv, setDefaultEnv] = useState<Environment.Root | null>(null);
  const [mode, setMode] = useState<"create" | "edit">("create");

  const loadMoreRef = useRef<HTMLDivElement>(null);

  const {
    isLoading,
    isFetching,
    data,
    fetchNextPage: loadMore,
    refetch: refetchEnvironments,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    ...getAllEnvironmentInfinateQueryConfig(),
  });

  const { mutateAsync: attemptToSwitchEnvironment, isPending: isUpdating } =
    useMutation({
      ...actionEnvironmentQueryConfig(),
      onSettled(resp) {
        setDefaultEnv((resp as AxiosResponse<Environment.Root>).data);
        refetchEnvironments();
      },
    });

  const environments = useMemo(() => {
    return (data?.pages?.reduce(
      (
        acc: (Environment.Root & { color: string })[],
        page: AxiosResponse<ResponseModel<Environment.Root>>
      ) => {
        const mapped =
          page?.data?.data?.map((env) => ({
            ...env,
            color: (env as any).colorPicker || (env as any).color || "#1976d2",
          })) || [];
        return [...acc, ...mapped];
      },
      []
    ) ?? []) as (Environment.Root & { color: string })[];
  }, [data?.pages]);

  const highestTier = getSubscriptionFlags(subscriptions);
  const isEnterprise = highestTier === "Enterprise";
  const isLaunch = highestTier === "Launch";
  const isFreePlan = highestTier === "Free";

  const onEdit = (requestedEnv: Environment.Root) => {
    setSelected(requestedEnv);
    setMode("edit");
    onModalOpen();
  };

  const onModalOpen = (key?: string, name?: string) => {
    setIsOpen(true);
    setSelectedKey(key || null);
    setCurrentType(name ?? "TEST");
  };

  const onModalClose = () => {
    setIsOpen(false);
    setMode("create");
    setSelected(null);
  };

  const onConfirmationModalOpen = (requestedEnv: Environment.Root) => {
    setIsConfirmationModalOpen(true);
    setSelected(requestedEnv);
  };

  const onConfirmationModalClose = () => {
    setIsConfirmationModalOpen(false);
    setSelected(null);
  };

  const onActiveEnvironment = (environment: Environment.Root) => {
    setSelected(environment);
    try {
      toast.promise(
        attemptToSwitchEnvironment({
          environmentId: environment.id,
          payload: { type: EnvironmentSwitchRequestState.DefaultRequest },
        }),
        {
          loading: "Switching...",
          success: (resp) => {
            return `Switched to ${(resp as AxiosResponse<Environment.Root>)?.data?.name}`;
          },
          error: (err) => {
            return parseError(err?.response?.data)?.message;
          },
        }
      );
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (environments.length > 0 && !isFetching) {
      const defaultEnv = environments.find((e) => getIsEnvDefault(e));
      if (defaultEnv) {
        setDefaultEnv(defaultEnv as Environment.Root);
      }
    }
  }, [environments, isFetching]);

  const planEnvironments = useMemo(() => {
    const planEnvs = getPlanEnvironments(highestTier || "Free") || [];

    return planEnvs.filter((planEnv: any) => {
      if (isFreePlan && planEnv.locked === true) {
        return true;
      }

      const existingEnv = environments.find((env) => env.key === planEnv.key);
      return !existingEnv;
    });
  }, [highestTier, environments, isFreePlan]);

  if (isLoading || isFetching) {
    return <CardGridSkeleton count={4} columns={{ xs: 12, sm: 6, md: 4, lg: 3 }} showActions />;
  }

  return (
    <Stack gap={2} ref={loadMoreRef}>
      <Stack spacing={2}>
        {/* Plan Information Card */}
        <PlanInfoCard
          highestTier={highestTier || "Free"}
          isLaunch={isLaunch}
          isEnterprise={isEnterprise}
        />

        {/* Combined Grid for Template Cards and Created Environments */}
        <Grid container spacing={2}>
          {/* Template Cards */}
          {planEnvironments.map((card: any, idx: number) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={`plan-card-${idx}`}>
              <LaunchStyleEnvironmentCard
                card={card}
                onClick={onModalOpen}
              />
            </Grid>
          ))}

          {/* Created Environment Cards */}
          {environments.map((env) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={`env-${env.id}`}>
              <EnvironmentCard
                env={env}
                onEdit={onEdit}
                onDelete={onConfirmationModalOpen}
                onStar={onActiveEnvironment}
                isDefault={getIsEnvDefault(env)}
              />
            </Grid>
          ))}
        </Grid>
      </Stack>

      <CreateEnv
        refetchEnvironments={refetchEnvironments}
        isOpen={isOpen}
        onClose={onModalClose}
        mode={mode}
        slelectedKey={selectedKey}
        selectedType={currenttype}
        selected={selected}
        existingEnvironments={environments}
      />
      
      <DeleteConfirmation
        isOpen={isConfirmationModalOpen}
        onClose={onConfirmationModalClose}
        selected={selected}
        onSuccess={() => {
          onConfirmationModalClose();
          refetchEnvironments();
        }}
      />
    </Stack>
  );
};

export default Environments;