import PropTypes from 'prop-types';
import { Link, useLocation, matchPath } from '@remix-run/react';

// material-ui
import { useTheme, alpha } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// project import
import Dot from 'components/@extended/Dot';
import IconButton from 'components/@extended/IconButton';

import { MenuOrientation, ThemeMode, NavActionType } from 'config';
import useConfig from 'hooks/useConfig';
import { handlerDrawerOpen, useGetMenuMaster } from 'api/menu';
import { Divider, Tooltip } from '@mui/material';
import IconSwitcher from './iconSelector';

// ==============================|| NAVIGATION - LIST ITEM ||============================== //

export default function NavItem({ item, level, isParents = false, setSelectedID }) {
  const theme = useTheme();

  const { menuMaster } = useGetMenuMaster();
  const drawerOpen = menuMaster.isDashboardDrawerOpened;

  const downLG = useMediaQuery(theme.breakpoints.down('lg'));

  const { mode, menuOrientation } = useConfig();
  let itemTarget = '_self';
  if (item.target) {
    itemTarget = '_blank';
  }

  const itemHandler = () => {
    if (downLG) handlerDrawerOpen(false);

    if (isParents && setSelectedID) {
      setSelectedID();
    }
  };

  const Icon = item.icon;

  const { pathname } = useLocation();
  const isSelected = !!matchPath({ path: item?.link ? item.link : item.url, end: false }, pathname);

  const itemIcon = <Icon size={drawerOpen ? 20 : 24} strokeWidth={isSelected ? 2.5 : 2} />

  const textColor = mode === ThemeMode.DARK ? 'grey.400' : 'text.primary';
  const iconSelectedColor = mode === ThemeMode.DARK && drawerOpen ? 'text.primary' : 'primary.main';

  return (
    <>
      {menuOrientation === MenuOrientation.VERTICAL || downLG ? (
        <Box sx={{ position: 'relative' }}>
          <ListItemButton
            component={Link}
            to={item.url}
            target={itemTarget}
            disabled={item.disabled}
            selected={isSelected}
            data-testid={`nav-item-${item.id}`}
            sx={{
              zIndex: 1201,
              ...(item.hidden && { display: 'none' }),
              pl: drawerOpen ? `${level * 28}px` : 0,
              py: !drawerOpen && level === 1 ? 0.75 : 0.75,
              justifyContent: !drawerOpen ? 'center' : 'flex-start',
              ...(drawerOpen && {
                '&:hover': { bgcolor: mode === ThemeMode.DARK ? alpha(theme.palette.primary.main, 0.04) : alpha(theme.palette.primary.main, 0.04) },
                '&.Mui-selected': {
                  bgcolor: mode === ThemeMode.DARK ? alpha(theme.palette.primary.main, 0.15) : alpha(theme.palette.primary.main, 0.08),
                  borderRadius: 0,
                  color: iconSelectedColor,
                  position: 'relative',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: '3px',
                    bgcolor: theme.palette.primary.main
                  },
                  '&:hover': { 
                    color: iconSelectedColor, 
                    bgcolor: mode === ThemeMode.DARK ? alpha(theme.palette.primary.main, 0.2) : alpha(theme.palette.primary.main, 0.12)
                  }
                }
              }),
              ...(!drawerOpen && {
                '&:hover': { bgcolor: 'transparent' },
                '&.Mui-selected': { '&:hover': { bgcolor: 'transparent' }, bgcolor: 'transparent' }
              })
            }}
            onClick={() => itemHandler()}
          >
            {itemIcon && (
              <ListItemIcon
                sx={{
                  minWidth: drawerOpen ? 28 : 40,
                  color: isSelected ? iconSelectedColor : textColor,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  ...(!drawerOpen && {
                    borderRadius: '8px',
                    width: 40,
                    height: 40,
                    '&:hover': {
                      bgcolor: mode === ThemeMode.DARK ? alpha(theme.palette.primary.main, 0.08) : alpha(theme.palette.primary.main, 0.04)
                    }
                  }),
                  ...(!drawerOpen &&
                    isSelected && {
                    bgcolor: mode === ThemeMode.DARK ? alpha(theme.palette.primary.main, 0.2) : alpha(theme.palette.primary.main, 0.12),
                    '&:hover': {
                      bgcolor: mode === ThemeMode.DARK ? alpha(theme.palette.primary.main, 0.25) : alpha(theme.palette.primary.main, 0.16)
                    }
                  })
                }}
                data-testid={`nav-icon-${item.id}`}
              >
                {drawerOpen ? (itemIcon) : (
                  <Tooltip
                    title={item.title}
                    placement='right'
                    arrow
                  >
                    {itemIcon}
                  </Tooltip>
                )}
              </ListItemIcon>
            )}
            {(drawerOpen || (!drawerOpen && level !== 1)) && (
              <ListItemText
                primary={
                  <Typography variant="body1" sx={{ color: isSelected ? iconSelectedColor : textColor, fontWeight: isSelected ? 500 : 400 }} data-testid={`nav-title-${item.id}`}>
                    {item.title}
                  </Typography>
                }
              />
            )}
            {(drawerOpen || (!drawerOpen && level !== 1)) && item.chip && (
              <Chip
                color={item.chip.color}
                variant={item.chip.variant}
                size={item.chip.size}
                label={item.chip.label}
                avatar={item.chip.avatar && <Avatar>{item.chip.avatar}</Avatar>}
              />
            )}
          </ListItemButton>
          {(drawerOpen || (!drawerOpen && level !== 1)) &&
            item?.actions &&
            item?.actions.map((action, index) => {
              const ActionIcon = action.icon;
              const callAction = action?.function;
              return (
                <IconButton
                  key={index}
                  data-testid={`nav-action-${item.id}-${index}`}
                  {...(action.type === NavActionType.FUNCTION && {
                    onClick: (event) => {
                      event.stopPropagation();
                      callAction();
                    }
                  })}
                  {...(action.type === NavActionType.LINK && {
                    component: Link,
                    to: action.url,
                    target: action.target ? '_blank' : '_self'
                  })}
                  color="secondary"
                  variant="outlined"
                  sx={{
                    position: 'absolute',
                    top: 12,
                    right: 20,
                    zIndex: 1202,
                    width: 20,
                    height: 20,
                    mr: -1,
                    ml: 1,
                    color: 'secondary.dark',
                    borderColor: isSelected ? 'primary.light' : 'secondary.light',
                    '&:hover': { borderColor: isSelected ? 'primary.main' : 'secondary.main' }
                  }}
                >
                  <ActionIcon sx={{ fontSize: '0.875rem' }} />
                </IconButton>
              );
            })}
        </Box>
      ) : (
        <ListItemButton
          component={Link}
          to={item.url}
          target={itemTarget}
          disabled={item.disabled}
          selected={isSelected}
          onClick={() => itemHandler()}
          sx={{
            zIndex: 1201,
            ...(isParents && { p: 1, mr: 1 })
          }}
        >
          {itemIcon && (
            <ListItemIcon
              sx={{
                minWidth: 28,
                ...(!drawerOpen && {
                  borderRadius: 1.5,
                  width: 28,
                  height: 28,
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                  '&:hover': { bgcolor: 'transparent' }
                }),
                ...(!drawerOpen && isSelected && { bgcolor: 'transparent', '&:hover': { bgcolor: 'transparent' } })
              }}
            >
              {itemIcon}
            </ListItemIcon>
          )}

          {!itemIcon && (
            <ListItemIcon
              sx={{
                color: isSelected ? 'primary.main' : 'secondary.dark',
                ...(!drawerOpen && {
                  borderRadius: 1.5,
                  alignItems: 'center',
                  justifyContent: 'flex-start',
                  '&:hover': { bgcolor: 'transparent' }
                }),
                ...(!drawerOpen && isSelected && { bgcolor: 'transparent', '&:hover': { bgcolor: 'transparent' } })
              }}
            >
              <Dot size={4} color={isSelected ? 'primary' : 'secondary'} />
            </ListItemIcon>
          )}
          <ListItemText
            primary={
              <Typography variant="body1" sx={{ color: isSelected ? 'primary.main' : 'secondary.dark', fontWeight: isSelected ? 500 : 400 }}>
                {item.title}
              </Typography>
            }
          />
          {(drawerOpen || (!drawerOpen && level !== 1)) && item.chip && (
            <Chip
              color={item.chip.color}
              variant={item.chip.variant}
              size={item.chip.size}
              label={item.chip.label}
              avatar={item.chip.avatar && <Avatar>{item.chip.avatar}</Avatar>}
            />
          )}
        </ListItemButton>
      )}
    </>
  );
}

NavItem.propTypes = {
  setSelectedID: PropTypes.func,
  item: PropTypes.object,
  level: PropTypes.number,
  isParents: PropTypes.bool
};
