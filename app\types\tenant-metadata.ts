export enum OnboardingStepType {
    SELECT_CATEGORY = 'SELECT_CATEGORY',
    SELECT_PROVIDERS = 'SELECT_PROVIDERS',
    SETUP_WEBHOOKS = 'SETUP_WEBHOOKS',
    SELECT_MEMBERS = 'SELECT_MEMBERS',
    STARTUP_PROGRAM = 'STARTUP_PROGRAM',
    GENERAL ='GENERAL',
}

export interface I18nKey {
    title: string;
    subTitle: string;
    description: string;
}

export interface StepValidations {
    mustMatchSelectedCategories: boolean;
    mustHaveValidUrlFormat: boolean;
    allowMultipleWebhooks: boolean;
    enforceSecureProtocol: boolean;
    contactSupportIfBlocked: boolean;
    mustBeInAllowedTypes?: string[];
    maxCategories?: number;
    maxProvidersPerCategory?: number;
    allowedRoles?: string[];
    trialPeriodDays?: number;
}

export interface OnboardingStep {
    type: OnboardingStepType;
    title: string;
    subTitle: string;
    assistanceInfo: string;
    description: string;
    i18nKey: I18nKey;
    validations: StepValidations;
}

export interface TenantMetadata {
    href: string;
    type: string;
    id: string;
    name: string;
    displayId: string;
    description: string;
    state: string;
    onboardingSteps: OnboardingStep[];
}