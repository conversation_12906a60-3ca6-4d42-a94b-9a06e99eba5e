import { State } from "hooks/useStatus"

/* eslint-disable @typescript-eslint/no-explicit-any */
interface Tenant {
   href: string
   type: string
   state: State.PROVISIONING | State.PROVISIONED
   id: string
   name: string
   firstName: string
   description: string
   address: Address
   region: Region
   categories: Category[]
   reasons: Reason[]
   plan: Plan
   organization: Organization
   user: User
   subscriptions: Subscription[]
   members: Member[]
   notifications: Notification[]
   tags: any[]
   attributes: any[]
   links: any[]
   changeLog: ChangeLog
   changeRequest: ChangeRequest
}

export interface Address {
   line1: string
   line2: string
   city: string
   state: string
   country: string
   zipcode: string
}

export interface Region {
   code: string
   name: string
   displayName: string
}

export interface Category {
   type: string
   providers: Provider[]
}

export interface Provider {
   id: string
   type: string
}

export interface Reason {
   type: string
   code: string
   reason: string
}

export interface Plan {
   type: string
   expiryDateTime: string
}

export interface Organization { }

export interface User {
   id: string
}

export interface Subscription {
   type: string
   product: Product
   charge: Charge
   startDate: string
   endDate: string
}

export interface Product {
   id: string
}

export interface Charge {
   pricingTier: PricingTier
   type: string
   currency: string
}

export interface PricingTier {
   id: string
}

export interface Member {
   emails: string[]
   role: Role
}

export interface Role {
   type: string
}

export interface Notification {
   type: string
   email: string[]
}

export interface ChangeLog {
   createdDateTime: string
   lastUpdatedDateTime: string
}

export interface ChangeRequest {
   type: string
   description: string
   status: string
}


export default Tenant;