import {  FilterField } from './type';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export const validateFilterValue = (
  value: string | string[],
  field: FilterField
): ValidationResult => {
  const validation = field.validation;
  
  if (!validation) {
    return { isValid: true };
  }

  // Handle multi-select values
  if (Array.isArray(value)) {
    if (validation.required && value.length === 0) {
      return { isValid: false, error: `${field.label} is required` };
    }
    
    // For multi-select, we don't validate individual values in this basic implementation
    if (validation.custom) {
      const customError = validation.custom(value);
      if (customError) {
        return { isValid: false, error: customError };
      }
    }
    
    return { isValid: true };
  }

  // Handle single values
  const stringValue = value.toString();

  // Required validation
  if (validation.required && (!stringValue || stringValue.trim() === '')) {
    return { isValid: false, error: `${field.label} is required` };
  }

  // Skip other validations if value is empty and not required
  if (!stringValue || stringValue.trim() === '') {
    return { isValid: true };
  }

  // String length validations
  if (field.type === 'text' || field.type === 'url') {
    if (validation.minLength && stringValue.length < validation.minLength) {
      return { 
        isValid: false, 
        error: `${field.label} must be at least ${validation.minLength} characters` 
      };
    }
    
    if (validation.maxLength && stringValue.length > validation.maxLength) {
      return { 
        isValid: false, 
        error: `${field.label} must be at most ${validation.maxLength} characters` 
      };
    }
  }

  // Number validations
  if (field.type === 'number') {
    const numValue = Number(stringValue);
    
    if (isNaN(numValue)) {
      return { isValid: false, error: `${field.label} must be a valid number` };
    }
    
    if (validation.min !== undefined && numValue < validation.min) {
      return { 
        isValid: false, 
        error: `${field.label} must be at least ${validation.min}` 
      };
    }
    
    if (validation.max !== undefined && numValue > validation.max) {
      return { 
        isValid: false, 
        error: `${field.label} must be at most ${validation.max}` 
      };
    }
  }

  // Pattern validation
  if (validation.pattern && !validation.pattern.test(stringValue)) {
    return { 
      isValid: false, 
      error: `${field.label} format is invalid` 
    };
  }

  // URL specific validation
  if (field.type === 'url') {
    try {
      new URL(stringValue);
    } catch {
      return { isValid: false, error: `${field.label} must be a valid URL` };
    }
  }

  // Custom validation
  if (validation.custom) {
    const customError = validation.custom(value);
    if (customError) {
      return { isValid: false, error: customError };
    }
  }

  return { isValid: true };
};

// Predefined validation patterns
export const ValidationPatterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-\(\)]+$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  noSpecialChars: /^[a-zA-Z0-9\s]+$/,
  ipAddress: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  httpUrl: /^https?:\/\/.+/,
} as const;