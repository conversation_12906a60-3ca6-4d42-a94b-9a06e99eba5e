const TYPE_START = 'START';
const TYPE_STOP = 'STOP';
const TYPE_END = 'END';
const TYPE_TIMELEFT = 'TIMELEFT';

// The following is a simple timer worker that will start counting down from the given duration (in seconds)
// and post message updates regularly on time remaining.
// We have to use inline code for the worker to avoid cross-origin problems.

const workerCode = `
	let refreshId;
	onmessage = function ({ data: { type, data: duration } }) {
		if (type === '${TYPE_STOP}') {
			clearInterval(refreshId);
		} else if (type === '${TYPE_START}') {
			const expiresAtTime = new Date().getTime() + (duration * 1000);
			refreshId = setInterval(function () {
				const diff = expiresAtTime - new Date().getTime();
				if (diff < 0) {
					clearInterval(refreshId);
					postMessage({ type: '${TYPE_END}' });
				} else {
					const diffSeconds = Math.floor(diff / 1000) + 1;
					postMessage({ type: '${TYPE_TIMELEFT}', data: diffSeconds });
				}
			}, 1000);
		}
	};
`;
const workerBlob = new Blob([workerCode], { type: 'application/javascript' });

const workerUrl = URL.createObjectURL(workerBlob);

export class TimerWorker {

	public worker: any;

	constructor(onCountdown: any, onEnd: any) {
		this.worker = new Worker(workerUrl);
		this.worker.onmessage = ({ data: { type, data } }: any) => {
			switch (type) {
				case TYPE_TIMELEFT:
					onCountdown(data);
					break;
				case TYPE_END:
					onEnd();
					break;
				default:
			}
		};
	}

	start(duration: number) {
		this.worker.postMessage({ type: TYPE_START, data: duration });
	}

	stop() {
		this.worker.postMessage({ type: TYPE_STOP });
	}
}