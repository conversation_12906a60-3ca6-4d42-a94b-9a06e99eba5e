"use client"

import { LoadingOutlined } from "@ant-design/icons"
import { Toaster as RadToaster } from "sonner"

export function Toaster() {
  return (
    <RadToaster
      position="bottom-right"
      toastOptions={{
        style: {

        },
        classNames: {
          success: 'border-solid border-green-700 bg-green-50 text-green-800',
          error: 'border-solid border-red-700 bg-red-50 text-red-800',
          loading: 'border-solid border-yellow-700 bg-yellow-50 text-yellow-800',
        },
        closeButton: true,
      }}
      pauseWhenPageIsHidden
    />
  )
}
