import { ArrowDownOutlined, CaretDownOutlined, CaretUpOutlined, DownOutlined } from "@ant-design/icons";
import { Box, Button, Grid, Menu, MenuItem, Stack, ToggleButton, ToggleButtonGroup, Typography, useMediaQuery } from "@mui/material";
import Dot from "components/@extended/Dot";
import MainCard from "components/MainCard";
import { ThemeMode } from "config";
import useConfig from "hooks/useConfig";
import { useState } from "react";

export type ItemTypes = {
   title: string,
   value: string
}

type Props = {
   categories: Array<ItemTypes>
   onCategorySelect: (selected: ItemTypes) => void,
   selectedCategory: ItemTypes
}

export default (props: Props) => {

   const { categories, onCategorySelect, selectedCategory } = props;

   const onSelect = (_: any, selected: ItemTypes['value']) => {
      onCategorySelect(categories.find((i) => i.value === selected) as any)
   }

   const value = selectedCategory?.value;

   const downSM = useMediaQuery((theme: any) => theme.breakpoints.down('sm'));

   if (downSM) {
      return (
         <SmMenu onSelect={onSelect} categories={categories} value={value} />
      )
   }

   return (
      <ToggleButtonGroup
         exclusive
         onChange={onSelect}
         size="small"
         value={value}
         sx={{
            flexWrap: 'wrap',
            gap: 1
         }}
      >
         {categories.map((category, index) => {
            return (
               <ToggleButton
                  disabled={value === category.value}
                  value={category?.value}
                  sx={(theme) => {
                     return {
                        px: 2,
                        py: 0.5,
                        maxWidth: 200,
                        minWidth: 200,
                        border: `1px solid ${theme.palette.divider} !important`
                     }
                  }}
                  key={index}
               >
                  <Typography className="truncate">
                     {category?.title}
                  </Typography>
               </ToggleButton>
            )
         })}
      </ToggleButtonGroup>
   )
}

function SmMenu({ categories, value, onSelect: onSelectProp }: Record<string, any>) {
   const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
   const open = Boolean(anchorEl);
   const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      setAnchorEl(event.currentTarget);
   };
   const handleClose = () => {
      setAnchorEl(null);
   };

   const onSelect = (e: any, newSelected: string) => {
      typeof onSelectProp === 'function' && (
         onSelectProp(e, newSelected)
      )
      handleClose()
   }

   return (
      <div>
         <Button
            id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
            endIcon={<DownOutlined />}
         >
            {categories?.find((i: any) => i?.value === value)?.title ?? value}
         </Button>
         <Menu
            id="basic-menu"
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            MenuListProps={{
               'aria-labelledby': 'basic-button',
            }}
         >
            {categories.map((item: Record<string, any>, index: number) => {
               return (
                  <MenuItem key={index} value={item?.value} onClick={(e) => onSelect(e, item?.value)}>
                     {item?.title}
                  </MenuItem>
               )
            })}
         </Menu>
      </div>
   );
}