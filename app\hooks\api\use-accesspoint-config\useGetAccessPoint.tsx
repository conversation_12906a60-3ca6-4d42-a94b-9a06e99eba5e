import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { API_ENDPOINTS } from "utils/api/api-endpoints";
import { toast } from "sonner"
import { parseError } from "lib/utils";
import { serviceProfileClient } from "services/service-profile.service";
import _ from "lodash";

export type SPQueryOptionsType = {
   offset?: number;
   limit?: number;
   orderBy?: string;
};

export enum AccessPointConfigType {
   AppFlow = 'APP_FLW',
   APIKeyFlow = 'APIKEY_FLW',
   OAuthPasswordFlow = "OAUTH_PASSWORD_FLW",
   OAuthFlow="OAUTH_FLW"
}

export enum ServiceConfigState {
   Configured = 'CONFIGURED',
   ReadyToConfig = 'READY_TO_BE_CONFIGURED',
   Disabled = 'DISABLED',
}

export const useGetAccessPoint = ({ serviceProfileId, serviceId }: {
   serviceProfileId: string | undefined,
   serviceId: string | undefined
}) => {

   const queryClient = useQueryClient();

   const { data, isLoading, isFetching, isPending } = useQuery(
      {
         queryKey: [
            API_ENDPOINTS.SERVICE_PROFILE,
            serviceProfileId,
         ],
         queryFn: async () => {
            return await Promise.allSettled([
               serviceProfileClient.getServiceProfileAccessPoints(serviceProfileId as string),
               serviceProfileClient.getServiceAccessPoints(serviceId as string)
            ])
         },
         enabled: !!serviceProfileId,
         select(resp: any) {
            const [selectable, selectable2 = null] = (
               _.filter(resp, { status: "fulfilled" })
                  .map(result => result.value)
            );

            const updated = _.map(selectable?.data?.data, (item) => {
               const exist = _.find(selectable2?.data?.data, (i) => (
                  i?.accessPointTypeConfig?.id === item?.id
               )) || null;
               return { ...item, accessPoint: exist };
            });

            return updated;
         },
      },
   );

   const {
      mutateAsync: updateAccessPoints,
   } = useMutation({
      mutationFn: ({ payload, id, accessPointId }: any) => {
         return serviceProfileClient.updateAccessPoints(id, accessPointId, payload)
      },
      mutationKey: [API_ENDPOINTS.SERVICE_PROFILE],
   });

   const {
      mutateAsync: updateAccessPointsSelectMutation,
   } = useMutation({
      mutationFn: ({ id, payload }: { id: any; payload: any }) => {
         return serviceProfileClient.updateAccessPointsSelection(id, payload)
      },
   });

   return {
      serviceProfileAccessPoints: data ?? [],
      isLoading,
      isFetching,
      isPending,
      clearClient: () => {
         queryClient.clear();
      },

      attemptUpdateAccessPoints: (
         serviceId: string,
         accessPointId: string,
         payload: Record<string, any>,
         cb?: any) => {
         toast.promise(
            updateAccessPoints({ payload, id: serviceId, accessPointId }),
            {
               loading: 'Updating...',
               success: ({ data }) => {

                  queryClient.invalidateQueries({
                     queryKey:
                        [API_ENDPOINTS.SERVICE_PROFILE, serviceProfileId]
                  })
                  cb && cb(data)
                  return 'Updated'
               },
               error: (error) => {
                  return parseError(error?.response?.data)?.message
               }
            }
         )
      },

      // call for update the selection of accessPoints for the respective services
      attemptUpdateAccessPointsSelection: (id: string, payload: Record<string, any>, cb: any) => {
         updateAccessPointsSelectMutation({ id, payload })
            .then(({ data }: { data: any }) => {
               queryClient.invalidateQueries({
                  queryKey: [API_ENDPOINTS.SERVICE_PROFILE, serviceProfileId],
               });
               if (cb) {
                  cb(data);
               }
            })
            .catch((error: any) => {
               console.error("Error updating access points:");
               return parseError(error?.response?.data)?.message

            });
      },
   }
};
