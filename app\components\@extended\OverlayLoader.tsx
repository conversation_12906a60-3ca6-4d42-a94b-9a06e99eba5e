import { Backdrop, Box, CircularProgress, styled } from "@mui/material";

interface OverlayLoaderProps {
   loading?: boolean;
   message?: string;
   size?: number;
   variant?: 'backdrop' | 'container';
   backgroundColor?: string;
   children?: React.ReactNode;
}

const StyledBackdrop = styled(Backdrop)(({ theme }) => ({
   color: '#fff',
   zIndex: theme.zIndex.drawer + 1, // ensures it's above everything else
}));

const OverlayLoader = ({ loading = false, children }: OverlayLoaderProps) => {

   if (!loading) return children;

   return (
      <StyledBackdrop open={loading}>
         <CircularProgress />
      </StyledBackdrop>
   );
};

export default OverlayLoader;
