import Cookies from 'js-cookie'

const KEY = 'environment-id' + window.authUserId;

export const setEnvironmentId = (id: string) => {

   let options: object = { secure: true, sameSite: 'lax' }

   if (window.location.protocol === 'http:') {
      options = {}
   }

   Cookies.set(KEY, id, options)
}

export const getEnvironmentId = () => {
   return Cookies.get(KEY)
}

export const clearEnvironmentId = () => {
   return Cookies.remove(KEY)
}