import type { ReactElement } from 'react';

import { TableRowTypeMap } from '@mui/material';
import { OverridableComponent } from '@mui/material/OverridableComponent';
import type { TableOptions, Row } from '@tanstack/react-table';
import { FormItemType } from 'constants/form';

declare module "@tanstack/react-table" {
   interface TableMeta<TData extends RowData> {
     filterType?: keyof typeof FormItemType
   }
 }

type TableProps<TData> = {
   striped?: boolean
   title?: string,
   onRowClick?: (e: Row<TData>) => void;
   rowClickable?: boolean

   // action bar like (reload, sorting)
   actionBar?: ReactElement[]

   // reload
   disableReload?: boolean

   // pagination
   totalData?: number
   paginationPosition?: 'top' | 'bottom'
   disablePagination?: boolean

   // filter
   onReset?: () => void
   onReload?: () => void

} & Partial<TableOptions<TData>>

type TableExpandedProps<TData> = {

   renderSubRow?: (row: Row<TData>) => ReactElement

} & TableProps<TData>;



export { TableProps, TableExpandedProps }