import { useEffect, useState, useMemo } from "react";

import { Typography, Stack } from "@mui/material";

import { useGetServiceProfile } from "hooks/api/service-profile/useGetServiceProfile";
import useServiceConfig from "store/setup-service";

import EditVersion from './edit-version';
import Selection from './selected';
import { parseVersionData } from './helper';


export default () => {
    const {
        selectedService,
    } = useServiceConfig();

    const { getAllVersionsServiceProfile } = useGetServiceProfile();

    const [versions, setVersions] = useState([]),
        [open, setOpen] = useState(false);

    const { data } = getAllVersionsServiceProfile(
        { id: selectedService?.id }
    );

    const onSelect = () => {
        setOpen(true)
    }

    const onClose = () => {
        setOpen(false)
    }

    const selectedVersions: Array<string> = useMemo(() => {
        return (
            selectedService?.service?.versions?.map((i) => i?.serviceProfileVersion?.id)
        ) ?? [];
    }, [selectedService])

    const title = useMemo(() => (
        `${selectedService?.name} versions for your customers, ensuring they can only access the versions you’ve selected.`
    ), [selectedService?.name]);

    useEffect(() => {
        if (data?.data) {
            setVersions(
                parseVersionData(data.data)
            );
        }
    }, [data]);

    if (!versions?.length) return null;

    return (
        <Stack direction={'column'} gap={1}>

            <Stack
                direction={'row'}
                gap={1}
                alignItems={'center'}
            >
                <Typography variant='h6' className="font-semibold">
                    {title}
                </Typography>
                <Typography
                    onClick={onSelect}
                    className="link"
                >
                    Edit
                </Typography>

            </Stack>

            <Selection
                versions={versions}
                mode='view'
                selected={selectedVersions}
            />

            <EditVersion
                {...{
                    open,
                    onClose,
                    selected: selectedVersions,
                    versions,
                } as any}
            />
        </Stack>
    )
}