// step contents
import SelectService from './step-contents/select-service';
import IntegrationDetails from './step-contents/integration-details';
import Preview from './step-contents/preview';
import React from 'react';

type ComponentMap = {
   label: string
   description?: string,
   component: any
   meta: {
      custom: boolean
   },
   ref?: React.RefObject<HTMLFormElement>
}

export default {
   0: {
      label: 'Select Service',
      description: '',
      component: <SelectService />,
      meta: {
         custom: false
      }
   },
   1: {
      label: 'Details',
      description: '',
      component: <IntegrationDetails />,
      meta: {
         custom: true
      }
   },
   2: {
      label: 'Review',
      description: '',
      component: <Preview />,
      meta: {
         custom: false
      }
   },
} as Record<number, ComponentMap>;

export type { ComponentMap }
