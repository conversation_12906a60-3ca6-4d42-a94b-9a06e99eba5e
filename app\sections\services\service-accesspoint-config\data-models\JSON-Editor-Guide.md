# JSON Editor for Additional Fields 📝

## Overview

The Additional Fields tab now includes a powerful JSON editor mode that allows you to directly edit field definitions as JSON. This provides a faster way to create complex field structures, especially when importing from existing schemas or working with teams who prefer code-based configuration.

## 🚀 Features

### 1. **Dual Mode Interface**
- **Visual Mode** 👁️ - Interactive UI with cards and forms
- **JSON Mode** 📄 - Direct JSON editing with syntax highlighting
- **Seamless Switching** - Toggle between modes with data preservation

### 2. **Real-time Validation**
- **Syntax Checking** - Validates JSON structure as you type
- **Schema Validation** - Ensures all required fields are present
- **Type Validation** - Verifies field types are valid
- **Error Messages** - Clear, specific error descriptions

### 3. **Smart Features**
- **Auto-formatting** - Proper indentation and formatting
- **Schema Helper** - Quick reference for JSON structure
- **Import/Export** - Easy data exchange with other systems
- **Syntax Highlighting** - Code editor experience

## 📋 JSON Schema Structure

### Basic Field Definition
```json
{
  "name": "field_name",           // Required: snake_case field identifier
  "displayName": "Field Name",    // Optional: Human-readable name
  "type": "string",              // Required: string|number|boolean|date|array|object
  "description": "Field purpose", // Optional: Field documentation
  "required": true,              // Optional: Default false
  "defaultValue": "",           // Optional: Default value for field
}
```

### String Field with Validation
```json
{
  "name": "email_address",
  "displayName": "Email Address",
  "type": "string",
  "required": true,
  "format": "email",  // email, url, uuid, date-time, password
  "validation": {
    "minLength": 5,
    "maxLength": 255,
    "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
  }
}
```

### Number Field with Range
```json
{
  "name": "age",
  "displayName": "Age",
  "type": "number",
  "format": "integer",  // integer, float, decimal
  "validation": {
    "min": 0,
    "max": 150
  }
}
```

### String Field with Enum
```json
{
  "name": "status",
  "displayName": "Status",
  "type": "string",
  "required": true,
  "enum": ["active", "inactive", "pending", "suspended"],
  "defaultValue": "pending"
}
```

### Array Field
```json
{
  "name": "tags",
  "displayName": "Tags",
  "type": "array",
  "arrayItemType": "string",  // Type of items in array
  "description": "List of tags for categorization"
}
```

### Object Field with Children
```json
{
  "name": "address",
  "displayName": "Address",
  "type": "object",
  "children": [
    {
      "name": "street",
      "displayName": "Street",
      "type": "string",
      "required": true
    },
    {
      "name": "city",
      "displayName": "City",
      "type": "string",
      "required": true
    },
    {
      "name": "postal_code",
      "displayName": "Postal Code",
      "type": "string",
      "validation": {
        "pattern": "^[0-9]{5}(-[0-9]{4})?$"
      }
    }
  ]
}
```

### Array of Objects
```json
{
  "name": "contacts",
  "displayName": "Contacts",
  "type": "array",
  "arrayItemType": "object",
  "children": [
    {
      "name": "name",
      "displayName": "Contact Name",
      "type": "string",
      "required": true
    },
    {
      "name": "phone",
      "displayName": "Phone Number",
      "type": "string",
      "validation": {
        "pattern": "^\\+?[1-9]\\d{1,14}$"
      }
    },
    {
      "name": "is_primary",
      "displayName": "Primary Contact",
      "type": "boolean",
      "defaultValue": false
    }
  ]
}
```

## 🎯 How to Use JSON Editor

### Switching to JSON Mode
1. Click the **JSON icon** (📄) in the toolbar
2. Current fields are automatically converted to JSON
3. Edit the JSON directly in the code editor

### Importing JSON
1. In visual mode with no fields, click **"Import JSON"**
2. Or switch to JSON mode and paste your schema
3. Validation runs automatically
4. Click **"Apply Changes"** to save

### Validation Process
```
1. Parse JSON ✓
2. Check array structure ✓
3. Validate required fields (name, type) ✓
4. Validate field types ✓
5. Validate nested structures ✓
6. Check validation rules ✓
```

### Common Validation Errors

**Invalid JSON Structure**
```json
❌ { name: "field" }  // Missing quotes
✅ { "name": "field" }
```

**Missing Required Fields**
```json
❌ { "displayName": "Field" }  // Missing name and type
✅ { "name": "field", "type": "string" }
```

**Invalid Type**
```json
❌ { "name": "field", "type": "text" }  // Invalid type
✅ { "name": "field", "type": "string" }
```

**Invalid Array Item Type**
```json
❌ { "name": "items", "type": "array" }  // Missing arrayItemType
✅ { "name": "items", "type": "array", "arrayItemType": "string" }
```

## 💡 Advanced Examples

### Complex E-commerce Product Schema
```json
[
  {
    "name": "product",
    "displayName": "Product",
    "type": "object",
    "children": [
      {
        "name": "variants",
        "displayName": "Product Variants",
        "type": "array",
        "arrayItemType": "object",
        "children": [
          {
            "name": "sku",
            "type": "string",
            "required": true,
            "validation": {
              "pattern": "^[A-Z0-9-]+$"
            }
          },
          {
            "name": "attributes",
            "type": "object",
            "children": [
              {
                "name": "color",
                "type": "string",
                "enum": ["red", "blue", "green", "black", "white"]
              },
              {
                "name": "size",
                "type": "string",
                "enum": ["XS", "S", "M", "L", "XL", "XXL"]
              }
            ]
          },
          {
            "name": "inventory",
            "type": "object",
            "children": [
              {
                "name": "quantity",
                "type": "number",
                "format": "integer",
                "validation": { "min": 0 }
              },
              {
                "name": "warehouse_locations",
                "type": "array",
                "arrayItemType": "string"
              }
            ]
          }
        ]
      }
    ]
  }
]
```

### Multi-level Nested Structure
```json
[
  {
    "name": "organization",
    "type": "object",
    "children": [
      {
        "name": "departments",
        "type": "array",
        "arrayItemType": "object",
        "children": [
          {
            "name": "teams",
            "type": "array",
            "arrayItemType": "object",
            "children": [
              {
                "name": "members",
                "type": "array",
                "arrayItemType": "object",
                "children": [
                  {
                    "name": "employee_id",
                    "type": "string",
                    "required": true
                  },
                  {
                    "name": "role",
                    "type": "string",
                    "enum": ["lead", "senior", "junior"]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
]
```

## 🔧 Tips & Tricks

### 1. **Quick Field Creation**
```json
// Copy-paste template for common fields
{
  "name": "new_field",
  "type": "string",
  "required": true
}
```

### 2. **Bulk Import**
- Design your schema in your favorite code editor
- Validate with a JSON linter
- Import entire schema at once

### 3. **Schema Templates**
- Save commonly used schemas as templates
- Export and share with team members
- Version control your field definitions

### 4. **Validation Patterns**
```json
// Common regex patterns
{
  "email": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
  "phone": "^\\+?[1-9]\\d{1,14}$",
  "url": "^https?://[\\w\\-]+(\\.[\\w\\-]+)+[/#?]?.*$",
  "alphanumeric": "^[a-zA-Z0-9]+$",
  "postal_code_us": "^[0-9]{5}(-[0-9]{4})?$"
}
```

### 5. **Type Conversion**
When switching between visual and JSON modes:
- Field names are converted to snake_case
- IDs are auto-generated if missing
- Validation is preserved
- Nested structures maintain hierarchy

## 🚨 Important Notes

1. **JSON Mode Changes** - Apply changes before switching to visual mode
2. **Validation Errors** - Fix all errors before applying changes
3. **Data Loss** - Invalid JSON won't be saved when switching modes
4. **Character Escaping** - Use proper JSON escaping for special characters
5. **Array Structure** - Root must be an array of field objects

## 🎉 Benefits

- **Speed** - Define complex structures quickly
- **Precision** - Exact control over field properties
- **Portability** - Easy to share and version control
- **Automation** - Generate schemas programmatically
- **Team Collaboration** - Standard JSON format for sharing

The JSON editor makes it easy to create and manage complex field structures, whether you prefer visual tools or code-based configuration! 🚀