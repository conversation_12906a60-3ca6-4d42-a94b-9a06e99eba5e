import { State } from 'hooks/useStatus'

/**
 * Generates a payload for service statistics based on the provided product key.
 *
 * @param {string} productKey - The product key used to filter the service statistics.
 * @returns {Object} The payload for service statistics API.
 */
export const getServiceStatsPayload = (productKey) => {

   let criteria = [
      {
         property: "/state",
         operator: "=",
         values: [State.ACTIVE]
      }
   ];

   const defaultPayload = {
      pagination: {
         limit: 1,
         offset: 0
      }
   };

   return {
      ...defaultPayload,
      filter: {
         and: [
            ...criteria,
            {
               property: "/type",
               operator: "=",
               values: [productKey]
            }
         ]
      }
   }
}

/**
 * Filters data by promise status.
 *
 * @param {Array} data - The array of promise result objects.
 * @param {string} [status='fulfilled'] - The status to filter by (e.g., 'fulfilled', 'rejected', etc.).
 * @returns {Array} The filtered array of promise result objects with the specified status.
 */
export function getPromisesDataByState(data, status = 'fulfilled') {
   return (
      data?.filter((i) => i.status === status)?.map(({ value }) => value)
   ) ?? []
}
