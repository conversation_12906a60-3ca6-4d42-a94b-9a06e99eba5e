import { keepPreviousData, useQuery } from "@tanstack/react-query";
import _ from "lodash";
import { ColumnFiltersState } from "@tanstack/react-table";

import { ExtendedTablePagination } from "types/utils";
import { API_ENDPOINTS } from "utils/api/api-endpoints";

import { parseLogFilterPayload } from "./helper";
import { logsClient } from "../../../services/logs.service";
import { useEffect } from "react";

export type SPQueryOptionsType = {
  offset?: number;
  limit?: number;
  orderBy?: string;
};

interface UseLog {
  logDetails?: Record<string, any>;
  eventLogDetails?: Record<string, any>;
}

const TARGET_SERVER_PAYLOADS = "TARGET_SERVER_PAYLOADS";

export const useGetLog = (props: UseLog) => {
  const logDetails = props?.logDetails ?? {},
    eventLogDetails = props?.eventLogDetails ?? {},
    commonActivityPayload: any = {
      filter: {
        and: [
          {
            property: "/logs/externalKey",
            operator: "=",
            values: [logDetails?.externalKey],
          },
          {
            property: "/logs/id",
            operator: "=",
            values: [logDetails?.id],
          },
        ],
      },
      pagination: {
        offset: 0,
        limit: 10,
      },
    },
    commonActivityEventPayload = {
      filter: {
        and: [
          {
            property: "/eventLogs/id",
            operator: "=",
            values: [eventLogDetails?.id],
          },
        ],
      },
      pagination: {
        offset: 0,
        limit: 10,
      },
    };

  const { data: apiActivities } = useQuery({
    queryKey: [API_ENDPOINTS.ORGANIZATION, logDetails],
    queryFn: async () => {
      const getPayload = (sort: Record<string, any>) => {
        return {
          ...commonActivityPayload,
          sort: [sort],
        };
      };

      return await Promise.all([
        logsClient.searchAPIActivities(
          getPayload({
            property: "/changeLog/startDateTime",
            direction: "ASC",
          })
        ),
        logsClient.searchAPIActivities(
          getPayload({
            property: "/changeLog/startDateTime",
            direction: "DESC",
          })
        ),
      ]);
    },
    select: ([ascData, dscData]) => {
      let input: any = [],
        output: any = [];

      if (Array.isArray(ascData.data.data) && ascData.data.data.length > 0) {
        const [asc]: Record<string, any>[] = ascData.data.data;
        if (asc.type === "ACTIVITY_TASK_STARTED") {
          input = [asc];
        }
        const descData =
          Array.isArray(dscData.data.data) &&
          dscData.data.data.find(({ type }: Record<string, any>) => {
            return type === "ACTIVITY_TASK_COMPLETED";
          });
        output = [descData];
      }

      return {
        input,
        output,
      };
    },
    enabled: !!logDetails?.id && !!logDetails?.externalKey,
  });

  const { data: eventActivities } = useQuery({
    queryKey: [API_ENDPOINTS.ACTIVITY_EVENTS, eventLogDetails],
    queryFn: async () => {
      const getPayload = (sort: Record<string, any>) => {
        return {
          ...commonActivityEventPayload,
          sort: [sort],
        };
      };

      return await Promise.all([
        logsClient.searchEventActivities(
          getPayload({
            property: "/changeLog/startDateTime",
            direction: "ASC",
          })
        ),
        logsClient.searchEventActivities(
          getPayload({
            property: "/changeLog/startDateTime",
            direction: "DESC",
          })
        ),
      ]);
    },
    select: ([ascData, dscData]) => {
      let input: any = [],
        output: any = [];

      if (Array.isArray(ascData.data.data) && ascData.data.data.length > 0) {
        const [asc]: Record<string, any>[] = ascData.data.data;
        if (asc.type === "ACTIVITY_TASK_STARTED") {
          input = [asc];
        }
        const descData =
          Array.isArray(dscData.data.data) &&
          dscData.data.data.find(({ type }: Record<string, any>) => {
            return type === "ACTIVITY_TASK_COMPLETED";
          });
        output = [descData];
      }

      return {
        input,
        output,
      };
    },
    enabled: !!eventLogDetails?.id,
  });

  const { data: targetServerPayload } = useQuery({
    queryKey: [API_ENDPOINTS.ORGANIZATION, logDetails, TARGET_SERVER_PAYLOADS],
    queryFn: async () => {
      commonActivityPayload["sort"] = [
        {
          property: "/order/id",
          direction: "ASC",
        },
      ];
      return await logsClient.searchAPIActivities(commonActivityPayload);
    },
    enabled: !!logDetails?.id && !!logDetails?.externalKey,
    select: (resp) => resp?.data?.data ?? [],
  });

  return {
    search: ({
      orgId,
      integrationId = "all",
      filter = [],
      ...pagination
    }: LogsSearchOptions) => {
      delete pagination?.total;

      const criteria = [
        {
          property: "/organization/id",
          operator: "=",
          values: [orgId],
        },
      ];

      if (integrationId !== "all") {
        criteria.push({
          property: "/endUser/integration/id",
          operator: "=",
          values: [integrationId],
        });
      }

      // adding filter
      criteria.push(...parseLogFilterPayload(filter));

      const payload = {
        filter: {
          and: criteria,
        },
        pagination: {
          limit: pagination.pageSize,
          offset: pagination.pageIndex,
        },
        sort: [
          {
            property: "/changeLog/endDateTime",
            direction: "DESC",
          },
        ],
      };

      return useQuery({
        queryKey: [
          API_ENDPOINTS.LOGS,
          orgId,
          integrationId,
          pagination,
          filter,
        ],
        queryFn: () => logsClient.searchLogs(payload),
        placeholderData: keepPreviousData,
        enabled: !!orgId,
      });
    },
    searchEvent: ({ orgId, filter = [], ...pagination }: LogsSearchOptions) => {
      delete pagination?.total;

      const criteria = [
        {
          property: "/organization/id",
          operator: "=",
          values: [orgId],
        },
      ];

      // adding filter
      criteria.push(...parseLogFilterPayload(filter));

      const payload = {
        filter: {
          and: criteria,
        },
        pagination: {
          limit: pagination.pageSize,
          offset: pagination.pageIndex,
        },
        sort: [
          {
            property: "/changeLog/endDateTime",
            direction: "DESC",
          },
        ],
      };

      return useQuery({
        queryKey: [API_ENDPOINTS.EVENT_LOGS, orgId, pagination, filter],
        queryFn: () => logsClient.searchEvenLogs(payload),
        placeholderData: keepPreviousData,
        enabled: !!orgId,
      });
    },
    apiActivities,
    targetServerPayload,
    eventActivities,
  };
};

type LogsSearchOptions = {
  filter: ColumnFiltersState;
} & Partial<ExtendedTablePagination> & {
    orgId: string;
    integrationId?: "all" | string;
  };
