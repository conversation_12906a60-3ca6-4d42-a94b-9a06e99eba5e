import { API_ENDPOINTS } from "utils/api/api-endpoints";
import fetchInstance from "utils/api/fetchinstance";

export const IntegrationClient = {
  getIntegration: (id: string) => {
    return fetchInstance.get(`${API_ENDPOINTS.INTEGRATIONS}/${id}?privacy=true`);
  },
  getIntegrationHealth: (id: string, params: Record<string, any>) => {
    return fetchInstance.get(`${API_ENDPOINTS.INTEGRATIONS}/${id}/healthchecks`, {params});
  },
  searchIntegrations: (payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.INTEGRATIONS}/search`, payload);
  },
  getIntegrationMonitorConfig: (id: string) => {
    return fetchInstance.get(`${API_ENDPOINTS.INTEGRATION_MONITOR}s?organizationId=${id}`);
  },
  createIntegrationMonitorConfig: (payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.INTEGRATION_MONITOR}s`, payload);
  },
  updateIntegrationMonitorConfig: (id: string,  payload: Record<string, any>) => {
    return fetchInstance.patch(`${API_ENDPOINTS.INTEGRATION_MONITOR}s/${id}`, payload);
  },
  deleteIntegration: (id: string) => {
    return fetchInstance.delete(`${API_ENDPOINTS.INTEGRATIONS}/${id}`)
  },
  updateIntegration: (id: string, payload: Record<string, any>) => {
    return fetchInstance.patch(`${API_ENDPOINTS.INTEGRATIONS}/${id}?privacy=true`, payload)
  }
};
