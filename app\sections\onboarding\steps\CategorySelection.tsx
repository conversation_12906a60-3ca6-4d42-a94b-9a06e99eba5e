import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Stack,
  useTheme,
  alpha,
  Chip,
} from '@mui/material';
// Icons are now imported from centralized domains data
import useOnboardingStore from 'store/onboarding';
import { DOMAINS } from 'data/domains';

// Enterprise integration categories - filter to show only released domains
const categories = DOMAINS
  .filter(domain => domain.visibility.showInFeatures.includes('connector-selectors'))
  .map(domain => ({
    id: domain.value,
    name: domain.label,
    icon: domain.icon,
    description: domain.description,
    isPopular: ['SCM', 'TICKETING', 'MONITORING', 'INCIDENT'].includes(domain.value),
    isNew: domain.isNew,
    isComing: !domain.released,
  }));

export default function CategorySelection() {
  const theme = useTheme();
  const { selectedCategories, setSelectedCategories } = useOnboardingStore();

  const toggleCategory = (categoryId: string) => {
    if (selectedCategories.includes(categoryId)) {
      setSelectedCategories(selectedCategories.filter(id => id !== categoryId));
    } else {
      setSelectedCategories([...selectedCategories, categoryId]);
    }
  };

  const availableCategories = categories.filter(cat => !cat.isComing);
  const comingSoonCategories = categories.filter(cat => cat.isComing);

  return (
    <Box>
      <Typography variant="h6" gutterBottom fontWeight={600}>
        What type of integrations do you need?
      </Typography>
      <Typography variant="body2" color="text.secondary" mb={3}>
        Select all categories that apply to your technology stack
      </Typography>

      <Grid container spacing={2}>
        {availableCategories.map((category) => {
          const Icon = category.icon;
          const isSelected = selectedCategories.includes(category.id);

          return (
            <Grid item xs={12} sm={6} md={4} key={category.id}>
              <Paper
                elevation={0}
                onClick={() => toggleCategory(category.id)}
                sx={{
                  p: 2,
                  cursor: 'pointer',
                  border: 1.5,
                  borderColor: isSelected 
                    ? theme.palette.primary.main 
                    : theme.palette.mode === 'dark' 
                    ? theme.palette.grey[800] 
                    : theme.palette.grey[300],
                  backgroundColor: isSelected
                    ? alpha(theme.palette.primary.main, 0.04)
                    : theme.palette.mode === 'dark'
                    ? theme.palette.background.paper
                    : '#fff',
                  borderRadius: 1,
                  transition: 'all 0.2s ease',
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    borderColor: theme.palette.primary.main,
                    backgroundColor: isSelected
                      ? alpha(theme.palette.primary.main, 0.08)
                      : alpha(theme.palette.primary.main, 0.02),
                    boxShadow: theme.shadows[1],
                  },
                }}
              >
                {/* Badge inside card */}
                {(category.isPopular || category.isNew) && (
                  <Chip
                    label={category.isPopular ? 'Popular' : 'New'}
                    size="small"
                    sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      height: 18,
                      fontSize: '0.65rem',
                      fontWeight: 600,
                      backgroundColor: category.isPopular 
                        ? alpha(theme.palette.primary.main, 0.1) 
                        : alpha(theme.palette.success.main, 0.1),
                      color: category.isPopular 
                        ? theme.palette.primary.main 
                        : theme.palette.success.main,
                      border: 1,
                      borderColor: category.isPopular 
                        ? alpha(theme.palette.primary.main, 0.2) 
                        : alpha(theme.palette.success.main, 0.2),
                      '& .MuiChip-label': {
                        px: 0.75,
                      },
                    }}
                  />
                )}

                <Stack direction="row" spacing={1.5} alignItems="flex-start">
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: 1,
                      backgroundColor: isSelected
                        ? alpha(theme.palette.primary.main, 0.1)
                        : theme.palette.mode === 'dark'
                        ? alpha(theme.palette.grey[700], 0.3)
                        : alpha(theme.palette.grey[300], 0.2),
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.2s ease',
                      flexShrink: 0,
                    }}
                  >
                    <Icon 
                      size={20} 
                      color={isSelected 
                        ? theme.palette.primary.main 
                        : theme.palette.text.secondary
                      } 
                      strokeWidth={2} 
                    />
                  </Box>
                  <Box sx={{ flex: 1, minWidth: 0 }}>
                    <Typography 
                      variant="body2" 
                      fontWeight={600}
                      sx={{ 
                        mb: 0.25,
                        color: theme.palette.text.primary,
                        lineHeight: 1.3,
                      }}
                    >
                      {category.name}
                    </Typography>
                    <Typography 
                      variant="caption" 
                      sx={{ 
                        color: theme.palette.text.secondary,
                        display: 'block',
                        lineHeight: 1.3,
                        fontSize: '0.7rem',
                      }}
                    >
                      {category.description}
                    </Typography>
                  </Box>
                </Stack>
              </Paper>
            </Grid>
          );
        })}
      </Grid>

      {/* Coming Soon Section */}
      {comingSoonCategories.length > 0 && (
        <>
          <Typography 
            variant="body2" 
            fontWeight={600} 
            sx={{ 
              mt: 4, 
              mb: 2, 
              color: theme.palette.text.secondary,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            Coming Soon
          </Typography>
          <Grid container spacing={2}>
            {comingSoonCategories.map((category) => {
              const Icon = category.icon;

              return (
                <Grid item xs={12} sm={6} md={4} key={category.id}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      border: 1.5,
                      borderColor: theme.palette.mode === 'dark' 
                        ? theme.palette.grey[800] 
                        : theme.palette.grey[300],
                      backgroundColor: theme.palette.mode === 'dark'
                        ? alpha(theme.palette.grey[900], 0.3)
                        : alpha(theme.palette.grey[100], 0.3),
                      borderRadius: 1,
                      position: 'relative',
                      opacity: 0.6,
                      cursor: 'not-allowed',
                    }}
                  >
                    <Chip
                      label="Coming Soon"
                      size="small"
                      sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        height: 18,
                        fontSize: '0.65rem',
                        fontWeight: 600,
                        backgroundColor: alpha(theme.palette.grey[700], 0.1),
                        color: theme.palette.text.secondary,
                        border: 1,
                        borderColor: alpha(theme.palette.grey[700], 0.2),
                        '& .MuiChip-label': {
                          px: 0.75,
                        },
                      }}
                    />
                    <Stack direction="row" spacing={1.5} alignItems="flex-start">
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: 1,
                          backgroundColor: alpha(theme.palette.grey[500], 0.15),
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0,
                        }}
                      >
                        <Icon size={20} color={theme.palette.text.disabled} strokeWidth={2} />
                      </Box>
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography 
                          variant="body2" 
                          fontWeight={600}
                          sx={{ 
                            mb: 0.25,
                            color: theme.palette.text.disabled,
                            lineHeight: 1.3,
                          }}
                        >
                          {category.name}
                        </Typography>
                        <Typography 
                          variant="caption" 
                          sx={{ 
                            color: theme.palette.text.disabled,
                            display: 'block',
                            lineHeight: 1.3,
                            fontSize: '0.7rem',
                          }}
                        >
                          {category.description}
                        </Typography>
                      </Box>
                    </Stack>
                  </Paper>
                </Grid>
              );
            })}
          </Grid>
        </>
      )}

    </Box>
  );
}