import moment, { Moment } from 'moment'

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

const PAYLOAD_FORMAT = 'YYYY-MM-DDTHH:mm:ss[Z]'

export const useDate = () => {
   return {
      loadDate: (date: Date | string) => {
         return date ? moment(date).format('lll') : '';
      },
      dayjs,
      payloadFormat: PAYLOAD_FORMAT,
      getPayloadFormatted: (date: Moment) => {
         return moment(date).format(PAYLOAD_FORMAT)
      }
   }
}