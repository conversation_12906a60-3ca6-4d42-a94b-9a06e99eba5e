// assets
import { Home, Plug2, Monitor, FileSearch, Map } from 'lucide-react';

// icons
const icons = {
  HomeOutlined: Home,
  ApiOutlined: Plug2,
  MonitorOutlined: Monitor,
  FileSearchOutlined: FileSearch,
  MapOutlined: Map
};

//============================|| MENU ITEMS - DASHBOARD ||============================== //

const dashboard = {
  id: 'group-dashboard',
  title: 'Overview',
  type: 'group',
  children: [
    {
      id: 'getting-started',
      title: 'Quick Start',
      type: 'item',
      url: '/console/quick-start',
      icon: icons.MapOutlined,
      breadcrumbs: true,
      hideFor: [],
      hidden: true, // Hide from navigation but keep for breadcrumbs
    },
    {
      id: 'dashboard',
      title: 'Home',
      type: 'item',
      url: '/console/dashboard',
      icon: icons.HomeOutlined,
      breadcrumbs: true,
      hideFor: [],
    },
    {
      id: 'integrations',
      title: 'Integrations',
      description: 'Integrations between your users accounts and external services.',
      type: 'item',
      url: '/console/integrations',
      icon: icons.ApiOutlined,
      hideFor: [],
    },
    {
      id: 'api-logs',
      title: 'Logs',
      type: 'item',
      description: 'Real-time display of chronologically recorded user actions and events.',
      url: '/console/logs',
      icon: icons.FileSearchOutlined,
      hideFor: [],
    },
  ]
};

export default dashboard;
