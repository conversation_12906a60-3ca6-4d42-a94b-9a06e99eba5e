/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect } from "react";
import { Stack } from "@mui/material";

import useCustomBreadcrumbs from "store/custom-breadcrumbs";

import { BREADCRUMB_S } from "./constant";

import Layout from "./layout"
import Banner from "./banner";


const Dasbboard = () => {
   const { reset, update } = useCustomBreadcrumbs();

   /**
    * making it open when auth user org id is not present
    */

   useEffect(() => {
      update(BREADCRUMB_S);
      return () => {
         reset()
      }
   }, [reset, update])

   return (
      <Stack gap={2}>
         <Banner />
         <Layout />
      </Stack>
   )
}

export default Dasbboard