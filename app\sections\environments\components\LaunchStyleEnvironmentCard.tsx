import React from 'react';

import {
  Plus,
  Shield,
  AlertCircle,
  CheckCircle,
  Calendar,
  Info,
  TestTube,
  Code,
  Puzzle,
  Lock,
} from 'lucide-react';

import { LaunchStyleEnvironmentCardProps } from '../Environment.types';
import {
  StyledTemplateCard,
  StyledIconContainer,
  TemplateContent,
  TemplateIconWrapper,
} from '../Environment.styles';
import { StyledTemplateTitle, StyledTemplateDescription } from '../Typography.styles';

const getIconComponent = (iconName: string) => {
  const iconComponents: Record<string, React.ReactElement> = {
    plus: <Plus size={24} />,
    secure: <Shield size={24} />,
    warning: <AlertCircle size={24} />,
    check: <CheckCircle size={24} />,
    calendar: <Calendar size={24} />,
    info: <Info size={24} />,
    'test-tube': <TestTube size={24} />,
    code: <Code size={24} />,
    puzzle: <Puzzle size={24} />,
  };

  return iconComponents[iconName] || <Info size={24} />;
};

export const LaunchStyleEnvironmentCard: React.FC<LaunchStyleEnvironmentCardProps> = ({
  card,
  onClick,
}) => {
  return (
    <StyledTemplateCard
      variant="outlined"
      envColor={card.colorPicker}
      locked={card.locked}
      onClick={() => (card.locked ? undefined : onClick(card.key, card.name))}
    >
      <StyledIconContainer iconColor={card.colorPicker}>
        <TemplateIconWrapper iconColor={card.colorPicker}>
          {card.locked ? (
            <Lock size={24} />
          ) : (
            getIconComponent(card.icon)
          )}
        </TemplateIconWrapper>
      </StyledIconContainer>
      
      <TemplateContent>
        <StyledTemplateTitle variant="subtitle2">
          {card.name}
        </StyledTemplateTitle>
        <StyledTemplateDescription variant="caption">
          {card.description}
        </StyledTemplateDescription>
      </TemplateContent>
    </StyledTemplateCard>
  );
};