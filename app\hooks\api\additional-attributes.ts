import { queryOptions, UseMutationOptions, useQuery, useQueryClient } from "@tanstack/react-query"
import { AxiosResponse } from "axios"
import { additionalAttributesClient } from "services/additional-attributes"
import { AdditionalFields } from "types/additional-attributes";

type CreateAdditionalFieldsMutationOptions = {
   payload: AdditionalFields.CreatePayload
}

const queryKey = ['get-all-additional-fields'];

const useGetAdditionalAttributes = () => {
   const queryClient = useQueryClient()
   return {
      getAllAdditionalAttributesQuery: () => {
         return queryOptions<AxiosResponse<{ data: AdditionalFields.Root[] }>>({
            queryKey: [queryKey],
            queryFn: () => {
               return additionalAttributesClient.getAll()
            }
         })
      },
      createAdditionalAttributesMutation: (): UseMutationOptions<
         AxiosResponse<AdditionalFields.Root>, {}, CreateAdditionalFieldsMutationOptions> => {
         return {
            mutationFn: ({ payload }) => {
               return additionalAttributesClient.create(payload);
            },
            onSuccess: () => {
               queryClient.invalidateQueries({ queryKey: [queryKey] })
            },
            onError: (error) => {
               console.log(error)
            }
         }
      },
      deleteAdditionalAttributesMutation: (): UseMutationOptions<
         AxiosResponse<AdditionalFields.Root>, {}, AdditionalFields.Root['id']> => {
         return {
            mutationFn: (id: AdditionalFields.Root['id']) => {
               return additionalAttributesClient.delete(id);
            },
            onSuccess: () => {
               queryClient.invalidateQueries({ queryKey: [queryKey] })
            },
            onError: (error) => {
               console.log(error)
            }
         }
      }
   }
}

export default useGetAdditionalAttributes;