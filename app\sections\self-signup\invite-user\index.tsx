/* eslint-disable @typescript-eslint/no-explicit-any */
import { Stack, Typography } from "@mui/material";
import { useState } from "react";
import Header from "../header";
import { UserRoleEnum } from "hooks/api/permission/usePermission";
import useSelfRegistration from "store/self-signUp/self-signup";
import { getIsValidEmail } from "lib/utils";
import EmailInput from "./components/EmailInput";
import ChipList from "./components/ChipList";
import { StepComponentProps } from "../component-mapper";

const InviteUser = ({ currentStep }: StepComponentProps) => {
   const { selectedMembers, addSelectedMember, removeSelectedMember } = useSelfRegistration();
   const [inputValue, setInputValue] = useState('');
   const [selectedRole, setSelectedRole] = useState<UserRoleEnum>(UserRoleEnum.ORG_USER);
   const [error, setError] = useState<string>('');

   const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter' && inputValue.trim()) {
         event.preventDefault();
         const email = inputValue.trim();

         setError('');

         if (!getIsValidEmail(email)) {
            setError('Please enter a valid email address');
            return;
         }

         if (selectedMembers.find(member => member.email === email)) {
            setError('This email has already been added');
            return;
         }

         addSelectedMember({
            id: Date.now().toString(),
            email,
            role: selectedRole
         });
         setInputValue('');
      }
   };

   return (
      <Stack spacing={3} sx={{ width: '100%', maxWidth: 800, margin: '0 auto', py: 2 }}>
         <Header subTitle={currentStep?.subTitle} title={currentStep?.title} />

         <Stack spacing={2.5} sx={{ px: 2 }}>
            <Typography
               variant="body2"
               color="text.secondary"
               sx={{
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  mb: -1
               }}
            >
               Enter email addresses
            </Typography>

            <Stack spacing={2}>
               <EmailInput
                  inputValue={inputValue}
                  selectedRole={selectedRole}
                  error={error}
                  onInputChange={(value) => {
                     setInputValue(value);
                     setError('');
                  }}
                  onKeyDown={handleKeyDown}
                  onRoleChange={setSelectedRole}
               />

               <ChipList
                  members={selectedMembers}
                  onRemove={removeSelectedMember}
               />
            </Stack>
         </Stack>
         <Typography
            variant="body1"
            align="center"
            color="text.secondary"
         >
            {currentStep?.assistanceInfo}
         </Typography>
      </Stack>
   );
};

export default InviteUser;
