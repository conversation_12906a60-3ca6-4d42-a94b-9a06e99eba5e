import { ServiceProfile } from "types/service-profile";
import { create } from "zustand";

interface ServiceConfigTypes {

   // selected tile in the service profile
   selectedService: ServiceProfile | null
   setSelectedService: (selectedService: ServiceProfile | null) => void
   // reset the selected tile in the service profile
   resetSelectedService: () => void

   /* selected type config under the selected tile
     eg: (Github app flow) / (Personal Access Token)
   */
   selectedTypeConfig: Record<string, any> | null
   //   setting the selected config type
   setSelectedTypeConfig: (selectedTypeConfig: Record<string, any> | null) => void
   updateSelectedTypeConfig: (path: string, value: any) => void
   resetSelectedTypeConfig: () => void

   // versions
   selectedVersions?: Array<any>
   setSelectedVersions: (selectedVersions: Array<any>) => void

   // access Type
   selectedAccessType: [],
   setSelectedAccessType: (selectedAccessType: any) => void

   domain: string | null
   setDomain: (domain: string) => void
}

const useServiceConfig = create<ServiceConfigTypes>((set, get) => ({

   // selected service block
   selectedService: null,
   setSelectedService: (selectedService) => {
      const values = get();
      set({ ...values, selectedService })
   },
   resetSelectedService: () => {
      const values = get();
      set({ ...values, selectedService: null })
   },

   // selected type config block
   selectedTypeConfig: null,
   setSelectedTypeConfig: (selectedTypeConfig) => {
      const values = get();
      set({ ...values, selectedTypeConfig })
   },
   updateSelectedTypeConfig: (path, value) => {
      set(({ selectedTypeConfig, ...prevState }) => {

         /* updating the selected typeConfig such as (appDetails, name, ect) */
         selectedTypeConfig = { ...selectedTypeConfig, [path]: value };
         return { ...prevState, selectedTypeConfig }
      })
   },
   resetSelectedTypeConfig: () => {
      const values = get();
      set({ ...values, selectedTypeConfig: null })
   },

   // selected Domian
   domain: null,
   setDomain: (domain) => {
      const values = get();
      set({ ...values, domain })
   },

   // versions
   selectedVersions: [],
   setSelectedVersions: (selectedVersions) => {
      const values = get();
      set({ ...values, selectedVersions })
   },

   // selected AccessType
   selectedAccessType: [],
   setSelectedAccessType: (selectedVersions) => {
      const values = get();
      set({ ...values, selectedVersions })
   },
}));

export default useServiceConfig;