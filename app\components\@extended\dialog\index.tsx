/* eslint-disable @typescript-eslint/no-explicit-any */
import { forwardRef } from "react";

import { CloseOutlined } from "@ant-design/icons"
import { DialogProps, IconButtonProps, Dialog as DialogPrimitive } from "@mui/material";
import { TransitionProps } from '@mui/material/transitions';
import IconButton from "components/@extended/IconButton";

import Slide from '@mui/material/Slide';

const Transition = forwardRef(function Transition(
   props: TransitionProps & {
      children: React.ReactElement<any, any>;
   },
   ref: React.Ref<unknown>,
) {
   return <Slide direction="up" ref={ref} {...props} />;
});

type DialogCloseProps = {
   onClose?: DialogProps['onClose']
}

export const DialogClose = ({ onClose: onCloseProp }: DialogCloseProps) => {

   const onClose: IconButtonProps['onClick'] = (e) => {
      if (typeof onCloseProp === 'function') {
         onCloseProp(e, 'escapeKeyDown')
      }
   }

   return (
      <IconButton
         aria-label="close"
         onClick={onClose}
         sx={(theme: any) => ({
            position: 'absolute',
            right: 8,
            top: 8,
            color: theme.palette.grey[500],
         })}
      >
         <CloseOutlined />
      </IconButton>
   )
}

export const Dialog = (props: DialogProps) => {
   return (
      <DialogPrimitive
         fullWidth
         TransitionComponent={Transition}
         maxWidth='sm'
         {...props}
         aria-hidden={null as any}
      />
   )
}