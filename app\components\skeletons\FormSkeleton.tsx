import React from 'react';
import { Box, Skeleton, Stack, Grid, Divider } from '@mui/material';
import MainCard from 'components/MainCard';

interface FormSkeletonProps {
  sections?: number;
  fieldsPerSection?: number;
  showActions?: boolean;
}

const FormSkeleton: React.FC<FormSkeletonProps> = ({
  sections = 3,
  fieldsPerSection = 4,
  showActions = true
}) => {
  return (
    <MainCard>
      <Stack spacing={4}>
        {Array.from({ length: sections }).map((_, sectionIndex) => (
          <Box key={sectionIndex}>
            {sectionIndex > 0 && <Divider sx={{ mb: 3 }} />}
            
            {/* Section Title */}
            <Skeleton width={150} height={24} sx={{ mb: 3 }} />
            
            {/* Form Fields */}
            <Grid container spacing={3}>
              {Array.from({ length: fieldsPerSection }).map((_, fieldIndex) => (
                <Grid item xs={12} sm={6} key={fieldIndex}>
                  <Stack spacing={1}>
                    <Skeleton width={80} height={16} />
                    <Skeleton variant="rectangular" width="100%" height={40} />
                  </Stack>
                </Grid>
              ))}
            </Grid>
          </Box>
        ))}

        {showActions && (
          <>
            <Divider />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Skeleton variant="rectangular" width={80} height={36} />
              <Skeleton variant="rectangular" width={100} height={36} />
            </Box>
          </>
        )}
      </Stack>
    </MainCard>
  );
};

export default FormSkeleton;