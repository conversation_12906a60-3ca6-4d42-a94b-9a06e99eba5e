import ModernChartCard from "components/cards/ModernChartCard"

import SimpleBar from 'components/third-party/SimpleBar';

import { GadgetConfig } from "../../layout/grid-type"
import DataGrid from './data-grid';

export default ({ gadget }: GadgetConfig) => {

   return (
      <ModernChartCard
         title={gadget.name}
         subtitle="Connector performance overview"
         sx={{ height: '100%' }}
         content={false}
      >
         <SimpleBar sx={{ height: 386 }} >
            <DataGrid />
         </SimpleBar>
      </ModernChartCard>
   )
}