import useUserDetails from "store/user";

export type SPQueryOptionsType = {
   offset?: number;
   limit?: number;
   orderBy?: string;
};

export enum UserRoleEnum {
   ORG_ADMIN = 'ORG_ADMIN_ROLE',
   ORG_USER = 'ORG_DEVOPS_ROLE',
   ORG_OBSERVER = 'ORG_OBSERVER_ROLE'
}

export const ROLE_MAPPER = {
   [UserRoleEnum.ORG_ADMIN]: 'OrgAdmin',
   [UserRoleEnum.ORG_USER]: 'DevOps',
   [UserRoleEnum.ORG_OBSERVER]: 'Observer',
}

export const ROLE_S = [
   {
      label: ROLE_MAPPER[UserRoleEnum.ORG_ADMIN],
      value: UserRoleEnum.ORG_ADMIN
   },
   {
      label: ROLE_MAPPER[UserRoleEnum.ORG_OBSERVER],
      value: UserRoleEnum.ORG_OBSERVER
   },
   {
      label: ROLE_MAPPER[UserRoleEnum.ORG_USER],
      value: UserRoleEnum.ORG_USER
   },
]

export const useGetPermission = () => {

   const deriveRoleLabel = (role: UserRoleEnum) => {
      return ROLE_MAPPER?.[role] ?? '-'
   }

   const { isSupeAdmin } = useUserDetails();

   return {
      deriveRoleLabel,
      getAllRoles: () => ROLE_S,
      getIsSuperAdmin: () => isSupeAdmin
   }
};
