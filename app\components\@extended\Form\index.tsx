import {
   Form,
   FormDescription,
   FormField,
   FormItem,
   FormLabel,
   FormMessage
} from "./Form";

import {
   FormControl,
} from '@mui/material'
import { HTMLAttributes, forwardRef } from "react";
import { cn } from 'lib/utils';


type ItemProps = {
   label?: string
   description?: string

   layout?: 'vertical' | 'horizontal'
   labelPosition?: 'end' | 'start'
}

const Item = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement> & ItemProps>(
   ({ label, description, children, className, layout = 'vertical', labelPosition = 'end' }, ref) => {
      return (
         <FormItem
            className={cn('flex flex-col', className)}
            ref={ref}>
            <div>
               {label ? (
                  <FormLabel
                     className={cn('mb-1 ', `${layout === 'vertical' ? '' : 'mt-3'}`)}
                  >{label}</FormLabel>
               ) : null}
               {description ? (
                  <FormDescription>
                     {description}
                  </FormDescription>
               ) : null}
            </div>
            {children ? children : null}
            <FormMessage />
         </FormItem>
      )
   })

export { FormField, Item as FormItem, Form, FormControl, FormMessage }