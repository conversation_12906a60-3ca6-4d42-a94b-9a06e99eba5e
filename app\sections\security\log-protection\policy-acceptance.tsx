import { Dispatch, SetStateAction, use<PERSON><PERSON><PERSON>, useState } from "react";

import {
   <PERSON>ert,
   Button,
   ButtonProps,
   Checkbox,
   CheckboxProps,
   DialogActions,
   DialogContent,
   DialogProps,
   DialogTitle,
   FormControlLabel,
   Grid,
   Skeleton,
   Stack,
   TextField,
   Typography
} from "@mui/material";

import MainCard from "components/MainCard";
import { Dialog } from "components/@extended/dialog";
import useUserDetails from "store/user";
import { useIntegrationCreateProvider } from "./integration-create-provider";

const TITLE = 'Use Unizo’s Managed Logging​';
const DESCRIPTION = (
   'Log all integration activity securely using Unizo’s native logging system—no setup required. This is the recommended option for most users.​'
);

const WARN_MESSAGE = (
   'Enabling Unizo’s built-in logger provides you with continuous monitoring, real-time visibility, and health tracking of all your integrations out of the box. Ideal for teams who want simplified observability and proactive alerts​.')

// confirmation static text
const CONFIRMATION_TITLE = 'Are you sure you?';

type Props = {
   enabled: boolean; // Initial enabled state for the log storage policy checkbox
   setEnabled: Dispatch<SetStateAction<boolean>>

   logId: string | null
   isLoading: boolean

   hasEnterpriseTier: boolean
}

export default ({
   enabled,
   logId = null,
   isLoading,
   setEnabled
}: Props) => {

   const [open, setOpen] = useState(false); // State to manage the open status of the modal/dialog

   const {
      attemptCreateLogProtection,
      attemptUpdateLogProtection,
   } = useIntegrationCreateProvider()

   const onOpen = () => {
      setOpen(true); // Open the modal/dialog
   };

   const onClose = () => {
      setOpen(false); // Close the modal/dialog
   };

   const onCheckChange: CheckboxProps['onChange'] = () => {
      onOpen(); // Open the modal when the checkbox value changes
   };

   const onConfirm = () => {
      const payload = {
         unizoManagedLog: !enabled, // Flag for managed logs
      };

      if (!logId) {
         attemptCreateLogProtection(payload, () => {
            onClose();
            setEnabled(!enabled);
         });
      } else {
         attemptUpdateLogProtection(
            [{
               op: "replace",
               path: "/unizoManagedLog",
               value: !enabled,
            }],
            logId,
            () => {
               onClose();
               setEnabled(!enabled);
            }
         );
      }
   };

   return (
      <MainCard >
         <Grid container>
            <Grid item xs={12} lg={12} xl={12}>
               <Stack gap={2} alignItems={'start'}>

                  <Stack>
                     <Typography variant="h5">
                        {TITLE}
                     </Typography>

                     {/* description */}
                     <Grid item xs={12} md={9}>
                     <Typography color={'secondary.600'} mt={1} sx={{textAlign:'justify'}}>
                        {DESCRIPTION}
                     </Typography>
                     </Grid>
                    
                     {/* description */}
                  </Stack>

                  {/* warn block */}
                  <Alert severity="error" sx={{textAlign:'justify'}} >
                     {WARN_MESSAGE}
                  </Alert>
                  {/* warn block */}

                  {isLoading ? (
                     <Skeleton sx={{ width: '20%', height: '1rem' }} />
                  ) : (
                     <FormControlLabel
                        control={(
                           <Checkbox
                              onChange={onCheckChange}
                              checked={enabled}
                           />
                        )}
                        label={`Enable Built-in Logging​`}
                     />
                  )}

               </Stack>
            </Grid>
         </Grid>
         <Confirmation
            open={open}
            onClose={onClose}
            onOk={onConfirm}
         />
      </MainCard>
   )
}

type ConfirmationProps = {
   onOk: ButtonProps['onClick']
} & DialogProps;

function Confirmation(props: ConfirmationProps) {

   const { open, onClose, onOk, ...rest } = props;

   const { user } = useUserDetails();
   const [typed, setTyped] = useState<string>('');

   const organizationName = user?.organization?.name;

   const enabled = useMemo(() => {
      return typed === organizationName;
   }, [typed])

   return (
      <Dialog
         maxWidth={'xs'}
         open={open}
         onClose={onClose}
         {...rest}
      >
         <DialogTitle variant="h5">
            {CONFIRMATION_TITLE}
         </DialogTitle>
         <DialogContent>
            <Stack gap={2}>
               <Typography color={'secondary.600'}>
                  This is a critical action that will directly affect how records are stored in the system. Before proceeding,
                  Please enter your <Typography className="text-semibold" component={'b'}>Organization name</Typography>
               </Typography>
               <TextField
                  placeholder={`Eg: ${organizationName}`}
                  onChange={({ target: { value } }) => void setTyped(value)}
               />
            </Stack>
         </DialogContent>
         <DialogActions>
            <Stack direction={'row'} gap={1}>
               <Button
                  onClick={(e) => {
                     onClose && onClose(e, 'escapeKeyDown')
                  }}
               >
                  Cancel
               </Button>
               <Button
                  color="error"
                  variant="contained"
                  disabled={!enabled}
                  onClick={onOk}
               >
                  Proceed
               </Button>
            </Stack>
         </DialogActions>
      </Dialog>
   )
}