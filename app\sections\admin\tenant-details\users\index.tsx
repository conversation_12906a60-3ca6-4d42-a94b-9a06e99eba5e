import { <PERSON><PERSON>, <PERSON><PERSON>, Typography } from "@mui/material"
import { useParams } from "@remix-run/react";
import { useTable } from "hooks/table/useTable";
import { useDate } from "hooks/useDate";
import _ from "lodash";
import { useMemo, useState } from "react";
import { InviteUser } from "./invite-users";
import { PendingUserModal } from "./pending-user";
import { useGetUser } from "hooks/api/user/useUser";
import Avatar from "components/@extended/Avatar";
import { useGetPermission } from "hooks/api/permission/usePermission";
import Table from "components/@extended/Table";

const HEADER_NAMES = {
   NAME: 'Name',
   EMAIL: 'Email',
   ROLE: 'Role',
   ACTIONS: 'Actions',
}

export const Users = () => {

   const [open, setOpen] = useState<boolean>(false);
   const [selected, setSelected] = useState<any>();
   const [isEditMode, setIsEditMode] = useState<boolean>(false);


   const { id } = useParams(),
      { deriveRoleLabel } = useGetPermission(),
      {
         paginationModal: { pagination, onPaginationChange, setTotal }
      } = useTable();

   const { users: data, getAvatarLabel } = useGetUser(
      { orgId: id });

   const onEdit = (newSelected: any) => {
      setSelected(newSelected);
      setIsEditMode(true)
      onOpen()
   }

   const onOpen = () => {
      setOpen(true)
   },
      onClose = () => {
         setOpen(false)
         setSelected(undefined);
         setIsEditMode(false)
      };

   const columns: any = useMemo(
      () => [
         {
            accessorKey: 'name',
            header: HEADER_NAMES.NAME,
            cell({ row: { original: row } }: any) {
               return (
                  <Stack direction={'row'} gap={1} alignItems={'center'} className='mt-1'>
                     <Avatar size='small' className='bg-orange-500 text-white' alt="Remy Sharp" type={undefined}>
                        {getAvatarLabel(row?.firstName)}
                     </Avatar>
                     <Typography variant='button'>
                        {row?.firstName ?? row?.email}
                     </Typography>
                  </Stack>
               )
            },
         },
         {
            accessorKey: 'email', //normal accessorKey
            header: HEADER_NAMES.EMAIL,
         },
         {
            accessorKey: 'role',
            header: HEADER_NAMES.ROLE,
            cell({ row: { original } }: any) {
               return deriveRoleLabel(original?.role?.type)
            },
         },
         {
            accessorKey: 'action',
            header: HEADER_NAMES.ACTIONS,
            cell({ row: { original } }: any) {
               return (
                  <Button
                     onClick={() => onEdit(original)}
                     size="small"
                  >
                     Edit
                  </Button>
               )
            },
         },

      ],
      [],
   );

   const [paginationState, setPaginationState] = useState<any>({
      pageIndex: 0,
      pageSize: 10,
   })

   return (
      <Stack gap={1}>
         <Stack direction={'row'} justifyContent={'space-between'}>
            <Typography variant='h5'></Typography>
            <Stack direction={'row'} gap={1}>
               <PendingUserModal />
               <Button onClick={onOpen} variant='contained'>Invite Users</Button>
            </Stack>
         </Stack>
         <Table
            data={data}
            columns={columns}
            totalData={pagination?.total}
            {...{
               onPaginationChange: setPaginationState,
               state: {
                  pagination: {
                     pageIndex: paginationState.pageIndex,
                     pageSize: paginationState?.pageSize,
                  }
               } as any
            }}
         />
         <InviteUser open={open} onClose={onClose} isEditMode={isEditMode} selected={selected} />
      </Stack>
   )
}
