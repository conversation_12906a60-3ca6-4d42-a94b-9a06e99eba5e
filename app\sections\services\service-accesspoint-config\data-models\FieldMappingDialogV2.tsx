import React, { useState, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Typo<PERSON>,
  Box,
  IconButton,
  Chip,
  useTheme,
  alpha,
  Divider,
  useMediaQuery,
} from '@mui/material';
import { X } from 'lucide-react';
import { PROVIDER_FIELDS } from './mock-data';
import HierarchicalFieldMappingV5 from './HierarchicalFieldMappingV5';
import FieldMapping from './field-mapping';
import { MappingErrorTypes, MappingValues } from './field-mapping/type';
import useGetFieldMappings from 'hooks/api/use-field-mappings';
import { useMutation, useQuery } from '@tanstack/react-query';
import { FieldMappings } from 'types/field-mappings';
import { LoadingOutlined } from '@ant-design/icons';
import { ServiceProfileSpecificationFieldType } from 'constants/service-profile';
import { toast } from 'sonner';
import { parseError } from 'lib/utils';

interface FieldMappingDialogProps {
  open: boolean;
  onClose: () => void;
  dataModel: any;
  serviceProfile: any;
}

export default function FieldMappingDialogV2({
  open,
  onClose,
  dataModel,
  serviceProfile,
}: FieldMappingDialogProps) {
  const theme = useTheme();

  const generateId = () => Date.now().toString();
  const defaultMappingValues = { id: generateId(), source: "", target: "", expanded: false, parent: null, children: [] };

  const getNewDefaultMapping = () => defaultMappingValues;

  const { createFieldMappingsQuery, updateFieldMappingsQuery, getAllFieldMappings } = useGetFieldMappings();

  const [mappings, setMappings] = useState<Array<MappingValues>>([getNewDefaultMapping()]);
  const [mappingErrors, setMappingErrors] = useState<Array<MappingErrorTypes>>([])

  const { mutateAsync: createFieldMappings, isPending: isCreateFieldMappings } = useMutation(createFieldMappingsQuery());
  const { mutateAsync: updateFieldMappings } = useMutation(updateFieldMappingsQuery());

  const { data: fieldMappingsQueryData, refetch: refetchFieldMapping } = useQuery(getAllFieldMappings({
    serviceId: serviceProfile?.service?.id, params: {
      dataType: dataModel?.ref?.entity
    }
  }));

  // Responsive width based on screen size
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));
  const drawerWidth = isMobile ? '100%' : isTablet ? '90%' : '80%';

  const fieldMapping = fieldMappingsQueryData?.data?.data?.at(0) ?? null;

  const mode = fieldMapping?.id ? 'edit' : 'create';

  const onSaveMappings = () => {

    const mappingPayload: FieldMappings.Mappings = {}

    const collectMapping = (mappingsArr: Array<MappingValues> = [], sourcePrefix: string = '', targetPrefix: string = '', parentSourceValue: string = '') => {
      mappingsArr.forEach((mapping) => {
        const { source, target, parentSource, sourceDetails, targetDetails, children } = mapping;

        let sourcePath = [sourcePrefix, source]?.filter(Boolean)?.join('.') as string;
        let targetPath = [targetPrefix, target]?.filter(Boolean)?.join('.') as string;

        if (sourceDetails?.type === ServiceProfileSpecificationFieldType.Array) {
          sourcePath = `${sourcePath}[]`;
        }

        if (targetDetails?.dataType?.type === ServiceProfileSpecificationFieldType.Array) {
          targetPath = `${targetPath}[]`;
        }

        const tempObj = {
          key: source,
          type: parentSource?.id ? 'child' : 'root',
          isPredefined: false,
          parentKey: parentSourceValue ?? null,
          source: { field: source, type: sourceDetails?.type, path: sourcePath },
          target: { field: target, type: targetDetails?.dataType?.type, path: targetPath }
        };

        const hasChildren = !!children?.length;

        if (hasChildren) {
          collectMapping(mapping?.children, sourcePath, targetPath, mapping?.source);
        }

        mappingPayload[sourcePath] = tempObj as FieldMappings.MappingValue;
      });
    }

    collectMapping(mappings);

    if (mode === 'create') {
      const payload = {
        mappings: mappingPayload,
        dataModel: {
          type: dataModel?.ref?.entity
        }
      }
      toast.promise(
        createFieldMappings({ payload, serviceId: serviceProfile?.service?.id as string }),
        {
          loading: 'Creating Field Mappings....',
          success: () => {
            onClose();
            refetchFieldMapping();
            return 'Field mapping created successfully'
          },
          error: (err) => {
            return parseError(err?.response?.data)?.message ?? 'Failed to create field mapping!'
          }
        }
      )
    } else {
      const payload = [{ op: 'replace', path: '/mappings', value: mappingPayload }]
      toast.promise(
        updateFieldMappings({ serviceId: serviceProfile?.service?.id as string, fieldMappingId: fieldMapping?.id as string, payload }),
        {
          loading: 'Updation Field Mappings....',
          success: () => {
            onClose();
            refetchFieldMapping();
            return 'Field mapping updated successfully'
          },
          error: (err) => {
            return parseError(err?.response?.data)?.message ?? 'Failed to update field mapping!'
          }
        }
      )
    }
  }

  // Initialize mappings from existing data
  React.useEffect(() => {
    if (dataModel.fieldMappings) {
      setMappings(dataModel.fieldMappings);
    }
  }, [dataModel]);


  const getMappingStatus = (mapping: any) => {
    if (
      mapping.source &&
      mapping.target &&
      mapping.source.trim() !== '' &&
      mapping.target.trim() !== ''
    ) {
      return 'mapped';
    }
    return 'unmapped';
  };

  const mappedCount = mappings.filter(m => getMappingStatus(m) === 'mapped').length;

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: drawerWidth,
          backgroundColor: theme.palette.background.paper,
          boxShadow: theme.palette.mode === 'dark'
            ? '0 0 20px rgba(0,0,0,0.4)'
            : '0 0 20px rgba(0,0,0,0.1)',
        },
      }}
    >
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{
          p: 3,
          borderBottom: `1px solid ${theme.palette.mode === 'dark'
            ? 'rgba(255, 255, 255, 0.12)'
            : 'rgba(0, 0, 0, 0.08)'}`
        }}>
          <Stack direction="row" alignItems="flex-start" justifyContent="space-between">
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 0.5 }}>
                {serviceProfile?.name || 'Service Provider'} - {dataModel.name} Field Mapping
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Connect your {dataModel.name.toLowerCase()} data model fields to {serviceProfile?.name || 'provider'} API fields.
                Define how data flows between your system and {serviceProfile?.name || 'the service provider'}.
              </Typography>
            </Box>
            <IconButton
              onClick={onClose}
              size="small"
              sx={{
                ml: 2,
                border: 'none',
                '&:hover': {
                  bgcolor: alpha(theme.palette.action.hover, 0.1)
                }
              }}
            >
              <X size={20} />
            </IconButton>
          </Stack>
        </Box>


        {/* Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          {/* <HierarchicalFieldMappingV5
            dataModel={dataModel}
            serviceProfile={serviceProfile}
            onMappingsChange={(newMappings) => setMappings(newMappings)}
          /> */}
          <FieldMapping
            serviceProfile={serviceProfile}
            mappings={mappings}
            setMappings={setMappings}
            fieldMapping={fieldMapping}
            setMappingErrors={setMappingErrors}
            mappingErrors={mappingErrors}
            dataModel={dataModel}
          />
        </Box>

        {/* Footer */}
        <Box sx={{
          p: 3,
          borderTop: `1px solid ${theme.palette.mode === 'dark'
            ? 'rgba(255, 255, 255, 0.12)'
            : 'rgba(0, 0, 0, 0.08)'}`
        }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between">
            <Typography variant="body2" color="text.secondary">
              {mappedCount} of {mappings.length} fields mapped
            </Typography>
            <Stack direction="row" spacing={2}>
              <Button onClick={onClose} color="inherit">
                Cancel
              </Button>
              <Button
                variant="contained"
                onClick={onSaveMappings}
                sx={{
                  boxShadow: 'none',
                  '&:hover': {
                    boxShadow: 'none',
                  }
                }}
                disabled={!!isCreateFieldMappings || !!mappingErrors?.length}
                startIcon={isCreateFieldMappings ? <LoadingOutlined /> : null}
              >
                Save Mappings
              </Button>
            </Stack>
          </Stack>
        </Box>
      </Box>
    </Drawer>
  );
}