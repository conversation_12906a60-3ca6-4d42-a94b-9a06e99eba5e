import { FormItemType } from "constants/form"

export const formConfig: FormConfigTypes[] = [
   {
      name: 'name',
      type: FormItemType.Text,
      label: 'Organization Name',
      placeholder: 'Enter Organization Name',
      description: 'Please provide the official name of the company. This information will be printed in your Invoice Details.',
   },
   {
      name: 'companyUrl',
      type: FormItemType.Text,
      label: 'Website',
      placeholder: 'Enter Website',
      description: `Please enter the URL (web address) of the organization's website. This could be the homepage or any other relevant page that provides information about the organization.`,
   },
   {
      name: 'industry',
      type: FormItemType.Text,
      label: 'Industry',
      placeholder: 'Enter industry',
      description: `Please choose the primary industry or sector that best represents the organization's focus or activities.`,
   },
   {
      name: 'line1',
      type: FormItemType.Text,
      label: 'Address Line 1',
      placeholder: 'Enter address line 1',
      description: 'Please enter the first line of mailing address, including street address, apartment or suite number.',
   },
   {
      name: 'line2',
      type: FormItemType.Text,
      label: 'Address Line 2',
      placeholder: 'Enter address line 2',
      description: 'This field is optional and can be used to enter additional address information, such as building name, floor, or department',
   },
   {
      name: 'city',
      type: FormItemType.Text,
      label: 'City',
      placeholder: 'Enter city',
      description: 'Please enter the name of the city or town where the organization is located.',
   },
   {
      name: 'zip',
      type: FormItemType.Text,
      label: 'Zipcode',
      placeholder: 'Enter Zipcode',
      description: `Please provide the zipcode corresponding to the organization's address.`,
   },
   {
      name: 'state',
      type: FormItemType.Text,
      label: 'State',
      placeholder: 'Enter state',
      description: 'Please enter the name of the state where the organization is located.',
   },
   {
      name: 'country',
      type: FormItemType.Text,
      label: 'Country',
      placeholder: 'Enter country',
      description: `Please enter the name of the country where the organization is located.`,
   }
]