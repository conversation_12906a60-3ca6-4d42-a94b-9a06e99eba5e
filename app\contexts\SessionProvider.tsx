import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { useSessionManager } from '../hooks/useSessionManager';
import { 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  Typography,
  Box,
} from '@mui/material';
import LinearProgress from '@mui/material/LinearProgress';
import { useNavigate } from '@remix-run/react';
import { useCountdown } from '../hooks/useCountdown';

interface SessionContextValue {
  checkSession: () => void;
  refreshSession: () => void;
  isSessionValid: boolean;
}

const SessionContext = createContext<SessionContextValue | undefined>(undefined);

export const useSession = () => {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error('useSession must be used within SessionProvider');
  }
  return context;
};

interface SessionProviderProps {
  children: ReactNode;
  enabled?: boolean;
}

export const SessionProvider: React.FC<SessionProviderProps> = ({ 
  children, 
  enabled = true 
}) => {
  const navigate = useNavigate();
  const [showWarning, setShowWarning] = useState(false);
  const [warningTime, setWarningTime] = useState(0);
  const [isSessionValid, setIsSessionValid] = useState(true);

  // Countdown hook for warning dialog
  const { timeLeft, startCountdown, stopCountdown } = useCountdown({
    initialTime: warningTime,
    onComplete: () => {
      setShowWarning(false);
      handleLogout();
    }
  });

  // Handle session warning
  const handleSessionWarning = useCallback((timeRemaining: number) => {
    setWarningTime(Math.floor(timeRemaining / 1000)); // Convert to seconds
    setShowWarning(true);
    startCountdown(Math.floor(timeRemaining / 1000));
  }, [startCountdown]);

  // Handle session expired
  const handleSessionExpired = useCallback(() => {
    setIsSessionValid(false);
    setShowWarning(false);
    stopCountdown();
    
    // Clear auth data
    localStorage.removeItem('authToken');
    sessionStorage.clear();
    
    // Redirect to login
    const currentPath = window.location.pathname + window.location.search;
    navigate(`/login?sessionExpired=true&returnUrl=${encodeURIComponent(currentPath)}`, { 
      replace: true 
    });
  }, [navigate, stopCountdown]);

  // Session manager hook
  const { checkSession, refreshSession, isChecking, isRefreshing } = useSessionManager({
    enabled,
    onSessionWarning: handleSessionWarning,
    onSessionExpired: handleSessionExpired,
    checkInterval: 60000, // 1 minute
    warningTime: 300000, // 5 minutes
    refreshBuffer: 60000 // 1 minute
  });

  // Handle continue session
  const handleContinueSession = useCallback(async () => {
    setShowWarning(false);
    stopCountdown();
    refreshSession();
  }, [refreshSession, stopCountdown]);

  // Handle logout
  const handleLogout = useCallback(() => {
    setShowWarning(false);
    stopCountdown();
    navigate('/logout', { replace: true });
  }, [navigate, stopCountdown]);

  // Format time display
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const contextValue: SessionContextValue = {
    checkSession,
    refreshSession,
    isSessionValid
  };

  return (
    <SessionContext.Provider value={contextValue}>
      {children}
      
      {/* Session Warning Dialog */}
      <Dialog
        open={showWarning}
        onClose={() => {}}
        disableEscapeKeyDown
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Session Expiring Soon
        </DialogTitle>
        <DialogContent>
          <Box sx={{ textAlign: 'center', py: 2 }}>
            <Typography variant="body1" gutterBottom>
              Your session will expire in:
            </Typography>
            <Typography variant="h3" color="primary" sx={{ my: 2 }}>
              {formatTime(timeLeft)}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Would you like to continue your session?
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={(timeLeft / warningTime) * 100} 
              sx={{ mt: 3, height: 8, borderRadius: 1 }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ justifyContent: 'center', pb: 3 }}>
          <Button 
            variant="outlined" 
            onClick={handleLogout}
            disabled={isRefreshing}
          >
            Logout
          </Button>
          <Button 
            variant="contained" 
            onClick={handleContinueSession}
            disabled={isRefreshing}
            autoFocus
          >
            Continue Session
          </Button>
        </DialogActions>
      </Dialog>
    </SessionContext.Provider>
  );
};