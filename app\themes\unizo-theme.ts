// Unizo Brand Theme Configuration
export const unizoBrandColors = {
  primary: {
    50: '#f8fafc',
    100: '#edf2f7',
    200: '#d4e0ed',
    300: '#a9c2db',
    400: '#6f99c3',
    500: '#4473a2',
    600: '#35597e',
    700: '#2a4765',
    800: '#21384f', // Main primary
    900: '#1a2b3d',
    950: '#0f1924',
    main: '#21384f',
    light: '#4473a2',
    dark: '#1a2b3d',
    contrastText: '#ffffff',
  },
  secondary: {
    50: '#fef5ee',
    100: '#fde8d6',
    200: '#fac9a9',
    300: '#f6a372',
    400: '#f17238',
    500: '#ea580c', // Main secondary
    600: '#db3f0a',
    700: '#b52e0a',
    800: '#912510',
    900: '#762211',
    950: '#410f06',
    main: '#ea580c',
    light: '#f17238',
    dark: '#b52e0a',
    contrastText: '#ffffff',
  },
  grey: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  background: {
    default: '#f9fafb',
    paper: '#ffffff',
  },
  text: {
    primary: '#1f2937',
    secondary: '#6b7280',
    disabled: '#9ca3af',
  },
  divider: 'rgba(33, 56, 79, 0.12)',
  action: {
    active: '#21384f',
    hover: 'rgba(33, 56, 79, 0.04)',
    selected: 'rgba(33, 56, 79, 0.08)',
    disabled: 'rgba(33, 56, 79, 0.26)',
    disabledBackground: 'rgba(33, 56, 79, 0.12)',
  },
  success: {
    main: '#10b981',
    light: '#34d399',
    dark: '#059669',
    contrastText: '#ffffff',
  },
  error: {
    main: '#ef4444',
    light: '#f87171',
    dark: '#dc2626',
    contrastText: '#ffffff',
  },
  warning: {
    main: '#f59e0b',
    light: '#fbbf24',
    dark: '#d97706',
    contrastText: '#ffffff',
  },
  info: {
    main: '#3b82f6',
    light: '#60a5fa',
    dark: '#2563eb',
    contrastText: '#ffffff',
  },
};

// Navigation specific colors
export const navigationColors = {
  background: '#21384f',
  backgroundHover: '#2a4765',
  activeBackground: '#1a2b3d',
  text: '#ffffff',
  textSecondary: 'rgba(255, 255, 255, 0.7)',
  icon: 'rgba(255, 255, 255, 0.9)',
  divider: 'rgba(255, 255, 255, 0.12)',
  highlight: '#ea580c',
};

// Chart colors for dashboard
export const chartColors = {
  primary: ['#21384f', '#2a4765', '#35597e', '#4473a2', '#6f99c3'],
  secondary: ['#ea580c', '#f17238', '#f6a372', '#fac9a9', '#fde8d6'],
  success: ['#10b981', '#34d399', '#6ee7b7', '#a7f3d0', '#d1fae5'],
  error: ['#ef4444', '#f87171', '#fca5a5', '#fecaca', '#fee2e2'],
  warning: ['#f59e0b', '#fbbf24', '#fcd34d', '#fde68a', '#fef3c7'],
  info: ['#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe', '#dbeafe'],
};

// CSS Variables for global use
export const cssVariables = `
  :root {
    --unizo-brand-primary: #213350;
    --unizo-primary-50: #f8fafc;
    --unizo-primary-100: #edf2f7;
    --unizo-primary-200: #d4e0ed;
    --unizo-primary-300: #a9c2db;
    --unizo-primary-400: #6f99c3;
    --unizo-primary-500: #4473a2;
    --unizo-primary-600: #35597e;
    --unizo-primary-700: #2a4765;
    --unizo-primary-800: #21384f;
    --unizo-primary-900: #1a2b3d;
    --unizo-primary-950: #0f1924;
    
    --unizo-secondary-50: #fef5ee;
    --unizo-secondary-100: #fde8d6;
    --unizo-secondary-200: #fac9a9;
    --unizo-secondary-300: #f6a372;
    --unizo-secondary-400: #f17238;
    --unizo-secondary-500: #ea580c;
    --unizo-secondary-600: #db3f0a;
    --unizo-secondary-700: #b52e0a;
    --unizo-secondary-800: #912510;
    --unizo-secondary-900: #762211;
    --unizo-secondary-950: #410f06;
    
    --unizo-nav-bg: #21384f;
    --unizo-nav-hover: #2a4765;
    --unizo-nav-active: #1a2b3d;
    --unizo-nav-highlight: #ea580c;
  }
`;