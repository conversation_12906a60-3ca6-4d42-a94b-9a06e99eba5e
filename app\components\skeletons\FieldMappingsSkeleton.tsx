import { Grid, Skeleton, Box } from '@mui/material';
import PageCard from 'components/cards/PageCard';

export default function FieldMappingsSkeleton() {
  return (
    <Grid container spacing={3}>
      {/* Header Section */}
      <Grid item xs={12}>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item>
            <Skeleton variant="text" width={200} height={40} />
            <Skeleton variant="text" width={400} height={20} sx={{ mt: 1 }} />
          </Grid>
          <Grid item>
            <Skeleton variant="rectangular" width={150} height={40} sx={{ borderRadius: 1 }} />
          </Grid>
        </Grid>
      </Grid>

      {/* Main Content */}
      <Grid item xs={12}>
        <PageCard>
          <Box>
            {/* Table Header */}
            <Skeleton variant="rectangular" width="100%" height={50} sx={{ mb: 2 }} />
            {/* Table Rows */}
            {[1, 2, 3, 4, 5].map((index) => (
              <Skeleton key={index} variant="rectangular" width="100%" height={60} sx={{ mb: 1 }} />
            ))}
          </Box>
        </PageCard>
      </Grid>
    </Grid>
  );
}