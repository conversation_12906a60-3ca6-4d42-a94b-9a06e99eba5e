import {
   <PERSON><PERSON>,
   <PERSON><PERSON>,
   <PERSON><PERSON><PERSON><PERSON>,
   <PERSON><PERSON><PERSON>ontent,
   <PERSON><PERSON><PERSON><PERSON>le,
   <PERSON><PERSON>,
   <PERSON>,
   <PERSON><PERSON><PERSON><PERSON>,
   <PERSON>per,
   Typo<PERSON>,
} from '@mui/material';
import {  useMemo, useRef, useState } from 'react';
import { State, useStatus } from 'hooks/useStatus';

import { useGetOrganization } from 'hooks/api/organization/useGetOrganization';
import useUserDetails from 'store/user';
import useInitialSetup from 'store/initial-setup';
import Watch from 'sections/services/setup/bi-directional';

import { SetupServices } from 'routes/console/integrations/setup/service'
import AccessKey from 'routes/console/configure/api-key';
import User from 'routes/console/settings/users';
import './style.scss';


const steps = ['Categories', 'Webhooks', 'API key', 'Users'];

function validateNextOnEachWindow<T>(step: number, values: any) {
   switch (step) {
      case 0:
         // validate services
         return !!values?.services?.length
      case 1:
         // validate 2Bi-directional
         return !!values?.watches?.length;
      case 2:
         // validate access key
         return !!values?.accessKey;
      case 3:
         // validate users
         break;
      default:
         break;
   }
   return true;
}

export const InitialSetUp = () => {

   const [open, setOpen] = useState(true);

   const { user } = useUserDetails(),
      {
         setDone,

         // steps
         step,
         move,
         jump,

         values
      } = useInitialSetup();

   const { attemptToUpdateOrgOnboarding } = useGetOrganization({
      id: user?.organization?.id
   })

   const onClose = () => {
      attemptToUpdateOrgOnboarding(() => {
         setOpen(false);
         setDone(true);
      });
   }
   const { resolveIcon } = useStatus();

   const isWelcome = useMemo(() => step === 'welcome', [step]),
      isConfirmation = useMemo(() => step === 'confirm', [step]),

      // validation stepper component is active or not
      isStepActive = useMemo(() => (!isWelcome && !isConfirmation), [isWelcome, isConfirmation]),

      // validation is this a last index
      isFirst = useMemo(() => step === 0, [step]),

      // validation is this a last index
      isLast = useMemo(() => {
         if (typeof step === 'number') {
            return (step === steps?.length - 1)
         }
         return false
      }, [steps, step]),

      // validating before going to next step
      canGoNext = useMemo(() => {
         return typeof step === 'number' ?
            validateNextOnEachWindow(step, values) : true
      }, [values, step]);

   const onNext = () => {
      isLast ? jump('confirm') : move('next');
   },
      onBack = () => {
         move('prev')
      }

   return (
      <Dialog
         open={open}
         // onClose={onClose}
         fullScreen={isStepActive}
         className='xl:p-10'
      >
         <DialogTitle id="scroll-dialog-title" >

            {isStepActive ? (
               <Stepper activeStep={step as number} className='py-3'>
                  {steps.map((label) => (
                     <Step key={label}>
                        <StepLabel>{label}</StepLabel>
                     </Step>
                  ))}
               </Stepper>
            ) : null}

         </DialogTitle>

         <DialogContent>

            {isWelcome && (
               <Stack gap={2} alignItems={'center'} className='welcome-message'>
                  {resolveIcon(State.ACTIVE)}
                  <Typography variant='h3'>
                     Welcome aboard!
                  </Typography>
                  <Typography className='text-center'>
                     We've prepared a guided onboarding tour for a smooth and enriching experience. Click 'Next' to start. You can always skip and revisit site features from the Menu anytime.
                  </Typography>
                  <Button variant='contained' color='primary' onClick={onNext} >
                     Next
                  </Button>
               </Stack>
            )}

            {isConfirmation && (
               <Stack gap={2} alignItems={'center'} className='welcome-message'>
                  {resolveIcon(State.ACTIVE)}
                  <Typography variant='h3'>
                     Onboarding Complete - Congratulations!
                  </Typography>
                  <Typography className='text-center'>
                     Now you're fully equipped to explore and utilize our platform's features and possibilities. Let your adventure begin!
                  </Typography>
                  <Button variant='contained' color='primary' onClick={onClose}>
                     Go to Home
                  </Button>
               </Stack>
            )}

            {isStepActive && (
               <Guide active={step as number} />
            )}

         </DialogContent>

         {isStepActive ? (
            <DialogActions>
               <Stack direction={'row'} justifyContent={'space-between'} className='w-full'>
                  {!isFirst ? (
                     <Button variant='outlined' onClick={onBack}>Back</Button>
                  ) : <div></div>}

                  <Stack direction={'row'} gap={2}>
                     <Button color='primary' onClick={onNext} >Skip</Button>
                     <Button variant='contained' color='primary' onClick={onNext} disabled={!canGoNext} >Next</Button>
                  </Stack>
               </Stack>
            </DialogActions>
         ) : null}

      </Dialog>
   )
}

type GuideProps = {
   active: number
}

type SegmentValues = {
   services: Record<string, any>[]
   watches: Record<string, any>[]
   accessKey: Record<string, any>[]
   users: Record<string, any>[]
}

const Guide = ({ active }: GuideProps) => {

   const content: any = useMemo(() => {
      return {
         0: <SetupServices />,
         1: <Watch />,
         2: <AccessKey showDescription={true} />,
         3: <User />,
      }
   }, [])

   return (
      <>
         {content?.[active]}
      </>
   )
}