import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { parseError } from "lib/utils";
import { DocksClient } from "services/dock.service";
import { serviceProfileClient } from "services/service-profile.service";
import { toast } from "sonner";
import useUserDetails from "store/user";
import { API_ENDPOINTS } from "utils/api/api-endpoints";

const DOCK_PROFILE_API = "DOCK_PROFILE_API";

const DOCK_PROFILE_S = "DOCK_PROFILE_S";

const DOCK_PROFILE_S_ALL_INTEGRATION_S = "DOCK_PROFILE_S_ALL_INTEGRATION_S";
const DOCK_PROFILE_DELETE = "DOCK_PROFILE_DELETE";

const SERVICE_KEY = "SERVICE_KEY";

export const useGetDockProfile = () => {
  const { user } = useUserDetails();
  const orgId = user?.organization?.id;

  const queryClient = useQueryClient();

  const { mutateAsync: createDockProfileMutation } = useMutation({
    mutationFn: ({ payload }: { payload: Record<string, any> }) => {
      return DocksClient.createDockProfiles(payload);
    },
  });

  const { mutateAsync: updateDockProfileMutation } = useMutation({
    mutationFn: ({
      id,
      payload,
    }: {
      id: string;
      payload: Record<string, any>;
    }) => {
      return DocksClient.updateDockProfiles(id, payload);
    },
  });

  const { mutateAsync: deleteDockProfileMutation } = useMutation({
    mutationFn: ({ id }: { id: string }) => {
      return DocksClient.deleteDockProfiles(id);
    },
  });

  const { mutateAsync: createServiceKeyMutation } = useMutation({
    mutationFn: ({ payload }: { payload: Record<string, any> }) => {
      return DocksClient.createServiceKeys(payload);
    },
  });

  const getDockProfileById = (id: string) => {
    return useQuery({
      queryKey: [API_ENDPOINTS.DOCK_PROFILES, id],
      queryFn: async () => {
        const response = await DocksClient.getDockProfileById(id);
        return response?.data;
      },
      enabled: !!id,
    });
  };

  const getSearchServices = (payload: any) => {
    return useQuery({
      queryKey: [API_ENDPOINTS.SERVICE_KEYS, payload],
      queryFn: async () => {
        const response = await serviceProfileClient.searchServices(payload);
        return response?.data;
      },
      enabled: !!payload,
    });
  };

  return {
    attemptCreateDockProfile: (payload: Record<string, any>, cb?: any) => {
      return toast.promise(createDockProfileMutation({ payload } as any), {
        loading: "Creating...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [
              API_ENDPOINTS.DOCK_PROFILES,
              DOCK_PROFILE_API,
              DOCK_PROFILE_S_ALL_INTEGRATION_S,
              DOCK_PROFILE_S,
            ],
          });
          cb && cb();
          return "Connect UI Profile Created";
        },
        error: (err: any) => {
          return parseError(err?.response?.data)?.message;
        },
      });
    },

    attemptEditDockProfile: (
      id: string,
      payload: Record<string, any>,
      cb?: any
    ) => {
      return toast.promise(updateDockProfileMutation({ id, payload }), {
        loading: "Updating...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [
              API_ENDPOINTS.DOCK_PROFILES,
              DOCK_PROFILE_API,
              DOCK_PROFILE_S_ALL_INTEGRATION_S,
              DOCK_PROFILE_S,
            ],
          });
          cb && cb();
          return "Connect UI Profile Updated";
        },
        error: (err: any) => {
          return parseError(err?.response?.data)?.message;
        },
      });
    },

    attemptDeleteDockProfile: (id: string, cb?: any) => {
      if (!id) {
        toast.error("Invalid ID provided for deletion");
        return;
      }
      return toast.promise(deleteDockProfileMutation({ id }), {
        loading: "Deleting...",
        success: () => {
          queryClient.invalidateQueries({
            queryKey: [
              API_ENDPOINTS.DOCK_PROFILES,
              DOCK_PROFILE_API,
              DOCK_PROFILE_S_ALL_INTEGRATION_S,
              DOCK_PROFILE_S,
            ],
          });
          cb && cb();
          return "Connect UI Profile Deleted";
        },
        error: (err: any) => {
          return parseError(err?.response?.data)?.message;
        },
      });
    },

    getDockProfiles: () => {
      return useQuery({
        queryKey: [
          API_ENDPOINTS.DOCK_PROFILES,
          DOCK_PROFILE_API,
          DOCK_PROFILE_S_ALL_INTEGRATION_S,
          DOCK_PROFILE_S,
        ],
        queryFn: async () => {
          return await DocksClient.getDockProfiles();
        },
        select: (resp) => {
          return resp?.data;
        },
        enabled: !!orgId,
      });
    },

    attemptCreateServiceKey: (payload: Record<string, any>, cb?: any) => {
      return toast.promise(createServiceKeyMutation({ payload } as any), {
        loading: "Creating...",
        success: (data) => {
          queryClient.invalidateQueries({
            queryKey: [API_ENDPOINTS.SERVICE_KEYS, SERVICE_KEY],
          });
          cb && cb(data);
          return "Test link created successfully";
        },
        error: (err: any) => {
          const msg = parseError(err?.response?.data)?.message;

          if (msg?.toLowerCase().includes("service key cannot be empty")) {
            return "Please select atleast one category.";
          }

          return msg || "An unexpected error occurred.";
        },
      });
    },
    getSearchServices,
    getDockProfileById,
  };
};
