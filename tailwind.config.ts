import type { Config } from "tailwindcss";

export const links = () => [
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Public+Sans:wght@400;500;700&display=swap",
  },
];

export default {
  content: ["./app/**/{**,.client,.server}/**/*.{js,jsx,ts,tsx}"],
  theme: {
    extend: {
      colors: {
        unizo: {
          primary: {
            50: '#f8fafc',
            100: '#edf2f7',
            200: '#d4e0ed',
            300: '#a9c2db',
            400: '#6f99c3',
            500: '#4473a2',
            600: '#35597e',
            700: '#2a4765',
            800: '#21384f',
            900: '#1a2b3d',
            950: '#0f1924',
          },
          secondary: {
            50: '#fef5ee',
            100: '#fde8d6',
            200: '#fac9a9',
            300: '#f6a372',
            400: '#f17238',
            500: '#ea580c',
            600: '#db3f0a',
            700: '#b52e0a',
            800: '#912510',
            900: '#762211',
            950: '#410f06',
          },
        },
      },
      scrollbar: {
        width: {
          thin: '4px',   // Thin scrollbar width
          DEFAULT: '8px',  // Default scrollbar width
        },
        thumb: {
          DEFAULT: '#6B7280', // Color of the scrollbar thumb
          hover: '#4B5563',   // Color when hovered
        },
        track: {
          DEFAULT: '#F3F4F6', // Color of the scrollbar track
        },
      }
    },
  },
  plugins: [
  ],
} satisfies Config;
