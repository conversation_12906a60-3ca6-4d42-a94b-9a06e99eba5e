import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Box, Grid, GridProps, MenuItem, Select, TextField } from "@mui/material";
import { Form, FormField, FormItem, FormControl } from "components/@extended/Form";
import MainCard from "components/MainCard";
import { useForm } from "react-hook-form";

import { z } from "zod";
import { CardTitle } from "../card-title";
import { formConfig } from "./form";
import useCreateTenant from "store/create-tenants";
import { useEffect } from "react";


const schema = z.object({
   name: z
      .string().nonempty("Name is required."),
   companyUrl: z
      .string()
      .nonempty("Company Url URL is required.")
      .url("Please provide a valid URL."),
   industry: z.string().nonempty("Industry is required."),
   line1: z.string().nonempty("Line1 is required."),
   line2: z.string().optional(),
   city: z.string().nonempty("City is required."),
   zip: z.string().nonempty("Zip is required."),
   state: z.string().nonempty("State is required."),
   country: z.string().nonempty("Country is required."),
})


const TITLE = 'Customer Details';

const SPANS: GridProps = {
   xs: 12,
   md: 12,
   lg: 12,
   xl: 6,
}

type CustomerDetailsProps = {
   formRef: React.RefObject<HTMLFormElement>
   onSuccess: (values: z.infer<typeof schema>) => void
}

export const CustomerDetails = ({ formRef, onSuccess: onSuccessProp }: CustomerDetailsProps) => {

   const { formData, defaultValues } = useCreateTenant();

   const values = formData?.customer

   const form = useForm<z.infer<typeof schema>>({
      resolver: zodResolver(schema),
      defaultValues: defaultValues?.formData?.customer,
      mode: "onChange",
   })

   const onSubmit = (values: z.infer<typeof schema>) => {
      if (typeof onSuccessProp === 'function') {
         onSuccessProp(values)
      }
   }

   useEffect(() => {
      form.reset(values)
   }, [values])

   return (
      <MainCard
         title={(
            <CardTitle title={TITLE}  />
         )}
         className="m-auto"
      >
         <Form {...form}>
            <form
               onSubmit={(...args) => (
                  void form.handleSubmit(onSubmit)(...args)
               )}
               ref={formRef}
               className="flex flex-col gap-5"
            >
               <Grid container columnSpacing={2} rowSpacing={2}>
                  {formConfig.map((item, index) => {

                     return (
                        <Grid item {...SPANS} key={index}>
                           <FormField
                              control={form.control}
                              name={item.name}
                              render={({ field }) => (
                                 <FormItem label={item.label} description={item.description}>
                                    <FormControl>
                                       <TextField
                                          {...field}
                                          placeholder={item?.placeholder}
                                       />
                                    </FormControl>
                                 </FormItem>
                              )}
                           />
                        </Grid>

                     )
                  })}
               </Grid>
            </form>
         </Form>
      </MainCard>
   )
}