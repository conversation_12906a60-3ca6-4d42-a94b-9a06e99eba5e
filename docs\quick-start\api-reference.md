# Quick Start API Reference

## Overview

This document provides a comprehensive reference for all Quick Start API endpoints, including request/response formats, authentication requirements, and example usage.

## Authentication

All Quick Start API endpoints require authentication. Include the user's authentication token in the request headers:

```http
Authorization: Bearer <token>
X-User-Id: <user-id>
X-Org-Id: <org-id>
```

## Endpoints

### Get Quick Start Status

Check if the current user needs to complete the Quick Start flow.

```http
GET /api/quick-start/status
```

#### Response

```typescript
{
  needsQuickStart: boolean;
  progress?: QuickStartProgress;
  configuration: TenantConfiguration;
  settings: QuickStartSettings;
}
```

#### Example Response

```json
{
  "needsQuickStart": true,
  "progress": {
    "completed": false,
    "currentStep": 1,
    "completedSteps": ["category-selection"],
    "selectedCategories": ["SCM", "TICKETING"],
    "configuredServices": [],
    "lastUpdated": "2024-01-15T10:30:00Z"
  },
  "configuration": {
    "type": "enterprise",
    "predefinedCategories": ["SCM", "TICKETING", "PCR", "INCIDENT"],
    "isPredefined": true,
    "allowCustomization": true,
    "skipAllowed": false
  },
  "settings": {
    "showOnLogin": true,
    "forceCompletion": true,
    "allowSkip": false,
    "customMessage": "Set up your enterprise workspace"
  }
}
```

### Get Progress

Get detailed progress information for the Quick Start flow.

```http
GET /api/quick-start/progress
```

#### Response

```typescript
{
  completed: boolean;
  currentStep: number;
  completedSteps: string[];
  selectedCategories: string[];
  configuredServices: string[];
  lastUpdated: string;
  timeSpent?: number; // Total time in seconds
  startedAt?: string;
}
```

#### Example Response

```json
{
  "completed": false,
  "currentStep": 2,
  "completedSteps": ["category-selection", "service-selection"],
  "selectedCategories": ["SCM", "TICKETING", "MONITORING"],
  "configuredServices": ["github", "jira"],
  "lastUpdated": "2024-01-15T11:45:00Z",
  "timeSpent": 300,
  "startedAt": "2024-01-15T11:40:00Z"
}
```

### Save Progress

Save progress for a specific step in the Quick Start flow.

```http
POST /api/quick-start/save
```

#### Request Body

```typescript
{
  step: string;
  data: Record<string, any>;
  isComplete?: boolean;
}
```

#### Example Request

```json
{
  "step": "category-selection",
  "data": {
    "selectedCategories": ["SCM", "TICKETING", "MONITORING"],
    "timestamp": "2024-01-15T11:30:00Z"
  },
  "isComplete": false
}
```

#### Response

```typescript
{
  success: boolean;
  progress: QuickStartProgress;
  nextStep?: string;
  errors?: string[];
}
```

#### Example Response

```json
{
  "success": true,
  "progress": {
    "completed": false,
    "currentStep": 1,
    "completedSteps": ["category-selection"],
    "selectedCategories": ["SCM", "TICKETING", "MONITORING"],
    "configuredServices": [],
    "lastUpdated": "2024-01-15T11:30:00Z"
  },
  "nextStep": "service-configuration"
}
```

### Save Categories

Save selected API categories.

```http
POST /api/quick-start/categories
```

#### Request Body

```typescript
{
  categories: string[];
}
```

#### Example Request

```json
{
  "categories": ["SCM", "TICKETING", "MONITORING", "CI_CD"]
}
```

#### Response

```json
{
  "success": true,
  "savedCategories": ["SCM", "TICKETING", "MONITORING", "CI_CD"],
  "availableServices": {
    "SCM": ["github", "gitlab", "bitbucket"],
    "TICKETING": ["jira", "linear", "asana"],
    "MONITORING": ["datadog", "newrelic"],
    "CI_CD": ["jenkins", "circleci", "github-actions"]
  }
}
```

### Save Service Configurations

Save configurations for selected services.

```http
POST /api/quick-start/services
```

#### Request Body

```typescript
{
  services: ServiceConfiguration[];
}
```

#### Example Request

```json
{
  "services": [
    {
      "serviceProfileId": "github",
      "category": "SCM",
      "name": "GitHub",
      "isConfigured": true,
      "configuration": {
        "organizationName": "my-org",
        "apiEndpoint": "https://api.github.com"
      },
      "authType": "oauth"
    },
    {
      "serviceProfileId": "jira",
      "category": "TICKETING",
      "name": "Jira",
      "isConfigured": true,
      "configuration": {
        "domain": "mycompany.atlassian.net",
        "projectKey": "PROJ"
      },
      "webhookUrl": "https://api.unizo.com/webhooks/jira/abc123",
      "authType": "oauth"
    }
  ]
}
```

#### Response

```json
{
  "success": true,
  "configuredServices": 2,
  "webhooksCreated": 1,
  "errors": []
}
```

### Complete Quick Start

Mark the Quick Start flow as complete.

```http
POST /api/quick-start/complete
```

#### Request Body

```typescript
{
  completedAt?: string;
  selectedCategories: string[];
  configuredServices: ServiceConfiguration[];
  skippedSteps?: string[];
  totalDuration?: number; // in seconds
}
```

#### Example Request

```json
{
  "selectedCategories": ["SCM", "TICKETING", "MONITORING"],
  "configuredServices": [
    {
      "serviceProfileId": "github",
      "category": "SCM",
      "name": "GitHub",
      "isConfigured": true
    },
    {
      "serviceProfileId": "jira",
      "category": "TICKETING",
      "name": "Jira",
      "isConfigured": true
    }
  ],
  "totalDuration": 600
}
```

#### Response

```json
{
  "success": true,
  "redirectUrl": "/console/dashboard",
  "welcomeMessage": "Welcome to Unizo! Your workspace is ready.",
  "nextSteps": [
    "Explore the API documentation",
    "Create your first integration",
    "Invite team members"
  ]
}
```

### Skip Quick Start

Skip the Quick Start flow (if allowed by tenant configuration).

```http
POST /api/quick-start/skip
```

#### Request Body

```typescript
{
  reason?: string;
  skipPermanently?: boolean;
}
```

#### Example Request

```json
{
  "reason": "Already familiar with Unizo",
  "skipPermanently": true
}
```

#### Response

```json
{
  "success": true,
  "message": "Quick Start skipped. You can set this up later in Settings.",
  "redirectUrl": "/console/dashboard"
}
```

### Reset Quick Start

Reset Quick Start progress (admin only).

```http
POST /api/quick-start/reset
```

#### Request Body

```typescript
{
  userId?: string; // If not provided, resets for current user
  clearAll?: boolean; // Clear all data or just mark as incomplete
}
```

#### Response

```json
{
  "success": true,
  "message": "Quick Start progress has been reset"
}
```

## Error Responses

All endpoints follow a consistent error response format:

```typescript
{
  error: {
    code: string;
    message: string;
    details?: any;
  };
  status: number;
}
```

### Common Error Codes

| Code | Status | Description |
|------|--------|-------------|
| `AUTH_REQUIRED` | 401 | Authentication token missing or invalid |
| `FORBIDDEN` | 403 | User doesn't have permission |
| `NOT_FOUND` | 404 | Resource not found |
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `ALREADY_COMPLETED` | 409 | Quick Start already completed |
| `TENANT_CONFIG_ERROR` | 400 | Invalid tenant configuration |
| `STEP_NOT_ALLOWED` | 400 | Step not allowed in current state |
| `RATE_LIMITED` | 429 | Too many requests |
| `SERVER_ERROR` | 500 | Internal server error |

### Example Error Response

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid category selection",
    "details": {
      "field": "categories",
      "reason": "Required categories missing: SCM, TICKETING",
      "requiredCategories": ["SCM", "TICKETING"],
      "providedCategories": ["MONITORING"]
    }
  },
  "status": 400
}
```

## Webhooks

### Quick Start Completed Webhook

When a user completes the Quick Start flow, a webhook can be sent to notify external systems.

```http
POST https://your-webhook-url.com/quick-start-completed
```

#### Webhook Payload

```json
{
  "event": "quick_start.completed",
  "timestamp": "2024-01-15T12:00:00Z",
  "data": {
    "userId": "user_123",
    "tenantId": "tenant_456",
    "completedAt": "2024-01-15T12:00:00Z",
    "configuration": {
      "selectedCategories": ["SCM", "TICKETING"],
      "configuredServices": ["github", "jira"],
      "totalDuration": 600
    }
  }
}
```

## Rate Limiting

Quick Start API endpoints have the following rate limits:

| Endpoint | Rate Limit |
|----------|------------|
| GET /status | 60 requests/minute |
| GET /progress | 120 requests/minute |
| POST /save | 30 requests/minute |
| POST /complete | 5 requests/minute |
| POST /skip | 5 requests/minute |

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1642250400
```

## SDK Examples

### JavaScript/TypeScript

```typescript
import { QuickStartClient } from '@unizo/quick-start-sdk';

const client = new QuickStartClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.unizo.com'
});

// Check status
const status = await client.getStatus();

if (status.needsQuickStart) {
  // Save categories
  await client.saveCategories(['SCM', 'TICKETING']);
  
  // Configure services
  await client.configureService({
    serviceProfileId: 'github',
    category: 'SCM',
    configuration: {
      organizationName: 'my-org'
    }
  });
  
  // Complete quick start
  await client.complete();
}
```

### Python

```python
from unizo.quick_start import QuickStartClient

client = QuickStartClient(
    api_key='your-api-key',
    base_url='https://api.unizo.com'
)

# Check status
status = client.get_status()

if status['needsQuickStart']:
    # Save categories
    client.save_categories(['SCM', 'TICKETING'])
    
    # Configure services
    client.configure_service({
        'serviceProfileId': 'github',
        'category': 'SCM',
        'configuration': {
            'organizationName': 'my-org'
        }
    })
    
    # Complete quick start
    client.complete()
```

### cURL

```bash
# Check status
curl -X GET https://api.unizo.com/api/quick-start/status \
  -H "Authorization: Bearer your-token"

# Save categories
curl -X POST https://api.unizo.com/api/quick-start/categories \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{"categories": ["SCM", "TICKETING"]}'

# Complete quick start
curl -X POST https://api.unizo.com/api/quick-start/complete \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{"selectedCategories": ["SCM", "TICKETING"], "configuredServices": ["github", "jira"]}'
```