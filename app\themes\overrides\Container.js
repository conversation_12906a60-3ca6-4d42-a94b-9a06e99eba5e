// ==============================|| OVERRIDES - CONTAINER ||============================== //

export default function Container(theme) {
  return {
    MuiContainer: {
      styleOverrides: {
        root: {
          // Remove the maxWidth constraint for lg breakpoint
          [`@media (min-width:${theme.breakpoints.values.lg}px)`]: {
            '&.MuiContainer-maxWidthLg': {
              maxWidth: '100%',
            }
          }
        },
        maxWidthLg: {
          // Override the default maxWidth for lg size
          [`@media (min-width:${theme.breakpoints.values.lg}px)`]: {
            maxWidth: '100%',
          }
        }
      }
    }
  };
}