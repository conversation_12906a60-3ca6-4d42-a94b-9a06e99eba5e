{"href": "https://api.platform.com/api/v1/serviceProfile/{serviceProfileId}/specifications/gh-org-001", "type": "PREDEFINED", "id": "61354879-7c95-4263-8f8f-7db649233634", "name": "Organization", "description": "Represents a GitHub organization entity", "state": "ACTIVE | DEPRICATED", "visibility": "PUBLIC", "version": "1.0.0", "serviceProfile": {"id": "39b8e937-9832-47e7-9732-bc5f62157438", "name": "GitHub SCM Provider"}, "dataModel": {"type": "ORGANIZATION", "api": {"endpoint": "https://api.github.com/orgs/{org}", "method": "GET"}}, "fields": [{"id": "login", "name": "login", "type": "string", "description": "The organization login name"}, {"id": "id", "name": "id", "type": "integer", "description": "Unique numeric identifier for the organization"}, {"id": "is_verified", "name": "is_verified", "type": "boolean", "description": "Whether the organization is verified"}, {"id": "avatar_url", "name": "avatar_url", "type": "uri", "description": "Avatar image URL of the organization"}, {"id": "created_at", "name": "created_at", "type": "datetime", "description": "Timestamp when the organization was created"}, {"id": "plan", "name": "plan", "type": "object", "description": "Plan information of the organization", "properties": [{"id": "details", "name": "details", "type": "object", "properties": [{"id": "name", "name": "name", "type": "string"}]}, {"id": "name", "name": "name", "type": "string"}, {"id": "space", "name": "space", "type": "integer"}, {"id": "private_repos", "name": "private_repos", "type": "integer"}, {"id": "collaborators", "name": "collaborators", "type": "integer"}]}, {"id": "billing_email", "name": "billing_email", "type": "email", "description": "Billing contact email"}, {"id": "topics", "name": "topics", "type": "array", "items": {"type": "string"}, "description": "List of topics associated with the organization"}, {"id": "plan_features", "name": "plan_features", "type": "array", "items": {"type": "object", "properties": [{"id": "feature_name", "name": "feature_name", "type": "string"}, {"id": "enabled", "name": "enabled", "type": "boolean"}, {"id": "quota", "name": "quota", "type": "integer"}]}, "description": "List of features available in the organization's plan"}, {"id": "default_repository_permission", "name": "default_repository_permission", "type": "enum", "enum": ["read", "write", "admin", "none"], "description": "Default permission for repositories"}, {"id": "archived_repos_count", "name": "archived_repos_count", "type": "number", "format": "float", "description": "Number of archived repositories"}], "tags": ["source: github", "domain: scm"], "attributes": [{"schemaVersion": "v1"}, {"apiFamily": "REST"}], "links": [{"rel": "openApiSpec", "href": "https://docs.github.com/en/rest/orgs/orgs?apiVersion=2022-11-28", "method": "GET", "contentType": "application/json", "authenticate": true}, {"rel": "example", "href": "https://api.github.com/orgs/github", "method": "GET", "contentType": "application/json", "authenticate": true}], "changeLog": {"createdBy": "<PERSON>", "createdDateTime": "2025-07-11T10:30:00Z", "updatedBy": "<PERSON>", "updatedDateTime": "2025-07-11T10:30:00Z"}}