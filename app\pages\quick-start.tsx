import React, { useEffect, useState } from 'react';
import { Box, CircularProgress, Alert } from '@mui/material';
import { useNavigate } from '@remix-run/react';
import { GettingStartedProvider } from 'sections/getting-started/context/GettingStartedContext';
import { GettingStartedStepper } from 'sections/getting-started';
import { 
  useQuickStartStatus, 
  useQuickStartProgress,
  shouldShowQuickStart,
  getTenantConfiguration
} from 'services/tenant-configuration';
import { useAuthUser } from 'hooks/use-auth-user';
import { useGettingStarted } from 'sections/getting-started/context/GettingStartedContext';


export default function QuickStartPage() {
  const navigate = useNavigate();
  const { user } = useAuthUser();
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Fetch quick start status
  const { 
    data: quickStartStatus, 
    isLoading: statusLoading, 
    error: statusError 
  } = useQuickStartStatus();
  
  // Fetch progress if quick start is needed
  const { 
    data: progress, 
    isLoading: progressLoading 
  } = useQuickStartProgress(quickStartStatus?.needsQuickStart);

  useEffect(() => {
    if (!statusLoading && quickStartStatus) {
      // Check if quick start should be shown
      if (!shouldShowQuickStart(quickStartStatus)) {
        // Redirect to dashboard if quick start is not needed
        navigate('/console/dashboard');
        return;
      }
      
      // Set initialized flag
      setIsInitialized(true);
    }
  }, [quickStartStatus, statusLoading, navigate]);

  // Show loading state
  if (statusLoading || progressLoading || !isInitialized) {
    return (
      <Box
        sx={{
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: (theme) => theme.palette.background.default,
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Show error state
  if (statusError) {
    return (
      <Box
        sx={{
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 4,
          backgroundColor: (theme) => theme.palette.background.default,
        }}
      >
        <Alert 
          severity="error" 
          sx={{ maxWidth: 600 }}
        >
          Failed to load quick start configuration. Please try refreshing the page or contact support.
        </Alert>
      </Box>
    );
  }

  // Get tenant configuration
  const tenantConfig = quickStartStatus ? getTenantConfiguration(quickStartStatus.configuration.type) : null;

  if (!tenantConfig || !quickStartStatus) {
    return null;
  }

  return (
    <GettingStartedProvider>
      <QuickStartContent 
        tenantConfig={tenantConfig}
        quickStartStatus={quickStartStatus}
        progress={progress}
      />
    </GettingStartedProvider>
  );
}

interface QuickStartContentProps {
  tenantConfig: any;
  quickStartStatus: any;
  progress?: any;
}

/**
 * Inner component that has access to GettingStartedContext
 */
function QuickStartContent({ tenantConfig, quickStartStatus, progress }: QuickStartContentProps) {
  const { setPredefinedConfig } = useGettingStarted();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    // Initialize with tenant configuration
    if (tenantConfig.isPredefined && tenantConfig.predefinedCategories) {
      setPredefinedConfig(
        tenantConfig.predefinedCategories, 
        tenantConfig.isPredefined
      );
    }
    
    // Resume from saved progress if available
    if (progress?.currentStep) {
      setCurrentStep(progress.currentStep);
    }
  }, [tenantConfig, progress]);

  // Handle completion
  const handleComplete = () => {
    // Navigate to dashboard after completion
    navigate('/console/dashboard');
  };

  // Handle skip (if allowed)
  const handleSkip = () => {
    if (quickStartStatus.settings.allowSkip) {
      navigate('/console/dashboard');
    }
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <GettingStartedStepper 
        initialStep={currentStep}
        onComplete={handleComplete}
        onSkip={quickStartStatus.settings.allowSkip ? handleSkip : undefined}
        tenantConfig={tenantConfig}
      />
    </Box>
  );
}

// Re-export the context hook for use in other components
export { useGettingStarted } from 'sections/getting-started/context/GettingStartedContext';