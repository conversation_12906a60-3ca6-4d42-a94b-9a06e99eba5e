# Universal Field Configuration Wizard - Implementation Plan

## Executive Summary

This document outlines the implementation plan for a universal field configuration wizard that enables users to define custom fields, select integration tools, and configure field mappings across all 14 integration categories in the Unizo platform.

## Requirements Understanding

### Core Requirements

1. **Universal Application**: The wizard must work across all integration categories (SCM, Ticketing, Communications, Incident Management, etc.)
2. **3-Step Guided Flow**:
   - Step 1: Define custom data fields for each category's data models
   - Step 2: Select integration tools from a marketplace view
   - Step 3: Configure field mappings between custom fields and tool-specific fields
3. **User Experience Features**:
   - Inline field creation and deletion
   - Visual field type indicators
   - Auto-mapping suggestions
   - Search and filter capabilities
   - Progress tracking across steps

### Technical Requirements

1. **Field Management**:
   - Support multiple field types (string, number, boolean, date, object, array)
   - Field validation and descriptions
   - Category-specific data models
   - Persistent storage of custom fields

2. **Tool Integration**:
   - Dynamic tool loading based on category
   - Multi-select capability
   - Connection status indicators
   - Tool-specific authentication handling

3. **Field Mapping**:
   - Intelligent auto-mapping suggestions
   - Manual mapping override
   - Validation of mapping compatibility
   - Skip field option

## Current State Analysis

### Existing Infrastructure

1. **API Endpoints**:
   - `/api/integrations` - CRUD operations for integrations
   - `/api/field-mappings` - Field mapping management
   - Category-specific data models already defined

2. **UI Components**:
   - Material-UI based stepper components
   - Field mapping dialogs (HierarchicalFieldMappingV5)
   - Integration creation wizard framework

3. **Data Models**:
   - 14 integration categories defined
   - Category-specific data models (Organization, Repository, Ticket, etc.)
   - Field type system in place

## Technical Approach

### Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                   Universal Field Config Wizard              │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌──────────────┐  ┌──────────────────┐  │
│  │   Step 1    │  │    Step 2    │  │     Step 3       │  │
│  │   Define    │  │    Select    │  │    Configure     │  │
│  │   Fields    │  │    Tools     │  │    Mappings      │  │
│  └─────────────┘  └──────────────┘  └──────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    Shared Components                         │
│  • Field Type System  • Validation  • Auto-mapper          │
├─────────────────────────────────────────────────────────────┤
│                    State Management                          │
│            (React Context + Local Storage)                   │
├─────────────────────────────────────────────────────────────┤
│                      API Layer                               │
│     • Field Service  • Integration Service  • Mapping Service│
└─────────────────────────────────────────────────────────────┘
```

### Component Structure

```
/app/components/universal-field-wizard/
├── index.tsx                          # Main wizard component
├── context/
│   └── FieldConfigContext.tsx         # Wizard state management
├── steps/
│   ├── DefineFields/
│   │   ├── index.tsx                  # Step 1 container
│   │   ├── DataModelCard.tsx          # Individual data model card
│   │   ├── FieldList.tsx              # Field list with CRUD
│   │   └── AddFieldDialog.tsx         # Field creation dialog
│   ├── SelectTools/
│   │   ├── index.tsx                  # Step 2 container
│   │   ├── ToolMarketplace.tsx        # Tool grid view
│   │   ├── ToolCard.tsx               # Individual tool card
│   │   └── ToolSearch.tsx             # Search functionality
│   └── ConfigureMappings/
│       ├── index.tsx                  # Step 3 container
│       ├── MappingTabs.tsx            # Tool-specific tabs
│       ├── FieldMappingRow.tsx        # Individual mapping row
│       └── AutoMapper.tsx             # Auto-mapping logic
├── components/
│   ├── FieldTypeSelector.tsx         # Reusable field type selector
│   ├── FieldTypeBadge.tsx            # Visual field type indicator
│   └── ProgressIndicator.tsx         # Step progress tracker
└── hooks/
    ├── useFieldValidation.ts          # Field validation logic
    ├── useAutoMapping.ts              # Auto-mapping suggestions
    └── useCategoryTools.ts            # Category-specific tools
```

### Implementation Phases

#### Phase 1: Core Infrastructure (Week 1)
1. Create wizard context and state management
2. Build reusable field components
3. Implement field type system with validation
4. Create base wizard layout with stepper

#### Phase 2: Step 1 - Define Fields (Week 2)
1. Build DataModelCard component for each data type
2. Implement inline field CRUD operations
3. Add field type selection and validation
4. Create persistent storage for custom fields

#### Phase 3: Step 2 - Select Tools (Week 3)
1. Create ToolMarketplace component
2. Implement category-based tool filtering
3. Add multi-select functionality
4. Build search and filter capabilities

#### Phase 4: Step 3 - Configure Mappings (Week 4)
1. Build tabbed interface for selected tools
2. Implement field mapping rows
3. Create auto-mapping algorithm
4. Add mapping validation

#### Phase 5: Integration & Polish (Week 5)
1. Connect to existing APIs
2. Add loading states and error handling
3. Implement wizard completion flow
4. Add animations and transitions

## Key Technical Decisions

### 1. State Management
- Use React Context for wizard state
- Persist progress in localStorage
- Maintain field configurations in backend

### 2. Field Type System
```typescript
enum FieldType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  OBJECT = 'object',
  ARRAY = 'array'
}

interface CustomField {
  id: string;
  name: string;
  type: FieldType;
  description?: string;
  required: boolean;
  defaultValue?: any;
  validation?: ValidationRule[];
}
```

### 3. Auto-Mapping Algorithm
- Use Levenshtein distance for field name matching
- Consider field type compatibility
- Provide confidence scores for suggestions
- Allow manual override

### 4. Category-Agnostic Design
- Abstract common patterns across categories
- Use configuration-driven UI rendering
- Maintain category-specific business logic in separate modules

## API Integration

### Required API Endpoints

1. **Field Management**:
   - `GET /api/categories/{categoryId}/custom-fields`
   - `POST /api/categories/{categoryId}/custom-fields`
   - `DELETE /api/categories/{categoryId}/custom-fields/{fieldId}`

2. **Tool Discovery**:
   - `GET /api/categories/{categoryId}/available-tools`
   - `GET /api/tools/{toolId}/fields`

3. **Mapping Configuration**:
   - `POST /api/integrations/{integrationId}/field-mappings`
   - `GET /api/field-mappings/suggestions`

## Success Metrics

1. **User Experience**:
   - Time to complete wizard < 5 minutes
   - Auto-mapping accuracy > 80%
   - Error rate < 5%

2. **Technical**:
   - Component reusability > 70%
   - Test coverage > 85%
   - Performance: < 100ms response time

## Risks and Mitigations

| Risk | Impact | Mitigation |
|------|--------|------------|
| Complex category-specific requirements | High | Build flexible, configuration-driven system |
| Performance with large field sets | Medium | Implement virtualization and pagination |
| Auto-mapping accuracy | Medium | Provide manual override and user feedback loop |
| Breaking existing integrations | High | Maintain backward compatibility layer |

## Next Steps

1. Review and approve technical approach
2. Set up project structure and base components
3. Begin Phase 1 implementation
4. Schedule weekly progress reviews
5. Plan user testing sessions for each phase

## Appendix: Category-Specific Considerations

### SCM (Source Control)
- Special handling for branch protection rules
- Support for webhook event selection
- Repository permission mappings

### Ticketing
- Custom field types for ticket priorities
- User assignment mappings
- Status workflow configurations

### Communications
- Channel mapping requirements
- User mention format conversions
- Message threading support

### Incident Management
- Severity level mappings
- Escalation policy fields
- On-call schedule integration