/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from 'axios';
import { BASE_URL_S, getFetchConfigsHeaders } from 'lib/fetch';
import { getEnvironmentId } from 'utils/environment-id';

const fetchInstance = axios.create({
  baseURL: BASE_URL_S.POD,
  headers: {
    ...getFetchConfigsHeaders()
  },
});

fetchInstance.interceptors.request.use((config) => {
  config.headers['organizationid'] = window.authUserOrgId;
  config.headers['environmentId'] = getEnvironmentId()
  return config;
});

fetchInstance.interceptors.response.use(
  (response: any) => {

    if (response.status === 302) {
      const newLocation = response.headers.get("location");
      window.location.href = newLocation;
    } else {
      return response;
    }

  },
  (error) => {
    if (error?.response?.status === 403) {
      return window.location.href = "/registration/unAuthorized";
    }
    error.response && error.response.status === 401 && (() => {
      window.location.href = '/';
    })();
    return Promise.reject(error);
  }
);

const axiosUtils = {
  CancelToken: axios.CancelToken,
  isCancel: axios.isCancel,
};

export { axiosUtils, };



export default fetchInstance;
