/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { CircularProgress, Grid, Stack, useMediaQuery } from "@mui/material";
import { useEffect, useMemo } from "react";

import { Menu as ExtendedMenu } from "components/@extended/Menu";

import ServiceList from '../list';
import MdMenu from '../Menu-md';

import { RIGHT_GRID, LEFT_GRID } from "../constant";

import { ResponsiveGrid } from "components/@extended/ResponsiveGrid";
import { ServiceProfile } from "types/service-profile";
import useSelfRegistration from "store/self-signUp/self-signup";
import { OnboardingStep } from "types/tenant-metadata";
import { getIsSelectionEnd } from "sections/self-signup/helper";

interface ContentProps {
   categories: any[]
   showLeftNav?: boolean

   isProfileLoading?: boolean
   profiles: Array<ServiceProfile>

   currentStep: OnboardingStep

   // Callbacks
   onSelectCategory?: (category: string) => void
}


const Content = (props: ContentProps) => {
   const matchDownMD = useMediaQuery((theme: any) => theme?.breakpoints?.down('md'));

   const { selectedServices, addSelectedService, currentCategory: domain } = useSelfRegistration();

   const {
      showLeftNav,
      categories,
      isProfileLoading,
      profiles = [],
      currentStep,
      onSelectCategory
   } = props;

   const maxCategories = currentStep?.validations?.maxProvidersPerCategory;

   const selectedPerCategory = useMemo(() => {
      return selectedServices?.filter((i) => i?.type === domain)
   }, [selectedServices, domain])

   /**
    * Looking for max categories selected
    */
   const isSelectionEnd = getIsSelectionEnd(maxCategories, selectedPerCategory?.length);

   const onToggleEnable = (newToggledService: ServiceProfile) => {
      addSelectedService(newToggledService)
   }

   const gridProps = useMemo(() => {
      /**
       * if left nav does't show, let right grid take full
       */
      if (!showLeftNav) {
         return { left: {}, right: { ...RIGHT_GRID, md: 12 } }
      }

      return { right: RIGHT_GRID, left: LEFT_GRID }
   }, [showLeftNav]);

   useEffect(() => {
      if (isSelectionEnd) {

      }
   }, [isSelectionEnd, domain])

   return (
      <Grid container spacing={2}>

         {showLeftNav && (
            < Grid item xs={4} {...gridProps.left}>
               {matchDownMD ? (
                  <MdMenu
                     setDomain={onSelectCategory}
                     domain={domain}
                     items={categories}
                  />
               ) : (
                  <ExtendedMenu
                     items={categories}
                     selected={domain}
                     onValueChange={({ id }) => onSelectCategory?.(id)}
                  />
               )}
            </Grid>
         )}

         <Grid item xs={8} {...gridProps.right}>
            {isProfileLoading ? (
               <Stack
                  sx={{
                     alignItems: 'center',
                     justifyContent: 'center',
                     height: '100%'
                  }}
               >
                  <CircularProgress />
               </Stack>
            ) : (
               <>
                  <ResponsiveGrid
                     tiles={(
                        <ServiceList
                           profiles={profiles}
                           onToggleEnable={onToggleEnable}
                           selectedServiceIds={selectedServices?.map((i) => i?.id)}
                        />
                     ) as any}
                  />
               </>
            )}
         </Grid>
      </Grid >
   )
}

export default Content;