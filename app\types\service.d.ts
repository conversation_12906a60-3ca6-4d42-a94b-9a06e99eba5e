import { ServiceProfile } from "./service-profile"

export interface Service {
  href: string
  type: string
  id: string
  name: string
  state: string
  notifications: any[]
  accessPointsSummary: AccessPointsSummary
  serviceProfile: ServiceProfile
  organization: Organization
  attributes: any[]
  sandbox: {
    enabled: boolean;
  };
  tags: any[]
  changeLog: ChangeLog
  links: Link[]

  versions?: {
    type: string
    serviceProfileVersion: {
      id: string
      name: string
    }
  }[]
}

export interface AccessPointsSummary {
  requiresAction: boolean
  readyForConfiguration: number
  totalAccessPointCount: number
}


export interface Organization {
  id: string
}

export interface ChangeLog {
  createdDateTime: string
  lastUpdatedDateTime: string
}

export interface Link {
  name: string
  rel: string
  href: string
  method: string
  contentType: string
  authenticate: boolean
}
