import { useEffect, useMemo, useState } from "react";

import { ServiceProfileSpecifications } from "types/service-profile-specification";
import { useGetServiceProfile } from "hooks/api/service-profile/useGetServiceProfile";

import {
  getFlattanedSourceFields,
  getFlattendedTargetFields,
  getServiceProfileSpecificationFields,
  getSourceFieldType,
  getTargetFieldType,
} from "./utils";
import { useQuery } from "@tanstack/react-query";
import { LoadingOutlined } from "@ant-design/icons";

import {
  FieldMappingProps,
  HierarchyMappingValues,
  MappingErrorTypes,
  MappingValues,
  ModeTypes,
} from "./type";
import Mappings from "./mapping";

import MappingContextProvider from "./contexts/mapping.context";

import Header from "./header";
import useGetAdditionalAttributes from "hooks/api/additional-attributes";

import { ServiceProfileSpecificationFieldType } from "constants/service-profile";
import { AdditionalFields } from "types/additional-attributes";
import { FieldMappings } from "types/field-mappings";
import { keyBy } from "lodash";

type AddMappingRecursivelyOptions = {
  mappings: HierarchyMappingValues[];
  parentSource: MappingValues | null;
  newMapping?: HierarchyMappingValues;
  canAddChild?: boolean;
  sourceDetails?: ServiceProfileSpecifications.Fields;
  targetDetails?: AdditionalFields.Root;
  type?: "source" | "target";
};

type DeleteMappingRecursivelyOptions = {
  parentId?: string;
  id: string;
  mappings: HierarchyMappingValues[];
};

const nonPrimitiveDataTypes = [
  ServiceProfileSpecificationFieldType.Object,
  ServiceProfileSpecificationFieldType.Array,
];

const FieldMapping = ({
  dataModel,
  serviceProfile,
  mappings,
  setMappings,
  fieldMapping,
  setMappingErrors,
  mappingErrors,
}: FieldMappingProps) => {
  const generateId = () =>
    Math.floor(1000000000 + Math.random() * 9000000000)?.toString();

  const { serviceProfileSpecificationQueryOptions } = useGetServiceProfile(),
    { getAllAdditionalAttributesQuery } = useGetAdditionalAttributes();

  const { data: serviceProfileSpecificationQueryData, isLoading } = useQuery(
    serviceProfileSpecificationQueryOptions({
      id: serviceProfile?.id,
      params: { dataType: dataModel?.key },
    })
  );

  const { data: additionalAttributesQueryData } = useQuery(
    getAllAdditionalAttributesQuery()
  );

  const serviceProfileSpecification =
    serviceProfileSpecificationQueryData?.data?.data?.at(0) ?? null;
  const additionalAttributes = additionalAttributesQueryData?.data?.data ?? [];

  const [viewMode, setViewMode] = useState<ModeTypes>("table");

  const sourceFields = getServiceProfileSpecificationFields(
    serviceProfileSpecification as unknown as ServiceProfileSpecifications.Root
  );

  const targetFields = additionalAttributes;

  const flattanedSourceFields = useMemo(
    () => getFlattanedSourceFields(sourceFields),
    [sourceFields]
  );
  const flattanedTargetFields = useMemo(
    () => getFlattendedTargetFields(targetFields),
    [targetFields]
  );

  const getNewDefaultMapping = () => {
    return {
      id: generateId(),
      source: "",
      target: "",
      expanded: false,
      children: [],
    };
  };

  const addMappingRecursively = ({
    mappings,
    sourceDetails,
    targetDetails,
    parentSource,
    newMapping,
    canAddChild,
    type = "source",
  }: AddMappingRecursivelyOptions): HierarchyMappingValues[] => {
    return mappings.map((mapping) => {
      /**
       * Trying to find exact mapping by id
       * Adding Additional meta data into it
       */
      if (mapping.id === parentSource?.id) {
        const updated = { ...mapping, expanded: true };

        if (sourceDetails) {
          updated.source = sourceDetails?.name;
          updated.sourceDetails = sourceDetails;
        }

        if (targetDetails) {
          updated.target = targetDetails?.name;
          updated.targetDetails = targetDetails;
        }

        if (type === "source") {
          if (newMapping && canAddChild) {
            updated.children = canAddChild
              ? [...mapping.children, newMapping]
              : [];
          } else {
            // updated.children = [];
          }
        }

        return updated;
      }
      const tempObj = {
        mappings: mapping.children,
        parentSource,
        newMapping,
        canAddChild,
        sourceDetails,
        targetDetails,
      };
      return { ...mapping, children: addMappingRecursively(tempObj) };
    });
  };

  const expandMappingRecursively = (
    mappings: MappingValues[],
    mappingId: string
  ): HierarchyMappingValues[] => {
    return mappings.map((mapping) => {
      if (mapping.id === mappingId) {
        return { ...mapping, expanded: !mapping?.expanded };
      }
      return {
        ...mapping,
        children: expandMappingRecursively(mapping?.children, mappingId),
      };
    });
  };

  const deleteMappingRecursively = ({
    parentId,
    id,
    mappings,
  }: DeleteMappingRecursivelyOptions): HierarchyMappingValues[] => {
    return mappings.map((mapping) => {
      if (mapping.id === parentId) {
        const filteredChildren = mapping?.children?.filter((i) => i?.id !== id);
        return { ...mapping, expanded: true, children: filteredChildren };
      }
      const tempObj = { mappings: mapping.children, parentId, id };
      return { ...mapping, children: deleteMappingRecursively(tempObj) };
    });
  };

  useEffect(() => {
    const collectMappingsError = (childMappings: MappingValues[]) => {
      let tempArr: MappingErrorTypes[] = [];
      childMappings?.forEach((childMapping) => {
        const sourceFieldType = getSourceFieldType(
          childMapping?.source,
          flattanedSourceFields
        );
        const targetFieldType = getTargetFieldType(
          childMapping?.target,
          flattanedTargetFields
        );
        if (targetFieldType !== sourceFieldType) {
          tempArr.push({ mapping: childMapping, errors: [] });
        }

        if (childMapping?.children?.length) {
          tempArr.push(...collectMappingsError(childMapping?.children));
        }
      });
      return tempArr;
    };

    setMappingErrors(collectMappingsError(mappings));
  }, [mappings, flattanedSourceFields, flattanedTargetFields]);

  useEffect(() => {
    const mappingEntries = Object.entries(fieldMapping?.mappings ?? {});
    if (!mappingEntries.length) return;

    const fieldMappingArr = Object.values(fieldMapping?.mappings ?? {});
    const tempArr: MappingValues[] = [];

    const getChildren = (parent: MappingValues): MappingValues[] => {
      const children = fieldMappingArr.filter(
        (child) => child?.parentKey === parent.source
      );

      return children.map((childField, index) => {
        const child: Partial<MappingValues> = {
          id: `${generateId()}-${index}`,
          source: childField?.source?.field,
          target: childField?.target?.field,
          parentSource: parent,
          sourceDetails: {
            type: childField?.source?.type,
          } as MappingValues["sourceDetails"],
          targetDetails: {
            dataType: { type: childField?.target?.type },
          } as MappingValues["targetDetails"],
        };
        child.children = child?.source
          ? getChildren(child as MappingValues)
          : [];
        child.expanded = !!child.children?.length;
        return child as MappingValues;
      });
    };

    for (const [_, value] of mappingEntries) {
      if (!value?.parentKey) {
        const mapping: Partial<MappingValues> = {
          id: `${generateId()}-${tempArr.length}`,
          source: value?.source?.field,
          target: value?.target?.field,
          sourceDetails: {
            type: value?.source?.type,
          } as MappingValues["sourceDetails"],
          targetDetails: {
            dataType: { type: value?.target?.type },
          } as MappingValues["targetDetails"],
        };
        mapping.children = getChildren(mapping as MappingValues);
        mapping.expanded = !!mapping.children?.length;
        tempArr.push(mapping as MappingValues);
      }
    }

    console.log(tempArr);

    setMappings(tempArr);
  }, [fieldMapping]);

  if (isLoading) return <LoadingOutlined />;

  return (
    <>
      <Header
        serviceProfile={serviceProfile}
        viewMode={viewMode}
        setViewMode={setViewMode}
      />
      <MappingContextProvider
        value={{
          mappings,
          viewMode,
          serviceProfile,
          serviceProfileSpecification:
            serviceProfileSpecification as unknown as ServiceProfileSpecifications.Root,
          sourceFields,
          flattanedSourceFields,
          flattanedTargetFields,
          targetFields,
          fieldMapping,
          mappingErrors,
          onAddMapping(parent) {
            const newMapping = {
              ...getNewDefaultMapping(),
              parentSource: parent,
            };
            if (parent?.id) {
              setMappings((prev) => {
                const tempObj = {
                  mappings: prev,
                  parentSource: parent,
                  newMapping,
                  canAddChild: true,
                };
                return addMappingRecursively(tempObj);
              });
            } else {
              setMappings((prev) => [...prev, newMapping]);
            }
          },
          onUpdateMappings(mapping, field, value) {
            setMappings((prevState) => {
              return [
                ...prevState.map((m) =>
                  m.id !== mapping?.id
                    ? m
                    : { ...m, [field]: value, expanded: false }
                ),
              ];
            });

            if (field === "source") {
              setMappings((prev) => {
                const isObject = nonPrimitiveDataTypes.includes(
                  getSourceFieldType(value, flattanedSourceFields)
                );
                const sourceDetails = flattanedSourceFields?.find(
                  (i) => i?.name === value
                ) as ServiceProfileSpecifications.Fields;

                const newMapping = {
                  ...getNewDefaultMapping(),
                  parentSource: mapping,
                };

                const tempObj = {
                  mappings: prev,
                  newMapping,
                  sourceDetails,
                  canAddChild: isObject,
                  parentSource: mapping,
                };

                return addMappingRecursively(tempObj);
              });
            } else {
              setMappings((prev) => {
                const isObject =
                  getTargetFieldType(value, flattanedTargetFields) ===
                  ServiceProfileSpecificationFieldType.Object;
                const targetDetails = flattanedTargetFields?.find(
                  (i) => i?.name === value
                );

                const tempObj = {
                  mappings: prev,
                  canAddChild: isObject,
                  targetDetails,
                  parentSource: mapping,
                };

                return addMappingRecursively({ ...tempObj, type: "target" });
              });
            }
          },
          onRemoveMapping(id, parentId) {
            if (!parentId) {
              setMappings((prev) => prev.filter((m) => m.id !== id));
            } else {
              const temObj = {
                id,
                parentId,
                mappings,
              } as DeleteMappingRecursivelyOptions;
              setMappings(() => deleteMappingRecursively(temObj));
            }
          },
          onToggleExpand(mappingId: string) {
            setMappings((prev) => expandMappingRecursively(prev, mappingId));
          },
        }}
      >
        <Mappings />
      </MappingContextProvider>
    </>
  );
};

export default FieldMapping;
