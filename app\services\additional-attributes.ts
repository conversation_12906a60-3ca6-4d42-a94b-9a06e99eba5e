import { customUnifiedFields } from "types/additional-attributes";
import fetchInstance from "utils/api/fetchinstance";

export const additionalAttributesClient = {
  getAll: () => {
    return fetchInstance.get(`/customUnifiedFields`, {
      params: { limit: 100 }
    })
  },
  create: (payload: customUnifiedFields.CreatePayload) => {
    return fetchInstance.post(`/customUnifiedFields`, payload)
  },
  delete: (id: customUnifiedFields.Root['id']) => {
    return fetchInstance.delete(`/customUnifiedFields/${id}`)
  }
};