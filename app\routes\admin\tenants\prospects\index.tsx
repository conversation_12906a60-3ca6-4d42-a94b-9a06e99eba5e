import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ell, TableRow, Typography,IconButton,Menu, MenuItem, } from "@mui/material";
import ExpandedTable from "components/@extended/Table/expanded-table";
import { useGetSuperAdmin } from "hooks/api/super-admin/useGetSuperAdmin";
import { useTable } from "hooks/table/useTable";
import { useDate } from "hooks/useDate";
import { EllipsisOutlined,PlusOutlined, CloseOutlined } from "@ant-design/icons";
import { useNavigate } from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";


const Prospects = () => {
   const navigate = useNavigate();
   const [paginationState, setPaginationState] = useState<any>({
      pageIndex: 0,
      pageSize: 10,
   })


   const { loadDate } = useDate(),
      {
         extendedProps,
         paginationModal: { pagination, onPaginationChange, setTotal }
      } = useTable();

   const { searchProspects } = useGetSuperAdmin(),
      { data, isFetching } = searchProspects({ pagination: paginationState });

   const prospects = data?.data;
   const total = data?.pagination?.total;


   const ActionsColumn = ({ record, getItems }: any) => {
      const [anchorEl, setAnchorEl] = useState(null);
      const open = Boolean(anchorEl);

      const handleClick = (event:any) => {
         event.stopPropagation();
         setAnchorEl(event.currentTarget);
      };

      const handleClose = () => {
         setAnchorEl(null);
      };

      const items = getItems(record);

      return (
         <div>
            <IconButton onClick={handleClick}>
               <EllipsisOutlined />
            </IconButton>
            <Menu
               anchorEl={anchorEl}
               open={open}
               onClose={handleClose}
               onClick={(e) => e.stopPropagation()}
            >
               {items.map((item:any, index:number) => (
                  <MenuItem
                     key={index}
                     {...item}
                     onClick={() => {
                        item.onClick();
                        handleClose();
                     }}
                  >
                      {item.icon && <span style={{ marginRight: 8 }}>{item.icon}</span>}
                     {item.label}
                  </MenuItem>
               ))}
            </Menu>
         </div>
      );
   };

   const getItems = (record: any) => [
      {
         label: 'Create',
         icon: <PlusOutlined />,
         onClick: () => (
            void navigate('/admin/tenants/create')
         ),
        
      },
      {
         label: 'Reject',
         icon: <CloseOutlined />,
         disabled: true,
      },
   ];

   useEffect(() => {
      setTotal(total);
   }, [total])

   const columns: any = useMemo(
      () => [
         {
            accessorKey: 'name',
            header: 'Organization',
         },
         {
            accessorKey: 'contact.firstName', //normal accessorKey
            header: 'Point of Contact (POC)',
         },
         {
            accessorKey: 'size', //normal accessorKey
            header: 'Organization Size',
         },
         {
            accessorKey: 'date',
            header: 'Updated Date',
            cell({ row: { original } }: any) {
               return loadDate(original?.changeLog?.lastUpdatedDateTime)
            },
         },
         {
            title: 'Actions',
            header: 'Actions',
            cell({ row: { original } }: any) {
               return <ActionsColumn record={original} getItems={getItems} />
            }
         },

      ],
      [],
   );

   return (
      <>
         <ExpandedTable
            data={prospects ?? []}
            columns={columns}
            {...{
               getRowCanExpand: () => true,
               renderSubRow({ original }) {
                  return (
                     <TableRow>
                        <TableCell colSpan={5} >
                           <Details row={original} />
                        </TableCell>
                     </TableRow>
                  )
               },
               totalData: pagination?.total,
               onPaginationChange: setPaginationState,
               state: {
                  pagination: {
                     pageIndex: paginationState.pageIndex,
                     pageSize: paginationState?.pageSize,
                  }
               } as any
            }}
         />
      </>
   )
}

const Details = ({ row }: any) => {
   const original = row;

   const preference = original?.integrationPreferences ?? 'No preferences'
   return (
      <Grid container >
         <Grid item xs={4}>
            <Typography color='green' variant="h5" >Customer Preference</Typography>
            <Typography>{preference}</Typography>
         </Grid>
         <Grid item xs={6}>
            <Stack gap={2} className="w-full ">
               <Typography variant="h5" >Additional Info</Typography>

               <Grid container spacing={12} >
                  <Grid item xs={4}>
                     <Stack gap={1}>
                        <Typography>Email:</Typography>
                        <Typography className="link">{original?.contact?.emails?.[0]?.email}</Typography>
                     </Stack>
                  </Grid>
                  <Grid item xs={4}>
                     <Stack gap={1}>
                        <Typography>Phone:</Typography>
                        <Typography className="link">{original?.contact?.phones?.[0]?.number}</Typography>
                     </Stack>
                  </Grid>
                  <Grid item xs={4}>
                     <Stack gap={1}>
                        <Typography>Organization Size:</Typography>
                        <Typography>{original?.size}</Typography>
                     </Stack>
                  </Grid>
               </Grid>
               <Grid container mt={2} columnSpacing={12}>
                  <Grid item xs={6}>
                     <Stack gap={1}>
                        <Typography>Address:</Typography>
                        <address>
                           {Object.keys(original?.address).length ? (
                              Object.values(original?.address).join(', ')
                           ) : '-'}
                        </address>
                     </Stack>
                  </Grid>

                  <Grid item xs={6}>
                     <Stack gap={1}>
                        <Typography>Region:</Typography>
                        <Typography>{'North America'}</Typography>
                     </Stack>
                  </Grid>
               </Grid>
            </Stack>
         </Grid>
      </Grid>
   )
}

export default Prospects;