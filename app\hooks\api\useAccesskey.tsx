import { useMutation } from "@tanstack/react-query";
import { accesskeyClient } from "services/access-key.service";
import Cookies from 'js-cookie'

import { API_ENDPOINTS } from "utils/api/api-endpoints";
import moment from "moment";
import { useEffect } from "react";
import useInitialSetup from "store/initial-setup";

export type SPQueryOptionsType = {
   offset?: number;
   limit?: number;
   orderBy?: string;
};
const KEY_PREFIX = 'ACCESS_KEY_';
const KEY = `${KEY_PREFIX}${window.authUserOrgId}`;

const EXPIRES_AT_KEY = `${KEY}_expires`;
const CREATED_AT_KEY = `${KEY}_created`;

export const useAccesskey = () => {

   const {

      
      mutateAsync: generateKey,
      isPending,
   } = useMutation({
      mutationFn: accesskeyClient.createAccessKey,
      mutationKey: [API_ENDPOINTS.ACCESS_KEY]
   }),
      { setValues } = useInitialSetup()

   const generate = async () => {
      const resp = await generateKey({ type: 'GENERATE_REQUEST' });
      const data = resp?.data?.data?.[0];

      Cookies.set(KEY, data?.key, { expires: 5 / 1440 })
      Cookies.set(EXPIRES_AT_KEY, moment(data?.expirationDateTime).format('lll'), { expires: 5 / 1440 })
      Cookies.set(CREATED_AT_KEY, moment(data?.changeLog?.lastUpdatedDateTime).format('lll'), { expires: 5 / 1440 })
   };

   const accessKey: any = Cookies.get(KEY);

   useEffect(() => {
      setValues('accessKey', accessKey)
   }, [accessKey])

   return {
      accessKey,
      expiresAt: Cookies.get(EXPIRES_AT_KEY),
      createdAt: Cookies.get(CREATED_AT_KEY),
      isPending,
      generate,
   }
};
