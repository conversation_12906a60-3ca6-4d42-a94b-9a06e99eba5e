import { Badge, Box, Chip, ChipProps, Grid, GridProps, Radio, RadioGroup, Stack, Typography } from "@mui/material"
import MainCard from "components/MainCard"
import { useCallback, useMemo, useState } from "react"
import { DISABLED_STATE_STYLE } from "../constant"
import BoxSelect from "components/box-select"

type Props = {
   versions: any[]
   onSelect?: (selectedVersion: any) => void
   selected: any[]
   mode: 'edit' | 'view'
} & {
   gridProp?: GridProps
}


const GRID_VALUE_S: GridProps = {
   xs: 12,
   md: 6,
   lg: 3,
   xl: 2,
}

export default ({
   versions,
   onSelect: onSelectProp,
   selected: selectedProp,
   gridProp,
   mode
}: Props) => {

   const [localSelect, setLocalSelect] = useState<any[]>([]);

   const selected = selectedProp ?? localSelect;

   const onSelect = (updated: (string | number)[]) => {
      setLocalSelect(updated)
      typeof onSelectProp === 'function' && (
         onSelectProp(updated)
      )
   }

   const getSxBeta = useCallback(({ isSelected }: Record<string, any>): ChipProps => {
      if (mode === 'view') {
         // if it is not selected
         if (!isSelected) {
            return { color: 'secondary' }
         }
      };
      return {};
   }, [mode])

   return (
      <BoxSelect
         isMulti
         items={(
            versions.map((version) => {
               return {
                  title: version?.title,
                  value: version?.value,
                  ...version
               }
            })
         )}
         renderItem={(version) => {
            const { meta }: any = version;
            const isSelected = selected.includes(version?.value);
            return (
               <Stack>
                  <Stack mt={.6}  direction={'row'} gap={1} alignItems={'center'}>
                     <Typography variant='h6' className="font-semibold"  >{version?.title}</Typography>
                     {meta?.isBeta && (
                        <Chip
                           variant={('light') as any}
                           size="small"
                           label='Beta'
                           color={isSelected ? "primary" : "info"}
                           sx={{ fontSize: 12 }}
                           {...getSxBeta({ isSelected: true })}
                        />
                     )}
                  </Stack>
               </Stack>
            )
         }}
         gridItemProps={{
            ...GRID_VALUE_S,
            ...gridProp
         }}
         onSelectItems={onSelect}
         disableBehaviorForUnselectItem={mode === 'view'}
         disabled={mode === 'view'}
         selectedItems={selected}
      />
   )
}