import { useEffect, useMemo } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { FormControlLabel, MenuItem, Select, Stack, Switch, TextField } from "@mui/material";
import { Form, FormField, FormItem, FormControl } from "components/@extended/Form";
import { useForm } from "react-hook-form";
import { z } from "zod"
import useLogProtection from "store/security/log-protection";
import { useIntegrationCreateProvider } from "../../../../../integration-create-provider";
import { State } from "hooks/useStatus";

// Validation schema for API configuration
const schema = z.object({
   // URL: Must be a valid, non-empty URL
   url: z
      .string()
      .nonempty("URL is required.")
      .url({ message: "Please enter a valid URL." }),

   // SSL Requirement: Must be 'true' to ensure secure connection
   securedSSLRequired: z
      .boolean(),

   // Content Type: Must match 'application/json'
   contentType: z
      .string()
      .nonempty("Content type is required.")
      .refine((val) => val === "application/json", {
         message: "Content type must be 'application/json'.",
      }),

   // API Key: Must be provided and in valid UUID format
   apiKey: z
      .string()
      .nonempty("API key is required.")
});

type FormValues = z.infer<typeof schema>;

const contentTypeOptions = [
   { value: 'application/json', label: 'application/json' },
];

const defaultValues: FormValues = {
   url: '',
   securedSSLRequired: true,
   contentType: 'application/json',
   apiKey: '',
}

export default () => {

   const { values, mode, setValues, move, setWindowStates } = useLogProtection();
   const { actionRefs } = useIntegrationCreateProvider();

   const form = useForm<FormValues>({
      resolver: zodResolver(schema),
      defaultValues,
      mode: 'onChange',
   });

   const isEditMode = useMemo(() => mode === 'edit', [mode]);

   const onSubmit = (updatedValues: FormValues) => {
      setValues('integrationDetails', updatedValues);
      move('next');
   };

   useEffect(() => {
      form.reset(values?.integrationDetails);
   }, [isEditMode, form, values?.integrationDetails]);

   useEffect(() => {
      setWindowStates('integrationDetails', form.formState.isValid ? State.ACTIVE : State.INACTIVE)
   }, [form.formState.isValid])

   return (
      <>
         <Form {...form}>
            <Stack
               component={'form'}
               onSubmit={(...args) => (
                  void form.handleSubmit(onSubmit)(...args)
               )}
               ref={actionRefs?.integrationDetails}
               gap={3}
            >

               <FormField
                  control={form.control}
                  name='url'
                  render={({ field }) => (
                     <FormItem label='URL' description='Lorem ipsum dolor sit amet consectetur adipisicing elit.​​'>
                        <FormControl>
                           <TextField placeholder='https://example.com' {...field} />
                        </FormControl>
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name='securedSSLRequired'
                  render={({ field }) => (
                     <FormItem layout="horizontal">
                        <FormControlLabel
                           label='Secured SSL' sx={{ width: 'fit-content' }}
                           control={(
                              <Switch  {...field} checked={!!field?.value} />
                           )}
                        />
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name='contentType'
                  render={({ field }) => (
                     <FormItem label='Content type' description={`Lorem ipsum dolor sit amet consectetur adipisicing elit.​`}>
                        <FormControl>
                           <Select label="Category" {...field} >
                              {contentTypeOptions.map((domain) => (
                                 <MenuItem value={domain?.value} key={domain?.value}  >{domain?.label}</MenuItem>
                              ))}
                           </Select>
                        </FormControl>
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name='apiKey'
                  render={({ field }) => (
                     <FormItem label='API Key' description='Lorem ipsum dolor sit amet consectetur adipisicing elit.​​​'>
                        <FormControl>
                           <TextField placeholder='Eg: bbcded67-6c98-4cd9-bc4e-35b833781c2f' {...field} />
                        </FormControl>
                     </FormItem>
                  )}
               />
            </Stack>
         </Form>
      </>
   )
}