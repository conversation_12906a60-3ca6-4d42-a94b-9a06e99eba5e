/* Enhanced Array-Object Mapping Styles */

.array-item-container {
  position: relative;
  border-left: 3px solid transparent;
  padding-left: 12px;
  margin-left: 8px;
}

.array-item-container.item-type {
  border-left-color: #8b5cf6;
  background: linear-gradient(90deg, rgba(139, 92, 246, 0.02) 0%, transparent 50%);
}

.array-item-container.indexed-type {
  border-left-color: #f59e0b;
  background: linear-gradient(90deg, rgba(245, 158, 11, 0.02) 0%, transparent 50%);
}

.array-item-container.dynamic-type {
  border-left-color: #ec4899;
  background: linear-gradient(90deg, rgba(236, 72, 153, 0.02) 0%, transparent 50%);
}

.array-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.7rem;
  font-weight: 600;
  margin-left: 6px;
  min-width: 32px;
  justify-content: center;
}

.array-badge.item {
  background-color: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.array-badge.indexed {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.array-badge.dynamic {
  background-color: rgba(236, 72, 153, 0.1);
  color: #ec4899;
  border: 1px solid rgba(236, 72, 153, 0.2);
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 4px rgba(236, 72, 153, 0.3); }
  50% { box-shadow: 0 0 8px rgba(236, 72, 153, 0.5); }
}

.array-mapping-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  margin: 16px 0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.75rem;
  color: #64748b;
}

.legend-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.array-context-info {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 6px;
  font-size: 0.75rem;
  line-height: 1.4;
}

.array-context-info .info-title {
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 4px;
}

.array-context-info .info-items {
  margin: 0;
  padding-left: 16px;
}

.array-context-info .info-items li {
  margin: 2px 0;
  color: #475569;
}

.array-context-info .info-items strong {
  color: #1e40af;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Hover effects */
.array-item-container:hover {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
  border-radius: 4px;
}

.array-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* Responsive design */
@media (max-width: 768px) {
  .array-mapping-legend {
    flex-direction: column;
    gap: 8px;
  }
  
  .legend-item {
    font-size: 0.7rem;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .array-mapping-legend {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: #475569;
  }
  
  .legend-item {
    color: #94a3b8;
  }
  
  .array-context-info {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
  }
  
  .array-context-info .info-title {
    color: #60a5fa;
  }
  
  .array-context-info .info-items li {
    color: #cbd5e1;
  }
  
  .array-context-info .info-items strong {
    color: #60a5fa;
  }
}