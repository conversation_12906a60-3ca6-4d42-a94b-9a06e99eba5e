import { Tooltip } from "@mui/material";
import _ from "lodash";
import Image, { ImageProps } from "remix-image"
import { DOMAINS } from "data/domains";

type DeriveImageOptions = {
   size?: 'xSmall' | 'small' | 'medium' | 'large' | 'large',
} & Partial<ImageProps>

export enum FormFieldTypeEnum {
   Text = 'TEXT',
   File = 'FILE',
   List = 'LIST'
}

export enum ValidationType {
   Regex = 'regex'
}

const validOrgIds = [
   "008470a1-8122-40f6-9962-3f4167647f99",
   "c1b3c3f1-b32f-4b1c-be88-6501231df738"
 ];

// Export DOMAIN_S from centralized data
export const DOMAIN_S = DOMAINS.map(domain => ({
   ...domain,
   // Convert icon component to string name for backward compatibility
   icon: domain.icon.displayName?.toLowerCase().replace(/([A-Z])/g, '-$1').replace(/^-/, '') || 'layers'
}));

const IMAGE_WIDTH: any = {
   "xSmall": "max-w-[1.6rem]",
   "small": 'max-w-[2.2rem]',
   "medium": "max-w-[3.4rem]",
   "large": ""
}



export const useServiceProfile = () => {

   return {
      loadImage: (serviceProfile: Record<string, any>, options?: DeriveImageOptions) => {
         const size = options?.size ?? 'xSmall',
            image = serviceProfile?.image?.[size],
            name = serviceProfile?.name;
         return image ? (
            <Tooltip
               title={name}
               placement='top'
            >
               <Image
                  src={image}
                  alt={name}
                  className={IMAGE_WIDTH?.[size]}
                  {...options}
               />
            </Tooltip>
         ) : name
      },
      getAllDomains: () => DOMAIN_S,
      getDomainName: (domainType: any) => DOMAIN_S?.find(({ key }) => {
         return domainType === key
      })?.label,
      getColor: (key: string): string | undefined => {
         return _.find(DOMAIN_S, (i) => key === i.key)?.color;
      },
      getLabel: (key: string): string | undefined => {
         return _.find(DOMAIN_S, (i) => key === i.key)?.label;
      },
      getTotalServiceProfileCount: (key: string): number => {
         return _.find(DOMAIN_S, (i) => key === i.key)?.totalServiceProfile ?? 0;
      },
      getDomainUrl: (key: string): string | undefined => {
         return _.find(DOMAIN_S, (i) => key === i.key)?.url;
      }
   }
}