import { <PERSON>, Stack, Switch, Tooltip, Typography, useTheme, Chip } from "@mui/material";
import { DownOutlined, ExclamationCircleOutlined, LockFilled, RightOutlined } from "@ant-design/icons";

import MainCard from "components/MainCard";

import { useServiceProfile } from "hooks/useServiceProfile";
import { ServiceState } from "hooks/api/service-profile/useGetServiceProfile";

import useServiceConfig from "store/setup-service";
import useManageProfile from "../service-context/use-manage-profile";
import { useMemo } from "react";


export default ({ profiles = [], createService, updateService, onSelect }: any) => {

   const {
      selectedService,
   } = useServiceConfig(),
      { loadImage } = useServiceProfile();

   const theme = useTheme();

   const {
      isCategoryDisabled,
      selectedIndex
   } = useManageProfile();

   const highlightStyles: any = useMemo(() => {
      return {
         boxShadow: theme?.shadows?.[1]
      }
   }, [])



   return (
      <>
         {profiles.map((item: any, index: number) => {

            const { name, id } = item;
            const active = [ServiceState.Active, ServiceState.InProgress].includes(item?.service?.state),
               service = item?.service ?? null

            const toggle = (id: string) => {
               if (!service) {
                  createService({ serviceProfile: { id } })
               } else {
                  updateService(item)
               }
            }
            const accessPointsSummary = service?.accessPointsSummary;
            const isOpened = selectedService?.id === item?.id;
            const testId = `service-card-${name?.toLowerCase().replace(/\s/g, '-')}`;

            return (
               <MainCard
               data-testid={testId}
                  onClick={() => (
                     onSelect(index, item)
                  )}
                  sx={{
                     "&:hover": { cursor: 'pointer', ...highlightStyles },
                     boxShadow: index === selectedIndex ? highlightStyles?.boxShadow : 'none'
                  }}
                  key={index}
               >
                  {
                     item?.isBeta &&
                     <Box sx={{
                        position: 'absolute',
                        top: '-2px',
                        right: '0px',
                        zIndex: 1
                     }}>
                        <Chip
                           variant={('light') as any}
                           size="small"
                           label='Beta'
                           color={"info"}
                           sx={{
                              height: '18px',
                              borderRadius: "0px !important", // Added !important to override
                              '& .MuiChip-root': {
                                borderRadius: "0px !important", // Target the root element
                              },
                              '& .MuiChip-label': {
                                fontSize: '11px',
                                lineHeight: 1.2,
                                borderRadius: "0px !important", // Added !important
                                padding: "0 8px", // Optional: adjust padding if needed
                              }
                            }}
                        />
                     </Box>
                  }

                  <Stack
                     direction={'row'} justifyContent={'space-between'} >
                     <Stack direction={'row'} gap={2} alignItems={'center'}>
                        <div className="flex-shrink-0" >
                           {loadImage(item, { size: 'small' })}
                        </div>
                        <Stack>
                           <Typography className="font-semibold select-none" >{name}</Typography>
                           {accessPointsSummary?.requiresAction ? (
                              <Stack direction={'row'} alignItems={'center'} className="">
                                 <ExclamationCircleOutlined className="h-3 w-3 text-[10px]" style={{ color: theme.palette.error.main }} />
                                 <Typography color={'secondary.600'} noWrap variant='caption' className="text-[10px] ml-1 ">
                                    {`${accessPointsSummary?.readyForConfiguration} `}pending action item</Typography>
                              </Stack>
                           ) : null}

                        </Stack>
                     </Stack>

                     {/* card action block */}
                     <Stack direction={'row'} gap={2} color={'secondary.600'}>
                        {isCategoryDisabled ? (
                           <Tooltip title='Your plan does not support this service' placement="top">
                              <LockFilled style={{ fontSize: 16 }} />
                           </Tooltip>
                        ) : (
                           <Switch
                              data-testid={`${testId}-toggle`}
                              checked={active}
                              onClick={(e) => void e.stopPropagation()}
                              onChange={() => void toggle(id)}
                           />
                        )}

                        {isOpened ? (
                           <DownOutlined className="scale-[.9]" />
                        ) : (
                           <RightOutlined className="scale-[.9]" />
                        )}
                     </Stack>
                     {/* card action block */}

                  </Stack>
               </MainCard>
            )
         })}
      </>
   )
}