import { UploadOutlined } from '@ant-design/icons';
import  Button, {  ButtonProps } from '@mui/material/Button';
import RcUpload, { UploadProps as DefaultUploadProps } from 'rc-upload';

export type ExtendedFile = File & { uid: string }
export type UploadProps = {
   fileList?: ExtendedFile[]
   buttonProps?: ButtonProps
} & DefaultUploadProps

export const Upload = ({ children, buttonProps, ...props }: UploadProps) => {

   const handleUploadSuccess = (response: Record<string, any>) => {
      console.log('Upload Success:', response);
   };

   const handleUploadError = (error: Record<string, any>) => {
      console.error('Upload Error:', error);
   };

   const uploaderProps: UploadProps = {
      onSuccess(response, file) {
         handleUploadSuccess(response);
         console.log('onSuccess', file.name, response);
      },
      onError(error, response, file) {
         handleUploadError(error);
         console.error('onError', file.name, error);
      },
      onProgress({ percent }, file) {
         console.log('onProgress', file.name, `${percent}%`);
      },
      ...props,
   };

   return (
      <RcUpload {...uploaderProps}>
         <Button
            variant='contained'
            // className='bg-gray-200 hover:bg-gray-300'
            startIcon={<UploadOutlined />}
            color='secondary'
            {...buttonProps}
            // sx={{ color: "secondary.600" }}
         >
            {children ? children : null}
         </Button>
      </RcUpload>
   );
};
