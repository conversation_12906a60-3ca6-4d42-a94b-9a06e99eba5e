import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>, DialogContent, DialogTitle, Grid, IconButton, ListItem, ListItemAvatar, ListItemText, Skeleton, Stack, Tooltip, Typography, useMediaQuery, Alert } from "@mui/material";
import loadable from '@loadable/component';

import MainCard from "components/MainCard";
import useLogProtection from "store/security/log-protection";

import { useDate } from "hooks/useDate";
import { useServiceProfile } from "hooks/useServiceProfile";
import { DeleteOutlined } from "@ant-design/icons";
import { useIntegrationCreateProvider } from "../integration-create-provider";

import customIntegration from "./create/step-contents/select-service/custom-integration-options";
import { LogProtectionConnectorType } from "constants/log-protection";
import { useEffect, useMemo, useState } from "react";
import { Dialog, DialogClose } from "components/@extended/dialog";
import Empty from "components/@extended/List/Empty";
import { List } from "components/@extended/List";


const CreateIntegration = loadable(() => import('./create'), {
   fallback: <div>Loading...</div>, // Custom loading UI
});

const TITLE = 'Bring Your Own Logger (BYOL)​';
const DESCRIPTION = (
   'Already have an existing logging pipeline or observability platform? You can choose to export logs directly to your own system (e.g., Datadog, Splunk, or custom services).​ This option is best for teams with strict logging policies or centralized observability requirements.')

const CREATE_BTN_TEXT = 'Create BYOL connection​'

const WARN_MESSAGE = 'If you opt for BYOL, Unizo will not provide continuous visibility, health monitoring, or diagnostics for your integrations. You’ll be responsible for managing and analyzing your own logs.​'

export default () => {

   const downMd = useMediaQuery((theme: any) => theme.breakpoints.down('md'));

   const {
      reset,
      setOpenCreate,
      openCreate
   } = useLogProtection();

   const {
      getIsWebhookConfigExist,
      searchLogProtections,
      attemptDeleteIntegration,
   } = useIntegrationCreateProvider();

   const { loadImage } = useServiceProfile();

   const { data: logProtection, isPending } = searchLogProtections()

   const [open, setOpen] = useState(false);
   const [selected, setSelected] = useState<Record<string, any> | null>(null);
   const [selectedIndex, setSelectedIndex] = useState<number | null>(null);


   const onOpen = () => {
      setOpenCreate(true);
   }

   const onClose = () => {
      setOpenCreate(false);

      // make sure to clean all the log protection state
      reset();
   }

   const onDeleteRequest = (newSelected: Record<string, any>, newSelectedIndex: number) => {
      setOpen(true)
      setSelected(newSelected);
      setSelectedIndex(newSelectedIndex)
   }

   const onDeleteCancel = () => {
      setOpen(false)
      setSelected(null)
      setSelectedIndex(null)
   }

   const webHook = getIsWebhookConfigExist()?.webhookConfig;

   const onDelete = () => {
      attemptDeleteIntegration(logProtection?.id, [
         {
            op: "remove",
            path: `/connectorTypeConfigs/${selectedIndex}`,
            value: [
               selected
            ]
         }
      ],
         () => {
            onDeleteCancel()
         })

   }

   const staticOptions = useMemo(() => {
      return webHook ? customIntegration?.find((i) => i?.type === LogProtectionConnectorType.Webhook) : null
   }, [webHook]);

   const connectorTypeConfigs = useMemo(() => {
      return logProtection?.connectorTypeConfigs ?? []
   }, [logProtection?.connectorTypeConfigs])

   return (
      <MainCard>
         <Grid
            container
            alignItems={'center'}
            spacing={downMd ? 1 : 0}
         >
            <Grid item xs={12} md={8} >
               <Stack gap={2} alignItems={'start'} >
                  <Typography variant="h5" className="truncate">
                     {TITLE}
                  </Typography>
                  <Typography color={'secondary.600'} mt={1} sx={{ textAlign: 'justify' }} >
                     {DESCRIPTION}
                  </Typography>
               </Stack>
            </Grid>
            <Grid
               item
               xs={12}
               md={4}
               sx={{
                  textAlign: !downMd ? 'end' : 'start',
                  width: '100%'
               }}
            >
               <Button
                  variant='contained'
                  onClick={onOpen}
               >
                  {CREATE_BTN_TEXT}
               </Button>
            </Grid>
         </Grid>
         <Grid
            container
            alignItems={'center'}
            spacing={1}
            marginTop="1rem"
            marginBottom='1rem'
         >
            <Grid item xs={12} md={12} >
               <Alert severity="error" sx={{ textAlign: 'justify', width: 'fit-content' }}>
                  {WARN_MESSAGE}
               </Alert>
            </Grid>
         </Grid>
         <List
            sx={{ mt: 2 }}
            bordered={true}
         >
            {(isPending) ? (
               Array.from({ length: 3 }, () => {
                  return (
                     <ListItem button key={Math.random()}>
                        <ListItemAvatar sx={{ mr: 2 }}>
                           <Skeleton variant="circular" height={30} />
                        </ListItemAvatar>
                        <ListItemText
                           primary={(
                              <Skeleton width={'40%'} height={20} />
                           )}
                           sx={{ gap: 2 }}
                           secondary={(
                              <Skeleton width={'20%'} height={20} />
                           )}
                        />
                     </ListItem>
                  )
               })
            ) : (
               connectorTypeConfigs?.length ? (
                  connectorTypeConfigs?.map((item: Record<string, any>, index: number) => {
                     if (item?.type === LogProtectionConnectorType.Webhook) {
                        return (
                           <ListItem
                              button key={index}
                           >
                              <ListItemAvatar>
                                 {staticOptions && loadImage(staticOptions?.serviceProfile, { size: 'small' })}
                              </ListItemAvatar>
                              <ListItemText
                                 primary={(
                                    webHook?.url && (
                                       <Typography variant="subtitle1" color="text.primary">
                                          {webHook?.url}
                                       </Typography>
                                    )
                                 )}
                                 secondary={webHook?.contentType}
                              />
                              <Tooltip title={`Delete Webhook`}>
                                 <IconButton
                                    onClick={() => (
                                       onDeleteRequest(getIsWebhookConfigExist(), index)
                                    )}
                                    color="error" >
                                    <DeleteOutlined />
                                 </IconButton>
                              </Tooltip>
                           </ListItem>
                        )
                     }

                     return (
                        <ListItemComp
                           onDeleteRequest={onDeleteRequest}
                           key={index}
                           index={index}
                           item={item}
                        />
                     )
                  })
               ) : (
                  <Empty
                     title="No BYOL Connections Found​"
                     description="Your BYOL connections will show-up here​."
                  />
               )
            )}
         </List>

         <CreateIntegration
            open={openCreate}
            onClose={onClose}
         />

         <DeleteConfirmation
            open={open}
            onClose={onDeleteCancel}
            onConfirm={onDelete}
         />
      </MainCard>
   )
}

type Props = {
   onDeleteRequest: any
   item: any
   index: any
}

const ListItemComp = ({
   onDeleteRequest,
   item,
   index,
}: Props) => {

   const { loadDate } = useDate();
   const { loadImage } = useServiceProfile();

   const { getIntegration, searchLogProtections } = useIntegrationCreateProvider();

   const { data } = searchLogProtections()

   const { data: integration = {} } = getIntegration({
      logProtectionId: data?.id,
      id: item?.integration?.id as string
   })

   return (
      <ListItem
         button
      >
         <ListItemAvatar>
            {integration?.serviceProfile && (
               loadImage(integration?.serviceProfile, { size: 'small' })
            )}
         </ListItemAvatar>
         <ListItemText
            primary={(
               integration?.name && (
                  <Typography variant="subtitle1" color="text.primary">
                     {integration?.name}
                  </Typography>
               )
            )}
            secondary={loadDate(integration?.changeLog?.lastUpdatedDateTime)}
         />
         <Tooltip title={`Delete ${integration?.name}`} placement="top">
            <IconButton color="error"
               onClick={() => onDeleteRequest(item, index)}
            >
               <DeleteOutlined />
            </IconButton>
         </Tooltip>
      </ListItem>
   )
}

const DeleteConfirmation = ({ onConfirm, onClose, ...props }: any) => {
   return (
      <Dialog maxWidth={'xs'} {...props} onClose={onClose}>
         <DialogTitle>Delete Integration</DialogTitle>
         <DialogClose onClose={onClose} />
         <DialogContent dividers>
            Are you sure you want to delete this integration?
         </DialogContent>
         <DialogActions>
            <Button onClick={onClose} color="primary">
               Cancel
            </Button>
            <Button onClick={onConfirm} variant="contained" color="error">
               Delete
            </Button>
         </DialogActions>
      </Dialog>
   )
}