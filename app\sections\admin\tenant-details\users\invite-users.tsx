import { UserOutlined } from "@ant-design/icons";
import { useParams } from "@remix-run/react";

import { zodResolver } from "@hookform/resolvers/zod";
import { InputAdornment, ModalProps, Select, Stack, TextField, Typography, Button, MenuItem, DialogContent, DialogTitle, DialogActions, Grid, } from "@mui/material";
import { Form, FormControl, FormField, FormItem } from "components/@extended/Form";
import { UserRoleEnum, useGetPermission } from "hooks/api/permission/usePermission";
import { useGetUser } from "hooks/api/user/useUser";
import { useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useGetSuperAdmin } from "hooks/api/super-admin/useGetSuperAdmin";
import { Dialog, DialogClose } from "components/@extended/dialog";

const FormScheme = z.object({
   email: z.string().nonempty('Email is required').email({ message: "Invalid email address" }),
   role: z.enum([UserRoleEnum.ORG_USER, UserRoleEnum.ORG_ADMIN, UserRoleEnum.ORG_OBSERVER]) // Add the desired role options in the enum array
});

type InviteUserFormProps = {
   isEditMode: boolean
   selected: Record<string, any> | null | undefined
} & Omit<ModalProps, 'children'>;

type FormValues = z.infer<typeof FormScheme>

const defaultValues: Partial<FormValues> = {
   email: '',
   role: UserRoleEnum.ORG_ADMIN
}

export const InviteUser = ({
   open,
   isEditMode,
   onClose: onCloseProp,
   selected
}: InviteUserFormProps) => {

   const { id } = useParams(),
      formRef = useRef<HTMLFormElement>(null);

   const { getAllRoles } = useGetPermission(),
      { inviteUsers: attemptBulkUserInvite } = useGetSuperAdmin(),
      { attemptBulkUserRoleEdit } = useGetUser({ orgId: id as string })
   const roles = getAllRoles();

   const onClose: ModalProps['onClose'] = (e, reason) => {
      form.reset({});
      onCloseProp && onCloseProp(e, reason);
   }

   const form = useForm<FormValues>({
      resolver: zodResolver(FormScheme),
      defaultValues,
      mode: "onSubmit",
   })

   const onSubmit = (e: FormValues) => {
      if (!isEditMode && !selected) {
         attemptBulkUserInvite({ data: [{ ...e, role: { type: e?.role } }] }, id as string, () => {
            onClose({}, 'backdropClick')
         });
      } else {
         attemptBulkUserRoleEdit({
            id: selected?.id,
            payload: [
               {
                  "op": "replace",
                  "path": "/role/type",
                  "value": e.role
               }
            ]
         }, () => {
            onClose({}, 'backdropClick')
         });
      }
   }

   useEffect(() => {
      if (isEditMode) {
         form.reset({
            email: selected?.email,
            role: selected?.role?.type as UserRoleEnum
         })
      } else {
         form.reset(defaultValues)
      }
   }, [isEditMode, open, selected?.id])

   return (
      <Dialog open={open} onClose={onClose} maxWidth='sm'>
         <DialogTitle>
            <Stack className='gap-1'>
               <Typography variant='h5'>
                  {!isEditMode ? 'Add users to organization' : 'Re-assign role'}
               </Typography>
            </Stack>
         </DialogTitle>
         <DialogClose onClose={onClose} />
         <DialogContent dividers sx={{ minWidth: 500 }}>
            <Stack gap={2}>
               <Form {...form}>
                  <form
                     ref={formRef}
                     onSubmit={(...args) => void form.handleSubmit(onSubmit)(...args)}
                     className="flex flex-col gap-5"
                  >
                     <Stack direction={'column'} gap={2} className="w-full">
                        <FormField
                           control={form.control}
                           name='email'
                           render={({ field }) => (
                              <FormItem className="w-full" description="Invite new users to this organization" >
                                 <FormControl
                                 >
                                    <TextField
                                       disabled={isEditMode}
                                       InputProps={{
                                          startAdornment: (
                                             <>
                                                <InputAdornment position="start">
                                                   <UserOutlined />
                                                </InputAdornment>
                                             </>
                                          ),
                                       }}
                                       fullWidth placeholder="Enter Email"{...field} />
                                 </FormControl>
                              </FormItem>
                           )}
                        />

                        <Grid container>
                           <Grid item xs={12} lg={8}>
                              <FormField
                                 control={form.control}
                                 name='role'
                                 render={({ field }) => (
                                    <FormItem description="New users will join as:" >
                                       <FormControl>
                                          <Select label="role" {...field} >
                                             {roles?.map((role) => (
                                                <MenuItem
                                                   disabled={role?.value === selected?.role?.type}
                                                   value={role?.value}
                                                   key={role?.value}
                                                >
                                                   {role?.label}
                                                </MenuItem>
                                             ))}
                                          </Select>
                                       </FormControl>
                                    </FormItem>
                                 )}
                              />
                           </Grid>
                        </Grid>
                     </Stack>
                  </form>
               </Form>
            </Stack>
         </DialogContent>
         <DialogActions>
            <Button
               onClick={() => (
                  void formRef.current?.requestSubmit()
               )}
               variant='contained'
               color='primary' type="submit"
            >Send invite</Button>
         </DialogActions>
      </Dialog>
   )
}