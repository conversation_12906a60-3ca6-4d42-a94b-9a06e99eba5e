import React from "react";
import { Box } from "@mui/material";
import AdditionalFields from "./AdditionalFields";

interface CategoryFieldManagerProps {
  category: string;
  categoryLabel: string;
  fieldCount?: number;
  onClose?: () => void;
  initialModel?: string | null;
}

export default function CategoryFieldManager({
  category,
  categoryLabel,
  initialModel,
}: CategoryFieldManagerProps) {
  return (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      {/* Direct content without header and tabs */}
      <AdditionalFields category={category} categoryLabel={categoryLabel} initialModel={initialModel} />
    </Box>
  );
}
