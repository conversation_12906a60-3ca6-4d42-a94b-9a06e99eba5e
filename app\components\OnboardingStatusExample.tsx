import React from 'react';
import { <PERSON>, <PERSON>, CardContent, Typo<PERSON>, <PERSON>ack, Chip, Button, CircularProgress } from '@mui/material';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import useOnboardingStatus from 'hooks/useOnboardingStatus';
import useOnboardingStore from 'store/onboarding';
import { OnboardingStepType } from 'types/tenant-metadata';

/**
 * Example component showing how to use the useOnboardingStatus hook
 * This component displays the current onboarding status fetched from the backend
 */
export const OnboardingStatusExample = () => {
  const orgId = window.authUserOrgId; // Get organization ID from global
  const { openOnboarding } = useOnboardingStore();
  
  const {
    isLoading,
    isComplete,
    completedSteps,
    pendingSteps,
    totalSteps,
    progress,
    hasCategories,
    hasServices,
    hasWebhooks,
    hasTeamMembers,
    tenantMetadata,
  } = useOnboardingStatus(orgId);

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  const getStepLabel = (stepType: OnboardingStepType): string => {
    const labels: Record<OnboardingStepType, string> = {
      [OnboardingStepType.SELECT_CATEGORY]: 'Select Categories',
      [OnboardingStepType.SELECT_PROVIDERS]: 'Select Service Providers',
      [OnboardingStepType.SETUP_WEBHOOKS]: 'Setup Webhooks',
      [OnboardingStepType.SELECT_MEMBERS]: 'Invite Team Members',
      [OnboardingStepType.STARTUP_PROGRAM]: 'Startup Program',
      [OnboardingStepType.GENERAL]: 'General Information',
    };
    return labels[stepType] || stepType;
  };

  const getStatusIcon = (isCompleted: boolean) => {
    return isCompleted ? (
      <CheckCircle size={20} color="#4caf50" />
    ) : (
      <XCircle size={20} color="#f44336" />
    );
  };

  return (
    <Card>
      <CardContent>
        <Stack spacing={3}>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="h5" component="h2">
              Onboarding Status
            </Typography>
            {!isComplete && (
              <Button 
                variant="contained" 
                onClick={openOnboarding}
                startIcon={<AlertCircle size={16} />}
              >
                Complete Setup
              </Button>
            )}
          </Stack>

          {/* Overall Status */}
          <Stack direction="row" spacing={2} alignItems="center">
            <Typography variant="body1" color="text.secondary">
              Overall Progress:
            </Typography>
            <Chip 
              label={`${progress}%`}
              color={isComplete ? 'success' : 'warning'}
              size="small"
            />
            <Typography variant="body2" color="text.secondary">
              ({completedSteps.length} of {totalSteps} steps completed)
            </Typography>
          </Stack>

          {/* Individual Step Status */}
          <Stack spacing={2}>
            <Typography variant="subtitle1" fontWeight={600}>
              Setup Checklist:
            </Typography>
            
            <Stack spacing={1.5}>
              {/* Categories Status */}
              <Stack direction="row" spacing={2} alignItems="center">
                {getStatusIcon(hasCategories)}
                <Typography variant="body2">
                  Categories Selected
                </Typography>
                {hasCategories && (
                  <Chip label="Completed" color="success" size="small" />
                )}
              </Stack>

              {/* Services Status */}
              <Stack direction="row" spacing={2} alignItems="center">
                {getStatusIcon(hasServices)}
                <Typography variant="body2">
                  Service Providers Configured
                </Typography>
                {hasServices && (
                  <Chip label="Completed" color="success" size="small" />
                )}
              </Stack>

              {/* Webhooks Status */}
              <Stack direction="row" spacing={2} alignItems="center">
                {getStatusIcon(hasWebhooks)}
                <Typography variant="body2">
                  Webhooks Setup
                </Typography>
                {hasWebhooks && (
                  <Chip label="Completed" color="success" size="small" />
                )}
              </Stack>

              {/* Team Members Status */}
              <Stack direction="row" spacing={2} alignItems="center">
                {getStatusIcon(hasTeamMembers)}
                <Typography variant="body2">
                  Team Members Invited
                </Typography>
                {hasTeamMembers ? (
                  <Chip label="Completed" color="success" size="small" />
                ) : (
                  <Chip label="Optional" variant="outlined" size="small" />
                )}
              </Stack>
            </Stack>
          </Stack>

          {/* Pending Steps */}
          {pendingSteps.length > 0 && (
            <Stack spacing={1}>
              <Typography variant="subtitle1" fontWeight={600} color="warning.main">
                Pending Steps:
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap">
                {pendingSteps.map((stepType) => (
                  <Chip
                    key={stepType}
                    label={getStepLabel(stepType)}
                    variant="outlined"
                    color="warning"
                    size="small"
                  />
                ))}
              </Stack>
            </Stack>
          )}

          {/* Tenant Metadata Info */}
          {tenantMetadata && (
            <Box mt={2} p={2} bgcolor="grey.50" borderRadius={1}>
              <Typography variant="caption" color="text.secondary">
                Tenant: {tenantMetadata.name} ({tenantMetadata.state})
              </Typography>
            </Box>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
};

export default OnboardingStatusExample;