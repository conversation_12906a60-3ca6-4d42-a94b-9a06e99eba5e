/**
 * Server-side API utilities for making backend requests
 */

interface ApiRequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: string;
}

interface ApiResponse<T = any> {
  data: T;
  status: number;
  headers: Headers;
}

/**
 * Make an API request from the server
 * This is a placeholder implementation - replace with actual API client
 */
export async function apiRequest<T = any>(
  endpoint: string,
  options: ApiRequestOptions = {}
): Promise<ApiResponse<T>> {
  const baseUrl = process.env.API_BASE_URL || 'http://localhost:3001';
  const url = `${baseUrl}${endpoint}`;

  try {
    const response = await fetch(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      body: options.body,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `API request failed: ${response.status}`);
    }

    return {
      data,
      status: response.status,
      headers: response.headers,
    };
  } catch (error) {
    console.error(`API request failed: ${endpoint}`, error);
    throw error;
  }
}