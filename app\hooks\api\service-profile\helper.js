import _ from 'lodash';

export const parseFilterPayload = ({ search, type }, whichAPI) => {
   const criteria = [];

   // type
   if (type && type !== "GET_ALL_PROVIDERS") {
      criteria.push({ property: "/type", operator: "=", values: [type] })
   }

   // search
   if (search && whichAPI === 'serviceProfile') {
      criteria.push({ property: "/name", operator: "LIKE", values: [search] })
   }

   return criteria
}

export const updateToInitialSetupStore = (setEnabledServices, profiles = []) => {
   setEnabledServices((prevState) => {
      const final = [...prevState,
      ...profiles?.filter((i) => i?.service)];
      return _.uniqBy(final, (e) => e?.id) ?? [];
   });
}

export const updateServiceProfile = (profiles, data) => {
  return profiles.map((item) => {
      if (item?.id === data?.serviceProfile?.id) {
         item['service'] = data;
      }
      return item;
   })
}

export const getMergedServiceWithServiceProfile = (selectable) => {
   const [resp1, resp2] = selectable;

   const serviceProfile = resp1?.data?.data,
      services = resp2?.data?.data;

   return serviceProfile.map((item) => (
      { ...item, service: services?.find((i) => i?.serviceProfile?.id === item?.id) }
   )) ?? []
}