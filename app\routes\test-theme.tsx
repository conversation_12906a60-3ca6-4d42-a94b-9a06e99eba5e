import { Box, Typography, Button, Paper } from '@mui/material';
import { useTheme } from '@mui/material/styles';

export default function TestTheme() {
  const theme = useTheme();
  
  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h4" gutterBottom>
        Theme Test Page
      </Typography>
      
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Theme Configuration:
        </Typography>
        <Typography>Primary Color: {theme.palette.primary.main}</Typography>
        <Typography>Secondary Color: {theme.palette.secondary.main}</Typography>
        <Typography>Mode: {theme.palette.mode}</Typography>
      </Paper>
      
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button variant="contained" color="primary">
          Primary Button
        </Button>
        <Button variant="contained" color="secondary">
          Secondary Button
        </Button>
        <Button variant="outlined" color="primary">
          Outlined Primary
        </Button>
      </Box>
      
      <Box sx={{ display: 'flex', gap: 2 }}>
        <Box sx={{ 
          width: 100, 
          height: 100, 
          bgcolor: 'primary.main',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'primary.contrastText'
        }}>
          Primary
        </Box>
        <Box sx={{ 
          width: 100, 
          height: 100, 
          bgcolor: 'secondary.main',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'secondary.contrastText'
        }}>
          Secondary
        </Box>
      </Box>
    </Box>
  );
}