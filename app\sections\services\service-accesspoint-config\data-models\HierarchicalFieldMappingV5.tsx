import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>ack,
  Typo<PERSON>,
  IconButton,
  Chip,
  <PERSON><PERSON>,
  Divider,
  useTheme,
  alpha,
  Paper,
  Alert,
  ToggleButton,
  ToggleButtonGroup,
  useMediaQuery,
  TextField,
  InputAdornment,
  Popper,
  ClickAwayListener,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from "@mui/material";
import {
  Plus,
  X,
  ArrowRight,
  FileText,
  Hash,
  ToggleLeft,
  Calendar,
  List as ListIcon,
  Braces,
  AlertCircle,
  CircleCheckBig,
  ChevronRight,
  ChevronDown,
  Grid3X3,
  Layers,
  Zap,
  GitBranch,
  Table2,
} from "lucide-react";
import {
  UNIFIED_DATA_MODELS,
  PROVIDER_DATA_MODELS,
} from "./hierarchical-mock-data";
import { getOpenApiBasedDataModels } from "./openapi-field-mapping";
import VisualFieldMappingV2 from "./VisualFieldMappingV2";

interface HierarchicalFieldMappingProps {
  dataModel: any;
  serviceProfile: any;
  onMappingsChange?: (mappings: any[]) => void;
}

interface FieldMapping {
  id: string;
  source: string;
  target: string;
  sourceFieldPath?: string;
  targetFieldPath?: string;
  sourceType?: string;
  targetType?: string;
  expanded?: boolean;
  children?: FieldMapping[];
}

const TYPE_ICONS: Record<string, React.ReactNode> = {
  string: <FileText size={12} />,
  number: <Hash size={12} />,
  boolean: <ToggleLeft size={12} />,
  date: <Calendar size={12} />,
  array: <ListIcon size={12} />,
  object: <Braces size={12} />,
};

// Helper functions
const flattenAllFields = (field: any, parentPath = ""): any[] => {
  const result: any[] = [];
  const currentPath = parentPath ? `${parentPath}.${field.name}` : field.name;

  const flatField = {
    ...field,
    path: currentPath,
    value: field.name,
    label: field.name,
    displayPath: currentPath.split(".").join(" → "),
  };

  result.push(flatField);

  if (field.children) {
    field.children.forEach((child: any) => {
      result.push(...flattenAllFields(child, currentPath));
    });
  }

  return result;
};

const getDirectChildren = (allFields: any[], parentPath: string): any[] => {
  if (!parentPath) return [];

  return allFields.filter((field) => {
    const fieldSegments = field.path.split(".");
    const parentSegments = parentPath.split(".");

    if (fieldSegments.length !== parentSegments.length + 1) return false;

    for (let i = 0; i < parentSegments.length; i++) {
      if (fieldSegments[i] !== parentSegments[i]) return false;
    }

    return true;
  });
};

// Field Mapping Row Component
interface FieldMappingRowProps {
  sourceValue: string;
  targetValue: string;
  onSourceChange: (value: string) => void;
  onTargetChange: (value: string) => void;
  sourceOptions: any[];
  targetOptions: any[];
  excludeSourceValues?: string[];
  excludeTargetValues?: string[];
  hasError?: boolean;
  showExpand?: boolean;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
  canRemove?: boolean;
  onRemove?: () => void;
  allowCreateTarget?: boolean;
}

const FieldMappingRow: React.FC<FieldMappingRowProps> = ({
  sourceValue,
  targetValue,
  onSourceChange,
  onTargetChange,
  sourceOptions,
  targetOptions,
  excludeSourceValues = [],
  excludeTargetValues = [],
  hasError,
  showExpand = false,
  isExpanded = false,
  onToggleExpand,
  canRemove = true,
  onRemove,
  allowCreateTarget = true,
}) => {
  const theme = useTheme();

  return (
    <Stack direction="row" spacing={1.5} alignItems="center">
      <IconButton
        size="small"
        onClick={onToggleExpand}
        sx={{
          p: 0.5,
          visibility: showExpand ? "visible" : "hidden",
          alignSelf: "center",
        }}
      >
        <ChevronDown
          size={16}
          style={{
            transform: isExpanded ? "rotate(0deg)" : "rotate(-90deg)",
            transition: "transform 0.2s",
          }}
        />
      </IconButton>

      <Box sx={{ flex: 1 }}>
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ mb: 0.5, display: "block", fontSize: "0.75rem" }}
        >
          Source Field
        </Typography>
        <AutocompleteField
          value={sourceValue}
          onChange={onSourceChange}
          options={sourceOptions}
          placeholder="Select source field"
          hasError={hasError}
          excludeValues={excludeSourceValues}
        />
      </Box>

      {/* ArrowRight icon container */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          alignSelf: "center",
          color: alpha(theme.palette.text.secondary, 0.4),
          transform: "translateY(9px)", // <-- increased from 3px to 7px
        }}
      >
        <ArrowRight size={20} />
      </Box>

      <Box sx={{ flex: 1 }}>
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ mb: 0.5, display: "block", fontSize: "0.75rem" }}
        >
          Target Field
        </Typography>
        <AutocompleteField
          value={targetValue}
          onChange={onTargetChange}
          options={targetOptions}
          placeholder="Select target field"
          hasError={hasError}
          excludeValues={excludeTargetValues}
          allowCreate={allowCreateTarget}
        />
      </Box>

      {/* Remove (X) button */}
      <IconButton
        size="small"
        onClick={onRemove}
        disabled={!canRemove}
        sx={{
          p: 0.5,
          alignSelf: "center",
          transform: "translateY(9px)", // <-- increased from 3px to 7px
          "&:hover": {
            bgcolor: !canRemove
              ? "transparent"
              : alpha(theme.palette.error.main, 0.08),
            color: !canRemove ? "inherit" : theme.palette.error.main,
          },
          "&.Mui-disabled": {
            color: alpha(theme.palette.text.secondary, 0.3),
          },
        }}
      >
        <X size={16} />
      </IconButton>
    </Stack>
  );
};

// Custom Autocomplete Component
const AutocompleteField = ({
  value,
  onChange,
  options,
  placeholder,
  hasError,
  excludeValues = [],
  onCreateField,
  allowCreate = false,
}: {
  value: string;
  onChange: (value: string) => void;
  options: any[];
  placeholder: string;
  hasError?: boolean;
  excludeValues?: string[];
  onCreateField?: (fieldName: string) => void;
  allowCreate?: boolean;
}) => {
  const theme = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState(value);
  const anchorRef = useRef<HTMLDivElement>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newFieldData, setNewFieldData] = useState({
    name: "",
    type: "string",
    description: "",
  });

  useEffect(() => {
    setSearchValue(value);
  }, [value]);

  const filteredOptions = options.filter(
    (option) =>
      option.label.toLowerCase().includes(searchValue.toLowerCase()) &&
      !excludeValues.includes(option.value) &&
      option.value !== value // Don't exclude the current value
  );

  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setSearchValue(optionValue);
    setIsOpen(false);
  };

  return (
    <ClickAwayListener onClickAway={() => setIsOpen(false)}>
      <Box ref={anchorRef} sx={{ position: "relative" }}>
        <TextField
          fullWidth
          size="small"
          value={searchValue}
          onChange={(e) => {
            setSearchValue(e.target.value);
            setIsOpen(true);
          }}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          autoComplete="off"
          inputProps={{
            autoComplete: "off",
            "data-form-type": "other",
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              pr: "40px",
              fontSize: "0.875rem",
              bgcolor: "background.paper",
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor: hasError ? "error.main" : "#d1d5db",
                borderWidth: 1,
              },
              "&:hover": {
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: hasError
                    ? "error.main"
                    : theme.palette.primary.main,
                  borderWidth: 1,
                },
              },
              "&.Mui-focused": {
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: theme.palette.primary.main,
                  borderWidth: 2,
                  boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
                },
              },
            },
            "& .MuiInputBase-input": {
              py: 0.75,
              px: 1.25,
              "&::placeholder": {
                color: theme.palette.text.secondary,
                opacity: 1,
              },
            },
          }}
          InputProps={{
            endAdornment: (
              <InputAdornment
                position="end"
                sx={{ position: "absolute", right: 8 }}
              >
                <Stack direction="row" spacing={0.5}>
                  {value && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        onChange("");
                        setSearchValue("");
                      }}
                      sx={{
                        p: 0.25,
                        mr: 0.5,
                        color: "#6b7280",
                        border: "none",
                        "&:hover": {
                          bgcolor: "rgba(107, 114, 128, 0.1)",
                          color: "#374151",
                        },
                        "&:focus": {
                          outline: "none",
                          bgcolor: "rgba(107, 114, 128, 0.1)",
                        },
                        transition: "all 0.2s ease",
                      }}
                    >
                      <X size={14} />
                    </IconButton>
                  )}
                  <IconButton
                    size="small"
                    onClick={() => setIsOpen(!isOpen)}
                    sx={{
                      p: 0.5,
                      border: "none",
                      "&:hover": {
                        bgcolor: "transparent",
                      },
                    }}
                  >
                    <ChevronDown size={16} />
                  </IconButton>
                </Stack>
              </InputAdornment>
            ),
          }}
        />
        <Popper
          open={isOpen && filteredOptions.length > 0}
          anchorEl={anchorRef.current}
          placement="bottom-start"
          style={{ width: anchorRef.current?.offsetWidth, zIndex: 1300 }}
        >
          <Paper
            elevation={0}
            sx={{
              mt: 1,
              maxHeight: 240,
              overflow: "auto",
              border: `1px solid ${theme.palette.divider}`,
              boxShadow:
                "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
              borderRadius: 1,
            }}
          >
            <List dense sx={{ py: 0 }}>
              {/* Create new field option */}
              {allowCreate && searchValue && filteredOptions.length === 0 && (
                <ListItem disablePadding>
                  <ListItemButton
                    onClick={() => {
                      setNewFieldData({ ...newFieldData, name: searchValue });
                      setShowCreateDialog(true);
                      setIsOpen(false);
                    }}
                    sx={{
                      py: 1,
                      px: 1.5,
                      borderBottom: `1px solid ${theme.palette.divider}`,
                      bgcolor: alpha(theme.palette.primary.main, 0.04),
                      "&:hover": {
                        bgcolor: alpha(theme.palette.primary.main, 0.08),
                      },
                    }}
                  >
                    <Stack
                      direction="row"
                      spacing={1.5}
                      alignItems="center"
                      sx={{ width: "100%" }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          width: 24,
                          height: 24,
                          borderRadius: 0.5,
                          bgcolor: alpha(theme.palette.info.main, 0.1),
                        }}
                      >
                        <Plus size={14} color={theme.palette.info.main} />
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography
                          variant="body2"
                          sx={{
                            fontSize: "0.875rem",
                            fontWeight: 600,
                            color: theme.palette.primary.main,
                          }}
                        >
                          Create "{searchValue}"
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{ fontSize: "0.75rem", color: "text.secondary" }}
                        >
                          Add as a new field
                        </Typography>
                      </Box>
                    </Stack>
                  </ListItemButton>
                </ListItem>
              )}

              {/* Always show create field option when dropdown is open */}
              {allowCreate && filteredOptions.length > 0 && (
                <>
                  <ListItem
                    disablePadding
                    sx={{
                      borderBottom: `1px solid ${theme.palette.divider}`,
                      mb: 0.5,
                    }}
                  >
                    <ListItemButton
                      onClick={() => {
                        setNewFieldData({ ...newFieldData, name: searchValue });
                        setShowCreateDialog(true);
                        setIsOpen(false);
                      }}
                      sx={{
                        py: 0.75,
                        px: 1.5,
                        bgcolor: alpha(theme.palette.primary.main, 0.02),
                        "&:hover": {
                          bgcolor: alpha(theme.palette.primary.main, 0.08),
                        },
                      }}
                    >
                      <Stack
                        direction="row"
                        spacing={1.5}
                        alignItems="center"
                        sx={{ width: "100%" }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            width: 24,
                            height: 24,
                            borderRadius: 0.5,
                            border: `1px dashed ${theme.palette.primary.main}`,
                          }}
                        >
                          <Plus size={14} color={theme.palette.info.main} />
                        </Box>
                        <Typography
                          variant="body2"
                          sx={{
                            fontSize: "0.875rem",
                            color: theme.palette.primary.main,
                            fontWeight: 500,
                          }}
                        >
                          Create new field
                        </Typography>
                      </Stack>
                    </ListItemButton>
                  </ListItem>
                </>
              )}

              {filteredOptions.map((option) => (
                <ListItem key={option.value} disablePadding>
                  <ListItemButton
                    onClick={() => handleSelect(option.value)}
                    sx={{
                      py: 0.75,
                      px: 1.5,
                      "&:hover": {
                        bgcolor: alpha(theme.palette.primary.main, 0.08),
                      },
                    }}
                  >
                    <Stack
                      direction="row"
                      spacing={1.5}
                      alignItems="center"
                      sx={{ width: "100%" }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          width: 24,
                          height: 24,
                          borderRadius: 0.5,
                          bgcolor:
                            option.type === "string"
                              ? alpha(theme.palette.success.main, 0.1)
                              : option.type === "number"
                                ? alpha(theme.palette.info.main, 0.1)
                                : option.type === "boolean"
                                  ? alpha(theme.palette.warning.main, 0.1)
                                  : option.type === "date"
                                    ? alpha(theme.palette.secondary.main, 0.1)
                                    : option.type === "array"
                                      ? alpha(theme.palette.error.light, 0.1)
                                      : option.type === "object"
                                        ? alpha(theme.palette.primary.dark, 0.1)
                                        : alpha(
                                          theme.palette.text.secondary,
                                          0.1
                                        ),
                        }}
                      >
                        {option.type === "string" ? (
                          <FileText
                            size={14}
                            color={theme.palette.success.main}
                          />
                        ) : option.type === "number" ? (
                          <Hash size={14} color={theme.palette.info.main} />
                        ) : option.type === "boolean" ? (
                          <ToggleLeft
                            size={14}
                            color={theme.palette.warning.main}
                          />
                        ) : option.type === "date" ? (
                          <Calendar
                            size={14}
                            color={theme.palette.secondary.main}
                          />
                        ) : option.type === "array" ? (
                          <ListIcon
                            size={14}
                            color={theme.palette.error.light}
                          />
                        ) : option.type === "object" ? (
                          <Braces
                            size={14}
                            color={theme.palette.primary.dark}
                          />
                        ) : (
                          <FileText
                            size={14}
                            color={theme.palette.text.secondary}
                          />
                        )}
                      </Box>
                      <Box sx={{ flex: 1 }}>
                        <Typography
                          variant="body2"
                          sx={{ fontSize: "0.875rem", fontWeight: 500 }}
                        >
                          {option.label}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{ fontSize: "0.75rem", color: "text.secondary" }}
                        >
                          {option.type}
                        </Typography>
                      </Box>
                    </Stack>
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          </Paper>
        </Popper>

        {/* Quick Field Creation Dialog */}
        <Dialog
          open={showCreateDialog}
          onClose={() => setShowCreateDialog(false)}
          maxWidth="xs"
          fullWidth
        >
          <DialogTitle>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Box
                sx={{
                  width: 32,
                  height: 32,
                  borderRadius: 1,
                  bgcolor: alpha(theme.palette.info.main, 0.1),
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <Plus size={18} color={theme.palette.info.main} />
              </Box>
              <Typography variant="h6">Create New Field</Typography>
            </Stack>
          </DialogTitle>
          <DialogContent>
            <Stack spacing={3} sx={{ mt: 1 }}>
              <TextField
                label="Field Name"
                value={newFieldData.name}
                onChange={(e) =>
                  setNewFieldData({ ...newFieldData, name: e.target.value })
                }
                fullWidth
                size="small"
                helperText="This will be the field identifier"
              />

              <FormControl size="small" fullWidth>
                <InputLabel>Field Type</InputLabel>
                <Select
                  value={newFieldData.type}
                  onChange={(e) =>
                    setNewFieldData({ ...newFieldData, type: e.target.value })
                  }
                  label="Field Type"
                >
                  <MenuItem value="string">
                    <Stack direction="row" spacing={1} alignItems="center">
                      <FileText size={14} color={theme.palette.success.main} />
                      <span>String</span>
                    </Stack>
                  </MenuItem>
                  <MenuItem value="number">
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Hash size={14} color={theme.palette.info.main} />
                      <span>Number</span>
                    </Stack>
                  </MenuItem>
                  <MenuItem value="boolean">
                    <Stack direction="row" spacing={1} alignItems="center">
                      <ToggleLeft
                        size={14}
                        color={theme.palette.warning.main}
                      />
                      <span>Boolean</span>
                    </Stack>
                  </MenuItem>
                  <MenuItem value="date">
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Calendar
                        size={14}
                        color={theme.palette.secondary.main}
                      />
                      <span>Date</span>
                    </Stack>
                  </MenuItem>
                  <MenuItem value="object">
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Braces size={14} color={theme.palette.primary.dark} />
                      <span>Object</span>
                    </Stack>
                  </MenuItem>
                </Select>
              </FormControl>

              <TextField
                label="Description (Optional)"
                value={newFieldData.description}
                onChange={(e) =>
                  setNewFieldData({
                    ...newFieldData,
                    description: e.target.value,
                  })
                }
                fullWidth
                size="small"
                multiline
                rows={2}
              />
            </Stack>
          </DialogContent>
          <DialogActions sx={{ px: 3, pb: 2 }}>
            <Button onClick={() => setShowCreateDialog(false)} color="inherit">
              Cancel
            </Button>
            <Button
              onClick={() => {
                // Here you would typically make an API call to create the field
                console.log("Creating field:", newFieldData);
                if (onCreateField) {
                  onCreateField(newFieldData.name);
                }
                onChange(newFieldData.name);
                setShowCreateDialog(false);
                setNewFieldData({ name: "", type: "string", description: "" });
              }}
              variant="contained"
              startIcon={<Plus size={16} />}
              disabled={!newFieldData.name.trim()}
            >
              Create Field
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ClickAwayListener>
  );
};

interface MappingValues {
  id: string
  source: string
  target: string
  expanded: boolean
  children: Array<MappingValues>
}

const DEFAULT_MAPPING_VALUES = { id: "1", source: "", target: "", expanded: false, children: [] };

export default function HierarchicalFieldMappingV5({
  dataModel,
  serviceProfile,
  onMappingsChange,
}: HierarchicalFieldMappingProps) {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));

  const [viewMode, setViewMode] = useState<"table" | "visual">("table");
  const [mappings, setMappings] = useState<FieldMapping[]>([DEFAULT_MAPPING_VALUES]);
  const [validMappingsCount, setValidMappingsCount] = useState(0);

  // Get data based on service provider
  const provider = serviceProfile?.name?.toLowerCase() || "github";

  let sourceFields, targetFields;

  try {
    const openApiModels = getOpenApiBasedDataModels();
    sourceFields =
      openApiModels.PROVIDER_DATA_MODELS[provider]?.[dataModel.id] || {};
    targetFields = openApiModels.UNIFIED_DATA_MODELS[dataModel.id] || {};
  } catch (error) {
    sourceFields = PROVIDER_DATA_MODELS[provider]?.[dataModel.id] || {};
    targetFields = UNIFIED_DATA_MODELS[dataModel.id] || {};
  }

  const flatSourceFields = sourceFields?.children
    ? sourceFields.children.flatMap((child: any) => flattenAllFields(child))
    : [];

  const flatTargetFields = targetFields?.children
    ? targetFields.children.flatMap((child: any) => flattenAllFields(child))
    : [];

  const rootSourceFields = flatSourceFields.filter(
    (f) => !f.path.includes(".")
  );
  const rootTargetFields = flatTargetFields.filter(
    (f) => !f.path.includes(".")
  );

  // Calculate valid mappings count
  useEffect(() => {
    let count = 0;
    mappings.forEach((mapping) => {
      if (mapping.source && mapping.target && !getValidationError(mapping)) {
        count++;
      }
      if (mapping.children) {
        mapping.children.forEach((child) => {
          if (child.source && child.target && !getValidationError(child)) {
            count++;
          }
        });
      }
    });
    setValidMappingsCount(count);

    if (onMappingsChange) {
      onMappingsChange(mappings);
    }
  }, [mappings]);

  const getFieldType = (fieldValue: string, fields: any[]) => {
    const field = fields.find((f) => f.value === fieldValue);
    return field ? field.type : "string";
  };

  const getValidationError = (mapping: FieldMapping) => {
    if (!mapping.source || !mapping.target) return null;

    const sourceType = getFieldType(mapping.source, flatSourceFields);
    const targetType = getFieldType(mapping.target, flatTargetFields);

    if (sourceType !== targetType) {
      return `Type mismatch: Cannot map ${sourceType} to ${targetType}`;
    }
    return null;
  };

  const toggleExpand = (mappingId: string) => {
    setMappings((prev) =>
      prev.map((m) =>
        m.id === mappingId ? { ...m, expanded: !m.expanded } : m
      )
    );
  };

  const updateMapping = (
    id: string,
    field: "source" | "target",
    value: string
  ) => {
    setMappings((prev) =>
      prev.map((m) => {
        if (m.id === id) {
          const updated = { ...m, [field]: value };

          // If source is object type and target is also object, auto-expand and add child if none exist
          if (
            field === "source" &&
            getFieldType(value, flatSourceFields) === "object"
          ) {
            updated.expanded = true;
            if (!updated.children || updated.children.length === 0) {
              updated.children = [
                {
                  id: `${id}-child-1`,
                  source: "",
                  target: "",
                  children: [],
                },
              ];
            }
          }

          return updated;
        }
        return m;
      })
    );
  };

  const updateChildMapping = (
    parentId: string,
    childId: string,
    field: "source" | "target",
    value: string
  ) => {
    setMappings((prev) =>
      prev.map((m) => {
        if (m.id === parentId && m.children) {
          return {
            ...m,
            children: m.children.map((child) =>
              child.id === childId ? { ...child, [field]: value } : child
            ),
          };
        }
        return m;
      })
    );
  };

  const removeMapping = (id: string) => {
    setMappings((prev) => prev.filter((m) => m.id !== id));
  };

  const removeChildMapping = (parentId: string, childId: string) => {
    setMappings((prev) =>
      prev.map((m) => {
        if (m.id === parentId && m.children) {
          return {
            ...m,
            children: m.children.filter((child) => child.id !== childId),
          };
        }
        return m;
      })
    );
  };

  const addMapping = () => {
    const newId = Date.now().toString();
    setMappings((prev) => [
      ...prev,
      {
        id: newId,
        source: "",
        target: "",
        expanded: false,
        children: [],
      },
    ]);
  };

  const addChildMapping = (parentId: string) => {
    const parent = mappings.find((m) => m.id === parentId);
    if (!parent) return;

    const newId = `${parentId}-child-${Date.now()}`;

    setMappings((prev) =>
      prev.map((m) => {
        if (m.id === parentId) {
          return {
            ...m,
            children: [
              ...(m.children || []),
              {
                id: newId,
                source: "",
                target: "",
                children: [],
              },
            ],
          };
        }
        return m;
      })
    );
  };

  // Get child fields for a parent
  const getChildFields = (parentField: string, isSource: boolean) => {
    const allFields = isSource ? flatSourceFields : flatTargetFields;
    return getDirectChildren(allFields, parentField);
  };

  return (
    <Box>
      <Box>
        {/* Header */}
        <Box sx={{ px: 3, pb: 0 }}>
          <Stack
            direction={{ xs: "column", sm: "row" }}
            justifyContent="space-between"
            alignItems={{ xs: "stretch", sm: "center" }}
            spacing={{ xs: 2, sm: 3 }}
            sx={{ mb: 2 }}
          >
            <Typography variant="body2" color="text.secondary" sx={{ flex: 1 }}>
              Map fields from {serviceProfile?.name || "Source"} to the Unizo
              Unified Common data model. Select an object to map its nested
              fields. You can create new fields as needed.
            </Typography>

            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={(_, value) => value && setViewMode(value)}
              size="small"
              sx={{
                display: "flex",
                gap: 1,
                "& .MuiToggleButton-root": {
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  gap: 1,
                  py: 0.75,
                  px: 2,
                  minWidth: 120,
                  textTransform: "none",
                  fontSize: "0.875rem",
                  whiteSpace: "nowrap",
                  border: `1px solid ${theme.palette.divider}`,
                  "&.Mui-selected": {
                    bgcolor:
                      viewMode === "visual"
                        ? alpha(theme.palette.primary.main, 0.1)
                        : alpha(theme.palette.grey[500], 0.1),
                    borderColor:
                      viewMode === "visual"
                        ? theme.palette.primary.main
                        : theme.palette.grey[500],
                  },
                },
              }}
            >
              <ToggleButton value="table">
                <Table2 size={16} />
                <span>Table View</span>
              </ToggleButton>
              <ToggleButton value="visual">
                <GitBranch size={16} />
                <span>Visual View</span>
              </ToggleButton>
            </ToggleButtonGroup>
          </Stack>
        </Box>

        {/* Mappings */}
        <Box
          sx={{
            p: 3,
            bgcolor:
              theme.palette.mode === "dark"
                ? alpha(theme.palette.background.paper, 0.3)
                : "#fafafa",
          }}
        >
          {viewMode === "table" ? (
            <Box
              sx={{
                maxWidth: isMobile ? "100%" : isTablet ? "85%" : "75%",
              }}
            >
              <Stack spacing={3}>
                {mappings.map((mapping) => {
                  const validationError = getValidationError(mapping);
                  const isObjectMapping =
                    mapping.source &&
                    getFieldType(mapping.source, flatSourceFields) === "object";

                  // Get all mapped source and target fields excluding current mapping
                  const mappedSourceFields = mappings
                    .filter((m) => m.id !== mapping.id && m.source)
                    .map((m) => m.source);

                  const mappedTargetFields = mappings
                    .filter((m) => m.id !== mapping.id && m.target)
                    .map((m) => m.target);

                  return (
                    <Box
                      key={mapping.id}
                      sx={{
                        bgcolor: "background.paper",
                        border: `1px solid ${theme.palette.divider}`,
                        borderRadius: 1,
                        overflow: "hidden",
                      }}
                    >
                      <Box sx={{ p: 2 }}>
                        <FieldMappingRow
                          sourceValue={mapping.source}
                          targetValue={mapping.target}
                          onSourceChange={(value) =>
                            updateMapping(mapping.id, "source", value)
                          }
                          onTargetChange={(value) =>
                            updateMapping(mapping.id, "target", value)
                          }
                          sourceOptions={rootSourceFields}
                          targetOptions={rootTargetFields}
                          excludeSourceValues={mappedSourceFields}
                          excludeTargetValues={mappedTargetFields}
                          hasError={!!validationError}
                          showExpand={isObjectMapping}
                          isExpanded={mapping.expanded}
                          onToggleExpand={() => toggleExpand(mapping.id)}
                          canRemove={mappings.length > 1}
                          onRemove={() => removeMapping(mapping.id)}
                          allowCreateTarget={true}
                        />

                        {validationError && (
                          <Alert
                            severity="error"
                            icon={<AlertCircle size={16} />}
                            sx={{
                              mt: 2,
                              py: 0.5,
                              "& .MuiAlert-message": { fontSize: "0.875rem" },
                            }}
                          >
                            {validationError}
                          </Alert>
                        )}
                      </Box>

                      {/* Child mappings - only show if source is object type */}
                      {isObjectMapping && mapping.expanded && (
                        <Box
                          sx={{
                            borderTop: `1px solid ${theme.palette.divider}`,
                            pt: 2,
                            pb: 2,
                            position: "relative",
                          }}
                        >
                          <Box sx={{ ml: 7, pr: 2, position: "relative" }}>
                            {/* Vertical line for group */}
                            <Box
                              sx={{
                                position: "absolute",
                                left: -24,
                                top: -8,
                                bottom: -8,
                                width: 2,
                                bgcolor: theme.palette.divider,
                                borderRadius: "1px",
                              }}
                            />

                            {mapping.children?.map((child, childIndex) => {
                              const childValidationError =
                                getValidationError(child);
                              const isLast =
                                childIndex ===
                                (mapping.children?.length || 0) - 1;
                              const childSourceFields = getChildFields(
                                mapping.source,
                                true
                              );
                              const childTargetFields = getChildFields(
                                mapping.target,
                                true
                              );

                              // Get all mapped child fields excluding current child
                              const mappedChildSourceFields =
                                mapping.children
                                  ?.filter((c) => c.id !== child.id && c.source)
                                  .map((c) => c.source) || [];
                              const mappedChildTargetFields =
                                mapping.children
                                  ?.filter((c) => c.id !== child.id && c.target)
                                  .map((c) => c.target) || [];

                              return (
                                <Box
                                  key={child.id}
                                  sx={{ position: "relative", mb: 1 }}
                                >
                                  {/* Horizontal connector with rounded corner */}
                                  <Box
                                    sx={{
                                      position: "absolute",
                                      left: -24,
                                      top: "50%",
                                      transform: "translateY(-50%)",
                                      width: 24,
                                      height: 2,
                                      bgcolor: theme.palette.divider,
                                      "&::before": {
                                        content: '""',
                                        position: "absolute",
                                        left: 0,
                                        top: -8,
                                        width: 2,
                                        height: 8,
                                        bgcolor: theme.palette.divider,
                                      },
                                    }}
                                  />

                                  <Box sx={{ pl: 4 }}>
                                    <Stack
                                      direction="row"
                                      spacing={1.5}
                                      alignItems="center"
                                    >
                                      <Box sx={{ flex: 1 }}>
                                        <AutocompleteField
                                          value={child.source}
                                          onChange={(value) =>
                                            updateChildMapping(
                                              mapping.id,
                                              child.id,
                                              "source",
                                              value
                                            )
                                          }
                                          options={childSourceFields}
                                          placeholder="Select source field"
                                          hasError={!!childValidationError}
                                          excludeValues={
                                            mappedChildSourceFields
                                          }
                                        />
                                      </Box>

                                      <Box
                                        sx={{
                                          color: alpha(
                                            theme.palette.text.secondary,
                                            0.4
                                          ),
                                          mx: 0.5,
                                        }}
                                      >
                                        <ArrowRight size={16} />
                                      </Box>

                                      <Box sx={{ flex: 1 }}>
                                        <AutocompleteField
                                          value={child.target}
                                          onChange={(value) =>
                                            updateChildMapping(
                                              mapping.id,
                                              child.id,
                                              "target",
                                              value
                                            )
                                          }
                                          options={childTargetFields}
                                          placeholder="Select target field"
                                          hasError={!!childValidationError}
                                          excludeValues={
                                            mappedChildTargetFields
                                          }
                                          allowCreate={true}
                                        />
                                      </Box>

                                      <IconButton
                                        size="small"
                                        onClick={() =>
                                          removeChildMapping(
                                            mapping.id,
                                            child.id
                                          )
                                        }
                                        sx={{
                                          p: 0.5,
                                          "&:hover": {
                                            bgcolor: alpha(
                                              theme.palette.error.main,
                                              0.08
                                            ),
                                            color: theme.palette.error.main,
                                          },
                                        }}
                                      >
                                        <X size={16} />
                                      </IconButton>
                                    </Stack>

                                    {childValidationError && (
                                      <Alert
                                        severity="error"
                                        icon={<AlertCircle size={12} />}
                                        sx={{
                                          mt: 1,
                                          py: 0.25,
                                          "& .MuiAlert-message": {
                                            fontSize: "0.75rem",
                                          },
                                          "& .MuiAlert-icon": { py: 0.5 },
                                        }}
                                      >
                                        {childValidationError}
                                      </Alert>
                                    )}
                                  </Box>
                                </Box>
                              );
                            })}

                            <Box sx={{ position: "relative", mt: 1 }}>
                              {/* Horizontal connector for button */}
                              <Box
                                sx={{
                                  position: "absolute",
                                  left: -24,
                                  top: "50%",
                                  transform: "translateY(-50%)",
                                  width: 24,
                                  height: 2,
                                  bgcolor: theme.palette.divider,
                                }}
                              />
                              {/* Cover the protruding vertical line */}
                              <Box
                                sx={{
                                  position: "absolute",
                                  left: -24,
                                  top: "calc(50% + 1px)",
                                  bottom: -16,
                                  width: 2,
                                  bgcolor: theme.palette.background.paper,
                                  zIndex: 1,
                                }}
                              />
                              <Button
                                size="small"
                                startIcon={<Plus size={14} />}
                                onClick={() => addChildMapping(mapping.id)}
                                sx={{
                                  ml: 4,
                                  fontSize: "0.875rem",
                                  fontWeight: 500,
                                  textTransform: "none",
                                  color: theme.palette.primary.main,
                                  py: 0.75,
                                  "&:hover": {
                                    bgcolor: alpha(
                                      theme.palette.primary.main,
                                      0.08
                                    ),
                                  },
                                }}
                              >
                                Add{" "}
                                {mapping.source
                                  ? `${mapping.source} field`
                                  : "field"}{" "}
                                mapping
                              </Button>
                            </Box>
                          </Box>
                        </Box>
                      )}
                    </Box>
                  );
                })}

                <Button
                  startIcon={<Plus size={16} />}
                  onClick={addMapping}
                  variant="outlined"
                  sx={{
                    alignSelf: "flex-start",
                    textTransform: "none",
                    fontSize: "0.875rem",
                    fontWeight: 500,
                    borderColor: theme.palette.primary.main,
                    color: theme.palette.primary.main,
                    borderRadius: 1.5,
                    px: 2,
                    py: 1,
                    "&:hover": {
                      bgcolor: alpha(theme.palette.primary.main, 0.04),
                      borderColor: theme.palette.primary.main,
                    },
                  }}
                >
                  Add mapping
                </Button>
              </Stack>
            </Box>
          ) : (
            <Box sx={{ height: "60vh", overflow: "hidden" }}>
              <VisualFieldMappingV2
                dataModel={dataModel}
                serviceProfile={serviceProfile}
                embedded={true}
                existingMappings={mappings.map((m) => ({
                  id: m.id,
                  source: m.source,
                  target: m.target,
                  sourceFieldPath: m.source,
                  targetFieldPath: m.target,
                  sourceType: getFieldType(m.source, flatSourceFields),
                  targetType: getFieldType(m.target, flatTargetFields),
                }))}
                onMappingsChange={(newMappings) => {
                  const convertedMappings = newMappings.map((m: any) => ({
                    id: m.id,
                    source: m.source,
                    target: m.target,
                    expanded: false,
                    children: [],
                  }));
                  setMappings(convertedMappings);
                }}
              />
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
}
