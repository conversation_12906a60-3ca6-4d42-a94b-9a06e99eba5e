export interface Organization {
   href: string
   type: string
   id: string
   name: string
   description: string
   state: Partial<State>
   address: Address
   contacts: any[]
   subscription: Subscription
   notifications: any[]
   tags: any[]
   attributes: any[]
   changeLog: ChangeLog
   links: any[]
 }

 export interface Address {
   line1: string
   city: string
   zipcode: number
 }

 export interface Subscription {
   id: string
 }

 export interface ChangeLog {
   createdDateTime: string
   lastUpdatedDateTime: string
 }
