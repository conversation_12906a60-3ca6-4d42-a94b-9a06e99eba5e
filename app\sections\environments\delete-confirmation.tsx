import { But<PERSON>, <PERSON>alog<PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>itle, Stack, Typography } from "@mui/material"
import { Dialog } from "components/@extended/dialog"
import useEnvironment from "hooks/api/useEnvironment"
import { useMutation } from "@tanstack/react-query"
import Environment from "types/environment"

interface DeleteConfirmationProps {
   isOpen: boolean
   selected?: Environment.Root | null
   onClose: () => void
   onSuccess: () => void
}

const DeleteConfirmation = ({ selected, isOpen, onClose, onSuccess }: DeleteConfirmationProps) => {

   const { deleteEnvironmentQueryConfig } = useEnvironment();

   const { mutateAsync: attemptToDeleteEnvironment, isPending: isDeleting } = useMutation(
      {
         ...deleteEnvironmentQueryConfig(),
         onSettled() {
            onSuccess();
         },
      }
   );

   return (
      <Dialog open={isOpen} maxWidth={'xs'}>
         <DialogTitle component={Stack} gap={.7}>
            <Typography variant="h5">{'Confirmation'}</Typography>
         </DialogTitle>
         <DialogContent>
            This action cannot be undone. Once completed, it is permanent and cannot be reversed.
         </DialogContent>
         <DialogActions>
            <></>
            <Stack direction={'row'} gap={2}>
               <Button disabled={isDeleting} onClick={onClose} variant="outlined">Cancel</Button>
               <Button
                  color="primary"
                  variant="contained"
                  onClick={() => attemptToDeleteEnvironment({ environmentId: selected?.id as string })}
                  disabled={isDeleting}
               >
                  Confirm
               </Button>
            </Stack>
         </DialogActions>
      </Dialog>
   )
}

export default DeleteConfirmation;