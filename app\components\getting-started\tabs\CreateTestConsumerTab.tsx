import React, { useState, useMemo, useCallback } from 'react';
import {
  Box,
  Typography,
  Stack,
  Button,
  Grid,
  useTheme,
  alpha,
} from '@mui/material';
import { ExternalLink, Check, Webhook } from 'lucide-react';
import { useNavigate } from '@remix-run/react';
import ConfigurationSection from 'components/getting-started/ConfigurationSection';
import WebhookConfigurationCard from 'components/getting-started/WebhookConfigurationCard';
import ApiWebhooksCard from 'components/getting-started/ApiWebhooksCard';
import TabNavigationFooter from 'components/getting-started/TabNavigationFooter';
import { useGetOrganization } from 'hooks/api/organization/useGetOrganization';

import CreateBiDirection from 'sections/services/setup/bi-directional/create';
import { DOMAINS } from 'data/domains';
import { extractProperties } from 'lib/utils';
import useUserDetails from 'store/user';

// Type mapping for all webhook types
const typeMap: Record<string, string> = {
  'SCM_WATCH_HOOK': 'Source Code',
  'TICKETING_WATCH_HOOK': 'Ticketing',
  'PCR_WATCH_HOOK': 'Packages & Container registry',
  'COMMS_WATCH_HOOK': 'Communications',
  'INCIDENT_WATCH_HOOK': 'Incident management',
  'VMS_WATCH_HOOK': 'Vulnerability management',
  'KEY_MGMT_WATCH_HOOK': 'Key management',
  'MONITORING_WATCH_HOOK': 'Observability',
  'IDENTITY_WATCH_HOOK': 'Identity',
  'CLOUD_INFRA_WATCH_HOOK': 'Public cloud (Infra)',
  'EDR_XDR_WATCH_HOOK': 'EDR & XDR',
  'SIEM_WATCH_HOOK': 'SIEM',
  'GEN_AI_WATCH_HOOK': 'Gen AI',
  'BLOB_STORAGE_WATCH_HOOK': 'Blob storage',
  'FILE_STORAGE_WATCH_HOOK': 'File storage',
  'PLATFORM_WATCH_HOOK': 'Platform',
};

const CUSTOM_OPTIONS = [{ label: 'Platform', value: 'PLATFORM_WATCH_HOOK', key: 'PLATFORM' }]

interface CreateTestConsumerTabProps {
  onNext?: () => void;
  onPrevious?: () => void;
  isFirstTab?: boolean;
  isLastTab?: boolean;
  nextLabel?: string;
  previousLabel?: string;
  onWebhookAdded?: () => void;
}

export default function CreateTestConsumerTab({
  onNext,
  onPrevious,
  isFirstTab,
  isLastTab,
  nextLabel,
  previousLabel,
  onWebhookAdded,
}: CreateTestConsumerTabProps) {
  const theme = useTheme();
  const navigate = useNavigate();
  const [isLoading] = useState(false);
  const [isError] = useState(false);
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [selected, setSelected] = useState<any>(null);
  const [preSelectedType, setPreSelectedType] = useState<string | null>(null)

  const { subscriptions: subscriptionsData, categories } = useUserDetails()
  const { configs } = useGetOrganization({ id: window?.authUserOrgId })

  const webHookData: any = configs?.data || []

  const enabledCategories = categories?.filter((i) => !i?.disabled)?.map((i) => i?.hook) ?? []

  // Extract platform webhook from the mock data
  const platformWebhook = useMemo(() => {
    return webHookData?.find((webhook: { type: string }) => webhook.type === 'PLATFORM_WATCH_HOOK');
  }, [webHookData]);

  // Extract API webhooks (excluding PLATFORM_WATCH_HOOK)
  const apiWebhooks = useMemo(() => {
    return webHookData?.filter((webhook: { type: string }) => webhook.type !== 'PLATFORM_WATCH_HOOK' &&  enabledCategories?.includes(webhook.type)) ?? [];
  }, [webHookData, enabledCategories]);

  const getOptions = useCallback(() => {

    const options = DOMAINS
      .map(({ hook: value, key, label }) => ({ label, value, key }));

    const filteredOptions = options.filter(option =>
      subscriptionsData.map(({ product }) => product.code?.toUpperCase()).includes(option?.key)
    );
    return filteredOptions.concat(CUSTOM_OPTIONS).filter(({ value, ...rest }: any) => {
      return (
        !extractProperties(webHookData, 'type')?.includes(value) &&
        !rest.disabled
      );
    });

  }, [webHookData, subscriptionsData]);

  // Don't pass requiredTypes - let ApiWebhooksCard show all types by default

  const onClose = (refresh = false) => {
    setIsOpen(false);
    setSelected(null);
    if (refresh && onWebhookAdded) {
      onWebhookAdded();
    }
  }

  const onEditWebhook = (requestedWebhook: any) => {
    const label = typeMap[requestedWebhook?.type] || requestedWebhook?.type;
    setSelected({ ...requestedWebhook, name: label });
    setIsOpen(true);
  }

  const handleConfigureWebhooks = (type: string | null = null) => {
    setIsOpen(true);
    setPreSelectedType(type)
  };

  // Check if ALL webhooks (platform + all API webhooks) are configured
  const allWebhooksConfigured = useMemo(() => {
    // Platform webhook must be configured
    const platformConfigured = !!platformWebhook && platformWebhook.state === 'SUBMITTED';

    // Get all possible webhook types (excluding PLATFORM_WATCH_HOOK)
    const allApiWebhookTypes = Object.keys(typeMap).filter(type => type !== 'PLATFORM_WATCH_HOOK' && enabledCategories?.includes(type));

    // Check if all API webhook types have a configured webhook
    const allApiWebhooksConfigured = allApiWebhookTypes.every(type =>
      apiWebhooks.some((webhook: { type: string, state: string }) => webhook.type === type && webhook.state === 'SUBMITTED')
    );

    return platformConfigured && allApiWebhooksConfigured;
  }, [platformWebhook, apiWebhooks, enabledCategories]);


  const configuredContent = (
    <Box>
      <Box
        sx={{
          backgroundColor: theme.palette.background.default,
          borderRadius: 1,
          p: 4,
        }}
      >
        <Stack spacing={4}>
          {/* Header Section */}
          <Box>
        <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 2 }}>
          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: '50%',
              backgroundColor: theme.palette.primary.main,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: theme.palette.common.white,
            }}
          >
            <Webhook size={18} strokeWidth={3} />
          </Box>
          <Typography variant="h5" fontWeight={600}>
            Event Listeners
          </Typography>
        </Stack>
        <Typography
          variant="body1"
          sx={{
            color: theme.palette.mode === 'light'
              ? theme.palette.grey[600]
              : theme.palette.text.secondary,
            maxWidth: 800,
          }}
        >
          Setup endpoints to receive real-time notifications when data changes occur in your connected services.
        </Typography>
      </Box>

      {/* Webhook Configuration Cards */}
      <Box>
        <Stack spacing={3}>
          {/* Platform Webhook Configuration */}
          <WebhookConfigurationCard
            webhook={platformWebhook}
            isLoading={isLoading}
            isError={isError}
            isPlatformCard={true}
            onEditWebhook={() => onEditWebhook(platformWebhook)}
            onGetStarted={() => handleConfigureWebhooks('PLATFORM_WATCH_HOOK')}
          />

          {/* Per-API Webhook Configuration */}
          {!isError && (
            <ApiWebhooksCard
              configuredWebhooks={apiWebhooks}
              isLoading={isLoading}
              isError={isError}
              onConfigureWebhook={(e) => {
                handleConfigureWebhooks(e)
              }}
              onEditWebhook={onEditWebhook}
            />
          )}
        </Stack>
      </Box>
        </Stack>
      </Box>
    </Box>
  );

  return (
    <Box>
      <ConfigurationSection
        stepNumber="2"
        title="Setup Event Listeners"
        description="Get notification events when your users performs change"
        isConfigured={true}
        onGetStarted={() => handleConfigureWebhooks('PLATFORM_WATCH_HOOK')}
        configuredContent={configuredContent}
        isaddView={!platformWebhook && !apiWebhooks?.length}
      />
      <CreateBiDirection preSelectedType={preSelectedType} isEditMode={!!selected} selected={selected} isOpen={isOpen} onClose={onClose} options={getOptions()} />
      <TabNavigationFooter
        onNext={onNext}
        onPrevious={onPrevious}
        isFirstTab={isFirstTab}
        isLastTab={isLastTab}
        nextLabel={nextLabel}
        previousLabel={previousLabel}
      />
    </Box>
  );
}