import { IconButton, ListItemIcon, Menu, MenuItem, MenuItemProps, MenuProps, Typography } from "@mui/material"
import { EllipsisOutlined, SettingOutlined } from "@ant-design/icons"
import { useState } from "react";
import useServiceConfig from "store/setup-service";

const OPTION_S: MenuItemProps[] = [
   { children: 'Configure', value: 'configure', }
];

const MENU_STATIC_PROPS: Partial<MenuProps> = {
   anchorOrigin: {
      vertical: 'top',
      horizontal: 'center',
   },
   transformOrigin: {
      vertical: 'top',
      horizontal: 'center',
   }
}

type Props = {
   item: any
}

export default ({ item }: Props) => {
   const {
      setSelectedTypeConfig,
   } = useServiceConfig();

   const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
   const onOpen = () => {
      setSelectedTypeConfig(item)
   }

   const onSelect = (type: string) => {
      if (type === 'configure') {
         onOpen()
      }
   }

   return (
      <>
         <Typography
            className="link"
            onClick={() => (
               void onSelect('configure' as string)
            )}
         >
            Configure
         </Typography>
      </>
   )
}