import { DataModel } from "./type";

export const dataModelsByCategory: Record<string, DataModel[]> = {
   SCM: [
      {
         id: "organization",
         name: "organization",
         displayName: "Organization",
         description: "Organization or team information",
         fieldCount: 5,
      },
      {
         id: "repository",
         name: "repository",
         displayName: "Repository",
         description: "Code repository details",
         fieldCount: 3,
      },
      {
         id: "branch",
         name: "branch",
         displayName: "Branch",
         description: "Branch information",
         fieldCount: 1,
      },
      {
         id: "commit",
         name: "commit",
         displayName: "Commit",
         description: "Commit details",
         fieldCount: 0,
      },
      {
         id: "pull_request",
         name: "pull_request",
         displayName: "Pull Request",
         description: "Pull request information",
         fieldCount: 2,
      },
   ],
   TICKETING: [
      {
         id: "organization",
         name: "organization",
         displayName: "Organization",
         description: "Organization details",
         fieldCount: 0,
      },
      {
         id: "user",
         name: "user",
         displayName: "User",
         description: "User information",
         fieldCount: 2,
      },
      {
         id: "collections",
         name: "collections",
         displayName: "Collections",
         description: "Collections information",
         fieldCount: 1,
      },
      {
         id: "ticket",
         name: "ticket",
         displayName: "Ticket",
         description: "Support ticket information",
         fieldCount: 3,
      },
      {
         id: "comment",
         name: "comment",
         displayName: "Comment",
         description: "Ticket comments",
         fieldCount: 1,
      },
      {
         id: "attachment",
         name: "attachment",
         displayName: "Attachment",
         description: "Ticket attachments",
         fieldCount: 4,
      },
      {
         id: "label",
         name: "Label",
         displayName: "Label",
         description: "Ticket labels",
         fieldCount: 5,
      },

   ],
   CRM: [
      {
         id: "contact",
         name: "contact",
         displayName: "Contact",
         description: "Contact information",
         fieldCount: 3,
      },
      {
         id: "account",
         name: "account",
         displayName: "Account",
         description: "Account details",
         fieldCount: 2,
      },
      {
         id: "deal",
         name: "deal",
         displayName: "Deal",
         description: "Deal information",
         fieldCount: 1,
      },
   ],
};