import { styled } from '@mui/material/styles';
import MuiAvatar, { AvatarProps } from '@mui/material/Avatar';

interface ProfileAvatarProps extends AvatarProps {
  size?: 'small' | 'medium' | 'large';
}

const ProfileAvatar = styled(MuiAvatar)<ProfileAvatarProps>(({ theme, size = 'medium' }) => {
  const sizeMap = {
    small: {
      width: 32,
      height: 32,
      fontSize: '0.75rem',
    },
    medium: {
      width: 40,
      height: 40,
      fontSize: '0.875rem',
    },
    large: {
      width: 56,
      height: 56,
      fontSize: '1.25rem',
    },
  };

  const { width, height, fontSize } = sizeMap[size];

  return {
    width,
    height,
    fontSize,
    fontWeight: 700,
    textTransform: 'uppercase',
    borderRadius: '50%',
    backgroundColor: theme.palette.primary.main,
    color: '#ffffff',
    border: 'none',
    transition: 'all 0.2s ease',
    
    '&:hover': {
      backgroundColor: theme.palette.primary.dark,
      transform: 'scale(1.05)',
    },
    
    // Ensure perfect circle
    '& .MuiAvatar-img': {
      borderRadius: '50%',
    },
    
    // Dark mode adjustments
    ...(theme.palette.mode === 'dark' && {
      backgroundColor: theme.palette.primary.light,
      color: theme.palette.primary.contrastText,
      
      '&:hover': {
        backgroundColor: theme.palette.primary.main,
      },
    }),
  };
});

export default ProfileAvatar;