const eventConfiguration = {
  "Source Code": [
    {
      value: "commit:pushed",
      name: "New commit pushed",
    },
    {
      value: "commit:updated",
      name: "Commit updated",
    },
    {
      value: "organization:renamed",
      name: "Organization renamed",
    },
    {
      value: "organization:deleted",
      name: "Organization deleted",
    },
    {
      value: "repository:created",
      name: "New repository created",
    },
    {
      value: "repository:renamed",
      name: "Repository gets renamed",
    },
    {
      value: "repository:deleted",
      name: "Existing repository gets deleted",
    },
    {
      value: "branch:created",
      name: "New branch created",
    },
    {
      value: "branch:deleted",
      name: "Existing branch deleted",
    },
  ],

  "Ticketing": [
    {
      value: "ticket:created",
      name: "New ticket created",
    },
    {
      value: "ticket:updated",
      name: "Ticket gets updated",
    },
    {
      value: "ticket:deleted",
      name: "Existing ticket gets deleted",
    },
  ],

  "Packages & Container registry": [
    {
      value: "artifact:created",
      name: "New artifact created",
    },
    {
      value: "artifact:deleted",
      name: "Existing artifact gets deleted",
    },
  ],

  "Incident Management": [
    {
      value: "incident:created",
      name: "New incident created",
    },
    {
      value: "incident:deleted",
      name: "Existing incident gets deleted",
    },
  ],
};
export default eventConfiguration;
