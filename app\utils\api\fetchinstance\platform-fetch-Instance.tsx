/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from 'axios';
import { BASE_URL_S, getFetchConfigsHeaders } from 'lib/fetch';
// TODO: Uncomment when backend session endpoints are ready
// import { setupSessionInterceptors } from '../sessionInterceptor';

const platformfetchInstance = axios.create({
  baseURL: BASE_URL_S.PLATFORM,
  headers: {
    ...getFetchConfigsHeaders()
  },
  // TODO: Enable when using session management
  // withCredentials: true // Enable cookie support
});

// TODO: Uncomment when backend session endpoints are ready
// Setup session interceptors for automatic token refresh
// setupSessionInterceptors(platformfetchInstance, () => {
//   // Custom session expired handler
//   const currentPath = window.location.pathname + window.location.search;
//   window.location.href = `/login?sessionExpired=true&returnUrl=${encodeURIComponent(currentPath)}`;
// });

// Additional request interceptor for org ID
platformfetchInstance.interceptors.request.use((config) => {
  config.headers['organizationid'] = window.authUserOrgId;
  return config;
});

// Additional response interceptor for specific status codes
platformfetchInstance.interceptors.response.use(
  (response: any) => {
    if (response.status === 302) {
      const newLocation = response.headers.get("location");
      window.location.href = newLocation;
    } else {
      return response;
    }
  },
  (error) => {
    // 403 Forbidden - handled separately from 401
    if (error?.response?.status === 403) {
      return window.location.href = "/registration/unAuthorized";
    }
    // TODO: Remove this when session interceptor is enabled
    if (error?.response?.status === 401) {
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);

const axiosUtils = {
  CancelToken: axios.CancelToken,
  isCancel: axios.isCancel,
};

export { axiosUtils, };

export default platformfetchInstance;
