import {
   useNavigation,
} from "@remix-run/react";
import navigation from 'menu-items/index';

import { Typography } from '@mui/material';
import { Link, useLocation } from '@remix-run/react';

import { ReactElement, useEffect, useMemo, useState } from 'react';
import useCustomBreadcrumbs from 'store/custom-breadcrumbs';


type MainType = {
   id: string,
   title: string
   type: 'group' | 'collapse' | 'item',
   children: ItemType[],
   hideForSuperAdmin: true
}

type ItemType = {
   id: string,
   title: string,
   type: string
   url: string
   icon?: ReactElement
   hideForSuperAdmin?: boolean
}

export const useBreadcrumbs = ({
   custom = false,
   separator,
}: any = {}) => {
   const { state: navigationState } = useNavigation();
   const location = useLocation();
   const [main, setMain] = useState<MainType | null>(null);
   const [item, setItem] = useState();

   const {  breadcrumbs } = useCustomBreadcrumbs()

   let customLocation = location.pathname;


   // set active item state
   const getCollapse = (menu: any) => {
      if (!custom && menu.children) {
         menu.children.filter((collapse: any) => {
            if (collapse.type && collapse.type === 'collapse') {
               getCollapse(collapse);
               if (collapse.url === customLocation) {
                  setMain(collapse);
                  setItem(collapse);
               }
            } else if (collapse.type && collapse.type === 'item') {
               if (customLocation === collapse.url) {
                  setMain(menu);
                  setItem(collapse);
               }
            }
            return false;
         });
      }
   };

   let mainContent;

   if (main && main.type === 'collapse') {
      mainContent = (
         <Typography component={Link} to={document.location.pathname} variant="h6" sx={{ textDecoration: 'none' }} color="textSecondary">
            {main.title}
         </Typography>
      );
   }

   const SeparatorIcon = separator;
   const separatorIcon = separator ? <SeparatorIcon style={{ fontSize: '0.75rem', marginTop: 2 }} /> : '/';

   useEffect(() => {
      navigation.items?.map((menu: any) => {
         if (menu.type && menu.type === 'group') {
            if (menu?.url && menu.url === customLocation) {
               setMain(menu);
               setItem(menu);
            } else {
               getCollapse(menu);
            }
         }
         return false;
      });
   }, [customLocation]);

   const overWrittenItem = useMemo(() => {
      return breadcrumbs ?? item
   }, [item, breadcrumbs])

   return {
      item: overWrittenItem,
      mainContent,
      main,
      separatorIcon,
      customBreadcrumbs: breadcrumbs
   }
}