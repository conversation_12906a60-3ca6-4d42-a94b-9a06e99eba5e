/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from "zustand";
import _ from 'lodash';

import { organizationClient } from "services/organization.service";
import { DOMAIN_S } from "hooks/useServiceProfile";

import { Subscription } from "types/subscription";
import { User } from "types/user";

import { userClient } from "../services/user.service";
import { Category } from "types/category";
import { State } from "hooks/useStatus";
import { AxiosResponse } from "axios";
import { Organization } from "types/common";
import { FALLBACK_ORG_ID } from "config";
import Environment from "types/environment";
import { podClient } from "services/enviroinment";
import { getEnvironmentId, setEnvironmentId } from "utils/environment-id";
import { getIsEnvDefault } from "sections/environments/utils";

interface UserTypes {
   user: User | null
   setUser: (user: User) => void;
   isLoadingEnvironments: boolean,
   setIsLoadingEnvironments: (isLoading: boolean) => void
   environments: Environment.Root[]

   isLoading: boolean
   isSupeAdmin: boolean

   subscriptions: Subscription[]
   isSubscriptionLoading: boolean

   fetchUser: (id: string) => void,
   fetchSubscriptions: (id: string) => void

   categories: Category[]
}

const useUserDetails = create<UserTypes>((set, get) => ({
   isLoadingEnvironments: true,
   setIsLoadingEnvironments(isLoading) {
      set((prevState: any) => {
         return { ...prevState, isLoading }
      });
   },
   user: null,
   setUser: (user) => {
      set((prevState: any) => {
         return {
            ...prevState,
            user: {
               ...user,
               organization: prevState?.user?.organization
            }
         }
      });
   },
   isLoading: true,
   subscriptions: [],
   isSubscriptionLoading: true,
   fetchUser: async (id: string) => {

      let { user, isSupeAdmin, } = get();
      const { fetchSubscriptions } = get();
      let environments: Environment.Root[] = [];

      try {
         const { data, headers }: AxiosResponse<User> = await userClient.getUser(id);
         const environmentId = getEnvironmentId();

         user = data;
         /**
         * setting the isSuper admin value into the window variable
         */
         isSupeAdmin = headers?.['issuperadmin'];
         window.isSuperAdmin = isSupeAdmin;

         const organizationId = data?.organization?.id;
         /**
          * setting the window variable once organization is available
          */
         if (data?.organization?.id) {
            window.authUserOrgId = organizationId;

            if (!([FALLBACK_ORG_ID].includes(organizationId))) {
               try {
                  const { data: organization }: AxiosResponse<Organization> = await organizationClient.getOrganizationById(organizationId)
                  user = { ...user, organization } as User;

                  /**
                   * once org id is valid lets pull the environments
                   */
                  const response = await podClient.getAllEnviroinment({ params: { limit: 50, offset: 0, state: State.ACTIVE } });
                  environments = (response?.data?.data ?? []) as Environment.Root[];

                  if (environments?.length) {
                     if (!environmentId) {
                        const defaultEnvironment = environments?.find(getIsEnvDefault);
                        setEnvironmentId(defaultEnvironment?.id as string);
                     }
                  };

               } catch (error) {
                  console.error(error);
               }
               fetchSubscriptions(organizationId)
            }
         } else {
            console.error(`Organization is not available for user ${id}`)
         }

      } catch (error) {
         console.error(error);
      } finally {
         set({ isSupeAdmin: isSupeAdmin ?? false, user, isLoading: false, environments, isLoadingEnvironments: false });
      }
   },
   isSupeAdmin: false,
   fetchSubscriptions: async (id: string) => {
      try {
         const payload = {
            "filter": {
               "and": [
                  {
                     "property": "/state",
                     "operator": "=",
                     "values": [
                        State.ACTIVE
                     ]
                  },
                  {
                     "property": "/organization/id",
                     "operator": "=",
                     "values": [id]
                  }
               ]
            },
            "sort": [
               {
                  "property": "/displayOrder",
                  "direction": "ASC"
               }
            ],
            "pagination": {
               "limit": 100,
               "offset": 0
            }
         }
         const { data } = await userClient.searchSubscriptions(payload);

         const subscriptions: Subscription[] = data?.data ?? [],
            subsKeys = _.map(subscriptions, (i) => _.get(i, 'productKey'));

         const categories: any = _.map(DOMAIN_S, (item) => {
            if (!_.includes(subsKeys, _.get(item, 'key'))) {
               // disabling if user org have not category
               return { ...item, disabled: true };
            }
            return item;
         }) ?? [];

         set({
            ...get(),
            subscriptions,
            categories,
            isSubscriptionLoading: false
         });
      } catch (error) {
         console.error(error)
      }
   },
   environments: [],
   categories: [],
}));

export default useUserDetails;