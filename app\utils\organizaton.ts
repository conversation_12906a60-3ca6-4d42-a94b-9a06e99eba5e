import { FALLBACK_ORG_ID } from "config";
import mockUser from '../mock.json';

import { Organization } from "types/common";
import { LoaderFunctionArgs } from "@remix-run/node";


export const getIsInValidOrganization = (organizationId: Organization['id'] | null) => {
   return !organizationId || [FALLBACK_ORG_ID].includes(organizationId);
}

export const parseOrganizationDetailsFromServerRequest = (request: LoaderFunctionArgs['request'], isLocal: boolean) => {
   const headers = request.headers;

   let userId = headers.get('authuserid');
   let orgId = headers.get('authuserorgid');

   if (isLocal) {
      userId = mockUser.id;
      orgId = mockUser?.organization?.id;
   };

   return { userId, orgId }
}