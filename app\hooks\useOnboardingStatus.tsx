import { useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "utils/api/api-endpoints";
import { tenantsClient } from "services/tenants.service";
import { organizationClient } from "services/organization.service";
import { TenantMetadata, OnboardingStepType } from "types/tenant-metadata";
import { useEffect, useMemo } from "react";

export interface OnboardingStatus {
  isLoading: boolean;
  isComplete: boolean;
  completedSteps: OnboardingStepType[];
  pendingSteps: OnboardingStepType[];
  totalSteps: number;
  progress: number;
  hasCategories: boolean;
  hasServices: boolean;
  hasWebhooks: boolean;
  hasTeamMembers: boolean;
  tenantMetadata?: TenantMetadata;
}

export const useOnboardingStatus = (orgId?: string) => {
  // Fetch tenant metadata to get onboarding steps configuration
  const { data: allTenantMetadata, isLoading: isMetadataLoading } = useQuery({
    queryKey: [API_ENDPOINTS.TENANT_META_DATA],
    queryFn: async () => {
      const response = await tenantsClient.getAllTenantMetadata();
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch organization configuration to check watches (webhooks)
  const { data: orgConfig, isLoading: isConfigLoading } = useQuery({
    queryKey: [API_ENDPOINTS.ORGANIZATION, orgId, 'config'],
    queryFn: async () => {
      const response = await organizationClient.getOrgConfig(orgId as string);
      return response.data;
    },
    enabled: !!orgId,
  });

  // Fetch organization services
  const { data: orgServices, isLoading: isServicesLoading } = useQuery({
    queryKey: [API_ENDPOINTS.ORGANIZATION, orgId, 'services'],
    queryFn: async () => {
      const payload = {
        filter: {
          and: [
            {
              property: "/state",
              operator: "=",
              values: ["ACTIVE"]
            },
            {
              property: "/organization/id",
              operator: "=",
              values: [orgId]
            }
          ]
        },
        pagination: {
          limit: 100,
          offset: 0
        }
      };
      const response = await organizationClient.searchOrgServices(payload);
      return response.data;
    },
    enabled: !!orgId,
  });

  // Fetch organization watches (webhooks)
  const { data: orgWatches, isLoading: isWatchesLoading } = useQuery({
    queryKey: [API_ENDPOINTS.ORGANIZATION, orgId, 'watches'],
    queryFn: async () => {
      const payload = {
        filter: {
          and: [
            {
              property: `/organization/id`,
              operator: "=",
              values: [orgId]
            }
          ]
        },
        pagination: {
          limit: 100,
          offset: 0
        }
      };
      const response = await organizationClient.searchOrganizationWatches(payload);
      return response.data;
    },
    enabled: !!orgId,
  });

  // TODO: Add team members check when user service supports organization-based filtering
  // For now, we'll assume team setup is optional

  const onboardingStatus: OnboardingStatus = useMemo(() => {
    const isLoading = isMetadataLoading || isConfigLoading || isServicesLoading || isWatchesLoading;
    
    // Get the first tenant metadata (usually there's only one)
    const tenantMetadata = Array.isArray(allTenantMetadata) ? allTenantMetadata[0] : allTenantMetadata;
    
    if (isLoading || !tenantMetadata) {
      return {
        isLoading,
        isComplete: false,
        completedSteps: [],
        pendingSteps: [],
        totalSteps: 0,
        progress: 0,
        hasCategories: false,
        hasServices: false,
        hasWebhooks: false,
        hasTeamMembers: false,
        tenantMetadata,
      };
    }

    // Extract service categories from active services
    const activeServices = orgServices?.data || [];
    const serviceCategories = [...new Set(activeServices.map((service: any) => service.category).filter(Boolean))];
    
    // Check what's completed
    const hasCategories = serviceCategories.length > 0;
    const hasServices = activeServices.length > 0;
    const hasWebhooks = (orgWatches?.data || []).length > 0;
    const hasTeamMembers = false; // TODO: Implement when API supports org-based user filtering
    
    // Determine completed steps based on actual data
    const completedSteps: OnboardingStepType[] = [];
    const pendingSteps: OnboardingStepType[] = [];
    
    // Map each onboarding step type to completion status
    const stepCompletionMap: Record<OnboardingStepType, boolean> = {
      [OnboardingStepType.SELECT_CATEGORY]: hasCategories,
      [OnboardingStepType.SELECT_PROVIDERS]: hasServices,
      [OnboardingStepType.SETUP_WEBHOOKS]: hasWebhooks,
      [OnboardingStepType.SELECT_MEMBERS]: hasTeamMembers,
      [OnboardingStepType.STARTUP_PROGRAM]: false, // This is typically optional
      [OnboardingStepType.GENERAL]: true, // General info is usually pre-filled
    };
    
    // Process onboarding steps from tenant metadata
    if (tenantMetadata?.onboardingSteps) {
      tenantMetadata.onboardingSteps.forEach((step) => {
        if (stepCompletionMap[step.type]) {
          completedSteps.push(step.type);
        } else {
          pendingSteps.push(step.type);
        }
      });
    }
    
    // Calculate progress
    const totalSteps = tenantMetadata?.onboardingSteps?.length || 0;
    const progress = totalSteps > 0 ? Math.round((completedSteps.length / totalSteps) * 100) : 0;
    
    // Check if core onboarding is complete (categories, services, webhooks)
    const isComplete = hasCategories && hasServices && hasWebhooks;
    
    return {
      isLoading: false,
      isComplete,
      completedSteps,
      pendingSteps,
      totalSteps,
      progress,
      hasCategories,
      hasServices,
      hasWebhooks,
      hasTeamMembers,
      tenantMetadata,
    };
  }, [
    isMetadataLoading,
    isConfigLoading,
    isServicesLoading,
    isWatchesLoading,
    allTenantMetadata,
    orgConfig,
    orgServices,
    orgWatches,
  ]);

  return onboardingStatus;
};

export default useOnboardingStatus;