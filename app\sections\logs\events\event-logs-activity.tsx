import React, { createContext, useContext, useMemo } from 'react';
import { Chip, Dialog, DialogContent, DialogProps, DialogTitle, Grid, Stack, Typography } from '@mui/material';
import { CloseOutlined } from '@ant-design/icons';
import { useStatus } from 'hooks/useStatus';
import Collapse from 'components/@extended/Accordian';
import moment from 'moment';
import { useGetLog } from 'hooks/api/log/useGetLog';
import { CodeBlock } from 'components/@extended/Codeblock';
import { DialogClose } from 'components/@extended/dialog';

interface EventLogsActivityProps extends DialogProps {
   selected?: Record<string, any>;
}

const LogDetailsContext = createContext<Record<string, any> | null>(null);

const LogDetailsProvider = ({ children, value }: Record<string, any>) => {
   return (
      <LogDetailsContext.Provider value={value}>
         {children}
      </LogDetailsContext.Provider>
   )
}

const useLogDetails = (): any => {
   try {
      return useContext(LogDetailsContext);
   } catch (error) {
      console.error('should wrapped under the <LogDetailsProvider />')
   }
}

export const EventLogsActivity = ({ onClose, ...props }: EventLogsActivityProps) => {

   const { selected, ...rest } = props;
   const requestId = selected?.id;
   const { resolveStatusColor } = useStatus()

   return (
      <LogDetailsProvider
         value={{ logDetails: selected }}>
         <Dialog
            {...rest}
            fullScreen
            onClose={onClose}
         >

            <DialogTitle component={'div'}>
               <Stack justifyContent={'space-between'} direction={'row'}>
                  <Stack alignItems={'flex-start'} gap={1}>
                     <Typography variant='h5' className="font-semibold">
                        Request Id: {requestId}
                     </Typography>
                     <Chip
                        size='small'
                        variant='outlined'
                        color={resolveStatusColor(selected?.operation?.type)}
                        label={`${selected?.operation?.statusCode} ${selected?.operation?.statusMessage}`}
                     />
                  </Stack>
               </Stack>
            </DialogTitle>
            <DialogClose onClose={onClose} />
            <DialogContent dividers >
               <Overview />
            </DialogContent>
         </Dialog>
      </LogDetailsProvider>
   )
}

const Summary = () => {

   const { logDetails } = useLogDetails();

   let durationInSeconds;

   const getDateTime: { startDateTime: string, endDateTime: string, durationInSeconds: any } = useMemo(() => {
      let time: any;
      if (logDetails && logDetails.changeLog && Object.keys(logDetails.changeLog).length == 2) {
         let { changeLog: { startDateTime, endDateTime } } = logDetails;
         durationInSeconds = moment.duration(moment(endDateTime).diff(moment(startDateTime), 'milliseconds')).asMilliseconds();
         startDateTime = moment(startDateTime).format('LLL') as any;
         endDateTime = moment(endDateTime).format('LLL') as any
         time = {
            ...time,
            startDateTime, endDateTime, durationInSeconds
         }
         return time;
      }

   }, [logDetails]);

   return (
      <Grid container columnSpacing={3}>
         <Grid item xs={6}>
            <Typography variant='h6' className="font-semibold">Resource Details</Typography>
            <Stack>
               <Grid container mt={2} rowGap={2}>
                  <Grid item xs={3}>
                     <Typography variant='body1'>
                        Event Type:
                     </Typography>
                  </Grid>
                  <Grid item xs={9}>
                     <Chip label={logDetails?.name} size="small" variant='outlined' color='primary' />
                  </Grid>
                  <Grid item xs={3}>
                     <Typography variant='body1'>
                        Name:
                     </Typography>
                  </Grid>
                  <Grid item xs={9}>
                     <Typography variant='body1'>
                        {'Watch'}
                     </Typography>
                  </Grid>
                  <Grid item xs={3}>
                     <Typography variant='body1'>
                        Action:
                     </Typography>
                  </Grid>
                  <Grid item xs={9}>
                     <Typography variant='body1'>
                        {logDetails?.eventActionType}
                     </Typography>
                  </Grid>
                  <Grid item xs={3}>
                     <Typography variant='body1'>
                        End Point:
                     </Typography>
                  </Grid>
                  <Grid item xs={9}>
                     <Typography variant='body1' sx={{ wordWrap: 'break-word' }} >
                        {logDetails?.url}
                     </Typography>
                  </Grid>
               </Grid>
            </Stack>
         </Grid>
         <Grid item xs={6}>
            <Typography variant='h6' className="font-semibold">Start Date Time & End Date Time</Typography>
            <Stack>
               <Grid container mt={2} rowGap={2}>
                  <Grid item xs={3}>
                     <Typography variant='body1'>
                        Start Date Time:
                     </Typography>
                  </Grid>
                  <Grid item xs={9}>
                     <Typography variant='body1'>
                        {getDateTime?.startDateTime ? getDateTime?.startDateTime : "-"}
                     </Typography>
                  </Grid>
                  <Grid item xs={3}>
                     <Typography variant='body1'>
                        End Date Time:
                     </Typography>
                  </Grid>
                  <Grid item xs={9}>
                     <Typography variant='body1'>
                        {getDateTime?.endDateTime ? getDateTime.endDateTime : "-"}
                     </Typography>
                  </Grid>
                  <Grid item xs={3}>
                     <Typography variant='body1'>
                        Duration:
                     </Typography>
                  </Grid>
                  <Grid item xs={9}>
                     <Typography variant='body1'>
                        {(getDateTime?.durationInSeconds ? `${getDateTime.durationInSeconds}ms` : "-")}
                     </Typography>
                  </Grid>
               </Grid>
            </Stack>
         </Grid>
      </Grid>
   )
}

const InputResults = () => {

   const { logDetails } = useLogDetails(),
      { eventActivities } = useGetLog({ eventLogDetails: logDetails });

   return (
      <Grid container className="overflow-hidden" columnSpacing={2}>
         {eventActivities?.input.map(({ activityTaskStartedEventAttributes }: any) => {
            const { payloads } = activityTaskStartedEventAttributes?.input;
            return payloads?.map(({ data }: any, index: number) => {
               return (
                  <Grid item xs={6} key={index}>
                     <CodeBlock>
                        {JSON.stringify(JSON.parse(atob(data)), null, 2)}
                     </CodeBlock>
                  </Grid>
               )
            })
         })}
         {eventActivities?.output.map(({ activityTaskCompletedEventAttributes }: any) => {
            const { payloads } = activityTaskCompletedEventAttributes?.result;
            return payloads?.map(({ data }: any, index: number) => {
               return (
                  <Grid item xs={6} key={index}>
                     <CodeBlock>
                        {JSON.stringify(JSON.parse(atob(data)), null, 2)}
                     </CodeBlock>
                  </Grid>
               )
            })
         })}
      </Grid>
   )
}

const Overview = () => {
   return (
      <Stack gap={2}>
         <Collapse
            defaultExpanded={["summary", 'input_results']}
            items={[
               {
                  title: 'Summary',
                  value: 'summary',
                  children: <Summary />
               },
               {
                  title: 'Input and Results',
                  value: 'input_results',
                  children: <InputResults />
               }
            ]}
         />
      </Stack>
   )
}