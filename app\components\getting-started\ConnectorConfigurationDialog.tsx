import React, { useState, useEffect, useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Typography,
  Button,
  TextField,
  Box,
  CircularProgress,
  Link,
  useTheme,
  IconButton,
  Avatar,
  alpha,
} from '@mui/material';
import { X } from 'lucide-react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useGetAccessPoint, AccessPointConfigType } from 'hooks/api/use-accesspoint-config/useGetAccessPoint';
import { toast } from 'sonner';

interface ConnectorConfigurationDialogProps {
  open: boolean;
  onClose: () => void;
  connector: {
    id: string;
    name: string;
    icon: string;
  };
  accessPoint: any;
  onSuccess?: () => void;
  serviceId: string;
}

// Get the configuration container based on access point type
const getAccessPointTypeContainer = (accessPoint: any) => {
  switch (accessPoint?.type) {
    case AccessPointConfigType.AppFlow:
      return accessPoint?.appConfig?.[0];
    case AccessPointConfigType.OAuthFlow:
      return accessPoint?.oAuthConfig?.[0];
    case AccessPointConfigType.APIKeyFlow:
      return accessPoint?.apiKeyConfig;
    default:
      console.warn(`No Container found for ${accessPoint?.type}`);
      return null;
  }
};

// Define the form schema based on the accessPoint type
const createFormSchema = (segments: any[]) => {
  const schemaObj: Record<string, any> = {};
  
  segments.forEach((segment) => {
    const segmentSchema: Record<string, any> = {};
    
    segment.fieldTypeConfigs?.forEach((config: any) => {
      if (config.type === 'FILE') {
        // Skip file fields in validation for now
        segmentSchema[config.property] = z.any().optional();
      } else if (config.required) {
        segmentSchema[config.property] = z.string().nonempty(`${config.label} is required`);
        
        // Add regex validation if present
        if (config.validations) {
          config.validations.forEach((validation: any) => {
            if (validation.type === 'regex') {
              segmentSchema[config.property] = segmentSchema[config.property].regex(
                new RegExp(validation.value),
                validation.errorMessage
              );
            }
          });
        }
      } else {
        segmentSchema[config.property] = z.string().optional();
      }
    });
    
    schemaObj[segment.key] = z.object(segmentSchema);
  });
  
  return z.object(schemaObj);
};

export default function ConnectorConfigurationDialog({
  open,
  onClose,
  connector,
  accessPoint,
  onSuccess,
  serviceId,
}: ConnectorConfigurationDialogProps) {
  const theme = useTheme();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get the configuration container
  const configContainer = useMemo(() => {
    return getAccessPointTypeContainer(accessPoint);
  }, [accessPoint]);

  // Get segments from the configuration container
  const segments = useMemo(() => {
    return configContainer?.segments || [];
  }, [configContainer]);

  // Create form schema
  const formSchema = useMemo(() => {
    if (!segments.length) return z.object({});
    return createFormSchema(segments);
  }, [segments]);

  // Create default values
  const defaultValues = useMemo(() => {
    const values: Record<string, any> = {};
    segments.forEach((segment: any) => {
      values[segment.key] = {};
      segment.fieldTypeConfigs?.forEach((config: any) => {
        // Extract the property name without slashes
        const propertyName = config.property.split('/').pop() || config.property;
        values[segment.key][propertyName] = '';
      });
    });
    return values;
  }, [segments]);

  // Initialize form
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const { attemptUpdateAccessPoints } = useGetAccessPoint({
    serviceId: serviceId,
    serviceProfileId: connector.id,
  });

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      reset(defaultValues);
    }
  }, [open, reset, defaultValues]);

  const onSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      // Flatten nested form data and create operations
      const operations: any[] = [];
      
      Object.entries(data).forEach(([segmentKey, segmentData]) => {
        Object.entries(segmentData as Record<string, any>).forEach(([property, value]) => {
          // Find the original property path from the config
          const field = segments
            .flatMap((s: any) => s.fieldTypeConfigs || [])
            .find((f: any) => f.property.endsWith(property));
          
          if (field && value) {
            operations.push({
              op: 'replace',
              path: field.property,
              value,
            });
          }
        });
      });

      const formData = new FormData();
      formData.append('operations', JSON.stringify(operations));

      await attemptUpdateAccessPoints(
        serviceId,
        accessPoint.id,
        formData,
        () => {
          toast.success('Configuration saved successfully');
          onSuccess?.();
          onClose();
        }
      );
    } catch (error) {
      toast.error('Failed to save configuration');
      console.error('Configuration error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get authentication guide URL based on connector
  const getAuthGuideUrl = (connectorName: string) => {
    const guideUrls: Record<string, string> = {
      'GitHub': 'https://docs.unizo.ai/guides/github-auth',
      'GitLab': 'https://docs.unizo.ai/guides/gitlab-auth',
      'Bitbucket': 'https://docs.unizo.ai/guides/bitbucket-auth',
      'Google Workspace': 'https://docs.unizo.ai/guides/google-workspace-auth',
    };
    return guideUrls[connectorName] || 'https://docs.unizo.ai/guides/';
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: `1px solid ${theme.palette.divider}`,
          pb: 2,
        }}
      >
        <Stack direction="row" spacing={2} alignItems="center">
          <Avatar
            src={connector.icon}
            sx={{
              width: 40,
              height: 40,
              backgroundColor: theme.palette.background.default,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            {connector.name.charAt(0)}
          </Avatar>
          <Typography variant="h5" fontWeight={600}>
            Configure {accessPoint?.label || connector.name}
          </Typography>
        </Stack>
        <IconButton onClick={onClose} size="small">
          <X size={20} />
        </IconButton>
      </DialogTitle>

      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent sx={{ pt: 3 }}>
          <Stack spacing={3}>
            {/* Header Section */}
            <Box>
              <Stack direction="row" alignItems="center" gap={1}>
                <Typography variant="h5" fontWeight={600}>
                  {accessPoint?.label || `${connector.name} Configuration`}
                </Typography>
              </Stack>
              <Typography variant="h6" color="text.secondary" sx={{ mt: 0.5 }}>
                {accessPoint?.description || `Configure authentication for ${connector.name}`}
              </Typography>
              {configContainer?.links?.[0] && (
                <Link
                  href={configContainer.links[0].href}
                  target="_blank"
                  sx={{
                    mt: 1,
                    display: 'inline-block',
                    color: theme.palette.primary.main,
                    textDecoration: 'none',
                    fontSize: '0.875rem',
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  {configContainer.links[0].name || 'Learn more about configuration'}
                </Link>
              )}
            </Box>

            {/* Form Fields */}
            {segments.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography color="text.secondary">
                  No configuration fields available
                </Typography>
              </Box>
            ) : (
              <Stack spacing={3}>
                {segments.map((segment: any) => (
                  <Box key={segment.key}>
                    {segment.label && (
                      <Typography
                        variant="subtitle1"
                        fontWeight={600}
                        sx={{ mb: 2 }}
                      >
                        {segment.label}
                      </Typography>
                    )}
                    <Stack spacing={2}>
                      {segment.fieldTypeConfigs?.map((field: any) => {
                        const propertyName = field.property.split('/').pop() || field.property;
                        const fieldError = errors[segment.key]?.[propertyName];
                        
                        // Skip button type fields and file fields for now
                        if (field.type === 'BUTTON' || field.type === 'FILE') {
                          return null;
                        }
                        
                        return (
                          <Box key={field.property}>
                            <Typography
                              variant="h6"
                              sx={{
                                fontSize: '0.875rem',
                                fontWeight: 600,
                                mb: 0.5,
                              }}
                            >
                              {field.label}
                              {field.required && (
                                <Typography
                                  component="span"
                                  sx={{
                                    color: theme.palette.error.main,
                                    ml: 0.5,
                                  }}
                                >
                                  *
                                </Typography>
                              )}
                            </Typography>
                            {field.description && (
                              <Typography
                                variant="caption"
                                color="text.secondary"
                                sx={{ display: 'block', mb: 1 }}
                              >
                                {field.description}
                              </Typography>
                            )}
                            <Controller
                              name={`${segment.key}.${propertyName}`}
                              control={control}
                              render={({ field: { onChange, value } }) => (
                                <TextField
                                  fullWidth
                                  value={value || ''}
                                  onChange={onChange}
                                  placeholder={field.placeholder || `Enter ${field.label}`}
                                  error={!!fieldError}
                                  helperText={fieldError?.message}
                                  size="medium"
                                  type={field.property.includes('Secret') || field.property.includes('password') ? 'password' : 'text'}
                                  sx={{
                                    '& .MuiOutlinedInput-root': {
                                      backgroundColor: theme.palette.background.paper,
                                    },
                                  }}
                                />
                              )}
                            />
                          </Box>
                        );
                      })}
                    </Stack>
                  </Box>
                ))}
              </Stack>
            )}
          </Stack>
        </DialogContent>

        <DialogActions
          sx={{
            borderTop: `1px solid ${theme.palette.divider}`,
            px: 3,
            py: 2,
          }}
        >
          <Button
            onClick={onClose}
            disabled={isSubmitting}
            sx={{
              textTransform: 'none',
              color: theme.palette.text.primary,
            }}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
            sx={{
              textTransform: 'none',
              backgroundColor: theme.palette.grey[900],
              color: theme.palette.common.white,
              '&:hover': {
                backgroundColor: theme.palette.grey[800],
              },
            }}
          >
            {isSubmitting ? (
              <>
                <CircularProgress size={20} sx={{ mr: 1 }} color="inherit" />
                Saving...
              </>
            ) : (
              'Submit'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}