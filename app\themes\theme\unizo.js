// ==============================|| PRESET THEME - UNIZO BRAND ||============================== //

export default function UnizoTheme(colors, mode) {
  const { grey } = colors;
  const isDark = mode === 'dark';
  
  // Orange/Tertiary color palette
  const tertiaryPalette = {
    50: '#fef5e7',
    100: '#fde6c4',
    200: '#fcd59b',
    300: '#fac372',
    400: '#f8a84f',
    500: '#f27013', // Main orange color
    600: '#e45a00',
    700: '#c94a00',
    800: '#a33c00',
    900: '#7d2e00',
    950: '#5a2100'
  };
  
  const greyColors = {
    0: grey[0],
    50: grey[1],
    100: grey[2],
    200: grey[3],
    300: grey[4],
    400: grey[5],
    500: grey[6],
    600: grey[7],
    700: grey[8],
    800: grey[9],
    900: grey[10],
    A50: grey[15],
    A100: grey[11],
    A200: grey[12],
    A400: grey[13],
    A700: grey[14],
    A800: grey[16]
  };
  const contrastText = '#fff';

  // Adjust colors based on mode
  const primaryPalette = isDark ? {
    50: '#0a111a',
    100: '#0f1924',
    200: '#1a2b3d',
    300: '#2a4765',
    400: '#21384f',
    500: '#4a6584',
    600: '#6f99c3',
    700: '#a9c2db',
    800: '#d4e0ed',
    900: '#edf2f7',
    950: '#f8fafc'
  } : {
    50: '#f8fafc',
    100: '#edf2f7',
    200: '#d4e0ed',
    300: '#a9c2db',
    400: '#6f99c3',
    500: '#4a6584',
    600: '#21384f',
    700: '#2a4765',
    800: '#1a2b3d',
    900: '#0f1924',
    950: '#0a111a'
  };

  const secondaryPalette = isDark ? {
    50: '#0a0a0a',
    100: '#171717',
    200: '#262626',
    300: '#404040',
    400: '#525252',
    500: '#ffffff',  // White for dark mode
    600: '#f5f5f5',
    700: '#e5e5e5',
    800: '#d4d4d4',
    900: '#fafafa'
  } : {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#000000',  // Black for light mode
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717'
  };

  return {
    primary: {
      lighter: primaryPalette[50],
      100: primaryPalette[100],
      200: primaryPalette[200],
      light: primaryPalette[300],
      400: primaryPalette[400],
      main: primaryPalette[600],
      600: primaryPalette[600],
      dark: primaryPalette[800],
      700: primaryPalette[700],
      darker: primaryPalette[900],
      900: primaryPalette[900],
      contrastText
    },
    secondary: {
      lighter: secondaryPalette[50],
      100: secondaryPalette[100],
      200: secondaryPalette[200],
      light: secondaryPalette[300],
      400: secondaryPalette[400],
      main: isDark ? 'rgb(255, 255, 255)' : 'rgb(0, 0, 0)',
      600: secondaryPalette[600],
      dark: secondaryPalette[700],
      800: secondaryPalette[800],
      darker: secondaryPalette[900],
      A100: greyColors[0],
      A200: greyColors.A400,
      A300: greyColors.A700,
      contrastText
    },
    error: {
      lighter: '#fee2e2',
      light: '#fca5a5',
      main: '#ef4444',
      dark: '#dc2626',
      darker: '#b91c1c',
      contrastText
    },
    warning: {
      lighter: '#fef3c7',
      light: '#fde68a',
      main: '#f59e0b',
      dark: '#d97706',
      darker: '#b45309',
      contrastText
    },
    info: {
      lighter: '#dbeafe',
      light: '#93c5fd',
      main: '#3b82f6',
      dark: '#2563eb',
      darker: '#1d4ed8',
      contrastText
    },
    success: {
      lighter: '#d1fae5',
      light: '#6ee7b7',
      main: '#10b981',
      dark: '#059669',
      darker: '#047857',
      contrastText
    },
    grey: greyColors,
    tertiary: {
      lighter: tertiaryPalette[50],
      100: tertiaryPalette[100],
      200: tertiaryPalette[200],
      light: tertiaryPalette[300],
      400: tertiaryPalette[400],
      main: 'rgb(242, 112, 19)',
      600: tertiaryPalette[600],
      dark: tertiaryPalette[700],
      800: tertiaryPalette[800],
      darker: tertiaryPalette[900],
      contrastText: '#fff'
    }
  };
}