# Creative Array-Object Mapping 🚀

## Overview

This enhanced field mapping system provides creative and flexible ways to handle arrays containing objects. Instead of treating arrays as simple collections, our system offers multiple mapping strategies to handle different real-world scenarios.

## 🎯 Key Features

### 1. **Multiple Array Access Patterns**
- **Generic Item (`[item]`)**: Map all array elements uniformly
- **Specific Index (`[0]`, `[1]`, `[2]`)**: Target specific positions with special meaning
- **Dynamic Index (`[*]`)**: Runtime-flexible mapping for any array position

### 2. **Visual Indicators**
- 📦 **Generic items** - Purple indicators for standard array elements
- 🔢 **Indexed items** - Orange indicators for position-specific elements  
- ⚡ **Dynamic items** - Pink indicators with pulse animation for runtime mapping

### 3. **Smart Field Processing**
- Automatically detects array-object structures in OpenAPI specs
- Generates multiple mapping options for each array
- Preserves nested object hierarchies within array items

## 🛠️ Implementation

### OpenAPI Processor Enhancement

```typescript
// Enhanced array processing in openapi-processor.ts
if (itemSchema.type === 'object' && (itemSchema.properties || itemSchema.$ref)) {
  // Create multiple mapping options for array objects
  
  // 1. Generic item container
  const itemField = this.processSchemaDefinition(itemSchema, 'item', currentPath, processedRefs);
  itemField.isArrayItem = true;
  itemField.arrayParent = fieldName;
  
  // 2. Indexed examples (first 3 items)
  for (let i = 0; i < 3; i++) {
    const indexedField = this.processSchemaDefinition(itemSchema, `[${i}]`, currentPath, processedRefs);
    indexedField.isArrayItem = true;
    indexedField.arrayIndex = i;
  }
  
  // 3. Dynamic index option
  const dynamicField = this.processSchemaDefinition(itemSchema, '[*]', currentPath, processedRefs);
  dynamicField.isDynamicIndex = true;
}
```

### UI Component Enhancements

```typescript
// Enhanced rendering with array-specific styling
{option.isArrayItem && (
  <Chip 
    size="small" 
    label={getArrayItemBadge(option)}
    sx={{ 
      backgroundColor: alpha(ARRAY_ITEM_COLORS[getArrayItemType(option)], 0.1),
      color: ARRAY_ITEM_COLORS[getArrayItemType(option)],
      fontFamily: 'monospace'
    }}
  />
)}
```

## 💡 Use Cases & Examples

### 1. Team Member Management
```typescript
// Source: GitHub Contributors Array
contributors: [
  { login: "john", id: 123, contributions: 50, role: "maintainer" },
  { login: "jane", id: 456, contributions: 30, role: "contributor" },
  { login: "bob", id: 789, contributions: 10, role: "contributor" }
]

// Mapping Options:
contributors.item.login → team_members[].username        // All contributors
contributors[0].login → primary_maintainer.username     // First contributor (maintainer)
contributors[1].login → secondary_maintainer.username   // Second contributor
contributors[*].login → dynamic_team_member.username    // Runtime-selected contributor
```

### 2. Webhook Configuration
```typescript
// Source: Webhook Events Array
webhooks: [{
  id: "hook_1",
  events: ["push", "pull_request", "issues"],
  config: { content_type: "json", secret: "***" }
}]

// Mapping Options:
webhooks.item.events[0] → integrations[].primary_event     // First event for each webhook
webhooks[0].events.item → primary_webhook.all_events       // All events from first webhook
webhooks[*].config.secret → webhook_configs[*].auth_token  // Dynamic webhook secrets
```

### 3. Environment Protection Rules
```typescript
// Source: Complex nested array-object structure
environments: [{
  name: "production",
  protection_rules: [
    { type: "required_reviewers", parameters: { count: 2 } },
    { type: "wait_timer", parameters: { minutes: 5 } }
  ]
}]

// Mapping Options:
environments.item.protection_rules.item.type → env_policies[].rule_type
environments[0].protection_rules[0].type → prod_env.primary_protection
environments[*].protection_rules[*].parameters → dynamic_rule_config
```

## 🎨 Visual Design Elements

### Array Item Badges
- **`[item]`** - Standard array element access
- **`[0]`, `[1]`, `[2]`** - Specific index access with numbered badges
- **`[*]`** - Dynamic index with animated glow effect

### Color Coding
- **Purple (#8b5cf6)** - Generic array items
- **Orange (#f59e0b)** - Indexed array items  
- **Pink (#ec4899)** - Dynamic array items

### Helper Information
- Contextual tooltips explaining array mapping options
- Visual connection lines showing parent-child relationships
- Smart suggestions based on array content types

## 🔧 Technical Benefits

### 1. **Flexibility**
- Handle arrays of any size without predefined limits
- Support both static and dynamic mapping scenarios
- Accommodate different data access patterns

### 2. **User Experience**
- Intuitive visual indicators for different array access types
- Clear labeling and descriptions for each mapping option
- Progressive disclosure of array item structures

### 3. **Developer Efficiency**
- Automatic generation of multiple mapping options
- Type-safe handling of array-object relationships
- Extensible framework for new array access patterns

## 🚀 Advanced Scenarios

### Conditional Array Mapping
```typescript
// Map different array elements based on conditions
contributors[0] → lead_developer           // First = lead
contributors[1] → senior_developer         // Second = senior  
contributors[*] → team_members[*]          // Rest = team members
```

### Cross-Array Relationships
```typescript
// Map related elements across different arrays
webhooks[0].events[0] → primary_integration.trigger_event
webhooks[0].config.secret → primary_integration.auth_token
```

### Nested Array Processing
```typescript
// Handle arrays within arrays
repository.environments[*].protection_rules[*] → security_policies[*][*]
```

## 📊 Testing

Use the test function to see array-object mapping in action:

```typescript
// In browser console
import { testArrayObjectMapping } from './test-array-mapping';
testArrayObjectMapping();
```

This will display:
- Available array fields with object content
- Generated mapping options for each array
- Visual indicators and badges for different access patterns
- Example use cases and benefits

## 🔮 Future Enhancements

1. **Smart Array Suggestions**: Auto-suggest mappings based on array content analysis
2. **Array Transformation Functions**: Built-in functions for array filtering, sorting, and transformation
3. **Conditional Array Mapping**: Map different array elements based on runtime conditions
4. **Array Validation Rules**: Ensure array mappings meet business requirements
5. **Performance Optimization**: Lazy loading for large arrays with thousands of elements

---

This creative array-object mapping system transforms how users interact with complex data structures, making array mapping intuitive, flexible, and powerful! 🎉