import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useMemo } from 'react';
import { toast } from 'sonner';
import { 
  TenantType, 
  TenantConfiguration, 
  QuickStartProgress, 
  QuickStartStatusResponse,
  QuickStartSaveRequest,
  QuickStartSaveResponse,
  QuickStartCompletionData,
  TENANT_CONFIGURATIONS,
  ServiceConfiguration
} from 'types/quick-start';
import fetchInstance from 'utils/api/fetchinstance';
import platformFetchInstance from 'utils/api/fetchinstance/platform-fetch-Instance';
import useUserDetails from 'store/user';
import { organizationClient } from 'services/organization.service';
import { API_ENDPOINTS } from 'utils/api/api-endpoints';

/**
 * Service for managing tenant quick-start configurations
 */

// Query keys
const QUERY_KEYS = {
  quickStartStatus: ['quick-start', 'status'],
  quickStartProgress: ['quick-start', 'progress'],
  tenantConfiguration: ['tenant', 'configuration'],
} as const;

/**
 * Get current quick-start status for the authenticated user
 */
export const useQuickStartStatus = () => {
  const { user, isLoading: isUserLoading } = useUserDetails();
  const organizationId = user?.organization?.id;

  return useQuery({
    queryKey: [...QUERY_KEYS.quickStartStatus, organizationId],
    queryFn: async (): Promise<QuickStartStatusResponse> => {
      // For now, always return that quick start is needed
      // This bypasses the tenant metadata check
      return {
        needsQuickStart: true,
        isCompleted: false,
        currentStep: 0
      };
    },
    enabled: !!organizationId && !isUserLoading,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
  });
};

/**
 * Get detailed quick-start progress
 */
export const useQuickStartProgress = (enabled = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.quickStartProgress,
    queryFn: async (): Promise<QuickStartProgress> => {
      const response = await fetchInstance.get<QuickStartProgress>('/quick-start/progress');
      return response.data;
    },
    enabled,
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

/**
 * Save quick-start progress
 */
export const useSaveQuickStartProgress = () => {
  const queryClient = useQueryClient();
  const { user } = useUserDetails();
  const organizationId = user?.organization?.id;

  return useMutation({
    mutationFn: async (request: QuickStartSaveRequest): Promise<QuickStartSaveResponse> => {
      if (!organizationId) {
        throw new Error('No organization found');
      }

      // Skip API call for now, just return success
      console.log('Saving quick-start progress:', {
        organizationId,
        currentStep: request.currentStep,
        selectedCategories: request.selectedCategories,
        selectedServices: request.selectedServices
      });

      return {
        success: true,
        currentStep: request.currentStep,
        isComplete: request.currentStep >= 2
      };
    },
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.quickStartProgress });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.quickStartStatus });
      
      if (data.isComplete) {
        toast.success('Quick start completed successfully!');
      }
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to save progress');
    },
  });
};

/**
 * Complete the quick-start flow
 */
export const useCompleteQuickStart = () => {
  const queryClient = useQueryClient();
  const { user } = useUserDetails();
  const organizationId = user?.organization?.id;

  return useMutation({
    mutationFn: async (data: Partial<QuickStartCompletionData>) => {
      if (!organizationId) {
        throw new Error('No organization found');
      }

      // Skip API calls for now, just log the completion
      console.log('Completing quick-start:', {
        organizationId,
        completionData: data
      });

      return { success: true };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.quickStartStatus });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.quickStartProgress });
      toast.success('Welcome to Unizo! Your workspace is ready.');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to complete quick start');
    },
  });
};

/**
 * Skip the quick-start flow
 */
export const useSkipQuickStart = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (reason?: string) => {
      const response = await fetchInstance.post('/quick-start/skip', { reason });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.quickStartStatus });
      toast.info('Quick start skipped. You can always set this up later in Settings.');
    },
  });
};

/**
 * Save selected categories
 */
export const useSaveCategories = () => {
  return useMutation({
    mutationFn: async (categories: string[]) => {
      const response = await fetchInstance.post('/quick-start/categories', { categories });
      return response.data;
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to save categories');
    },
  });
};

/**
 * Save service configurations
 */
export const useSaveServiceConfigurations = () => {
  return useMutation({
    mutationFn: async (services: ServiceConfiguration[]) => {
      const response = await fetchInstance.post('/quick-start/services', { services });
      return response.data;
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to save service configurations');
    },
  });
};

/**
 * Hook to determine tenant type from user's subscriptions
 */
export const useTenantType = (): { tenantType: TenantType; isLoading: boolean } => {
  const { subscriptions, isSubscriptionLoading } = useUserDetails();

  const tenantType = useMemo(() => {
    if (!subscriptions?.length) return 'trial';

    // Get all product keys and pricing tiers
    const productKeys = subscriptions.map(s => s.productKey?.toUpperCase());
    const pricingTiers = subscriptions
      .map(s => s.charge?.pricingTier?.name?.toLowerCase())
      .filter(Boolean);

    // Check pricing tiers first (more specific)
    if (pricingTiers.some(tier => tier?.includes('enterprise'))) return 'enterprise';
    if (pricingTiers.some(tier => tier?.includes('professional'))) return 'enterprise';
    if (pricingTiers.some(tier => tier?.includes('security'))) return 'security';
    if (pricingTiers.some(tier => tier?.includes('infrastructure'))) return 'infra';
    if (pricingTiers.some(tier => tier?.includes('starter') || tier?.includes('basic'))) return 'basic';

    // Check product keys as fallback
    const hasSecurityProducts = productKeys.some(key => 
      ['VMS', 'EDR', 'IDENTITY', 'SIEM'].includes(key)
    );
    const hasInfraProducts = productKeys.some(key => 
      ['INFRA', 'MONITORING'].includes(key)
    );

    if (hasSecurityProducts) return 'security';
    if (hasInfraProducts) return 'infra';

    // Default to basic if has any subscriptions
    return 'basic';
  }, [subscriptions]);

  return {
    tenantType,
    isLoading: isSubscriptionLoading
  };
};

/**
 * Helper function to determine tenant type from subscription data
 * @deprecated Use useTenantType hook instead
 */
export const getTenantType = (subscription?: any): TenantType => {
  if (!subscription) return 'trial';
  
  // Map subscription plans to tenant types
  const planMapping: Record<string, TenantType> = {
    'trial': 'trial',
    'starter': 'basic',
    'professional': 'enterprise',
    'enterprise': 'enterprise',
    'security-focused': 'security',
    'infrastructure': 'infra',
  };

  return planMapping[subscription.plan?.toLowerCase()] || 'custom';
};

/**
 * Get configuration based on user's actual subscriptions
 */
export const useActualTenantConfiguration = () => {
  const { subscriptions, categories, isSubscriptionLoading } = useUserDetails();
  const { tenantType } = useTenantType();

  return useQuery({
    queryKey: ['tenant-configuration', subscriptions, categories],
    queryFn: (): TenantConfiguration => {
      // For trial users, return trial configuration
      if (!subscriptions?.length || tenantType === 'trial') {
        return TENANT_CONFIGURATIONS.trial;
      }

      // Get enabled categories from actual subscriptions
      const enabledCategories = categories
        .filter(cat => !cat.disabled)
        .map(cat => cat.value);

      // Return configuration with actual enabled categories
      return {
        type: tenantType,
        name: TENANT_CONFIGURATIONS[tenantType]?.name || 'Custom Plan',
        predefinedCategories: enabledCategories,
        allowCategorySelection: false,
        allowSkip: false,
        customMessage: enabledCategories.length > 0 
          ? `Your ${TENANT_CONFIGURATIONS[tenantType]?.name || 'subscription'} includes access to ${enabledCategories.length} API categories.`
          : undefined
      };
    },
    enabled: !isSubscriptionLoading
  });
};

/**
 * Get configuration for a specific tenant type
 */
export const getTenantConfiguration = (type: TenantType): TenantConfiguration => {
  return TENANT_CONFIGURATIONS[type] || TENANT_CONFIGURATIONS.custom;
};

/**
 * Check if quick start should be shown
 */
export const shouldShowQuickStart = (
  status?: QuickStartStatusResponse,
  forceShow = false
): boolean => {
  if (forceShow) return true;
  if (!status) return false;
  
  return status.needsQuickStart && status.settings.showOnLogin;
};

/**
 * Calculate progress percentage
 */
export const calculateProgressPercentage = (progress: QuickStartProgress): number => {
  const totalSteps = 3; // Category selection, Service configuration, Final step
  const completedSteps = progress.completedSteps.length;
  return Math.round((completedSteps / totalSteps) * 100);
};

/**
 * Validate category selection based on tenant configuration
 */
export const validateCategorySelection = (
  categories: string[],
  configuration: TenantConfiguration
): { valid: boolean; message?: string } => {
  if (configuration.isPredefined && !configuration.allowCustomization) {
    // Must match predefined categories exactly
    const predefined = configuration.predefinedCategories || [];
    const isValid = 
      categories.length === predefined.length &&
      categories.every(cat => predefined.includes(cat));
    
    return {
      valid: isValid,
      message: isValid ? undefined : 'Please select all required categories for your plan',
    };
  }

  // For customizable configurations, just ensure at least one is selected
  if (categories.length === 0) {
    return {
      valid: false,
      message: 'Please select at least one category',
    };
  }

  return { valid: true };
};