/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect } from "react";

import { Stack, Typography, Paper } from "@mui/material";


import useSelfRegistration from "store/self-signUp/self-signup";

import Header from "../header";
import WebhookRow from "./webhook-row";
import { StepComponentProps } from "../component-mapper";

const isValidUrl = (url: string) => {
   try {
      new URL(url);
      return true;
   } catch {
      return false;
   }
};


const Webhook = ({ currentStep }: StepComponentProps) => {
   const { selectedCategories, webhookData, setWebhookData } = useSelfRegistration();

   const onUrlChange = (category: string, newUrl: string) => {
      const updated = webhookData.map((item) => {
         if (item.category === category) {
            return { ...item, url: newUrl, isValid: isValidUrl(newUrl) }
         } else {
            return item;
         }
      })
      setWebhookData(updated);
   };

   const handleDelete = (category: string) => {
      setWebhookData(webhookData.map(item =>
         item.category === category ? { ...item, url: '', isValid: false } : item
      ));
   };

   useEffect(() => {

      if (!selectedCategories?.length) return;

      const updated = selectedCategories?.reduce((acc: Array<{ category: string; url: string; isValid: boolean }>, category) => {

         /**
          * Finding the existing item in the webhookData array
          */
         const existingItem = webhookData?.find((i) => i?.category === category);

         if (!existingItem) {
            acc.push({
               category,
               url: '',
               isValid: false
            })
         } else {
            /**
             * Else , we're updating the existing item
             */
            acc.push(existingItem)
         }

         return acc;
      }, [])

      setWebhookData(updated);

   }, [selectedCategories])

   return (
      <Stack spacing={3} sx={{ width: '100%', maxWidth: 1100, margin: '0 auto', py: 2 }}>
         <Header subTitle={currentStep?.subTitle} title={currentStep?.title} />
         {/* <Typography variant="body1" sx={{ color: 'text.secondary', mb: 2 }}>
            {currentStep?.subTitle}
         </Typography> */}

         <Paper
            elevation={0}
            sx={{
               borderRadius: 2,
               overflow: 'hidden',
               border: 1,  // Add border around the entire table
               borderColor: 'divider'  // Use theme's divider color
            }}
         >
            {/* Header */}
            <Stack
               direction="row"
               sx={{
                  bgcolor: 'grey.50',
                  p: 1.5,  // Reduced from p: 2
                  borderBottom: 1,
                  borderColor: 'divider'
               }}
            >
               <Typography
                  sx={{
                     flex: '0 0 200px',
                     fontWeight: 600,
                     color: 'text.primary',
                     fontSize: '0.875rem'  // Reduced font size
                  }}
               >
                  Category
               </Typography>
               <Typography
                  sx={{
                     flex: 1,
                     fontWeight: 600,
                     color: 'text.primary',
                     fontSize: '0.875rem'  // Reduced font size
                  }}
               >
                  Callback URL
               </Typography>
            </Stack>

            {/* Rows */}
            {webhookData.map((item) => (
               <WebhookRow
                  key={item.category}
                  item={item}
                  onUrlChange={onUrlChange}
                  handleDelete={handleDelete}
               />
            ))}
         </Paper>

         <Typography
            variant="body1"
            align="center"
            color="text.secondary"
         >
            {currentStep?.assistanceInfo}
         </Typography>
      </Stack>
   );
};

export default Webhook;
