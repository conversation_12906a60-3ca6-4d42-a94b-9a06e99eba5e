import { useTheme } from '@mui/material';
import useMediaQuery from '@mui/material/useMediaQuery';

import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  BarElement,
  CategoryScale,
  LinearScale,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';

import { useMemo } from 'react';
import { ThemeMode } from 'config';

import { generateLabels } from './helper'

// Register necessary Chart.js components
ChartJS.register(BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend, ChartDataLabels);

type Props = {
  stats: Record<string, any>
}

export default ({ stats }: Props) => {

  const { palette } = useTheme();
  const downSM = useMediaQuery((theme: any) => theme.breakpoints.down('sm'));
  const downLG = useMediaQuery((theme: any) => theme.breakpoints.down('lg'));

  const data = useMemo(() => {
    return {
      labels: generateLabels(stats?.xAxisLabels, downLG),
      datasets: [
        {
          label: 'API',
          data: stats?.api?.data ?? [],
          backgroundColor: palette.primary.main,
          metadata: stats?.api?.meta ?? []
        },
        {
          label: 'Events',
          data: stats?.events?.data ?? [],
          backgroundColor: palette.primary.light,
          metadata: stats?.events?.meta ?? []
        },
      ],
      barPercentage: 1, // Adjust bar width
      categoryPercentage: downLG ? 30 : 100, // Add space between bars
    }
  }, [stats, palette, downSM, downLG]);

  const options: any = useMemo(() => {

    const lineColor = palette.divider;
    const fontColor = palette.mode !== ThemeMode.DARK ? palette.secondary.main : palette.secondary.light;

    return {
      plugins: {
        title: {
          display: false,
        },
        tooltip: {
          enabled: true,
          callbacks: {
            // Customizing tooltip label
            label: (context: any) => {
              const dataset = context.dataset;
              const metadata = dataset.metadata[context.dataIndex]; // Access metadata based on index
              return `${context.dataset.label}: ${metadata}`; // Show value and metadata in tooltip
            },
          },
        },
        legend: {
          display: false
        },
        datalabels: {
          anchor: 'end',
          align: 'start',
          font: {
            weight: 'lighter',
            size: 10,
          },
          color: palette.mode !== ThemeMode.DARK ? palette.grey[700] : palette.common.white,
          formatter: (value: string, context: any) => {
            const dataset = context.dataset;
            const metadata = dataset.metadata[context.dataIndex];
            return metadata ? `${metadata}` : null
          },
        },
      },
      scales: {
        x: {
          stacked: true,
          grid: {
            display: false
          },
          ticks: {
            beginAtZero: true,
            color: fontColor,
            font: {
              size: 10,
            },
          },
          gridLines: {
            offsetGridLines: false, // Ensures grid aligns to bars
          },
        },
        y: {
          stacked: true,
          min: 0,
          max: 100,
          barThickness: 10,
          ticks: {
            stepSize: 20,
            color: fontColor,
            font: {
              size: 10,
            },
          },
          grid: {
            color: lineColor,
          },
        },
      },
      barThickness: 50
    }
  }, [palette]);

  return (
    <Bar
      data={data} options={options} height={100}
      style={{ marginTop: 40, marginBottom: 20, marginRight: 20 }}
    />
  );
};
