import MainCard from "components/MainCard"
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons"
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alogActions, DialogContent, DialogContentText, DialogProps, DialogTitle, FormControl, Grid, GridProps, InputLabel, MenuItem, Select, Stack, TextField, Typography } from "@mui/material"
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

import { Dialog } from 'components/@extended/dialog'

import { useEffect, useMemo, useRef, useState } from "react"
import { CardTitle } from "../card-title"
import { Form, FormField, FormItem } from "components/@extended/Form";

import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useGetSuperAdmin } from "hooks/api/super-admin/useGetSuperAdmin"
import { CardSelect } from "components/@extended/CardSelect"
import useCreateTenant from "store/create-tenants"
import moment from "moment"
import { useDate } from "hooks/useDate";

import Table from "components/@extended/Table"
import IconButton from "components/@extended/IconButton"
import nameChecker from "constants/categoryMapper";

export const Subscriptions = () => {
   const { loadDate } = useDate()

   const { formData, deleteSubscription } = useCreateTenant();
   const subscriptions = formData.subscriptions;

   const [open, setOpen] = useState<boolean>(false);
   const [selected, setSelected] = useState<any>();

   const data: any = subscriptions;

   const onOpen = () => {
      setOpen(true)
   }

   const onClose = () => {
      setOpen(false)
   }

   const requestDelete = (newSelected: any) => {
      setSelected(newSelected)
   }

   const approve = () => {
      deleteSubscription(selected)
      cancelRequest()
   }

   const cancelRequest = () => {
      setSelected(null)
   }


   const columns: any = useMemo(
      () => [
         {
            accessorKey: 'category.name', //access nested data with dot notation
            header: 'Name',
            cell({ row: { original: originalRow } }: any) {
               return nameChecker(originalRow?.category?.name); 
            },
         },
         {
            accessorKey: 'tier.name',
            header: 'Tier',
         },
         {
            accessorKey: 'name', //normal accessorKey
            header: 'Start Date',
            cell({ row: { original: originalRow } }: any) {
               return originalRow?.startDate ? loadDate(originalRow?.startDate) : null
            },
         },
         {
            accessorKey: 'direction', header: 'End Date',
            cell({ row: { original: originalRow } }: any) {
               return originalRow?.endDate ? loadDate(originalRow?.endDate) : null
            },
         },
         {
            accessorKey: 'options',
            header: 'Actions',
            cell({ row: { original: originalRow } }: any) {
               return (
                  <>
                     <IconButton
                        onClick={() => void requestDelete(originalRow)}
                        size='small'
                     >
                        <DeleteOutlined />
                     </IconButton>
                  </>
               )
            },
         },
      ],
      [],
   );

   return (
      <MainCard
         title={(
            <CardTitle
               title="Subscription Details"
               description="Choose the subscription that best suits your customer's need. To continue with tenant creation there should be atleast one subscription added."
            />
         )}
      >
         <Stack gap={2}>
            <Stack direction={'row'} justifyContent={'space-between'}>
               <div >
               </div>
               <Button
                  color='primary'
                  startIcon={<PlusOutlined />}
                  variant='contained'
                  onClick={onOpen}
               >
                  Add Subscription
               </Button>
            </Stack>

            <Table
               data={data}
               columns={columns}
               disablePagination
            />
         </Stack>
         <Dialog
            open={!!selected}
            onClose={cancelRequest}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
         >
            <DialogTitle id="alert-dialog-title" variant="h5">
               {"Are you sure want to delete ?"}
            </DialogTitle>
            <DialogContent dividers>
               <DialogContentText >
                  This action is permanent and cannot be undone. Please confirm to proceed.
               </DialogContentText>
            </DialogContent>
            <DialogActions>
               <Button onClick={cancelRequest}>Disagree</Button>
               <Button onClick={approve} variant='contained' autoFocus>
                  Agree
               </Button>
            </DialogActions>
         </Dialog>
         <Create open={open} isEditMode={false} onClose={onClose} />
      </MainCard>
   )
}


type CreateProps = {
   isEditMode: boolean
} & DialogProps;

const FORMAT = "MM-DD-YYYY"

const schema = z.object({
   category: z.string().nonempty("Category is required."),
   tier: z.string().nonempty("Tier is required."),
   startDate: z.string()
      .nonempty({ message: "Start date is required." })
      .refine(val => moment(val, FORMAT, true).isValid(), {
         message: `Invalid start date format. Use ${FORMAT}.`,
      }),
   endDate: z.string()
      .nonempty({ message: "End date is required." })
      .refine(val => moment(val, FORMAT, true).isValid(), {
         message: `Invalid end date format. Use ${FORMAT}.`,
      }),
}).superRefine(({ startDate, endDate }, ctx) => {

   if (startDate && endDate && moment(startDate).isAfter(moment(endDate))) {
      ctx.addIssue({ path: ["endDate"], message: "Start date cannot be after end date." } as any)
   }

})


const defaultValues: any = {
   category: '',
   tier: '',
   dateRange: [null, null]
}

const SPANS: GridProps = {
   xs: 12,
};

const getDateFieldValue = (value: any): any => {
   return moment(value, FORMAT, true).isValid() ? moment(value, FORMAT) : null
}

export const Create = (props: CreateProps) => {

   const { open, onClose: onCloseProp } = props;

   const { setSubscriptions, formData } = useCreateTenant();

   const subscriptions = formData.subscriptions

   const form = useForm<z.infer<typeof schema>>({
      resolver: zodResolver(schema),
      defaultValues,
      // mode: "onChange",
   })

   const { watch, setValue, reset } = form,
      category = watch('category'),
      tier = watch('tier'),
      startDate = watch('startDate');


   const { searchProducts, getProductPricingTier, getPricingTierEntitlements } = useGetSuperAdmin();


   const { data: products = [] } = searchProducts();
   const { data: pricingTiers = [] } = getProductPricingTier({ productId: category })
   const { data: entitlements } = getPricingTierEntitlements({ productId: category, tierId: tier })

   const formRef = useRef<HTMLFormElement>(null);

   const onOk = () => {
      formRef.current?.requestSubmit()
   }

   const onClose = (e: any) => {
      if (typeof onCloseProp === 'function') {
         onCloseProp(e, 'backdropClick')
      }
   }

   const onSubmit = (values: any) => {
      const category = products?.find((i: any) => i?.id === values?.category);
      const tier = pricingTiers?.find((i: any) => i?.id === values?.tier);

      const { startDate, endDate } = values;

      setSubscriptions({ category, tier, startDate, endDate });
      onClose({})
   }

   const onFieldValueChange = (fieldName: "category" | "tier" | "startDate" | "endDate", value?: string | undefined) => {
      setValue(fieldName, value as any, { shouldValidate: true })
   }

   const categoryOptions = useMemo(() => {
      const productId = subscriptions?.map((i: any) => i?.category?.id)

      return (
         products
            ?.filter((i: any) => !productId.includes(i?.id))
            ?.map((tier: any) => {
               return {
                  name: nameChecker(tier.name),
                  value: tier?.id,
               }
            })
      )
   }, [products, subscriptions])

   useEffect(() => {
      setValue('tier', '')
   }, [category])

   useEffect(() => {
      reset()
   }, [open])

   return (
      <Dialog
         open={open}
         onClose={onClose}
         aria-labelledby="modal-modal-title"
         aria-describedby="modal-modal-description"
      >
         <DialogTitle id="modal-modal-title" variant="h5">
            Subscription Details
         </DialogTitle>

         <DialogContent dividers >

            <Form {...form}>
               <form
                  onSubmit={(...args) => (
                     void form.handleSubmit(onSubmit)(...args)
                  )}
                  ref={formRef}
                  className="flex flex-col gap-5"
               >
                  <Grid container columnSpacing={4} rowSpacing={3} >
                     <Grid item  {...SPANS} >
                        <FormField
                           control={form.control}
                           name={'category'}
                           render={({ field }) => (
                              <FormItem label={'Select the Category'} >
                                 <FormControl>
                                    <InputLabel id="select-label">Category</InputLabel>
                                    <Select  {...field} labelId="select-label">
                                       {categoryOptions.map((domain: any, index: number) => (
                                          <MenuItem value={domain?.value} key={index}  >{domain?.name}</MenuItem>
                                       ))}
                                    </Select>
                                 </FormControl>
                              </FormItem>
                           )}
                        />
                     </Grid>

                     {category && (
                        <Grid item  {...SPANS} >
                           <Stack gap={1}>
                              <Typography variant='h6' className="font-semibold" sx={{ color: "secondary.600" }}>Select the Tier</Typography>
                              <FormField
                                 control={form.control}
                                 name={'tier'}
                                 render={({ field: { onChange } }) => (
                                    <FormItem>
                                       <FormControl>
                                          <CardSelect
                                             options={(
                                                pricingTiers.map((tier: any) => {
                                                   return {
                                                      title: tier?.name,
                                                      value: tier?.id,
                                                   }
                                                })
                                             )}
                                             onValueSelect={(e: any) => {
                                                onChange(e.value)
                                             }}
                                          />
                                       </FormControl>
                                    </FormItem>
                                 )}
                              />
                           </Stack>
                        </Grid>
                     )}


                     {tier && (
                        <Grid item  {...SPANS} >
                           <Alert>
                              <Stack gap={1}>
                                 <Typography variant={'h6'} className="font-semibold" >
                                    {`Entitlements for selected tier`}
                                 </Typography>
                                 <Grid container columnSpacing={2} rowSpacing={2} >
                                    {entitlements?.entitlements?.map((item: any, index: number) => {
                                       return (
                                          <Grid item key={index} xs={6}>
                                             {item?.displayName && <Typography key={index} style={{ fontSize: "13px" }}>  ✔ &nbsp; &nbsp;{item.displayName}</Typography>}
                                          </Grid>
                                       )
                                    })}
                                 </Grid>
                              </Stack>
                           </Alert>
                        </Grid>
                     )}


                     <Grid container item  {...SPANS} spacing={2}>
                        <Grid item xs={6}>
                           <FormField
                              control={form.control}
                              name={'startDate'}
                              render={({ field }: any) => (
                                 <FormItem label={'Start Date'} >
                                    <FormControl>
                                       <DatePicker
                                          slotProps={{
                                             inputAdornment: {
                                                position: 'start',
                                             },
                                          }}
                                          onChange={(e) => {
                                             onFieldValueChange(field?.name, e?.format(FORMAT))
                                          }}
                                          format={FORMAT} // Set the format here
                                          value={getDateFieldValue(field?.value)}
                                          shouldDisableDate={(date) => date < moment().startOf('day')}
                                       />
                                    </FormControl>
                                 </FormItem>
                              )}
                           />
                        </Grid>
                        <Grid item xs={6}>
                           <FormField
                              control={form.control}
                              name={'endDate'}
                              render={({ field }: any) => (
                                 <FormItem label={'End Date'} >
                                    <FormControl>
                                       <DatePicker
                                          slotProps={{
                                             inputAdornment: {
                                                position: 'start',
                                             },
                                          }}
                                          onChange={(e) => {
                                             onFieldValueChange(field?.name, e?.format(FORMAT))
                                          }}
                                          value={getDateFieldValue(field?.value)}
                                          format={FORMAT} // Set the format here
                                          shouldDisableDate={(date) => {
                                             return date < moment().startOf('day') || date < moment(startDate).startOf('day')
                                          }}
                                       />
                                    </FormControl>
                                 </FormItem>
                              )}
                           />
                        </Grid>
                     </Grid>

                  </Grid>
               </form>
            </Form>
         </DialogContent>

         <DialogActions>
            <Stack direction={'row'} justifyContent={'flex-end'} gap={1}>
               <Button onClick={onClose} >Cancel</Button>
               <Button
                  variant='contained'
                  onClick={onOk}
                  color='primary'
               >Submit</Button>
            </Stack>
         </DialogActions>
      </Dialog >
   )
}