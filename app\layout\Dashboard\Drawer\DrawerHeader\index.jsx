import PropTypes from 'prop-types';
// material-ui
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';

// project import
import DrawerHeaderStyled from './DrawerHeaderStyled';
import Logo from 'components/logo';

import useConfig from 'hooks/useConfig';
import { MenuOrientation } from 'config';

// ==============================|| DRAWER HEADER ||============================== //

export default function DrawerHeader({ open }) {
  const theme = useTheme();
  const downLG = useMediaQuery(theme.breakpoints.down('lg'));

  const { menuOrientation } = useConfig();
  const isHorizontal = menuOrientation === MenuOrientation.HORIZONTAL && !downLG;

  return (
    <Box>
      <DrawerHeaderStyled
        theme={theme}
        open={open}
        sx={{
          minHeight: isHorizontal ? 'unset' : '60px',
          width: isHorizontal ? { xs: '100%', lg: '424px' } : 'initial',
          paddingTop: isHorizontal ? { xs: '10px', lg: '0' } : '8px',
          paddingBottom: isHorizontal ? { xs: '18px', lg: '0' } : '8px',
          paddingLeft: isHorizontal ? { xs: '24px', lg: '0' } : open ? '0px' : 0
        }}
      >
        <Logo isIcon={!open} sx={{ width: open ? 'auto' : 35, height: 35, margin: 'auto' }} />
      </DrawerHeaderStyled>
      <Box sx={{ px: open ? 2.5 : 1.25, pb: 2 }}>
        <Divider sx={{
          borderColor: '#ea580c',
          borderBottomWidth: '1px',
          mx: open ? 1 : 'auto',
          width: open ? 'calc(100% - 16px)' : '60%'
        }} />
      </Box>
    </Box>
  );
}

DrawerHeader.propTypes = { open: PropTypes.bool };
