declare global {
   interface Window {
      ENV?: {
         API_PLATFORM_BASE_URL: string;
         API_POD_BASE_URL: string

         // keyCloak configs
         KEYCLOAK_REALM_ID: string;
         KEYCLOAK_CLIENT_ID: string;
         KEYCLOAK_URI: string;
         WINDOW_ENV: string
      };
      authUserId: string
      authUserOrgId: string
      isSuperAdmin: boolean
   }

   interface CSSStyleDeclaration {
      zoom?: string;
   }

   type FormConfigTypes = {
      name: string;
      type: FormItemType;
      label?: string;
      description?: string;
      placeholder?: string;

      // additional props, getting differ from customer portal and demo app
      label?: string
      property?: string
      required?: boolean
   } & Partial<GridProps>;

   type Mode = 'edit' | 'create'
}

export { };