import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';

let isRefreshing = false;
let failedQueue: Array<{
  resolve: (token: string) => void;
  reject: (error: AxiosError) => void;
}> = [];

const processQueue = (error: AxiosError | null, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else if (token) {
      prom.resolve(token);
    }
  });
  
  failedQueue = [];
};

// Add auth headers to requests
export const setupRequestInterceptor = (axiosInstance: typeof axios) => {
  axiosInstance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      // Add auth headers from window object
      const authUserId = (window as any).authUserId;
      const authUserOrgId = (window as any).authUserOrgId;
      
      if (authUserId) {
        config.headers['authUserId'] = authUserId;
      }
      if (authUserOrgId) {
        config.headers['organizationId'] = authUserOrgId;
      }
      
      // Add CSRF protection
      config.headers['X-Requested-With'] = 'XMLHttpRequest';
      
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
};

// Handle auth errors and token refresh
export const setupResponseInterceptor = (
  axiosInstance: typeof axios,
  onSessionExpired?: () => void
) => {
  axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config as InternalAxiosRequestConfig & {
        _retry?: boolean;
      };

      // Handle 401 Unauthorized
      if (error.response?.status === 401 && !originalRequest._retry) {
        if (isRefreshing) {
          // If already refreshing, queue this request
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          }).then(() => {
            return axiosInstance(originalRequest);
          }).catch((err) => {
            return Promise.reject(err);
          });
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          // Attempt to refresh the session
          const response = await axios.post('/api/auth/session/refresh', {}, {
            withCredentials: true
          });

          if (response.data.isValid) {
            processQueue(null, 'refreshed');
            isRefreshing = false;
            return axiosInstance(originalRequest);
          } else {
            throw new Error('Session refresh failed');
          }
        } catch (refreshError) {
          processQueue(error, null);
          isRefreshing = false;
          
          // Clear auth data
          localStorage.removeItem('authToken');
          sessionStorage.clear();
          
          // Call session expired handler
          if (onSessionExpired) {
            onSessionExpired();
          } else {
            // Default behavior - redirect to login
            const currentPath = window.location.pathname + window.location.search;
            window.location.href = `/login?sessionExpired=true&returnUrl=${encodeURIComponent(currentPath)}`;
          }
          
          return Promise.reject(error);
        }
      }

      // Handle 403 Forbidden
      if (error.response?.status === 403) {
        // User doesn't have permission
        console.error('Access denied:', error.response.data);
      }

      return Promise.reject(error);
    }
  );
};

// Setup all interceptors
export const setupSessionInterceptors = (
  axiosInstance: typeof axios = axios,
  onSessionExpired?: () => void
) => {
  setupRequestInterceptor(axiosInstance);
  setupResponseInterceptor(axiosInstance, onSessionExpired);
};