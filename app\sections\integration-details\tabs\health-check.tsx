import { Stack, Typography } from "@mui/material"
import { useGetIntegrationDetails } from "hooks/api/integration/useIntegration-details"
import { useStatus } from "hooks/useStatus"
import { useMemo } from "react"
import { StateEnums } from "../constant"
import ScatterChart from "./health-check-graph"
import MainCard from "components/MainCard"
import moment from "moment"


export const HealthCheck = () => {

   const { integration, integrationHealth } = useGetIntegrationDetails()

   const { resolveOutlinedIcon } = useStatus();

   const resolveStatusTitle = useMemo(() => {
      if (integration?.health?.status !== StateEnums.available) {
         return 'Unhealthy'
      }
      return 'Healthy'
   }, [integration?.operation]);

   const lastPollDate = integration?.rateLimit?.lastPolledDateTime;

   return (
      <MainCard gap={2}>
         <Stack>
            <Stack direction={'row'} alignItems={'center'} gap={2}>
               {resolveOutlinedIcon(integration?.health?.status, { style: { fontSize: '1.5rem' } })}
               <Stack>
                  <Typography variant="h5">{resolveStatusTitle}</Typography>
                  {lastPollDate && (
                     <Typography color='secondary'>
                        {`Updated ${moment(lastPollDate).local().fromNow()}`}
                     </Typography>
                  )}
               </Stack>
            </Stack>
         </Stack>
         <ScatterChart data={integrationHealth} />
      </MainCard>
   )
}