import _ from 'lodash'

const getIsEmpty = (arr: Array<number>) => {
   return _.every(arr, (value) => value === 0)
}

const getChartData = (stats: Record<string, any> = {}) => {
   const [series1, series2] = stats?.series ?? [];

   const empty =  (
      !!getIsEmpty(stats?.series?.[0]?.data) && !!getIsEmpty(stats?.series?.[1]?.data)
   );

   return {
      isEmpty: empty,
      data1: empty ? [] : series1?.data,
      data2: empty ? [] : series2?.data
   };
}

export { getIsEmpty, getChartData }