import { styled } from '@mui/material/styles';
import { ListItemButton, ListItemIcon, Box, Drawer } from '@mui/material';
import { DRAWER_WIDTH } from 'config';

// Navigation background colors
export const StyledDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    backgroundColor: theme.palette.mode === 'dark' ? theme.palette.background.paper : '#ffffff',
    borderRight: `1px solid ${theme.palette.divider}`,
    boxShadow: 'none',
  },
}));

// Navigation item styles
export const StyledNavItem = styled(ListItemButton)<{ selected?: boolean }>(({ theme, selected }) => ({
  margin: theme.spacing(0.25, 1),
  borderRadius: 0,
  transition: 'all 0.2s ease',
  padding: '10px 16px',
  
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
  
  ...(selected && {
    backgroundColor: theme.palette.action.selected,
    borderLeft: `3px solid ${theme.palette.primary.main}`,
    paddingLeft: '13px',
    
    '&:hover': {
      backgroundColor: theme.palette.action.selected,
    },
  }),
}));

// Navigation icon styles
export const StyledNavIcon = styled(ListItemIcon)<{ selected?: boolean }>(({ theme, selected }) => ({
  minWidth: 40,
  color: selected 
    ? theme.palette.primary.main
    : theme.palette.text.secondary,
  
  '& svg': {
    fontSize: '1.2rem',
  },
}));

// Navigation group header
export const StyledNavGroup = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 2, 1),
  color: 'rgba(255, 255, 255, 0.5)',
  fontSize: '0.75rem',
  fontWeight: 600,
  textTransform: 'uppercase',
  letterSpacing: '0.5px',
}));

// Navigation drawer header
export const StyledDrawerHeader = styled(Box)(({ theme }) => ({
  backgroundColor: '#1a2b3d',
  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
  padding: theme.spacing(2),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

// Navigation card footer
export const StyledNavFooter = styled(Box)(({ theme }) => ({
  backgroundColor: '#1a2b3d',
  borderRadius: theme.shape.borderRadius,
  margin: theme.spacing(2),
  padding: theme.spacing(2),
  border: '1px solid rgba(255, 255, 255, 0.1)',
  
  '& .MuiTypography-root': {
    color: 'rgba(255, 255, 255, 0.9)',
  },
  
  '& .MuiButton-root': {
    backgroundColor: theme.palette.secondary.main,
    color: theme.palette.secondary.contrastText,
    
    '&:hover': {
      backgroundColor: theme.palette.secondary.dark,
    },
  },
}));