import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { userClient } from "services/user.service";

import { API_ENDPOINTS } from "utils/api/api-endpoints";
import { toast } from "sonner"
import { parseError } from "lib/utils";
import { useMemo } from "react";

export type SPQueryOptionsType = {
   offset?: number;
   limit?: number;
   orderBy?: string;
};
const INVITED_QUERY_KEY = "INVITED";
const ACTIVE_QUERY_KEY = "ACTIVE";
const USER_DETAILS_UPDATE_KEY = "USER_DETAILS_UPDATE_KEY";
const USER_PASSWORD_UPDATE_KEY = "USER_PASSWORD_UPDATE_KEY";
const USER_LOGOUT_KEY = "USER_LOGOUT";


export const useGetUser = (props: Record<string, any>) => {

   const queryClient = useQueryClient();
   const orgId = props?.orgId;

   const {
      mutateAsync: inviteBulkUsers,
   } = useMutation({
      mutationFn: (payload: any) => {
         return userClient.inviteBulkUsers(payload)
      },
      mutationKey: [API_ENDPOINTS.ORGANIZATION],
   });

   const {
      mutateAsync: updateBulkUsers,
   } = useMutation({
      mutationFn: (arg: any) => {
         return userClient.updateRoleToUsers(arg.id, arg.payload)
      },
      mutationKey: [API_ENDPOINTS.ORGANIZATION],
   });

   const {
      mutateAsync: userDetailsUpdateMutate,
   } = useMutation({
      mutationFn: ({ id, payload }: Record<string, any>) => {
         return userClient.updateUserDetails(id, payload)
      },
      mutationKey: [API_ENDPOINTS.USERS, USER_LOGOUT_KEY, USER_DETAILS_UPDATE_KEY],
   });

   // password update mutation
   const {
      mutateAsync: userPasswordUpdateMutate,
   } = useMutation({
      mutationFn: ({ id, payload }: Record<string, any>) => {
         return userClient.updateUserPassword(id, payload)
      },
      mutationKey: [API_ENDPOINTS.USERS, USER_LOGOUT_KEY, USER_PASSWORD_UPDATE_KEY],
   });

   const {
      mutateAsync: logoutMutate,
   } = useMutation({
      mutationFn: () => {
         return userClient.logout()
      },
      mutationKey: [API_ENDPOINTS.USERS, USER_LOGOUT_KEY],
   });

   const { data: userResp } = useQuery({
      queryKey: [API_ENDPOINTS.USERS, ACTIVE_QUERY_KEY, orgId],
      queryFn: async () => {
         const payload = {
            "filter": {
               "or": [
                  {
                     "and": [
                        {
                           "property": "/state",
                           "operator": "=",
                           "values": [
                              "ACTIVE"
                           ]
                        },
                        {
                           "property": "/organization/id",
                           "operator": "=",
                           "values": [
                              orgId
                           ]
                        }
                     ]
                  }
               ]
            },
            "sort": [
               {
                  "direction": "DESC",
                  "property": "/changeLog/lastUpdatedDateTime"
               }
            ],
            "pagination": {
               "offset": 0,
               "limit": 20
            }
         }
         return await userClient.getAllUser(payload)
      },
      enabled: !!orgId
   });

   const { data: pendingUserResp } = useQuery({
      queryKey: [API_ENDPOINTS.USERS, INVITED_QUERY_KEY, orgId],
      queryFn: async () => {
         const payload = {
            filter: {
               or: [
                  {
                     and: [
                        {
                           property: "/state",
                           operator: "=",
                           values: [INVITED_QUERY_KEY]
                        },
                        {
                           property: "/organization/id",
                           operator: "=",
                           values: [
                              orgId
                           ]
                        }
                     ]
                  }
               ]
            },
            sort: [
               {
                  direction: "DESC",
                  property: "/changeLog/lastUpdatedDateTime"
               }
            ],
            pagination: {
               offset: 0,
               limit: 20
            }
         }
         return await userClient.getAllUser(payload)
      },
      select: (resp) => {
         return resp?.data
      },
      enabled: !!orgId
   });

   const users = useMemo(() => {
      return userResp?.data?.data ?? []
   }, [userResp])


   const getAvatarLabel = (user: string) => {
      return user?.charAt(0)?.toUpperCase()
   }

   const attemptBulkUserInvite = (payload: Record<string, any>, cb: any) => {
      toast.promise(inviteBulkUsers(payload), {
         loading: 'Inviting...',
         success: () => {
            queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.USERS, INVITED_QUERY_KEY] })
            cb && cb()
            return <b>Invited successfully</b>;
         },
         error: (error: any) => {
            return <b>{parseError(error?.response?.data)?.message}</b>;
         },
      })
   }

   const attemptBulkUserRoleEdit = (payload: Record<string, any>, cb: any) => {
      toast.promise(updateBulkUsers(payload), {
         loading: 'Updating role...',
         success: () => {
            queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.USERS, ACTIVE_QUERY_KEY] })
            cb && cb()
            return <b>Role updated successfully</b>;
         },
         error: (error: any) => {
            return <b>{parseError(error?.response?.data)?.message}</b>;
         },
      })
   }

   const attemptUpdateUserDetails = (
      id: string,
      payload: Record<string, any>,
      cb?: any
   ) => {
      toast.promise(userDetailsUpdateMutate({ id, payload }), {
         loading: 'Updating...',
         success: ({ data }) => {
            cb && cb(data)
            return `Updated successfully`;
         },
         error: (error: any) => {
            return parseError(error?.response?.data)?.message;
         },
      })
   }

   // update password
   const attemptUpdateUserPassword = (
      id: string,
      payload: Record<string, any>,
      cb?: any
   ) => {
      toast.promise(userPasswordUpdateMutate({ id, payload }), {
         loading: 'Updating...',
         success: () => {
            cb && cb()
            return `Updated successfully`;
         },
         error: (error: any) => {
            return parseError(error?.response?.data)?.message;
         },
      })
   }

   const attemptUserLogout = (cb: any) => {
      toast.promise(logoutMutate(), {
         loading: 'Logging out...',
         success: () => {
            queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.USERS, ACTIVE_QUERY_KEY] })
            cb && cb()
            window.location.href = '/'
            return `Logout successfully`;
         },
         error: (error: any) => {
            return parseError(error?.response?.data)?.message;
         },
      })
   }

   return {
      users,
      pendingUserResp,
      getAvatarLabel,
      attemptBulkUserInvite,
      attemptBulkUserRoleEdit,

      // updateUserDetails
      attemptUpdateUserDetails,

      // update password
      attemptUpdateUserPassword,

      // logout
      attemptUserLogout
   }
};
