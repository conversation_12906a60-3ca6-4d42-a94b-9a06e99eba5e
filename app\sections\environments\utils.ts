import { EnvironmentOperationState } from "constants/environment";
import Environment from "types/environment";
import { getEnvironmentId } from "utils/environment-id";

export const getIsEnvDefault = (environment: Environment.Root) => {
   return environment?.settings?.isDefault === true;
}

export const getIsEnvInUse = (environment: Environment.Root) => {
   return environment?.id === getEnvironmentId();
}