# Session Management with Keycloak

This document describes the enterprise-grade session management implementation with Keycloak integration.

## Important Note

This implementation is currently **COMMENTED OUT** and waiting for backend implementation. The infrastructure is complete and ready to use.

### To Enable Session Management:

1. Implement the backend endpoints:
   - `/api/auth/session/check` - Check session validity
   - `/api/auth/session/refresh` - Refresh session tokens

2. Uncomment the following:
   - `SessionProvider` import and wrapper in `/app/layout/Dashboard/index.jsx`
   - `setupSessionInterceptors` import and setup in `/app/utils/api/fetchinstance/platform-fetch-Instance.tsx`
   - Enable `withCredentials: true` in axios instance

3. Remove the temporary 401 handler in platform-fetch-Instance.tsx

The infrastructure is complete - just uncomment and connect to your backend!

## Overview

The session management system provides:
- Automatic session expiry detection
- Token refresh with Keycloak
- Session warning dialogs
- Automatic logout on expiry
- Activity tracking
- Network resilience

## Architecture

### Components

1. **SessionProvider** (`/app/contexts/SessionProvider.tsx`)
   - Wraps the application with session management context
   - Displays warning dialogs before session expiry
   - Handles session refresh and logout

2. **useSessionManager Hook** (`/app/hooks/useSessionManager.tsx`)
   - Core session management logic
   - Periodic session checks
   - Token refresh scheduling
   - Activity tracking

3. **Session API Routes**
   - `/api/auth/session/check` - Validates current session
   - `/api/auth/session/refresh` - Refreshes tokens with Keycloak

4. **Axios Interceptors** (`/app/utils/api/sessionInterceptor.ts`)
   - Automatic token refresh on 401 errors
   - Request queuing during refresh
   - Global error handling

## Configuration

### Environment Variables

```env
# Keycloak Configuration
KEYCLOAK_SERVER_URI=https://your-keycloak-server/auth
KEYCLOAK_REALM=your-realm
KEYCLOAK_CLIENT_ID=your-client-id
KEYCLOAK_CLIENT_SECRET=your-client-secret  # Optional for public clients

# Session Configuration
SESSION_SECRET=your-session-secret
SESSION_TIMEOUT=3600000  # 1 hour in milliseconds
SESSION_WARNING_TIME=300000  # 5 minutes before expiry
```

### Usage

1. **Enable Session Management**

The SessionProvider is automatically included in the DashboardLayout:

```tsx
<SessionProvider enabled={true}>
  {/* Your app content */}
</SessionProvider>
```

2. **Configure Axios Instances**

Add session interceptors to your axios instances:

```typescript
import { setupSessionInterceptors } from '~/utils/api/sessionInterceptor';

// Setup interceptors
setupSessionInterceptors(axiosInstance);
```

3. **Customize Session Behavior**

```tsx
<SessionProvider 
  enabled={true}
  config={{
    checkInterval: 60000, // Check every minute
    warningTime: 300000, // Warn 5 minutes before
    refreshBuffer: 60000, // Refresh 1 minute before expiry
    onSessionExpired: () => {
      // Custom expiry handler
    },
    onSessionWarning: (timeRemaining) => {
      // Custom warning handler
    }
  }}
>
```

## Features

### 1. Automatic Session Checking
- Checks session validity every 60 seconds
- Validates token expiration with backend
- Checks on tab focus for better UX

### 2. Token Refresh
- Automatically refreshes tokens before expiry
- Uses Keycloak's refresh token grant
- Queues requests during refresh

### 3. Activity Tracking
- Monitors user activity (mouse, keyboard, scroll)
- Extends session on activity
- Prevents unnecessary logouts

### 4. Warning Dialog
- Shows countdown 5 minutes before expiry
- Allows users to extend session
- Progress bar for visual feedback

### 5. Network Resilience
- Handles network errors gracefully
- Retries failed requests after refresh
- Doesn't logout on temporary failures

## Security Best Practices

1. **HTTP-Only Cookies**
   - Session tokens stored in HTTP-only cookies
   - Prevents XSS attacks

2. **CSRF Protection**
   - X-Requested-With header on all requests
   - SameSite cookie attribute

3. **Secure Transport**
   - HTTPS required in production
   - Secure cookie flag

4. **Token Rotation**
   - New tokens on each refresh
   - Short-lived access tokens

## Nginx Configuration

Add these headers for Keycloak integration:

```nginx
location / {
    proxy_pass http://your-app;
    
    # Forward auth headers
    proxy_set_header X-Auth-User-Id $http_x_auth_user_id;
    proxy_set_header X-Auth-Org-Id $http_x_auth_org_id;
    
    # Session timeout header
    proxy_set_header X-Session-Timeout $http_x_session_timeout;
    
    # CORS headers for API calls
    add_header Access-Control-Allow-Credentials true;
}
```

## Troubleshooting

### Session expires immediately
- Check token expiration times
- Verify server time synchronization
- Check Keycloak token settings

### Refresh fails
- Verify refresh token is valid
- Check Keycloak client settings
- Ensure client has refresh token grant

### Warning doesn't appear
- Check browser console for errors
- Verify SessionProvider is mounted
- Check warning time configuration

## Testing

1. **Manual Testing**
   - Set short session timeout
   - Wait for warning dialog
   - Test refresh and logout

2. **Automated Testing**
   ```typescript
   // Mock session check
   jest.mock('~/hooks/useSessionManager');
   
   // Test warning dialog
   expect(screen.getByText('Session Expiring Soon')).toBeInTheDocument();
   ```

## Migration Guide

If migrating from existing auth:

1. Update login flow to set session cookies
2. Add SessionProvider to layout
3. Update API clients with interceptors
4. Test with short timeouts first
5. Deploy with appropriate timeout values