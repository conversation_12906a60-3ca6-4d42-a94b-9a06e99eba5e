# Custom Field Creation Feature 🎨

## Overview

The enhanced field mapping now includes the ability to create custom target fields on the right-hand side. This gives users maximum flexibility when the predefined unified schema doesn't meet their specific mapping needs.

## ✨ Key Features

### 1. **Custom Field Creation Button**
- 🔧 **Edit icon** next to each target field dropdown
- **Click to create** a new custom field for mapping
- **Context-aware** - suggests field type based on selected source field

### 2. **Smart Field Creation Dialog**
- 📝 **Field Name**: Snake_case validation for consistency
- 🎨 **Display Name**: Human-readable label
- 🏷️ **Field Type**: Dropdown with icons for each data type
- 📄 **Description**: Optional field purpose documentation
- ✅ **Required**: Mark as mandatory field

### 3. **Visual Indicators**
- 🏷️ **"Custom" badge** for user-created fields
- 📊 **Custom field counter** in mapping summary
- 🎨 **Purple styling** to distinguish from predefined fields

## 🚀 Use Cases

### 1. **Custom Identifier Fields**
```
Source: repository.node_id (string) → Custom: internal_repo_uuid (string)
Purpose: Map GitHub's node_id to your internal UUID system
```

### 2. **Calculated/Derived Fields**
```
Source: repository.size (number) → Custom: size_category (string)
Purpose: Categorize repos as "small", "medium", "large" based on size
```

### 3. **Business-Specific Fields**
```
Source: repository.topics (array) → Custom: business_domain (string)  
Purpose: Extract primary business domain from topic tags
```

### 4. **Composite Fields**
```
Source: repository.full_name (string) → Custom: display_identifier (string)
Purpose: Create custom display format for repositories
```

### 5. **Array Index Mapping**
```
Source: contributors[0].login (string) → Custom: lead_developer_username (string)
Purpose: Map first contributor to dedicated lead developer field
```

## 🎯 How It Works

### Step 1: Start Mapping
1. Select a source field from the left dropdown
2. Notice the **Edit icon (🔧)** next to the target field dropdown
3. Click the edit icon to create a custom target field

### Step 2: Define Custom Field
1. **Field Name**: Enter snake_case name (e.g., `custom_repo_score`)
2. **Display Name**: Enter readable name (e.g., `Custom Repository Score`)
3. **Field Type**: Choose from String, Number, Boolean, Date, Array, Object
4. **Description**: Explain the field's purpose
5. **Required**: Check if this field is mandatory

### Step 3: Automatic Mapping
- Custom field is immediately created and added to target options
- Current mapping is automatically updated to use the new custom field
- Field appears with "Custom" badge in future dropdown selections

## 🎨 Visual Design

### Custom Field Badge
```
[Field Name] [Custom]
```
- **Purple background** (#8b5cf6) for distinction
- **Compact size** for clean interface
- **Monospace font** for technical fields

### Creation Dialog
- **Clean modal** with organized form sections
- **Type icons** for visual field type selection
- **Real-time validation** for field names
- **Smart defaults** based on source field

### Mapping Summary
```
✅ 3 valid mappings    🔧 2 custom fields
```
- Shows both standard and custom mapping counts
- Color-coded for easy identification

## 🔧 Technical Implementation

### Custom Field Interface
```typescript
interface CustomField {
  id: string;
  name: string;
  displayName: string;
  type: string;
  description?: string;
  required?: boolean;
  parentPath?: string;
}
```

### Enhanced Mapping Interface  
```typescript
interface FieldMapping {
  // ... existing fields
  isCustomTarget?: boolean;
  customTargetField?: CustomField;
}
```

### Custom Field Creation Handler
```typescript
const handleCreateCustomField = (field: CustomField, mappingId: string) => {
  // Add to custom fields list
  setCustomFields(prev => [...prev, field]);
  
  // Update mapping to use custom field
  setMappings(prev => prev.map(m => 
    m.id === mappingId ? {
      ...m,
      targetFieldPath: field.name,
      isCustomTarget: true,
      customTargetField: field,
    } : m
  ));
};
```

## 💡 Benefits

### 1. **Maximum Flexibility**
- Create fields that don't exist in predefined schemas
- Adapt to unique business requirements
- Handle edge cases and special mappings

### 2. **Future-Proof Design**
- Add new fields without schema changes
- Accommodate evolving data requirements
- Support custom transformations

### 3. **User Empowerment**
- No dependency on predefined field sets
- Self-service field creation
- Business-specific customization

### 4. **Validation & Consistency**
- Type validation ensures data integrity
- Naming conventions for consistency
- Required field enforcement

## 🔮 Advanced Scenarios

### Nested Custom Fields
Create custom fields within object hierarchies:
```
Parent: owner_info (object)
Custom Child: owner_info.custom_risk_score (number)
```

### Array Item Custom Fields
Map specific array elements to custom fields:
```
Source: contributors[0].login → Custom: primary_maintainer_id
Source: contributors[*].role → Custom: team_member_roles[]
```

### Conditional Custom Fields
Create fields based on source data conditions:
```
If repository.private = true → Custom: security_classification
If repository.size > 1000 → Custom: enterprise_tier_eligible
```

## 🚀 Getting Started

1. **Navigate** to Field Mappings tab
2. **Select** a source field 
3. **Click** the edit icon (🔧) next to target dropdown
4. **Fill** in custom field details
5. **Save** and start mapping!

This feature transforms the field mapping experience from rigid schema matching to flexible, user-driven data modeling! 🎉