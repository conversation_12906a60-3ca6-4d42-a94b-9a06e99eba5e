/* eslint-disable @typescript-eslint/no-explicit-any */
import { Button, ButtonProps, DialogActions, MobileStepper, Stack } from "@mui/material";

import useSelfRegistration from "store/self-signUp/self-signup";

import { OnboardingStep, OnboardingStepType } from "types/tenant-metadata";

import { getPrevBtnDisabled, getPrimaryButtonText } from "./helper";
import { useMemo } from "react";

interface IFooter {
   currentStep: OnboardingStep,
   steps: any[],
   hideProgress: boolean,
   hideNext: boolean
}

const Footer = ({ steps, hideProgress = false, hideNext = false, currentStep }: IFooter) => {

   const {
      step,
      move,
      setIsSetupDone,
      selectedCategories,
      selectedServices,
      webhookData,
      selectedMembers
   } = useSelfRegistration();

   const isNotLast = steps?.length - 1 >= step + 1;

   const isStepValid = useMemo(() => {
      switch (currentStep?.type) {
         case OnboardingStepType.SELECT_CATEGORY:
            return selectedCategories.length > 0;

         case OnboardingStepType.SELECT_PROVIDERS:
            return selectedServices.length > 0;

         case OnboardingStepType.SETUP_WEBHOOKS:
            return webhookData.some(webhook => webhook.isValid);

         case OnboardingStepType.SELECT_MEMBERS:
            return selectedMembers.length > 0;

         // For other steps, return true to keep the button enabled
         default:
            return true;
      }
   }, [currentStep?.type, selectedCategories, selectedServices, webhookData, selectedMembers]);

   const createTenant = () => {
      /**
       * TODO: Connect with API then, handle the response
      */
      setIsSetupDone(true);
   }

   const onBack = () => {
      move('prev')
   }

   const onNext: ButtonProps['onClick'] = () => {
      if (isNotLast) {
         move('next')
      } else {
         createTenant();
      }
   };

   const onSkip: ButtonProps['onClick'] = () => {
      move('next')
   }

   return (
      <DialogActions
         sx={styles.dialogActionRoot}
      >
         {!hideProgress && (
            <MobileStepper
               variant="progress"
               steps={steps?.length}
               position="static"
               activeStep={step}
               sx={{
                  width: '100%', // Take full width
                  boxShadow: 'none', // Remove any shadow
                  background: 'transparent', // Remove background if any
                  p: 0,
                  m: 0,
                  '& .MuiLinearProgress-root': {
                     width: '100%'
                  },
                  mb:1
               }}
               nextButton={<div />}
               backButton={<div />}
            />
         )}

         <Stack
            direction={'row'}
            gap={1}
            justifyContent="space-between"
            width="100%"
         >
            {!getPrevBtnDisabled(step) ? (
               <Button onClick={onBack}>
                  Back
               </Button>
            ) : <div />}

            <Stack direction='row' gap={2}>
               {/* Skip button only shows for non-mandatory steps */}
               {(
                  isNotLast &&
                  currentStep?.type !== OnboardingStepType.SELECT_CATEGORY &&
                  currentStep?.type !== OnboardingStepType.SELECT_PROVIDERS
               ) && (
                     <Button onClick={() => move('next')}>
                        Skip
                     </Button>
                  )}
               {!hideNext && (
                  <Button
                     variant="contained"
                     onClick={onNext}
                     disabled={!isStepValid}
                  >
                     {getPrimaryButtonText(step, steps?.length)}
                  </Button>
               )}
            </Stack>

         </Stack>
      </DialogActions>
   )
}

const styles = {
   dialogActionRoot: {
      display: 'flex',
      flexDirection: 'column',
      gap: 2,
      px: 2.7,
      py: 2
   },
}

export default Footer;
