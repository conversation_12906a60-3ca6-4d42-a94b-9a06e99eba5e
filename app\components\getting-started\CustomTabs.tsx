import React, { useState } from 'react';
import {
  Box,
  Typography,
  useTheme,
  alpha,
} from '@mui/material';
import { Check } from 'lucide-react';

export interface TabItem {
  id: number;
  number: string;
  title: string;
  helpText: string;
  status: 'completed' | 'active' | 'pending';
}

interface CustomTabsProps {
  tabs: TabItem[];
  activeTab: number;
  onTabChange: (tabId: number) => void;
  children: React.ReactNode;
}

export default function CustomTabs({ tabs, activeTab, onTabChange, children }: CustomTabsProps) {
  const theme = useTheme();

  return (
    <Box sx={{ width: '100%' }}>
      {/* Horizontal Tab Headers */}
      <Box 
        sx={{ 
          overflowX: 'auto',
          mb: 4,
          borderBottom: `1px solid ${theme.palette.divider}`,
          '&::-webkit-scrollbar': {
            height: 6,
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: theme.palette.grey[100],
            borderRadius: 3,
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: theme.palette.grey[400],
            borderRadius: 3,
            '&:hover': {
              backgroundColor: theme.palette.grey[500],
            },
          },
        }}
      >
        <Box 
          sx={{ 
            display: 'flex', 
            gap: 1.5,
            minWidth: 'fit-content',
            width: '100%',
          }}
        >
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            const isCompleted = tab.status === 'completed';
            
            return (
              <Box
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                sx={{
                  position: 'relative',
                  cursor: 'pointer',
                  flex: '1 1 0',
                  minWidth: 240,
                  pb: 2,
                  borderBottom: `3px solid ${isActive ? theme.palette.tertiary.main : 'transparent'}`,
                  transition: 'all 0.2s',
                  opacity: isActive ? 1 : 0.6,
                  '&:hover': {
                    opacity: isActive ? 1 : 0.85,
                  },
                }}
              >
                {/* Left Arrow - show only after first tab */}
                {tab.id > 0 && (
                  <Box
                    sx={{
                      position: 'absolute',
                      inset: 0,
                      top: 0,
                      left: -12,
                      width: '12px',
                      display: { xs: 'none', xl: 'block' },
                      height: '100%',
                    }}
                    aria-hidden="true"
                  >
                    <svg
                      style={{ height: '100%', width: '100%' }}
                      viewBox="0 0 12 82"
                      fill="none"
                      preserveAspectRatio="none"
                    >
                      <path
                        d="M0.5 0V31L10.5 41L0.5 51V82"
                        stroke={theme.palette.grey[400]}
                        vectorEffect="non-scaling-stroke"
                      />
                    </svg>
                  </Box>
                )}

                <Box
                  sx={{
                    px: 2,
                    pt: 3.125, // 25px
                    pb: 0.25, // 2px
                    transition: 'all 0.2s',
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                    <Box
                      sx={{
                        width: 32,
                        height: 32,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.875rem',
                        fontWeight: 500,
                        flexShrink: 0,
                        backgroundColor: isActive 
                          ? theme.palette.primary.main 
                          : theme.palette.background.paper,
                        border: `1.5px solid ${
                          isCompleted || isActive
                            ? theme.palette.primary.main
                            : theme.palette.grey[400]
                        }`,
                        color: isActive
                          ? theme.palette.primary.contrastText
                          : (isCompleted ? theme.palette.primary.main : theme.palette.grey[600]),
                        transition: 'all 0.2s',
                      }}
                    >
                      {tab.number}
                    </Box>
                    <Box sx={{ flex: 1 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 600,
                          mb: 0.5,
                          color: isActive 
                            ? theme.palette.tertiary.main 
                            : theme.palette.mode === 'light' 
                              ? theme.palette.grey[800] 
                              : theme.palette.text.primary,
                          transition: 'color 0.2s',
                        }}
                      >
                        {tab.title}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          color: theme.palette.mode === 'light' 
                            ? theme.palette.grey[600]
                            : theme.palette.text.secondary,
                          lineHeight: 1.5,
                          display: 'block',
                        }}
                      >
                        {tab.helpText}
                      </Typography>
                    </Box>
                    {isCompleted && (
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0,
                          ml: 1,
                          width: 20,
                          height: 20,
                          borderRadius: '50%',
                          backgroundColor: theme.palette.success.main,
                        }}
                      >
                        <Check size={12} strokeWidth={3} color={theme.palette.common.white} />
                      </Box>
                    )}
                  </Box>
                </Box>
              </Box>
            );
          })}
        </Box>
      </Box>

      {/* Tab Content */}
      <Box sx={{ mt: 3 }}>
        {children}
      </Box>
    </Box>
  );
}