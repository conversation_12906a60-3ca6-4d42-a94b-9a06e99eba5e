
export default function Paper(theme) {

   return {
      MuiPaper: {
         styleOverrides: {
            root: ({ theme }) => ({
               boxShadow: theme.shadows[0],
               borderColor: theme.palette.mode === 'dark'
                  ? theme.palette.secondary[100]
                  : theme.palette.secondary[200],
               borderWidth: 0,
               borderStyle: 'solid',
            }),
         },
      }
   };
}