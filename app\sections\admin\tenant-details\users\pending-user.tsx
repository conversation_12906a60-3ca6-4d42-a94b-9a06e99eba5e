import { useMemo, useState } from "react";

import { <PERSON><PERSON>, <PERSON>, DialogContent, Di<PERSON>Title, Stack, Typography } from "@mui/material";
import Avatar from "components/@extended/Avatar";
import { useGetPermission } from "hooks/api/permission/usePermission";
import { useGetUser } from "hooks/api/user/useUser";
import { useParams } from "@remix-run/react";
import { Dialog, DialogClose } from "components/@extended/dialog";
import Table from "components/@extended/Table";

const HEADER_NAMES = {
   NAME: 'Name',
   EMAIL: 'Email',
   ROLE: 'Role',
   ACTIONS: 'Actions',
}


export const PendingUserModal = () => {
   const [open, setOpen] = useState<boolean>(false);
   const { id } = useParams()

   const onOpen = () => {
      setOpen(true);
   },
      onClose = () => {
         setOpen(false);
      }

   const { deriveRoleLabel } = useGetPermission();

   const { pendingUserResp, getAvatarLabel } = useGetUser(
      { orgId: id });

   const data = pendingUserResp?.data ?? [],
      total = pendingUserResp?.pagination?.total ?? 0

   const columns: any = useMemo(
      () => [
         {
            accessorKey: 'name',
            header: HEADER_NAMES.NAME,
            cell({ row: { original: row } }: any) {
               return (
                  <Stack direction={'row'} gap={1} alignItems={'center'} className='mt-1'>
                     <Avatar size='small' className='bg-orange-500 text-white' alt="Remy Sharp" type={undefined}>
                        {getAvatarLabel(row?.firstName)}
                     </Avatar>
                     <Typography variant='button'>
                        {row?.firstName ?? row?.email}
                     </Typography>
                  </Stack>
               )
            },
         },
         {
            accessorKey: 'email', //normal accessorKey
            header: HEADER_NAMES.EMAIL,
         },
         {
            accessorKey: 'role',
            header: HEADER_NAMES.ROLE,
            cell({ row: { original: row } }: any) {
               return deriveRoleLabel(row?.role?.type)
            },
         },
      ],
      [],
   );

   return (
      <>
         <Button variant='outlined' onClick={onOpen}>View Pending User</Button>
         <Dialog onClose={onClose} open={open} maxWidth={'sm'}>
            <DialogTitle>
               <Stack gap={2} direction={'row'}>
                  <Typography variant='h5'>
                     Pending user
                  </Typography>
                  <Chip color="error" label={total} size="small" variant='filled' />
               </Stack>
            </DialogTitle>
            <DialogClose onClose={onClose} />
            <DialogContent dividers sx={{ padding: 0 }}>
               <Table data={data} columns={columns} disablePagination />
            </DialogContent>
         </Dialog>
      </>
   )
}