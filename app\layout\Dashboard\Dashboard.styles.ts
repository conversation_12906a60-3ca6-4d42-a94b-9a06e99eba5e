import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';

export const DashboardContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  width: '100%',
  minHeight: '100vh',
  background: theme.palette.mode === 'dark'
    ? theme.palette.background.default
    : '#f8fafc',
  position: 'relative',
}));

export const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  paddingTop: theme.spacing(2),
  marginTop: '64px', // Header height + border
  position: 'relative',
  
  [theme.breakpoints.up('lg')]: {
    padding: theme.spacing(4),
    paddingTop: theme.spacing(3),
  },
}));