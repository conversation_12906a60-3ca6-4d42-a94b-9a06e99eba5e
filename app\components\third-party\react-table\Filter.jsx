import PropTypes from 'prop-types';
// material-ui

// project-import
import DebouncedInput from './DebouncedInput';

// assets
import { useMemo } from 'react';

// ==============================|| FILTER - TEXT FIELD ||============================== //

function TextInput(
  { columnId,
    columnFilterValue,
    header,
    setFilterValue,
    inputType,
    options = [],
    ...rest
  }) {
  const dataListId = columnId + 'list';

  return (
    <DebouncedInput
      type="text"
      fullWidth
      value={columnFilterValue}
      onFilterChange={(value) => setFilterValue(value)}
      placeholder={`${header}`}
      inputProps={{ list: dataListId }}
      size='small'
      inputType={inputType}
      options={options}
      {...rest}
    />
  );
}

// ==============================|| FILTER - INPUT ||============================== //

export default function Filter({ column, table }) {

  const field = useMemo(() => {

    const columnFilterValue = column.getFilterValue(),
      {
        filterType = null,
        options = [],
        label,
        ...rest
      } = column?.columnDef?.meta?.filter ?? {};

    if (filterType) {
      return (
        <TextInput
          columnId={column.id}
          columnFilterValue={columnFilterValue ?? ''}
          setFilterValue={column.setFilterValue}
          header={label ?? column.columnDef.header}
          inputType={filterType}
          options={options}
          {...rest}
        />
      )
    } else return null;

  }, [column?.columnDef?.meta?.filter, column.getFilterValue()]);

  return field;
}

TextInput.propTypes = {
  columnId: PropTypes.string,
  columnFilterValue: PropTypes.string,
  header: PropTypes.string,
  setFilterValue: PropTypes.func
};

Filter.propTypes = { column: PropTypes.object, table: PropTypes.object };
