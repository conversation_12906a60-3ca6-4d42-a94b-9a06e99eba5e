import { Link, useLocation } from '@remix-run/react';
import { useTheme, alpha } from '@mui/material/styles';
import Box from '@mui/material/Box';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import Tooltip from '@mui/material/Tooltip';
import { Map } from 'lucide-react';
import { useGetMenuMaster } from 'api/menu';
import { ThemeMode } from 'config';
import useConfig from 'hooks/useConfig';

export default function QuickStartButton() {
  const theme = useTheme();
  const { mode } = useConfig();
  const { pathname } = useLocation();
  const { menuMaster } = useGetMenuMaster();
  const drawerOpen = menuMaster.isDashboardDrawerOpened;
  
  const isSelected = pathname === '/console/quick-start';
  const textColor = mode === ThemeMode.DARK ? 'grey.400' : 'text.primary';
  const iconSelectedColor = mode === ThemeMode.DARK && drawerOpen ? 'text.primary' : 'primary.main';
  
  return (
    <Box sx={{ px: 1, pt: 2, pb: 0.5 }}>
      <ListItemButton
        component={Link}
        to="/console/quick-start"
        selected={isSelected}
        sx={{
          pl: drawerOpen ? 3 : 0,
          py: 0.75,
          mb: 0.5,
          justifyContent: !drawerOpen ? 'center' : 'flex-start',
          borderRadius: 1,
          // Subtle dark background aligned with theme
          backgroundColor: drawerOpen 
            ? mode === ThemeMode.DARK
              ? alpha(theme.palette.common.white, 0.03)
              : alpha(theme.palette.common.black, 0.03)
            : 'transparent',
          // Subtle shadow effect
          boxShadow: drawerOpen 
            ? `0 2px 8px ${alpha(theme.palette.common.black, 0.08)}` 
            : 'none',
          transition: 'all 0.3s ease',
          ...(drawerOpen && {
            '&:hover': { 
              bgcolor: mode === ThemeMode.DARK 
                ? alpha(theme.palette.common.white, 0.06) 
                : alpha(theme.palette.common.black, 0.05),
              boxShadow: `0 4px 12px ${alpha(theme.palette.common.black, 0.12)}`
            },
            '&.Mui-selected': {
              bgcolor: mode === ThemeMode.DARK 
                ? alpha(theme.palette.common.white, 0.08) 
                : alpha(theme.palette.common.black, 0.06),
              borderRadius: 0,
              color: iconSelectedColor,
              position: 'relative',
              boxShadow: `0 3px 10px ${alpha(theme.palette.common.black, 0.1)}`,
              '&::before': {
                content: '""',
                position: 'absolute',
                left: 0,
                top: 0,
                bottom: 0,
                width: '3px',
                bgcolor: theme.palette.primary.main
              },
              '&:hover': { 
                color: iconSelectedColor, 
                bgcolor: mode === ThemeMode.DARK 
                  ? alpha(theme.palette.primary.main, 0.2) 
                  : alpha(theme.palette.primary.main, 0.12)
              }
            }
          }),
          ...(!drawerOpen && {
            '&:hover': { bgcolor: 'transparent' },
            '&.Mui-selected': { 
              '&:hover': { bgcolor: 'transparent' }, 
              bgcolor: 'transparent' 
            }
          })
        }}
      >
        <ListItemIcon
          sx={{
            minWidth: drawerOpen ? 28 : 40,
            color: isSelected ? iconSelectedColor : textColor,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            ...(!drawerOpen && {
              borderRadius: '8px',
              width: 40,
              height: 40,
              '&:hover': {
                bgcolor: mode === ThemeMode.DARK 
                  ? alpha(theme.palette.primary.main, 0.08) 
                  : alpha(theme.palette.primary.main, 0.04)
              }
            }),
            ...(!drawerOpen && isSelected && {
              bgcolor: mode === ThemeMode.DARK 
                ? alpha(theme.palette.primary.main, 0.2) 
                : alpha(theme.palette.primary.main, 0.12),
              '&:hover': {
                bgcolor: mode === ThemeMode.DARK 
                  ? alpha(theme.palette.primary.main, 0.25) 
                  : alpha(theme.palette.primary.main, 0.16)
              }
            })
          }}
        >
          {drawerOpen ? (
            <Map size={20} strokeWidth={isSelected ? 2.5 : 2} />
          ) : (
            <Tooltip title="Quick Start" placement="right" arrow>
              <Map size={24} strokeWidth={isSelected ? 2.5 : 2} />
            </Tooltip>
          )}
        </ListItemIcon>
        
        {drawerOpen && (
          <ListItemText
            primary={
              <Typography 
                variant="body1" 
                sx={{ 
                  color: isSelected ? iconSelectedColor : textColor, 
                  fontWeight: isSelected ? 500 : 400 
                }}
              >
                Quick Start
              </Typography>
            }
          />
        )}
      </ListItemButton>
    </Box>
  );
}