import { InputLabel, MenuItem, OutlinedInput, Select } from "@mui/material";
import { FormFieldTypeEnum } from "hooks/useServiceProfile";
import { useMemo } from "react";



export default ({ externalFieldProperties: fieldProperties, field = {} }: Record<string, any>) => {

   const fieldElement = useMemo(() => {

      const { placeholder = '' } = fieldProperties;

      switch (fieldProperties?.type) {
         case FormFieldTypeEnum.Text:

            return (
               <OutlinedInput
                  placeholder={placeholder}
                  {...field}
               />
            )

         case FormFieldTypeEnum.List:

            const { items = [], name, label } = fieldProperties;
            const options: Record<string, any> = [];
            const names = items?.[0]?.properties?.name?.possibleValues;

            items?.[0]?.properties?.value?.possibleValues?.forEach((value: string, index: number) => {
               options.push({ label: names?.[index], value })
            })

            return (
               <>
                  <InputLabel id={name}>{label}</InputLabel>
                  <Select
                     {...field}
                     labelId={name}
                  >
                     {options.map(({ value, label }: Record<string, any>, index: number) => {
                        return (
                           <MenuItem key={index} value={value}>{label}</MenuItem>
                        )
                     })}
                  </Select>
               </>
            )

         default:
            return null
      }
   }, [fieldProperties, field])

   return fieldElement
}