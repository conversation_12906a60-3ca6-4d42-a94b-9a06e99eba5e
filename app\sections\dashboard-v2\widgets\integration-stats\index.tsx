import ModernChartCard from "components/cards/ModernChartCard"

import SimpleBar from 'components/third-party/SimpleBar';

import { GadgetConfig } from "../../layout/grid-type"
import Chart from './chart';

export default ({ gadget }: GadgetConfig) => {
   return (
      <ModernChartCard
         title={gadget.name}
         subtitle="Integration performance metrics"
      >
         <SimpleBar sx={{ height: 386 }} >
            <Chart />
         </SimpleBar>
      </ModernChartCard>
   )
}