import React from 'react';

import { useNavigate } from '@remix-run/react';
import { Check, Circle, FileText, Code2, Activity, Zap, ListChecks } from 'lucide-react';

import {
  Box,
  Typography,
  Button,
  Grid,
  useTheme,
  Stack,
  alpha,
} from '@mui/material';

import TabNavigationFooter from 'components/getting-started/TabNavigationFooter';

interface SetupCompleteTabProps {
  connectorsEnabled: boolean;
  apiKeyGenerated: boolean;
  connectUIEnabled: boolean;
  onPrevious?: () => void;
  isFirstTab?: boolean;
  previousLabel?: string;
}

interface StatusItemProps {
  label: string;
  isComplete: boolean;
}

const StatusItem: React.FC<StatusItemProps> = ({ label, isComplete }) => {
  const theme = useTheme();
  
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5, py: 1 }}>
      <Box
        sx={{
          width: 20,
          height: 20,
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: isComplete ? theme.palette.success.main : 'transparent',
          border: isComplete ? 'none' : `2px solid ${theme.palette.grey[400]}`,
        }}
      >
        {isComplete ? (
          <Check size={14} color={theme.palette.common.white} strokeWidth={3} />
        ) : (
          <Circle size={8} color={theme.palette.grey[400]} fill={theme.palette.grey[400]} />
        )}
      </Box>
      <Typography
        variant="body1"
        sx={{
          color: isComplete ? theme.palette.text.primary : theme.palette.text.secondary,
          fontWeight: isComplete ? 500 : 400,
        }}
      >
        {label}
      </Typography>
    </Box>
  );
};

export default function SetupCompleteTab({
  connectorsEnabled,
  apiKeyGenerated,
  connectUIEnabled,
  onPrevious,
  isFirstTab,
  previousLabel,
}: SetupCompleteTabProps) {
  const theme = useTheme();
  const navigate = useNavigate();

  const handleDone = () => {
    navigate('/console/dashboard');
  };

  const allStepsComplete = 
    connectorsEnabled && 
    apiKeyGenerated && 
    connectUIEnabled;

  return (
    <Box>
      <Stack spacing={3}>
        {/* What's ready section */}
        <Box
          sx={{
            backgroundColor: theme.palette.background.default,
            borderRadius: 1,
            p: 4,
          }}
        >
          <Stack spacing={4}>
            {/* Header */}
            <Box>
              <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 2 }}>
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: theme.palette.common.white,
                  }}
                >
                  <ListChecks size={18} strokeWidth={3} />
                </Box>
                <Typography variant="h5" fontWeight={600}>
                  What's ready
                </Typography>
              </Stack>
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.mode === 'light'
                    ? theme.palette.grey[600]
                    : theme.palette.text.secondary,
                  maxWidth: 800,
                }}
              >
                These steps are important to be completed before you get started using platform
              </Typography>
            </Box>

            {/* Status Card */}
            <Box sx={{ maxWidth: 800 }}>
              <Box
                sx={{
                  backgroundColor: theme.palette.background.paper,
                  borderRadius: 1,
                  p: 4,
                }}
              >
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={4}>
                        <StatusItem 
                          label="Connectors configured"
                          isComplete={connectorsEnabled} 
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <StatusItem 
                          label="API key generated" 
                          isComplete={apiKeyGenerated} 
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <StatusItem 
                          label="Connect UI Enabled"
                          isComplete={connectUIEnabled} 
                        />
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>

                {allStepsComplete && (
                  <Box
                    sx={{
                      mt: 3,
                      p: 2,
                      backgroundColor: alpha(theme.palette.success.main, 0.08),
                      borderRadius: 1,
                      border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
                    }}
                  >
                    <Typography
                      variant="body2"
                      sx={{
                        color: theme.palette.success.dark,
                        fontWeight: 500,
                        textAlign: 'center',
                      }}
                    >
                      All setup steps completed! You&apos;re ready to start building.
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          </Stack>
        </Box>

        {/* Quick Links section */}
        <Box
          sx={{
            backgroundColor: theme.palette.background.default,
            borderRadius: 1,
            p: 4,
          }}
        >
          <Stack spacing={4}>
            {/* Header */}
            <Box>
              <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 2 }}>
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: theme.palette.common.white,
                  }}
                >
                  <FileText size={18} strokeWidth={3} />
                </Box>
                <Typography variant="h5" fontWeight={600}>
                  Quick Links
                </Typography>
              </Stack>
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.mode === 'light'
                    ? theme.palette.grey[600]
                    : theme.palette.text.secondary,
                  maxWidth: 800,
                }}
              >
                Jump start your integration with these helpful resources
              </Typography>
            </Box>

            {/* Quick Links Grid */}
            <Box sx={{ maxWidth: 800 }}>
              <Box
                sx={{
                  backgroundColor: theme.palette.background.paper,
                  borderRadius: 1,
                  p: 3,
                }}
              >
                <Grid container spacing={2}>
                  {/* API Docs */}
                  <Grid item xs={12} sm={6}>
                    <Box
                      sx={{
                        p: 2,
                        borderRadius: 1,
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          backgroundColor: theme.palette.action.hover,
                        },
                      }}
                      onClick={() => window.open('https://docs.unizo.ai', '_blank')}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                        <Box
                          sx={{
                            p: 1,
                            borderRadius: 1,
                            backgroundColor: theme.palette.action.hover,
                            color: theme.palette.text.primary,
                          }}
                        >
                          <FileText size={20} />
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body1" fontWeight={600} gutterBottom>
                            API Docs
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Full reference for Unizo's Unified APIs
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Grid>

                  {/* Recipes & Examples */}
                  <Grid item xs={12} sm={6}>
                    <Box
                      sx={{
                        p: 2,
                        borderRadius: 1,
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          backgroundColor: theme.palette.action.hover,
                        },
                      }}
                      onClick={() => window.open('https://docs.unizo.ai/api-explorer', '_blank')}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                        <Box
                          sx={{
                            p: 1,
                            borderRadius: 1,
                            backgroundColor: theme.palette.action.hover,
                            color: theme.palette.text.primary,
                          }}
                        >
                          <Zap size={20} />
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body1" fontWeight={600} gutterBottom>
                            API Explorer
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Send real requests for all Unified APIs
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Grid>

                  {/* SDKs */}
                  <Grid item xs={12} sm={6}>
                    <Box
                      sx={{
                        p: 2,
                        borderRadius: 1,
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          backgroundColor: theme.palette.action.hover,
                        },
                      }}
                      onClick={() => window.open('https://docs.unizo.ai/sdks', '_blank')}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                        <Box
                          sx={{
                            p: 1,
                            borderRadius: 1,
                            backgroundColor: theme.palette.action.hover,
                            color: theme.palette.text.primary,
                          }}
                        >
                          <Code2 size={20} />
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body1" fontWeight={600} gutterBottom>
                            SDKs
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Java, Node, Python, Go and more starter kits
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Grid>

                  {/* Logs & Monitoring */}
                  <Grid item xs={12} sm={6}>
                    <Box
                      sx={{
                        p: 2,
                        borderRadius: 1,
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          backgroundColor: theme.palette.action.hover,
                        },
                      }}
                      onClick={() => window.open('https://docs.unizo.ai/docs/unizo-console/monitoring-logs', '_blank')}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                        <Box
                          sx={{
                            p: 1,
                            borderRadius: 1,
                            backgroundColor: theme.palette.action.hover,
                            color: theme.palette.text.primary,
                          }}
                        >
                          <Activity size={20} />
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body1" fontWeight={600} gutterBottom>
                            Logs & Activity
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Track system activity and changes with logs
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Box>
          </Stack>
        </Box>
      </Stack>

      {/* Navigation Footer */}
      <TabNavigationFooter
        onPrevious={onPrevious}
        isFirstTab={isFirstTab}
        isLastTab={true}
        previousLabel={previousLabel}
        showDoneButton={true}
        onDone={handleDone}
        isDoneDisabled={!allStepsComplete}
      />
    </Box>
  );
}