/* eslint-disable @typescript-eslint/no-explicit-any */
import { Stack, Switch, Typography, useTheme, Chip } from "@mui/material";

import MainCard from "components/MainCard";

import { useServiceProfile } from "hooks/useServiceProfile";

import { useMemo } from "react";


const List = ({ profiles = [], selectedServiceIds, onSelect, onToggleEnable: onToggleEnableProp }: any) => {

   const { loadImage } = useServiceProfile();

   const theme = useTheme();


   const highlightStyles: any = useMemo(() => {
      return {
         boxShadow: theme?.shadows?.[1]
      }
   }, [theme])

   const onToggleEnable = (newToggleEnabledService: any) => {
      onToggleEnableProp?.(newToggleEnabledService)
   }

   return (
      <>
         {profiles.map((item: any, index: number) => {

            const { name, id } = item;

            const active = !!selectedServiceIds?.find((i: string) => i === id);

            return (
               <MainCard
                  onClick={() => (
                     onSelect(index, item)
                  )}
                  sx={{
                     "&:hover": { cursor: 'pointer', ...highlightStyles },
                     boxShadow: 'none',
                     p: 1,  // Further reduced padding
                     minHeight: 'auto',
                     maxWidth: '100%',
                     '& .MuiCardContent-root': {
                        p: "2px",
                        '&:last-child': {
                           pb: 0
                        }
                     }
                  }}
                  key={index}
               >
                  {item?.isBeta && (
                     <div style={{
                        position: 'absolute',
                        top: '-2px',
                        right: '0px',
                        zIndex: 1
                     }}>
                        <Chip
                           variant={('light') as any}
                           size="small"
                           label='Beta'
                           color={"info"}
                           sx={{
                              height: '14px', // Reduced from 18px
                              '& .MuiChip-label': {
                                 fontSize: '10px', // Reduced from 11px
                                 lineHeight: 1,
                                 padding: "0 6px", // Reduced padding
                              }
                           }}
                        />
                     </div>
                  )}

                  <Stack
                     direction={'row'}
                     justifyContent={'space-between'}
                     sx={{
                        py: 0.25,
                        minHeight: '32px' // Set minimum height for consistency
                     }}
                  >
                     <Stack direction={'row'} gap={1} alignItems={'center'}>
                        <div className="flex-shrink-0" style={{ width: '24px', height: '24px' }}> {/* Fixed size container for image */}
                           {loadImage(item, {
                              size: 'xSmall', // Use smallest size available
                              style: {
                                 width: '24px',
                                 height: '24px',
                                 objectFit: 'contain'
                              }
                           })}
                        </div>
                        <Stack>
                           <Typography
                              className="font-semibold select-none"
                              variant="caption" // Even smaller text
                              sx={{
                                 fontSize: '0.75rem',
                                 lineHeight: 1.2
                              }}
                           >
                              {name}
                           </Typography>
                        </Stack>
                     </Stack>
                     <Stack direction={'row'} gap={1} color={'secondary.600'}>
                        <Switch
                           checked={active}
                           onClick={(e) => void e.stopPropagation()}
                           onChange={() => {
                              onToggleEnable(item);
                           }}
                           size="small"
                        // sx={{
                        //    '& .MuiSwitch-root': {
                        //       width: '28px', // Smaller switch
                        //       height: '16px',
                        //    },
                        //    '& .MuiSwitch-thumb': {
                        //       width: '12px',
                        //       height: '12px',
                        //    },
                        //    padding: '4px'
                        // }}
                        />
                     </Stack>
                  </Stack>
               </MainCard>
            )
         })}
      </>
   )
}

export default List