import { useEffect } from 'react';
import { useNavigate } from '@remix-run/react';
import { Box, Typography, CircularProgress } from '@mui/material';

export default function FixTheme() {
  const navigate = useNavigate();
  
  useEffect(() => {
    // Clear any existing config
    localStorage.removeItem('mantis-react-ts-config');
    
    // Set the correct config with Unizo theme
    const config = {
      fontFamily: "'Public Sans', sans-serif",
      i18n: 'en',
      menuOrientation: 'vertical',
      miniDrawer: false,
      container: false,
      mode: 'light',
      presetColor: 'unizo',
      themeDirection: 'ltr',
      defaultPath: '/console/dashboard',
    };
    
    localStorage.setItem('mantis-react-ts-config', JSON.stringify(config));
    
    // Redirect to dashboard after a short delay
    setTimeout(() => {
      navigate('/console/dashboard');
    }, 1000);
  }, [navigate]);
  
  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column',
      alignItems: 'center', 
      justifyContent: 'center', 
      height: '100vh',
      gap: 2
    }}>
      <CircularProgress />
      <Typography>Setting up Unizo theme...</Typography>
    </Box>
  );
}