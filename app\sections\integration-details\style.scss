$border-color: rgba(128, 128, 128, 0.202);

.react-flow__node-default,
.react-flow__node-group,
.react-flow__node-input,
.react-flow__node-output {
   cursor: default !important;
   outline: none !important;
   box-shadow: 0 1px 4px 1px rgba(0, 0, 0, .08) !important;
   // border: none !important;
   border-width: .2;
   background: white !important;
   color: none;
}

.source,
.target {
   border: 1px solid $border-color;

   &:focus {
      outline: none;
   }
}

.react-flow__node {
   span {
      font-size: 8px !important;

      &.middle-node {
         font-size: 6px !important;
      }
   }
}

.react-flow__handle {
   opacity: 0;
   cursor: default !important;
}

.connector {
   // border: 1px solid $border-color !important;
}

// attribution part
.react-flow__attribution {
   display: none;
}