export type FilterOperator = 'is' | 'contains' | 'is_before' | 'is_after' | 'greater_than' | 'less_than';

export type FilterFieldType = 'date' | 'text' | 'enum' | 'number' | 'url';

export interface FilterField {
  key: string;
  label: string;
  type: FilterFieldType;
  options?: string[]; // For enum fields
  multiSelect?: boolean; // Enable multi-select for this field
  useOnce?: boolean; // If true, hide this field once a filter is applied
  allowMultiple?: boolean;
  validation?: FilterValidation; // Validation rules for this field
}

export interface FilterValidation {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: string | string[]) => string | null; // Return error message or null if valid
}

export type FilterStep = 'field' | 'operator' | 'value' | 'complete';

export interface Filter {
  id: string; // Unique identifier for editing
  field: string;
  key: string;
  operator: FilterOperator;
  value: string | string[]; // Support both single and multi-value filters
  step: FilterStep; // Current step in the workflow
  isEditing?: boolean; // Whether this filter is currently being edited
}

export interface FilterBarProps {
  fields: FilterField[];
  filters: Filter[];
  onFiltersChange: (filters: Filter[]) => void;
  placeholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  resultCount?: number;
  hideSelectedFields?: boolean;
}

export const FIELD_OPERATORS: Record<FilterFieldType, FilterOperator[]> = {
  date: ['is', 'is_before', 'is_after'],
  text: ['is', 'contains'],
  url: ['is', 'contains'],
  enum: ['is', 'contains'],
  number: ['is', 'greater_than', 'less_than'],
};

export const OPERATOR_LABELS: Record<FilterOperator, string> = {
  is: 'is',
  contains: 'contains',
  is_before: 'is before',
  is_after: 'is after',
  greater_than: '>',
  less_than: '<',
};