import { useMutation, useQuery } from "@tanstack/react-query"
import useGetAdditionalAttributes from "hooks/api/additional-attributes"
import { AdditionalFields } from "types/additional-attributes"

const useAdditionalAttributes = () => {
   const {
      createAdditionalAttributesMutation,
      deleteAdditionalAttributesMutation,
      getAllAdditionalAttributesQuery
   } = useGetAdditionalAttributes()

   const { mutateAsync: createAdditionalAttributes } = useMutation(createAdditionalAttributesMutation());
   const { mutateAsync: deleteAdditionalAttributes } = useMutation(deleteAdditionalAttributesMutation());

   const { data: additionalAttributeQueryData } = useQuery(getAllAdditionalAttributesQuery())

   return {
      actions: {
         create: createAdditionalAttributes,
         update: () => { },
         delete: deleteAdditionalAttributes,
      },
      additionalAttributes: additionalAttributeQueryData?.data?.data
   }
}

export default useAdditionalAttributes;