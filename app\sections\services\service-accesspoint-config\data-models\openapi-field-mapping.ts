/**
 * OpenAPI Field Mapping Integration
 * Generates field mapping data from OpenAPI specifications
 */

import { OpenApiProcessor, convertToHierarchicalField } from './openapi-processor';
import { githubOpenApiSpec } from './github-openapi-spec';

// Unified schema definitions for target models
const unifiedSchemas = {
  repository: {
    type: 'object',
    required: ['repo_id', 'repo_name', 'private'],
    properties: {
      repo_id: {
        type: 'string',
        description: 'Unique identifier for the repository'
      },
      repo_name: {
        type: 'string',
        description: 'Name of the repository'
      },
      repo_full_name: {
        type: 'string',
        description: 'Full name of the repository (owner/repo)'
      },
      description: {
        type: 'string',
        description: 'Repository description'
      },
      private: {
        type: 'boolean',
        description: 'Whether the repository is private'
      },
      default_branch: {
        type: 'string',
        description: 'Default branch name'
      },
      language: {
        type: 'string',
        description: 'Primary programming language'
      },
      size: {
        type: 'integer',
        description: 'Repository size in KB'
      },
      created_at: {
        type: 'string',
        format: 'date-time',
        description: 'Repository creation timestamp'
      },
      updated_at: {
        type: 'string',
        format: 'date-time',
        description: 'Last update timestamp'
      },
      pushed_at: {
        type: 'string',
        format: 'date-time',
        description: 'Last push timestamp'
      },
      owner_info: {
        type: 'object',
        properties: {
          id: {
            type: 'string',
            description: 'Owner unique identifier'
          },
          name: {
            type: 'string',
            description: 'Owner display name'
          },
          username: {
            type: 'string',
            description: 'Owner username'
          },
          type: {
            type: 'string',
            description: 'Type of owner (user/organization)'
          },
          avatar_url: {
            type: 'string',
            description: 'Owner avatar URL'
          },
          contact: {
            type: 'object',
            properties: {
              email: {
                type: 'string',
                description: 'Contact email address'
              },
              website: {
                type: 'string',
                description: 'Website URL'
              },
              location: {
                type: 'string',
                description: 'Geographic location'
              },
              company: {
                type: 'string',
                description: 'Company name'
              }
            }
          }
        }
      },
      permissions: {
        type: 'object',
        properties: {
          admin: {
            type: 'boolean',
            description: 'Administrative access permission'
          },
          push: {
            type: 'boolean',
            description: 'Push/write access permission'
          },
          pull: {
            type: 'boolean',
            description: 'Pull/read access permission'
          },
          maintain: {
            type: 'boolean',
            description: 'Maintain access permission'
          }
        }
      },
      topics: {
        type: 'array',
        description: 'Repository topics/tags',
        items: {
          type: 'string'
        }
      },
      license_info: {
        type: 'object',
        properties: {
          key: {
            type: 'string',
            description: 'License key identifier'
          },
          name: {
            type: 'string',
            description: 'License full name'
          },
          spdx_id: {
            type: 'string',
            description: 'SPDX license identifier'
          },
          url: {
            type: 'string',
            description: 'License URL'
          }
        }
      },
      statistics: {
        type: 'object',
        properties: {
          forks_count: {
            type: 'integer',
            description: 'Number of forks'
          },
          stargazers_count: {
            type: 'integer',
            description: 'Number of stars'
          },
          watchers_count: {
            type: 'integer',
            description: 'Number of watchers'
          },
          open_issues_count: {
            type: 'integer',
            description: 'Number of open issues'
          },
          subscribers_count: {
            type: 'integer',
            description: 'Number of subscribers'
          }
        }
      },
      features: {
        type: 'object',
        properties: {
          has_issues: {
            type: 'boolean',
            description: 'Whether issues are enabled'
          },
          has_projects: {
            type: 'boolean',
            description: 'Whether projects are enabled'
          },
          has_wiki: {
            type: 'boolean',
            description: 'Whether wiki is enabled'
          },
          has_pages: {
            type: 'boolean',
            description: 'Whether GitHub Pages is enabled'
          },
          has_downloads: {
            type: 'boolean',
            description: 'Whether downloads are enabled'
          }
        }
      },
      settings: {
        type: 'object',
        properties: {
          visibility: {
            type: 'string',
            description: 'Repository visibility'
          },
          archived: {
            type: 'boolean',
            description: 'Whether the repository is archived'
          },
          disabled: {
            type: 'boolean',
            description: 'Whether the repository is disabled'
          },
          is_template: {
            type: 'boolean',
            description: 'Whether this is a template repository'
          },
          allow_merge_commit: {
            type: 'boolean',
            description: 'Whether merge commits are allowed'
          },
          allow_squash_merge: {
            type: 'boolean',
            description: 'Whether squash merging is allowed'
          },
          allow_rebase_merge: {
            type: 'boolean',
            description: 'Whether rebase merging is allowed'
          },
          allow_auto_merge: {
            type: 'boolean',
            description: 'Whether auto-merge is allowed'
          },
          delete_branch_on_merge: {
            type: 'boolean',
            description: 'Whether to delete head branches after merge'
          }
        }
      },
      security: {
        type: 'object',
        properties: {
          advanced_security_enabled: {
            type: 'boolean',
            description: 'Whether advanced security is enabled'
          },
          secret_scanning_enabled: {
            type: 'boolean',
            description: 'Whether secret scanning is enabled'
          },
          secret_scanning_push_protection: {
            type: 'boolean',
            description: 'Whether push protection is enabled'
          },
          dependabot_security_updates: {
            type: 'boolean',
            description: 'Whether Dependabot security updates are enabled'
          }
        }
      }
    }
  }
};

/**
 * Generate field mapping data from OpenAPI spec
 */
export function generateOpenApiFieldMapping(provider: string, modelName: string) {
  let sourceProcessor: OpenApiProcessor;

  // Select the appropriate OpenAPI spec based on provider
  switch (provider.toLowerCase()) {
    case 'github':
      sourceProcessor = new OpenApiProcessor(githubOpenApiSpec);
      break;
    default:
      throw new Error(`Unsupported provider: ${provider}`);
  }

  // Create processor for unified schema
  const unifiedSpec = {
    components: {
      schemas: {
        [modelName]: unifiedSchemas[modelName as keyof typeof unifiedSchemas]
      }
    }
  };
  const targetProcessor = new OpenApiProcessor(unifiedSpec);

  // Process both schemas
  const sourceSchema = sourceProcessor.processSchema(getSourceSchemaName(provider, modelName));
  const targetSchema = targetProcessor.processSchema(modelName);

  if (!sourceSchema || !targetSchema) {
    throw new Error(`Schema not found for ${provider} ${modelName}`);
  }

  return {
    sourceFields: convertToHierarchicalField(sourceSchema),
    targetFields: convertToHierarchicalField(targetSchema)
  };
}

/**
 * Get the source schema name based on provider and model
 */
function getSourceSchemaName(provider: string, modelName: string): string {
  const schemaMap: Record<string, Record<string, string>> = {
    github: {
      repository: 'Repository',
      user: 'User',
      organization: 'Organization',
      pull_request: 'PullRequest',
      issue: 'Issue'
    }
  };

  return schemaMap[provider.toLowerCase()]?.[modelName] || modelName;
}

/**
 * Export updated PROVIDER_DATA_MODELS and UNIFIED_DATA_MODELS
 */
export function getOpenApiBasedDataModels() {
  try {
    const githubRepository = generateOpenApiFieldMapping('github', 'repository');

    return {
      PROVIDER_DATA_MODELS: {
        github: {
          repository: githubRepository.sourceFields
        }
      },
      UNIFIED_DATA_MODELS: {
        repository: githubRepository.targetFields
      }
    };
  } catch (error) {
    console.error('Error generating OpenAPI-based data models:', error);
    return {
      PROVIDER_DATA_MODELS: {},
      UNIFIED_DATA_MODELS: {}
    };
  }
}

/**
 * Example usage and test function
 */
export function testOpenApiFieldMapping() {
  console.log('Testing OpenAPI Field Mapping...');

  try {
    const result = generateOpenApiFieldMapping('github', 'repository');

    console.log('Source Fields (GitHub Repository):');
    console.log(JSON.stringify(result.sourceFields, null, 2));

    console.log('\nTarget Fields (Unified Repository):');
    console.log(JSON.stringify(result.targetFields, null, 2));

    return result;
  } catch (error) {
    console.error('Test failed:', error);
    return null;
  }
}