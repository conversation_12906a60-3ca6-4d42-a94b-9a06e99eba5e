import { zodResolver } from "@hookform/resolvers/zod";
import {
  Box,
  FormControl,
  FormControlLabel,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Switch,
  TextareaAutosize,
  TextField,
} from "@mui/material";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Form, FormField, FormItem } from "components/@extended/Form";
import ColorPicker from "components/@extended/ColorPicker";
import { UseMutateAsyncFunction } from "@tanstack/react-query";
import { toast } from "sonner";
import { parseError } from "lib/utils";
import { useEffect, useMemo, useState } from "react";
import Environment from "types/environment";
import { getIsEnvDefault } from "../utils";

const formSchema = z.object({
  isDefault: z.boolean(),
  region: z.string().nonempty("Region is required"),
  description: z.string().optional(),
  name: z.string().nonempty("Name is required"),
  color: z.string().nonempty("Color is required"),
  type: z.string().nonempty("Type is required"),
});

const defaultValues: Partial<FormValues> = {
  name: "",
  region: "us-east-1",
  description: "",
  color: "#3b82f6",
  isDefault: false,
  type: "TEST",
};

type FormValues = z.infer<typeof formSchema>;

type SubscriptionFlags = {
  isEnterprise: boolean;
  isLaunch: boolean;
  isBuild: boolean;
  isFreePlan: boolean;
  highestTier: "Enterprise" | "Launch" | "Build";
};

interface FormCompProps {
  formRef: React.RefObject<HTMLFormElement>;
  selectedKey?: string | null;
  selectedType?: string | null;
  attemptCreateEnvironment: UseMutateAsyncFunction<
    {},
    unknown,
    {
      payload: Record<string, any>;
    },
    unknown
  >;
  attemptEditEnvironment: UseMutateAsyncFunction<
    {},
    unknown,
    {
      payload: Record<string, any>;
      environmentId: string;
    },
    unknown
  >;
  selected?: Environment.Root | null;
  mode?: "create" | "edit";
  onClose: () => void;
  // New props for filtering
  subscriptionFlags: SubscriptionFlags;
  existingEnvironments: Environment.Root[];
}

// All available environment types
const ALL_ENVIRONMENT_TYPES = [
  { label: "Test", value: "Test", key: "TEST", type: "TEST" },
  { label: "Development", value: "Development", key: "DEV", type: "TEST" },
  { label: "Staging", value: "Staging", key: "STAGING" ,type: "TEST"},
  { label: "Production", value: "Production", key: "PROD", type: "LIVE" },
  { label: "Custom", value: "Custom", key: "CUSTOM", type: "TEST" },
];

const FormComp = ({
  formRef,
  selected,
  mode,
  selectedKey: currentKey,
  selectedType: currentType,
  attemptCreateEnvironment,
  attemptEditEnvironment,
  onClose,
  subscriptionFlags,
  existingEnvironments,
}: FormCompProps) => {

  const [selectedKey, setSelectedKey] = useState(currentKey || "TEST");
  const [selectedLabel, setSelectedLabel] = useState <string | null>(currentType || "TEST");
  const [selectedType, setSelectedType] = useState(currentKey === "PROD" ? "LIVE" : "TEST");
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
    mode: "onChange",
  });


  // Get available environment types based on subscription tier
  const getAvailableTypesByPlan = (flags: string) => {
    if (flags == "Enterprise") {
      return ALL_ENVIRONMENT_TYPES;
    } else if (flags == "Launch") {
      // Launch: Test and Production only
      return ALL_ENVIRONMENT_TYPES.filter((type) =>
        ["PROD", "TEST"].includes(type.key)
      );
    } else {
      // Free/Build: Test only
      return ALL_ENVIRONMENT_TYPES.filter((type) => type.key === "TEST");
    }
  };

  // Get already used environment types (exclude current environment if editing)
  const getUsedEnvironmentTypes = () => {
    return existingEnvironments
      .filter((env) => (mode === "edit" ? env.id !== selected?.id : true))
      .map((env) => env.key)
      .filter(Boolean);
  };

  // Calculate available types for dropdown
  const availableTypes = useMemo(() => {
    const planAllowedTypes = getAvailableTypesByPlan(subscriptionFlags);
    const usedTypes = getUsedEnvironmentTypes();

    return planAllowedTypes.map((type) => ({
      ...type,
      disabled: usedTypes.includes(type.key),
      // Add helper text for disabled items
      helperText: usedTypes.includes(type.key)
        ? `${type.label} environment already exists`
        : undefined,
    }));
  }, [subscriptionFlags, existingEnvironments, selected, mode, currentType]);
  // console.log(availableTypes, "availableTypes");
  // Check if current selection would be invalid
  const currentTypeValue = form.watch("type");
  const isCurrentTypeDisabled = availableTypes.find(
    (type) => type.value === currentTypeValue
  )?.disabled;

  // Reset form if current type becomes unavailable
  useEffect(() => {
    if (mode === "create" && isCurrentTypeDisabled) {
      const firstAvailableType = availableTypes.find((type) => !type.disabled);
      if (firstAvailableType) {
        form.setValue("type", firstAvailableType.value);
      }
    }
  }, [isCurrentTypeDisabled, availableTypes, form, mode]);

  const onCreate = async (values: FormValues) => {
    try {
      const payload = {
        type: selectedType,
        key: selectedKey,
        name: values.name,
        description: values.description,
        settings: {
          isDefault: values.isDefault ?? false,
          region: values.region,
          active: true,
          configOverrides: {
            apiRateLimitPerMinute: 1000,
            enableAuditLogging: true,
            logRetentionDays: 30,
          },
        },
        colorPicker: values.color,
        notifications: [
          {
            type: "ALL",
          },
        ],
      };

      toast.promise(attemptCreateEnvironment({ payload }), {
        loading: "Creating...",
        success: () => {
          return "Created";
        },
        error: (err) => {
          return parseError(err?.response?.data)?.message;
        },
      });
    } catch (error) {
      console.error(error);
    }
  };

  const onEdit = async (values: FormValues) => {
    const fieldPropertyMap: Record<string, string> = {
      name: "name",
      description: "description",
      region: "settings/region",
      isDefault: "settings/isDefault",
      color: "colorPicker",
      type: "type",
    };

    const payload = Object.entries(values).reduce(
      (acc: Record<"op" | "path" | "value", string | boolean>[], cur) => {
        const [key, value] = cur;

        if (!(form.formState.dirtyFields as any)[key]) return acc;
        if (fieldPropertyMap[key]) {
          acc.push({
            op: "replace",
            path: `/${fieldPropertyMap[key]}`,
            value,
          });
        }
        return acc;
      },
      []
    );

    if (!payload.length) {
      onClose();
      return;
    }

    try {
      toast.promise(
        attemptEditEnvironment({
          payload,
          environmentId: selected?.id as string,
        }),
        {
          loading: "Updating...",
          success: () => {
            return "Updated";
          },
          error: (err) => {
            return parseError(err?.response?.data)?.message;
          },
        }
      );
    } catch (error) {
      console.error(error);
    }
  };

  const region = [
    {
      label: "US East 1",
      value: "us-east-1",
    },
    {
      label: "US East 2",
      value: "us-east-2",
    },
    {
      label: "US East 3",
      value: "us-east-3",
    },
    {
      label: "US East 4",
      value: "us-east-4",
    },
  ];

  useEffect(() => {
    if (mode === "edit") {
      form.reset({
        ...defaultValues,
        name: selected?.name,
        region: selected?.settings?.region,
        description: selected?.description,
        isDefault: getIsEnvDefault(selected as Environment.Root),
        color: selected?.colorPicker as string,
        type: selected?.type,
      });
    }
  }, [selected]);

  return (
    <Form {...form}>
      <Box
        component={"form"}
        onSubmit={(...args) =>
          void form.handleSubmit(mode === "create" ? onCreate : onEdit)(...args)
        }
        ref={formRef}
        gap={3}
      >
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem label="Name">
                  <FormControl>
                    <TextField placeholder="Environment Name" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem label="Description">
                  <FormControl>
                    <TextField
                      placeholder="Description"
                      {...field}
                      multiline
                      rows={2}
                      maxRows={6}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </Grid>

          <Grid item xs={12} sx={{ display: "none" }}>
            <FormField
              control={form.control}
              name="region"
              render={({ field }) => (
                <FormItem label="Region">
                  <FormControl>
                    <InputLabel id="select-label">Region</InputLabel>
                    <Select label="Region" {...field}>
                      {region.map((domain) => (
                        <MenuItem value={domain?.value} key={domain?.value}>
                          {domain?.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </FormItem>
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem label="Type">
                  <FormControl>
                    <TextField
                      placeholder="Type"
                      {...field}
                      select={true}
                      value={selectedLabel || "TEST"}
                      label="Type"
                    >
                      {availableTypes.map((item, index) => {

                        return (
                          <MenuItem
                            value={item.value}
                            key={index.toString()}
                            disabled={item.disabled}
                            onClick={() => {
                              setSelectedKey(item.key);
                              setSelectedLabel(item?.label ?? "TEST");
                              setSelectedType(item.type);
                            }}
                            sx={{
                              "&.Mui-disabled": {
                                opacity: 0.5,
                                color: "text.disabled",
                              },
                            }}
                          >
                            <Box>
                              <div>{item.label}</div>
                              {item.disabled && (
                                <div
                                  style={{
                                    fontSize: "0.75rem",
                                    color: "text.secondary",
                                    fontStyle: "italic",
                                  }}
                                >
                                  Already exists
                                </div>
                              )}
                            </Box>
                          </MenuItem>
                        );
                      })}
                    </TextField>
                  </FormControl>
                </FormItem>
              )}
            />
          </Grid>

          <Grid item xs={12}>
            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem label="Color">
                  <FormControl>
                    <ColorPicker
                      {...field}
                      onQuickSelect={(updatedColor) => {
                        field.onChange({ target: { value: updatedColor } });
                      }}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </Grid>

          <Grid item xs={12} sx={{ display: "none" }}>
            <FormField
              control={form.control}
              name="isDefault"
              render={({ field }) => (
                <FormItem label="">
                  <FormControl>
                    <FormControlLabel
                      control={
                        <Switch checked={field.value === true} {...field} />
                      }
                      label="Set as default environment"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </Grid>
        </Grid>
      </Box>
    </Form>
  );
};

export default FormComp;
