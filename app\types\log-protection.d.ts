
import { LogProtectionConnectorType } from 'constants/log-protection';

import { Changelog, Organization } from "./common";


namespace LogProtectionNamespace {
   export interface WebhookConfig {
      url: string;
      securedSSLRequired?: boolean;
      contentType: string;
      apiKey?: string;
      secret?: string
   }
   export interface IntegrationConfig {
      service: {
         id: string;
      }
      integration: {
         id: string
      }
   }

   /**
 * Configuration for a webhook connector.
 * This configuration is used when the connector type is Webhook.
 * It includes the webhook configuration and does not allow a service profile configuration.
 */
   export interface WebhookConnectorConfig extends BaseConnectorConfig {
      type: LogProtectionConnectorType.Webhook
      webhookConfig: LogProtectionNamespace.WebhookConfig
      serviceProfileConfig?: never
   };

   /**
 * Configuration for an integration connector.
 * This configuration is used when the connector type is Integration.
 * It includes the service profile configuration and does not allow a webhook configuration.
 */
   export interface IntegrationConnectorConfig extends BaseConnectorConfig {
      type: LogProtectionConnectorType.Integration
      serviceProfileConfig?: IntegrationConfig
      webhookConfig: never
   };

   export type ConnectorTypeConfig = LogProtectionNamespace.WebhookConnectorConfig | LogProtectionNamespace.IntegrationConnectorConfig

}


interface BaseConnectorConfig {
   type: LogProtectionConnectorType;
}


type LogProtection = {
   id: string;
   unizoManagedLog: boolean;
   connectorTypeConfigs: Array<LogProtectionNamespace.ConnectorTypeConfig>;
   organization: Pick<Organization, 'id'>;
   changeRequest: {
      type: string;
      description: string;
      status: string;
      createdDateTime: string;
      updatedDateTime: string;
   };
   changeLog: Changelog;
}

export type { LogProtection, LogProtectionNamespace }