import React from 'react';
import { Box, Skeleton, Stack } from '@mui/material';

interface NavigationSkeletonProps {
  variant?: 'drawer' | 'horizontal';
}

const NavigationSkeleton: React.FC<NavigationSkeletonProps> = ({ variant = 'drawer' }) => {
  if (variant === 'horizontal') {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', px: 3, py: 1, gap: 3 }}>
        {Array.from({ length: 6 }).map((_, index) => (
          <Skeleton key={index} width={80} height={32} />
        ))}
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      {/* Logo/Brand */}
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
        <Skeleton variant="rectangular" width={40} height={40} />
        <Skeleton width={100} height={24} />
      </Box>

      {/* Menu Groups */}
      {Array.from({ length: 3 }).map((_, groupIndex) => (
        <Box key={groupIndex} sx={{ mb: 3 }}>
          <Skeleton width={80} height={16} sx={{ mb: 1, opacity: 0.7 }} />
          <Stack spacing={1}>
            {Array.from({ length: 4 }).map((_, itemIndex) => (
              <Box key={itemIndex} sx={{ display: 'flex', alignItems: 'center', gap: 1.5, px: 1.5, py: 1 }}>
                <Skeleton variant="circular" width={24} height={24} />
                <Skeleton width={120} height={20} />
              </Box>
            ))}
          </Stack>
        </Box>
      ))}
    </Box>
  );
};

export default NavigationSkeleton;