import { Stack, Typography } from "@mui/material"

type CardTitleProps = {
   title: string
   description?: string
   icon?: React.ReactElement
}

export const CardTitle = (props: CardTitleProps) => {

   const { title, icon, description } = props

   return (
      <Stack direction={'row'} gap={3} alignItems={'start'}>
         {icon && (
            icon
         )}
         <Stack className="gap-1">
            {title && (
               <Typography variant="h5" mt={0}>{title}</Typography>
            )}
            {description && (
               <Typography sx={{color:"secondary.600"}}>
                  {description}
               </Typography>
            )}
         </Stack>
      </Stack>
   )
}