import { BILLING_CATALOG, CHARGE_TYPE, ChargeRecurringTypeEnum, EntitlementLevel, EntitlementTypeEnum } from "constants/subscription.constant"
import { Entitlement } from "types/subscription";

export const useSubscription = () => {

   return {
      getChargeType: (type: ChargeRecurringTypeEnum) => CHARGE_TYPE.get(type),
      getEntitlementsUsage: (entitlements: Entitlement[]) => {
         const topLevel: any[] = [], collapsed: any[] = [], catalog: any[] = [];

         entitlements.forEach((item) => {
            const exist = BILLING_CATALOG?.[item?.type as EntitlementTypeEnum];
            if (exist) {
               exist.level === EntitlementLevel.topLevel ? topLevel.push(item) : collapsed.push(item)
               catalog.push(exist)
            }
         });
         return {
            topLevel,
            collapsed,
            getCatalogByType(type: EntitlementTypeEnum) {
               return catalog?.find((i) => i.type === type) ?? []
            }
         }
      },
      getBillingCatalog: () => BILLING_CATALOG
   }
}