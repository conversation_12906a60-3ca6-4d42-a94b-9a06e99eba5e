import { Alert, Stack, Typography, useTheme } from "@mui/material"
import { LinearProgressWithLabel } from "components/@extended/linearProgress"
import MainCard from "components/MainCard"
import { useGetIntegrationDetails } from "hooks/api/integration/useIntegration-details";
import ProductPlaceholder from 'components/cards/skeleton/ProductPlaceholder'
import moment from "moment";
import { useEffect, useMemo, useState } from "react";

export const RateLimit = () => {
   const { integration, integrationLoading } = useGetIntegrationDetails();

   const isRateLimitAvailable = useMemo(() => {
      return integration?.rateLimit
   }, [integration?.rateLimit])

   if (integrationLoading) return (
      <ProductPlaceholder />
   )

   return (
      !isRateLimitAvailable ? <EmptyMessage /> : <RateLimitUsage />
   )
}

const EmptyMessage = () => {
   return (
      <MainCard>
         <Stack direction="column" alignItems={'center'} spacing={2}>
            <Typography >  Unizo monitors rate limits by checking response headers during real-time calls or by regularly fetching updates from the customer’s API provider. This process helps us manage request frequency and maintain optimal performance. Please note that the rate limits themselves are enforced by the customer’s API provider.</Typography>
         </Stack>
      </MainCard>
   )
}

const RateLimitUsage = () => {
   const { palette } = useTheme()
   const { integration } = useGetIntegrationDetails();

   const nextReset = moment.utc(integration?.rateLimit?.resetDateTime).format('MMM D, YYYY hh:mm:ss A');
   const polledValue = moment.utc(integration?.rateLimit?.lastPolledDateTime).format('MMM D, YYYY hh:mm:ss A');
   const usedLimit = +integration?.rateLimit?.used;
   const totalLimit = +integration?.rateLimit?.limit;
   const nextResetTime = new Date(integration?.rateLimit?.resetDateTime);
   const currentTime = new Date();

   const [limitUsedPercentage, setLimitUsedPercentage] = useState(0);
   const [color, setColor] = useState("#4caf50"); // Default to green
   const timeDifference = integration?.rateLimit?.lastPolledDateTime ?? '-';
   let crossedFlag = false;
   let currentDate = moment();
   const resetWindowTimecrossed = integration?.rateLimit?.resetDateTime;

   if (currentDate.isAfter(resetWindowTimecrossed)) {
      crossedFlag = true;
   }

   useEffect(() => {
      // Calculate percentage of limit used
      const usedPercent = Number(((usedLimit / totalLimit) * 100).toFixed(2));

      // Calculate the time remaining until the next reset in minutes
      const timeRemaining = (nextResetTime.getTime() - currentTime.getTime()) / (1000 * 60); // in minutes

      // Total time from now until reset in minutes
      const totalTime = (nextResetTime.getTime() - currentTime.getTime()) / (1000 * 60); // in minutes

      // Calculate how much time has passed as a percentage of the total time available
      const timePassedPercentage = ((totalTime - timeRemaining) / totalTime) * 100;

      let barColor = "error"
      if (usedPercent <= 50) {
         barColor = "success"
      } else if (usedPercent > 50 && usedPercent >= timePassedPercentage) {
         barColor = "warning";
      } else if (usedPercent > 50 && usedPercent <= timePassedPercentage) {
         barColor = "warning"
      } else if (usedPercent >= 100) {
         barColor = "error";
      }
      setLimitUsedPercentage(usedPercent);
      setColor(barColor);
   }, [usedLimit, totalLimit, currentTime, nextResetTime]);


   return (
      <Stack gap={2}>
         <Typography sx={{ color: 'secondary.main' }}>
            Unizo monitors rate limits by checking response headers during real-time calls or by regularly fetching updates from the customer’s API provider. This process helps us manage request frequency and maintain optimal performance. Please note that the rate limits themselves are enforced by the customer’s API provider.
         </Typography>
         <div className="md:max-w-[50%]">
            <Stack gap={1}>
               <LinearProgressWithLabel color={'success'} value={crossedFlag ? 0 : limitUsedPercentage} />
               <Typography style={{ fontSize: "12px" }} sx={{ color: "secondary.main" }}> {timeDifference ? `Updated ${moment(timeDifference).local().fromNow()}` : '-'}</Typography>
            </Stack>
         </div>
         {crossedFlag && (
            <Alert children='There has been no API calls with this integration. Hence the assumption rate limit is 0.' severity="info" />
         )}

         <MainCard title='Features' secondary={
            <Stack direction={'row'}>
               <Typography>Next Reset:&nbsp;</Typography>
               <Typography sx={{ color: "secondary.main" }} >
                  {crossedFlag ?
                     'To be determined'
                     : nextReset}
               </Typography>
            </Stack>
         }>
            <div className="grid grid-cols-4 gap-y-2 mt-2">
               <div >Used</div>
               <div className="col-span-3" style={{ color: palette?.secondary.main }}>
                  <strong>{crossedFlag ? 0 : integration?.rateLimit?.used}</strong> / {crossedFlag ? 1000 : integration?.rateLimit?.limit}
               </div>
               <div>Remaining</div>
               <div className="col-span-3" style={{ color: palette?.secondary.main }}>
                  {crossedFlag ? 1000 : integration?.rateLimit?.remaining}
               </div>
               <div>Last Polled	</div>
               <div className="col-span-3" style={{ color: palette?.secondary.main }}>
                  {polledValue}
               </div>
            </div>
         </MainCard>
      </Stack>
   )
}