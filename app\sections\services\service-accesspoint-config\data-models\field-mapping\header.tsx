import { ServiceProfile } from "types/service-profile";
import { Box, useTheme, Stack,Typography, ToggleButtonGroup, ToggleButton } from "@mui/material";

import { ModeTypes } from "./type";
import { styles } from "./styles";
import { GitBranch, Table2 } from "lucide-react";

interface HeaderProps {
   serviceProfile: ServiceProfile
   viewMode: ModeTypes
   setViewMode: React.Dispatch<React.SetStateAction<ModeTypes>>
}

const Header = ({ serviceProfile, viewMode, setViewMode }: HeaderProps) => {
   const theme = useTheme();
   return (
      <Box sx={{ px: 3, pb: 0 }}>
         <Stack
            direction={{ xs: "column", sm: "row" }}
            justifyContent="space-between"
            alignItems={{ xs: "stretch", sm: "center" }}
            spacing={{ xs: 2, sm: 3 }}
            sx={{ mb: 2 }}
         >
            <Typography variant="body2" color="text.secondary" sx={{ flex: 1 }}>
               Map fields from {serviceProfile?.name || "Source"} to the Unizo
               Unified Common data model. Select an object to map its nested
               fields. You can create new fields as needed.
            </Typography>
            {/* <ToggleButtonGroup
               value={viewMode}
               exclusive
               onChange={(_, value) => value && setViewMode(value)}
               size="small"
               sx={() => styles.toggleGroupStyles({ theme, viewMode })}
            >
               <ToggleButton value="table">
                  <Table2 size={16} />
                  <Typography>Table View</Typography>
               </ToggleButton>
               <ToggleButton value="visual">
                  <GitBranch size={16} />
                  <Typography>Visual View</Typography>
               </ToggleButton>
            </ToggleButtonGroup> */}
         </Stack>
      </Box>
   )
}

export default Header;
