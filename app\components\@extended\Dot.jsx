import PropTypes from 'prop-types';
// material-ui
import { useTheme } from '@mui/material';
import Box from '@mui/material/Box';

// project import
import getColors from '../../utils/getColors';

function isHexColor(str) {
  return typeof str === 'string' && /^#([0-9A-Fa-f]{3}){1,2}$/.test(str);
}

export default function Dot({ color: colorProp, size, variant, sx }) {
  const theme = useTheme();
  let main;
  if (isHexColor(colorProp)) {
    main = colorProp;
  } else {
    const colors = getColors(theme, colorProp || 'primary');
    main = colors.main;
  }

  return (
    <Box
      sx={{
        width: size || 8,
        height: size || 8,
        borderRadius: '50%',
        bgcolor: variant === 'outlined' ? '' : main,
        ...(variant === 'outlined' && { border: `1px solid ${main}` }),
        ...sx,
      }}
    />
  );
}

Dot.propTypes = { color: PropTypes.any, size: PropTypes.number, variant: PropTypes.string, sx: PropTypes.any };
