
import { Suspense, lazy, useMemo } from 'react';

import { Grid, GridProps, Skeleton, Stack } from "@mui/material";
import { DISABLED_STATE_STYLE } from '../constant';
import useManageProfile from '../service-context/use-manage-profile';
import { State } from 'hooks/useStatus';

const SandboxConfig = lazy(() => import('../sandbox-config'));
const VersionsSP = lazy(() => import('../enterprise-version'));
const ServiceTypeConfig = lazy(() => import('../service-accesspoint-config/index'));


const DetailedPanel = () => {

   const {
      serviceProfileAccessPoints: data,
      selectedService,
   } = useManageProfile()

   const isActive = useMemo(() => {
      return (
         selectedService?.service && selectedService?.service?.state === State.ACTIVE
      )
   }, [selectedService])

   return (
      <>
         <Stack
            gap={3}
            sx={{
               ...{
                  ...(isActive ? {} : DISABLED_STATE_STYLE),
               }
            }}
         >
            {/* VERSIONS */}
            <Suspense fallback={null}>
               <VersionsSP />
            </Suspense>

            {/* SERVICE TYPE CONFIG */}
            <Suspense fallback={null}>
               <ServiceTypeConfig />
            </Suspense>

            <Suspense fallback={null}>
               <SandboxConfig />
            </Suspense>
         </Stack>
      </>
   )
}

export default DetailedPanel;