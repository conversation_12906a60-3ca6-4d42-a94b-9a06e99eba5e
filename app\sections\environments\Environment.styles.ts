import { styled } from '@mui/material/styles';
import { Card, Box, alpha, IconButton } from '@mui/material';

// Environment Card Styles
export const StyledEnvironmentCard = styled(Card)<{ envColor?: string }>(({ theme, envColor = '#1976d2' }) => ({
  width: '100%',
  height: 160,
  borderRadius: 0,
  padding: theme.spacing(2.5),
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  position: 'relative',
  overflow: 'visible',
  boxShadow: theme.shadows[1],
  transition: 'box-shadow 0.2s, transform 0.2s',
  backgroundColor: theme.palette.background.paper,
  '&:hover': {
    boxShadow: theme.shadows[3],
    transform: 'translateY(-2px)',
  },
}));

// Template Card Styles
export const StyledTemplateCard = styled(Card)<{ envColor: string; locked: boolean }>(
  ({ theme, envColor, locked }) => ({
    width: '100%',
    height: 160,
    textAlign: 'center',
    borderStyle: 'dashed',
    borderColor: locked ? theme.palette.divider : envColor,
    borderWidth: 1,
    borderRadius: 0,
    boxShadow: 'none',
    padding: theme.spacing(2.5),
    cursor: locked ? 'not-allowed' : 'pointer',
    opacity: locked ? 0.5 : 1,
    transition: 'all 0.2s ease',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing(2),
    backgroundColor: theme.palette.background.paper,
    '&:hover': locked
      ? {}
      : {
          borderColor: envColor,
          backgroundColor: theme.palette.mode === 'dark' 
            ? alpha(envColor, 0.08)
            : alpha(envColor, 0.04),
          transform: 'translateY(-2px)',
        },
  })
);

// Plan Card Styles
export const StyledPlanCard = styled(Card)(({ theme }) => ({
  borderRadius: 0,
  border: `1px solid ${theme.palette.divider}`,
  boxShadow: 'none',
  marginBottom: theme.spacing(2),
  padding: 0,
  backgroundColor: theme.palette.mode === 'dark'
    ? alpha(theme.palette.info.main, 0.08)
    : alpha(theme.palette.info.main, 0.04),
}));

// Icon Container Styles
export const StyledIconContainer = styled(Box)<{ iconColor: string }>(({ theme, iconColor }) => ({
  border: 'none',
  borderRadius: 0,
  width: 40,
  height: 40,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: theme.palette.mode === 'dark'
    ? alpha(iconColor, 0.15)
    : alpha(iconColor, 0.08),
  flexShrink: 0,
}));

// Plan Badge Styles
export const StyledPlanBadge = styled(Box)(({ theme }) => ({
  paddingLeft: theme.spacing(1),
  paddingRight: theme.spacing(1),
  paddingTop: theme.spacing(0.25),
  paddingBottom: theme.spacing(0.25),
  borderRadius: 0,
  fontSize: 11,
  fontWeight: 600,
  textTransform: 'uppercase',
  background: theme.palette.mode === 'dark'
    ? alpha(theme.palette.primary.main, 0.2)
    : alpha(theme.palette.primary.main, 0.1),
  color: theme.palette.primary.main,
  letterSpacing: 0.5,
  marginLeft: theme.spacing(1),
}));

// Layout Components
export const CardHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginBottom: theme.spacing(1),
}));

export const CardContent = styled(Box)(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'flex-start',
}));

export const ActionButtons = styled(Box)(({ theme }) => ({
  display: 'flex',
  gap: theme.spacing(0.5),
  marginTop: 'auto',
  paddingTop: theme.spacing(1),
  borderTop: `1px solid ${theme.palette.divider}`,
}));

export const PlanInfo = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2),
}));

export const TemplateContent = styled(Box)(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  gap: theme.spacing(0.5),
}));

// Styled Icon Button that handles color prop
export const StyledEditButton = styled(IconButton)<{ iconColor?: string }>(({ theme, iconColor }) => ({
  padding: theme.spacing(0.5),
  color: theme.palette.text.secondary,
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
    color: iconColor || theme.palette.primary.main,
  },
  '&:disabled': {
    color: theme.palette.action.disabled,
  },
}));

// Template Icon Wrapper - to handle icon styling without inline styles
export const TemplateIconWrapper = styled(Box)<{ iconColor: string }>(({ theme, iconColor }) => ({
  color: theme.palette.mode === 'dark' ? iconColor : alpha(iconColor, 0.9),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
}));