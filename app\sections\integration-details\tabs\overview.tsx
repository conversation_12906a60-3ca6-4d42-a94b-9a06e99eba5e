import { useEffect, useMemo, useState } from "react";

import {
  Button,
  Grid,
  GridProps,
  IconButton,
  List,
  ListItem,
  Stack,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
} from "@mui/material";
import MainCard from "components/MainCard";

import { AccessTypeEnums, DEPLOYMENT_MODAL } from "../constant";

import { useGetIntegrationDetails } from "hooks/api/integration/useIntegration-details";
import { useStatus } from "hooks/useStatus";
import CopyableText from "components/@extended/Copyable-text";

const MAIN_GRID_VALUES: GridProps = {
  xs: 12,
  lg: 6,
};

interface CopyableIdProps {
  label?: string;
  value: string;
}
export const Overview = () => {
  return (
    <>
      <Grid container spacing={3}>
        <Grid item {...MAIN_GRID_VALUES}>
          <Basics />
        </Grid>
        <Grid item {...MAIN_GRID_VALUES}>
          <Authorizations />
        </Grid>
        <Grid item {...MAIN_GRID_VALUES}>
          <Customer />
        </Grid>
        <Grid item {...MAIN_GRID_VALUES}>
          <Provider />
        </Grid>
      </Grid>
    </>
  );
};


const Basics = () => {
  const matchDownMD = useMediaQuery((theme: any) =>
    theme.breakpoints.down("md")
  );

  const { integration } = useGetIntegrationDetails(),
    { renderStatus } = useStatus();

  const name = integration?.name ?? "";

  return (
    <MainCard title="Basic">
      <List sx={{ py: 0 }}>
        <ListItem divider={!matchDownMD}>
          <Grid container spacing={3}>
            <Grid item xs={12} xl={6}>
              <Stack spacing={0.5}>
                <Typography color="secondary">Name</Typography>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <CopyableText text={name}>
                    <Typography>{name}</Typography>
                  </CopyableText>
                </Stack>
              </Stack>
            </Grid>

            <Grid item xs={12} xl={6}>
              <Stack spacing={0.5}>
                <Typography color="secondary">Id</Typography>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <CopyableText text={integration?.id}>
                    <Typography>{integration?.id}</Typography>
                  </CopyableText>
                </Stack>
              </Stack>
            </Grid>
          </Grid>
        </ListItem>
        <ListItem>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Stack spacing={0.5}>
                <Typography color="secondary">State</Typography>
                {renderStatus(integration?.state)}
              </Stack>
            </Grid>
          </Grid>
        </ListItem>
      </List>
    </MainCard>
  );
};

const Authorizations = () => {
  const { integration } = useGetIntegrationDetails();

  const accessType: any = useMemo(
    () => integration?.target?.accessPoint,
    [integration?.target]
  );

  return (
    <MainCard title="Authorization">
      <List sx={{ py: 0 }}>
        <ListItem>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Stack spacing={0.5}>
                <Typography color="secondary">Type</Typography>
                <Typography>
                  {(AccessTypeEnums as any)?.[
                    (accessType as any)?.accessPointTypeConfig?.type
                  ] ?? accessType?.accessPointTypeConfig?.type}
                </Typography>
              </Stack>
            </Grid>
          </Grid>
        </ListItem>
      </List>
    </MainCard>
  );
};

const Customer = () => {
  const { integration } = useGetIntegrationDetails();

  return (
    <MainCard title="Customer">
      <List sx={{ py: 0 }}>
        <ListItem>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Stack spacing={0.5}>
                <Typography color="secondary">Name</Typography>
                <Typography> {integration?.subOrganization?.name}</Typography>
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <Stack spacing={0.5}>
                <Typography color="secondary">Key</Typography>
                <Typography>
                  {integration?.subOrganization?.externalKey}
                </Typography>
              </Stack>
            </Grid>
          </Grid>
        </ListItem>
      </List>
    </MainCard>
  );
};

const Provider = () => {
  const matchDownMD = useMediaQuery((theme: any) =>
    theme.breakpoints.down("md")
  );

  const { integration } = useGetIntegrationDetails();

  const deploymentType = useMemo(
    () => integration?.target?.accessPoint?.deploymentTypeConfig,
    [integration?.target?.accessPoint]
  );

  const isSelfManaged = deploymentType?.type === DEPLOYMENT_MODAL.SELF_MANAGED;

  return (
    <MainCard title="Provider">
      <List sx={{ py: 0 }}>
        <ListItem divider={!matchDownMD && isSelfManaged}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Stack spacing={0.5}>
                <Typography color="secondary">Name</Typography>
                <Typography> {integration?.serviceProfile?.name}</Typography>
              </Stack>
            </Grid>
            <Grid item xs={12} md={6}>
              <Stack spacing={0.5}>
                <Typography color="secondary">Deployment</Typography>
                <Typography>
                  {deploymentType?.type ?? DEPLOYMENT_MODAL.SASS}
                </Typography>
              </Stack>
            </Grid>
          </Grid>
        </ListItem>

        {isSelfManaged ? (
          <ListItem>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Stack spacing={0.5}>
                  <Typography color="secondary">Version</Typography>
                  <Typography>
                    {deploymentType?.version?.name ??
                      deploymentType?.version?.id}
                  </Typography>
                </Stack>
              </Grid>
            </Grid>
          </ListItem>
        ) : null}
      </List>
    </MainCard>
  );
};
