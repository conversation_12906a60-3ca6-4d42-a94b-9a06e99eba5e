import { API_ENDPOINTS } from "utils/api/api-endpoints";
import fetchInstance from "utils/api/fetchinstance";

export const DocksClient = {
  createDockProfiles: (payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.DOCK_PROFILES}`, payload);
  },

  getDockProfiles: () => {
    return fetchInstance.get(`${API_ENDPOINTS.DOCK_PROFILES}`);
  },
  getDockProfileById: (id: string) => {
    return fetchInstance.get(`${API_ENDPOINTS.DOCK_PROFILES}/${id}`);
  },
  createServiceKeys: (payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.SERVICE_KEYS}`, payload);
  },
  updateDockProfiles :(id:string,payload: Record<string, any>) =>{
    return fetchInstance.patch(`${API_ENDPOINTS.DOCK_PROFILES}/${id}`, payload);
  },
  deleteDockProfiles: (id:string) =>{
    return fetchInstance.delete(`${API_ENDPOINTS.DOCK_PROFILES}/${id}`);
  }
};
