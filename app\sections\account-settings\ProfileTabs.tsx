import PropTypes from 'prop-types';
import { useEffect, useMemo, useState } from 'react';
import { Link } from '@remix-run/react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import FormLabel from '@mui/material/FormLabel';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';

import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project import
import ProfileTab from './ProfileTab';
import MainCard from 'components/MainCard';
import Avatar from 'components/@extended/Avatar';


// assets
import useUserDetails from 'store/user';

// ==============================|| USER PROFILE - TAB CONTENT ||============================== //

export default function ProfileTabs({ setSelectedTab, selectedTab }: Record<string, any>) {
  const theme: any = useTheme();

  const { user } = useUserDetails();

  const userDetails = useMemo(() => {
    return {
      name: `${user?.firstName} ${user?.lastName}`,
      email: user?.email,
      initials: user ? `${user?.firstName?.charAt(0)}${user?.lastName?.charAt(0)}` : null
    }
  }, [user])


  return (
    <MainCard>
      <Grid container spacing={6}>
        <Grid item xs={12}>
          <Stack spacing={2.5} alignItems="center">
            <FormLabel
              htmlFor="change-avtar"
              sx={{
                position: 'relative',
                borderRadius: '50%',
                overflow: 'hidden',
                '&:hover .MuiBox-root': { opacity: 1 },
                cursor: 'pointer'
              }}
            >
              <Avatar
                alt={`user:${user?.firstName}`}
                sx={{ width: 124, height: 124, border: '1px dashed', fontSize: '2rem' }}
                children={userDetails?.initials}
                type={undefined}
              />
            </FormLabel>
            <Stack spacing={0.5} alignItems="center">
              <Typography variant="h5">{userDetails?.name}</Typography>
              <Typography color="secondary">{userDetails?.email}</Typography>
            </Stack>
          </Stack>
        </Grid>
        <Grid item sm={3} sx={{ display: { sm: 'block', md: 'none' } }} />

        <Grid item xs={12}>
          <ProfileTab setSelectedTab={setSelectedTab} selectedTab={selectedTab} />
        </Grid>
      </Grid>
    </MainCard>
  );
}

ProfileTabs.propTypes = { focusInput: PropTypes.func };
