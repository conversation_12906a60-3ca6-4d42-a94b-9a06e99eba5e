import { 
  GitBranch, 
  Ticket, 
  Package, 
  MessageCircle, 
  AlertTriangle, 
  Shield,
  Key,
  Activity,
  UserCheck,
  Cloud,
  ShieldCheck,
  Sparkles,
  Database,
  Folder,
  Layers,
  Rocket
} from 'lucide-react';

export interface Domain {
  name: string;
  label: string;
  value: string;
  key: string;
  hook: string;
  url?: string;
  icon: any;
  getUpgraded: () => string;
  subscription?: {
    code: string;
  };
  released: boolean;
  isNew: boolean;
  externalKey: string[];
  color: string;
  totalServiceProfile?: number;
  description: string;
  visibility: {
    showInFeatures: string[];
    hideInFeatures: string[];
  };
  connectors?: {
    total: number;
    items: Array<{
      id: string;
      image: string;
    }>;
  };
}

export const DOMAINS: Domain[] = [
  {
    name: 'Source Code',
    label: 'Source Code',
    value: 'SCM',
    key: 'SCM',
    hook: 'SCM_WATCH_HOOK',
    url: 'scm',
    icon: GitBranch,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'Source Control'
    },
    released: true,
    isNew: false,
    externalKey: ['SCM_API', 'SCM_2_WAY_API'],
    color: 'rgb(250, 173, 20)',
    totalServiceProfile: 6,
    description: 'Manages source code repositories and enables collaboration through version control systems.',
    visibility: {
      showInFeatures: ['quick-start', 'connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view']
    },
    connectors: {
      total: 6,
      items: [
        {
          id: '7ae4ae78-7e98-4995-9e16-547983871d38',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/github/github_64.png'
        },
        {
          id: '19078c0a-c343-4087-89ae-87ab06d0280a',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/gitlab/gitlab_64.png'
        },
        {
          id: 'b5ce7dfc-ca26-47f9-bdad-10218c967907',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/bitbucket/bitbucket_64.png'
        },
        {
          id: 'f188473b-4f15-4526-b2ba-d480a9ed3024',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/azure_devops/azure_devops_64.png'
        }
      ]
    },
    supportsWebhooks: true,
    webhookEvents: [
      {
        key: 'organization:renamed',
        label: 'Organization Renamed',
        description: "Event broadcast when the organization's name was changed",
        triggerConditions: 'A new organization is added to the source control system'
      },
      {
        key: 'organization:deleted',
        label: 'Organization Deleted',
        description: 'Triggered when an organization is deleted',
        triggerConditions: 'An existing organization is removed from the system'
      },
      {
        key: 'repository:created',
        label: 'Repository Created',
        description: 'Triggered when a new repository is created',
        triggerConditions: 'A new repository is initialized or imported'
      },
      {
        key: 'repository:deleted',
        label: 'Repository Deleted',
        description: 'Triggered when a repository is deleted',
        triggerConditions: 'A repository is permanently removed'
      },
      {
        key: 'branch:created',
        label: 'Branch Created',
        description: 'Triggered when a new branch is created',
        triggerConditions: 'A new branch is pushed to the repository'
      },
      {
        key: 'branch:deleted',
        label: 'Branch Deleted',
        description: 'Triggered when a branch is deleted',
        triggerConditions: 'A branch is removed from the repository'
      },
      {
        key: 'commit:created',
        label: 'Commit Created',
        description: 'Triggered when new commits are pushed',
        triggerConditions: 'One or more commits are pushed to any branch'
      },
      {
        key: 'commit:updated',
        label: 'Commit Updated',
        description: 'Triggered when commits are updated',
        triggerConditions: 'Commits are force-pushed or updated'
      },
    ]
  },
  {
    name: 'Ticketing',
    value: 'TICKETING',
    label: 'Ticketing',
    key: 'TICKETING',
    hook: 'TICKETING_WATCH_HOOK',
    url: 'ticketing',
    icon: Ticket,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'Ticketing'
    },
    released: true,
    isNew: false,
    externalKey: ['TICKETING_API', 'TICKETING_2_WAY_API'],
    color: 'rgb(82, 196, 26)',
    totalServiceProfile: 11,
    description: 'Tracks and manages tasks, issues, and service requests through a ticketing system.',
    visibility: {
      showInFeatures: ['quick-start', 'connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view']
    },
    connectors: {
      total: 11,
      items: [
        {
          id: '93185d3e-f8ca-49b8-b873-859381fc2f3a',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/jira/jira_64.png'
        },
        {
          id: '92003c35-0f66-4a42-a193-75afe9cf3411',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/servicenow/servicenow_64.png'
        },
        {
          id: '0abb7477-97de-4df1-8764-21fbe78324f7',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/linear/linear_64.png'
        },
        {
          id: '1e745980-b5b9-41da-b04f-e8b0a6f4cc84',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/github/github_64.png'
        }
      ]
    },
    supportsWebhooks: true,
    webhookEvents: [
      {
        key: 'ticket:created',
        label: 'Ticket Created',
        description: 'Triggered when a new ticket is created',
        triggerConditions: 'A new ticket, issue, or task is created in the system'
      },
      {
        key: 'ticket:updated',
        label: 'Ticket Updated',
        description: 'Triggered when ticket details are modified',
        triggerConditions: 'Any field in the ticket is updated (title, description, priority, etc.)'
      },
      {
        key: 'ticket:deleted',
        label: 'Ticket Deleted',
        description: 'Triggered when a ticket is deleted',
        triggerConditions: 'A ticket is permanently removed from the system'
      },
      {
        key: 'attachment:created',
        label: 'Attachment Created',
        description: 'A new attachment has been added to a ticket',
        triggerConditions: 'One or more files are uploaded to a ticket'
      },
      {
        key: 'attachment:deleted',
        label: 'Attachment Deleted',
        description: 'Triggered when attachments are removed from a ticket',
        triggerConditions: 'Files are deleted from a ticket'
      },
      {
        key: 'comment:created',
        label: 'Comment Created',
        description: 'Triggered when a comment is added to a ticket',
        triggerConditions: 'A new comment or note is posted on a ticket'
      },
      {
        key: 'comment:updated',
        label: 'Comment Updated',
        description: 'Triggered when a comment is edited',
        triggerConditions: 'An existing comment is modified'
      },
      {
        key: 'comment:deleted',
        label: 'Comment Deleted',
        description: 'Triggered when a comment is removed',
        triggerConditions: 'A comment is deleted from a ticket'
      },
      {
        key: 'user:created',
        label: 'User Created',
        description: 'Triggered when a new user is added to the ticketing system',
        triggerConditions: 'A new user account is created'
      },
      {
        key: 'user:updated',
        label: 'User Updated',
        description: 'Triggered when a user is deactivated',
        triggerConditions: 'User account is disabled or deactivated'
      },
      {
        key: 'user:deleted',
        label: 'User Deleted',
        description: 'Triggered when a user is deleted',
        triggerConditions: 'User account is permanently removed'
      },
      {
        key: 'link:created',
        label: 'Link Created',
        description: 'Triggered when tickets are linked together',
        triggerConditions: 'A relationship is created between two or more tickets'
      },
      {
        key: 'link:deleted',
        label: 'Link Deleted',
        description: 'Triggered when ticket links are removed',
        triggerConditions: 'A relationship between tickets is removed'
      },
    ]
  },
  {
    name: 'Packages & Container registry',
    value: 'PCR',
    label: 'Packages & Container registry',
    key: 'PCR',
    hook: 'PCR_WATCH_HOOK',
    url: 'pcr',
    icon: Package,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'Pcr'
    },
    released: true,
    isNew: false,
    externalKey: ['PCR_API', 'PCR_2_WAY_API'],
    color: 'rgb(255, 77, 79)',
    totalServiceProfile: 8,
    description: 'Handles storage and distribution of software packages and container images.',
    visibility: {
      showInFeatures: ['quick-start', 'connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view']
    },
    connectors: {
      total: 8,
      items: [
        {
          id: 'd46d33f2-9a1a-47d4-8799-7572845fcc4f',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/docker/docker_64.png'
        },
        {
          id: 'e4bda496-bf15-4d58-9c6d-0a8d3933f3db',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/jfrog/jfrog_64.png'
        },
        {
          id: '2d7dbb2b-afb1-47aa-accb-282b0ca2757a',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/nexus/nexus_64.png'
        },
        {
          id: '08c1fda9-efc0-484d-a88b-3c713316b4a9',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/google_artifact_registry/google_artifact_registry_64.png'
        }
      ]
    },
    supportsWebhooks: true,
    webhookEvents: [
      {
        key: 'artifact:created',
        label: 'Artifact Created',
        description: 'Triggered when a new artifact is pushed to the registry',
        triggerConditions: 'A package, container image, or artifact is uploaded'
      },
      {
        key: 'artifact:deleted',
        label: 'Artifact Deleted',
        description: 'Triggered when an artifact is removed from the registry',
        triggerConditions: 'An artifact is permanently deleted'
      },
    ]
  },
  {
    name: 'Communications',
    value: 'COMMS',
    label: 'Communications',
    key: 'COMMS',
    hook: 'COMMS_WATCH_HOOK',
    url: 'comms',
    icon: MessageCircle,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'COMMS'
    },
    released: true,
    isNew: false,
    externalKey: ['COMMUNICATIONS_API', 'COMMUNICATIONS_2_WAY_API'],
    color: '#2F3061',
    totalServiceProfile: 3,
    description: 'Facilitates real-time messaging, notifications, and collaboration tools.',
    visibility: {
      showInFeatures: ['quick-start', 'connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view']
    },
    connectors: {
      total: 3,
      items: [
        {
          id: 'd6b2e612-752e-4583-a239-fe9b2bf7962c',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/slack/slack_64.png'
        },
        {
          id: '7921b160-936f-42b8-943b-b72e0e1eb117',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/microsoft_teams/microsoft_teams_64.png'
        },
        {
          id: 'a44e179b-e5f7-4e61-b5a9-a44aa848477f',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/google_chat/google_chat_64.png'
        }
      ]
    },
    supportsWebhooks: false,
    webhookEvents: []
  },
  {
    name: 'Incident management',
    value: 'INCIDENT',
    label: 'Incident management',
    key: 'INCIDENT',
    hook: 'INCIDENT_WATCH_HOOK',
    url: 'incident-management',
    icon: AlertTriangle,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'INCIDENT'
    },
    released: true,
    isNew: false,
    externalKey: ['INCIDENT_API', 'INCIDENT_2_WAY_API'],
    color: '#1677ff',
    totalServiceProfile: 7,
    description: 'Handles detection, reporting, and resolution of incidents affecting services.',
    visibility: {
      showInFeatures: ['quick-start', 'connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view']
    },
    connectors: {
      total: 7,
      items: [
        {
          id: '0f08a0dc-bbd5-4927-bc03-6624ba06bcd9',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/pager_duty/pagerduty_64.png'
        },
        {
          id: '5fe3c71c-e098-452c-9d76-abebdac2fb97',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/opsgenie/opsgenie_64.png'
        },
        {
          id: 'a685b14d-01a4-4012-8b18-5a661c3f77db',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/servicenow/servicenow_64.png'
        },
        {
          id: '3d9b51ca-56ed-4df6-9ffe-b1028478c643',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/victorops/victorops_64.png'
        }
      ]
    },
    supportsWebhooks: true,
    webhookEvents: [
      {
        key: 'incident:created',
        label: 'Incident Created',
        description: 'Triggered when a new incident is reported',
        triggerConditions: 'A new incident is created manually or through monitoring alerts'
      },
      {
        key: 'incident:deleted',
        label: 'Incident Deleted',
        description: 'Triggered when an incident is acknowledged by responder',
        triggerConditions: 'A team member acknowledges the incident'
      },
    ]
  },
  {
    name: 'Vulnerability management',
    value: 'VMS',
    label: 'Vulnerability management',
    key: 'VMS',
    hook: 'VMS_WATCH_HOOK',
    url: 'vms',
    icon: Shield,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'VMS'
    },
    released: true,
    isNew: false,
    externalKey: ['VMS_API', 'VMS_2_WAY_API'],
    color: '#f58d09',
    totalServiceProfile: 8,
    description: 'Identifies, evaluates, and mitigates security vulnerabilities in systems.',
    visibility: {
      showInFeatures: ['quick-start', 'connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view']
    },
    connectors: {
      total: 8,
      items: [
        {
          "id": "52ba99a5-635f-429c-9503-cf640cc5927b",
          "image": "https://unizopublicpaas.blob.core.windows.net/imgs/nessus/nessus_64.png"
        },
        {
          "id": "90911cd1-d1e6-4bb5-aaf0-98ac94d8e1a7",
          "image": "https://unizopublicpaas.blob.core.windows.net/imgs/rapid7/rapid7_64.png"
        },
        {
          "id": "a5506e68-764c-4935-a17e-49f13d3fdb0f",
          "image": "https://unizopublicpaas.blob.core.windows.net/imgs/blackduck/blackduck_64.png"
        },
        {
          "id": "d8e9e958-e4be-4fd1-a7b9-a07f8b4595d0",
          "image": "https://unizopublicpaas.blob.core.windows.net/imgs/semgrep/semgrep_64.png"
        }
      ]
    },
    supportsWebhooks: false,
    webhookEvents: []
  },
  {
    name: 'Key Management',
    value: 'KMS',
    label: 'Key management',
    key: 'KMS',
    hook: 'KMS_WATCH_HOOK',
    icon: Key,
    released: true,
    isNew: false,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    externalKey: ['KMS_WATCH_HOOK', 'KMS_WATCH_HOOK_2_WAY'],
    color: 'cyan',
    description: 'Securely manages cryptographic keys for data protection and encryption.',
    visibility: {
      showInFeatures: ['quick-start','connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view', 'quick-start']
    },
    connectors: {
      total: 3,
      items: [
        {
          id: '04a24daf-f99b-48e7-aa47-3c0260eaf469',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/aws/aws_64.png'
        },
        {
          id: '5c813c33-2109-421a-97e1-8f4e5bbc5052',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/azureKV/azureKV_64.png'
        },
        {
          id: 'e2380bfd-f373-42b1-9708-6f01bee7e0e8',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/hashiCorp/hashiCorp_64.png'
        }
      ]
    },
    supportsWebhooks: false,
    webhookEvents: []
  },
  {
    name: 'Observability',
    value: 'MONITORING',
    label: 'Observability',
    key: 'MONITORING',
    hook: 'MONITORING_WATCH_HOOK',
    url: 'observability',
    icon: Activity,
    released: true,
    isNew: true,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    externalKey: ['MONITORING_WATCH_HOOK', 'MONITORING_WATCH_HOOK_2_WAY'],
    color: 'teal',
    totalServiceProfile: 12,
    description: 'Provides observability into system performance, uptime, and health metrics.',
    visibility: {
      showInFeatures: ['quick-start', 'connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view']
    },
    connectors: {
      total: 2,
      items: [
        {
          id: '3b68ca77-ea72-4b08-a7f8-9e8602bb3172',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/datadog/datadog_64.png'
        },
        {
          id: '0db62249-c626-47c1-b391-8814658da489',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/newrelic/newrelic_64.png'
        }
      ]
    },
    supportsWebhooks: false,
    webhookEvents: []
  },
  {
    name: 'Identity',
    value: 'IDENTITY',
    label: 'Identity',
    key: 'IDENTITY',
    hook: 'IDENTITY_WATCH_HOOK',
    url: 'identity',
    icon: UserCheck,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'IDENTITY'
    },
    released: true,
    isNew: true,
    externalKey: ['IDENTITY_API', 'IDENTITY_2_WAY_API'],
    color: '#1677ff',
    totalServiceProfile: 7,
    description: 'Manages user identities, authentication, and access control across systems.',
    visibility: {
      showInFeatures: ['quick-start', 'connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view']
    },
    connectors: {
      total: 7,
      items: [
        {
          id: '77179f87-99dd-4bf7-8be4-f93be7ad4647',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/office365/office365_64.png'
        },
        {
          id: '3de4ed0c-8312-4ea3-bf5d-3855eec1b07c',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/google_workspace/google_workspace_64.png'
        },
        {
          id: 'f06cd9d0-c85a-4302-a394-4d7661125522',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/okta/okta_64.png'
        },
        {
          id: 'a25b004b-1bb1-4b46-bff5-46fc03f222b7',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/onelogin/onelogin_64.png'
        }
      ]
    },
    supportsWebhooks: true,
    webhookEvents: [
      {
        key: 'user:created',
        label: 'User Created',
        description: 'Triggered when a new user account is created',
        triggerConditions: 'A new user is added to the identity provider'
      },
      {
        key: 'user:updated',
        label: 'User Updated',
        description: 'Triggered when user profile information is modified',
        triggerConditions: 'User attributes, profile, or metadata is changed'
      },
      {
        key: 'user:deleted',
        label: 'User Deleted',
        description: 'Triggered when a user account is deleted',
        triggerConditions: 'User account is permanently removed'
      },
    ]
  },
  {
    name: 'Public cloud (Infra)',
    value: 'INFRA',
    label: 'Public cloud (Infra)',
    key: 'INFRA',
    hook: 'INFRA_WATCH_HOOK',
    url: 'public-cloud-infra',
    icon: Cloud,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'INFRA'
    },
    released: true,
    isNew: true,
    externalKey: ['INFRA_API', 'INFRA_2_WAY_API'],
    color: '#4CAF50',
    totalServiceProfile: 20,
    description: 'Monitors and manages services across public cloud providers like AWS, Azure, and GCP.',
    visibility: {
      showInFeatures: ['quick-start', 'connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view']
    },
    connectors: {
      total: 4,
      items: [
        {
          id: '87acc19b-1f3c-4fab-a4df-adace4c89c38',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/azure/azure_64.png'
        },
        {
          id: '331c45a1-0342-4fa9-81c0-9a70d33c004b',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/aws/aws_64.png'
        },
        {
          id: '79609757-2d4a-41fc-939b-9f031ebe3b97',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/gcp/gcp_64.png'
        },
        {
          id: 'ce9b6940-c818-4542-9c37-67c6812faada',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/heroku/heroku_64.png'
        }
      ]
    },
    supportsWebhooks: false,
    webhookEvents: []
  },
  {
    name: 'EDR & XDR',
    value: 'EDR',
    label: 'EDR & XDR',
    key: 'EDR',
    hook: 'EDR_WATCH_HOOK',
    icon: Shield,
    released: true,
    isNew: true,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    externalKey: ['EDR_API', 'EDR_2_WAY_API'],
    color: '#ff6b6b',
    description: 'Provides endpoint and extended detection and response for cybersecurity threats.',
    visibility: {
      showInFeatures: ['quick-start', 'connector-selectors', 'dashboard', 'integrations-list'],
      hideInFeatures: ['minimal-view']
    },
    connectors: {
      total: 3,
      items: [
        {
          id: 'f6461aed-f168-449e-bf3d-c4e1ebc1a33b',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/crowdStrike/crowdStrike_64.png'
        },
        {
          id: '7246e4d6-7b13-4213-addc-be570fb5d930',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/sentinelOne/sentinelOne_64.png'
        },
        {
          id: 'd6d452d2-b9b8-4c5a-a235-7e1107fb463c',
          image: 'https://unizopublicpaas.blob.core.windows.net/imgs/defender/defender_64.png'
        }
      ]
    },
    supportsWebhooks: false,
    webhookEvents: []
  },
  {
    name: 'SIEM',
    value: 'SIEM',
    label: 'SIEM',
    key: 'SIEM',
    hook: 'SIEM_WATCH_HOOK',
    icon: ShieldCheck,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'SIEM'
    },
    released: false,
    isNew: false,
    externalKey: ['SIEM_API', 'SIEM_2_WAY_API'],
    color: '#1dc5ff',
    totalServiceProfile: 13,
    description: 'Aggregates and analyzes security data to detect and respond to threats.',
    visibility: {
      showInFeatures: ['connector-selectors', 'integrations-list'],
      hideInFeatures: ['minimal-view', 'quick-start', 'dashboard']
    },
    supportsWebhooks: false,
    webhookEvents: []
  },
  {
    name: 'Gen AI',
    value: 'GEN_AI',
    label: 'Gen AI',
    key: 'GEN_AI',
    hook: 'GEN_AI_WATCH_HOOK',
    icon: Sparkles,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'GEN_AI'
    },
    released: false,
    isNew: false,
    externalKey: ['GEN_AI_API_V1', 'GEN_AI_API_V2'],
    color: '#9c27b0',
    totalServiceProfile: 8,
    description: 'Tracks usage and performance of generative AI models and services.',
    visibility: {
      showInFeatures: ['connector-selectors', 'integrations-list'],
      hideInFeatures: ['minimal-view', 'quick-start', 'dashboard']
    },
    supportsWebhooks: false,
    webhookEvents: []
  },
  {
    name: 'Blob Storage',
    value: 'BLOB_STORE',
    label: 'Blob storage',
    key: 'BLOB_STORE',
    hook: 'BLOB_STORE_WATCH_HOOK',
    icon: Database,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    subscription: {
      code: 'BLOB_STORE'
    },
    released: false,
    isNew: false,
    externalKey: ['BLOB_STORE_API', 'BLOB_STORE_ADV_API'],
    color: '#2196f3',
    totalServiceProfile: 10,
    description: 'Provides monitoring for object storage systems handling unstructured data like images and videos.',
    visibility: {
      showInFeatures: ['connector-selectors', 'integrations-list'],
      hideInFeatures: ['minimal-view', 'quick-start', 'dashboard']
    },
    supportsWebhooks: false,
    webhookEvents: []
  },
  {
    name: 'File storage & Content management',
    value: 'FILE STORAGE AND CONTENT MANAGEMENT',
    label: 'File storage',
    key: 'FILE_STORAGE_AND_CONTENT_MANAGEMENT',
    hook: 'FILE_STORAGE_AND_CONTENT_MANAGEMENT_WATCH_HOOK',
    icon: Folder,
    released: false,
    isNew: false,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    externalKey: ['FILE_STORAGE_AND_CONTENT_MANAGEMENT_API', 'FILE_STORAGE_AND_CONTENT_MANAGEMENT_2_WAY_API'],
    color: '#795548',
    description: 'Stores, manages, and shares files and content across platforms.',
    visibility: {
      showInFeatures: ['connector-selectors', 'integrations-list'],
      hideInFeatures: ['minimal-view', 'quick-start', 'dashboard']
    },
    supportsWebhooks: false,
    webhookEvents: []
  },
  {
    name: 'Platform',
    value: 'PLATFORM',
    label: 'Platform',
    key: 'PLATFORM',
    hook: 'PLATFORM_WATCH_HOOK',
    url: 'platform',
    icon: Layers,
    released: false,
    isNew: false,
    getUpgraded: function () {
      return this.externalKey[this.externalKey.length - 1];
    },
    externalKey: ['PLATFORM_WATCH_HOOK', 'PLATFORM_WATCH_2_WAY_HOOK'],
    color: 'teal',
    description: 'Core platform services and integration capabilities supporting other modules.',
    visibility: {
      showInFeatures: ['connector-selectors', 'integrations-list'],
      hideInFeatures: ['minimal-view', 'quick-start', 'dashboard']
    },
    supportsWebhooks: false,
    webhookEvents: []
  }
];

// Helper function to get domains for quick-start
export const getQuickStartDomains = () => {
  return DOMAINS.filter(domain => 
    domain.released && 
    domain.visibility.showInFeatures.includes('quick-start')
  );
};

// Helper function to get domain by value
export const getDomainByValue = (value: string) => {
  return DOMAINS.find(domain => domain.value === value);
};

// Helper function to get domain by key
export const getDomainByKey = (key: string) => {
  return DOMAINS.find(domain => domain.key === key);
};