import { TextField, TextFieldProps } from "@mui/material"
import { FormFieldTypeEnum } from "hooks/useServiceProfile"
import Upload from "./app-config-fileupload"

export default ({ selectedAccessPoint }: any): any => {
      return {
         [FormFieldTypeEnum.Text]: {
            getField(props: Omit<TextFieldProps, 'variant'>) {
               delete (props as any)?.formState;
               return (
                  <TextField  {...props} />
               )
            },
         },
         [FormFieldTypeEnum.File]: {
            getField(props: any) {
               if (props?.value) {
                  props['value'] = {
                     id: 1,
                     name: props?.value
                  }
               };

               return (
                  <Upload
                     selectedAccessPoint={selectedAccessPoint}
                     property={props?.property}
                     value={props?.value}
                     formState={props?.formState}
                  >
                     Upload File
                  </Upload>
               )
            },
         }
      }
}
