// ==============================|| OVERRIDES - CARD ||============================== //

export default function Card(theme) {
  return {
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 0,
          boxShadow: theme.palette.mode === 'dark'
            ? '0 1px 3px rgba(0, 0, 0, 0.2)'
            : '0 1px 3px rgba(0, 0, 0, 0.05)',
          backgroundImage: 'none',
          transition: 'box-shadow 0.2s ease',
          
          '&:hover': {
            boxShadow: theme.palette.mode === 'dark'
              ? '0 4px 12px rgba(0, 0, 0, 0.3)'
              : '0 4px 12px rgba(0, 0, 0, 0.08)',
          },
        }
      }
    }
  };
}