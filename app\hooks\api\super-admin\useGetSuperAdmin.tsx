/* eslint-disable react-hooks/rules-of-hooks */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { API_ENDPOINTS } from "utils/api/api-endpoints";

import { tenantsClient } from "services/tenants.service";
import { toast } from "sonner";
import { parseError } from "lib/utils";

export type SPQueryOptionsType = {
   offset?: number;
   limit?: number;
   orderBy?: string;
};

const getIsEnabled = (pageIndex: number) => (
   pageIndex >= 0 ? true : false
)

const pickKeys = (pagination: any) => {
   return ([pagination?.pageIndex, pagination.pageSize])
}

const SEARCH_TENANT_KEY = 'TENANTS';
const GET_ORG_SUBS_KEY = 'ORG-SUBSCRIPTION';


export const useGetSuperAdmin = () => {

   const {
      mutateAsync: createTenantMutation,
      isPending: isCreating,
   } = useMutation({
      mutationFn: (payload: any) => {
         return tenantsClient.create(payload)
      },
      mutationKey: [API_ENDPOINTS.ORGANIZATION],
   });

   const { mutateAsync: deleteTenantMutation,
      isPending: isDeleting,
   } = useMutation({
      mutationFn: (id: string) => {
         return tenantsClient.delete(id)
      },
      mutationKey: [API_ENDPOINTS.TENANTS]
   })


   const {
      mutateAsync: createSubscriptionMutation,
      status: creatingStatus,
      ...rest
   } = useMutation({
      mutationFn: ({ payload, id }: any) => {
         return tenantsClient.createOrganizationSubscription(id, payload)
      },
      mutationKey: [API_ENDPOINTS.ORGANIZATION],
   });

   const {
      mutateAsync: inviteBulkUsers,
   } = useMutation({
      mutationFn: ({ payload, id }: any) => {
         return tenantsClient.inviteBulkUsers(payload, id)
      },
      mutationKey: [API_ENDPOINTS.ORGANIZATION],
   });

   const queryClient = useQueryClient();

   return {

      searchTenants: ({ pagination }: Record<string, any>) => {
         return useQuery({
            queryKey: [API_ENDPOINTS.TENANTS, pickKeys(pagination), SEARCH_TENANT_KEY],
            queryFn: async () => {
               return await tenantsClient.search({
                  "filter": {
                     "or": [
                        {
                           "and": [
                              {
                                 "property": "/state",
                                 "operator": "=",
                                 "values": [
                                    "PROVISIONING",
                                    "PROVISIONED"
                                 ]
                              }
                           ]
                        }
                     ]
                  },
                  "sort": [
                     {
                        "direction": "DESC",
                        "property": "/changeLog/lastUpdatedDateTime"
                     }
                  ],
                  "pagination": {
                     "offset": pagination?.pageIndex,
                     "limit": pagination?.pageSize
                  }
               })
            },
            select: (resp) => {
               return resp?.data
            },
            enabled: getIsEnabled(pagination?.pageIndex)
         });
      },

      searchProspects: ({ pagination }: Record<string, any>) => {
         return useQuery({
            queryKey: [API_ENDPOINTS.TENANTS, pickKeys(pagination), 'prospects'],
            queryFn: async () => {
               return await tenantsClient.searchProspects({
                  "filter": {
                     "or": [
                        {
                           "and": [
                              {
                                 "property": "/state",
                                 "operator": "=",
                                 "values": [
                                    "SUBMITTED"
                                 ]
                              }
                           ]
                        }
                     ]
                  },
                  "sort": [
                     {
                        "direction": "DESC",
                        "property": "/changeLog/lastUpdatedDateTime"
                     }
                  ],
                  "pagination": {
                     "offset": pagination?.pageIndex,
                     "limit": pagination?.pageSize
                  }
               })
            },
            select: (resp) => {
               return resp?.data ?? []
            },
            enabled: getIsEnabled(pagination?.pageIndex)
         });
      },

      searchProducts: () => {
         return useQuery({
            queryKey: [API_ENDPOINTS.TENANTS, 'products'],
            queryFn: async () => {
               return await tenantsClient.searchProducts()
            },
            select: (resp) => {
               return resp?.data?.data ?? []
            },
         });
      },

      getProductPricingTier: ({ productId }: Record<string, any>) => {
         return useQuery({
            queryKey: [API_ENDPOINTS.TENANTS, 'pricingTier', productId],
            queryFn: async () => {
               return await tenantsClient.getPricingTier(productId)
            },
            select: (resp) => {
               return resp?.data?.data ?? []
            },
            enabled: !!productId
         });
      },

      getPricingTierEntitlements: ({ productId, tierId }: Record<string, any>) => {
         return useQuery({
            queryKey: [API_ENDPOINTS.TENANTS, 'pricingTier', productId, tierId],
            queryFn: async () => {
               return await tenantsClient.getPricingTierEntitlements(productId, tierId)
            },
            select: (resp) => {
               return resp?.data ?? []
            },
            enabled: !!productId
         });
      },

      getOrgSubscription: ({ orgId }: { orgId: string }) => {
         return useQuery({
            queryKey: [API_ENDPOINTS.TENANTS, GET_ORG_SUBS_KEY, orgId],
            queryFn: async () => {
               return await tenantsClient.getOrganizationSubscription(orgId)
            },
            select: (resp) => {
               return resp?.data ?? []
            },
            enabled: !!orgId
         });
      },

      getSubscriptionById: ({ id, orgId }: Record<string, any>) => {
         return useQuery({
            queryKey: [API_ENDPOINTS.TENANTS, GET_ORG_SUBS_KEY, orgId, id],
            queryFn: async () => {
               return await tenantsClient.getSubscriptionById(orgId, id)
            },
            select: (resp) => {
               return resp?.data ?? []
            },
            enabled: !!orgId && !!id
         });
      },

      create: (payload: Record<string, any>, cb: any) => {
         toast.promise(createTenantMutation(payload), {
            loading: 'Creating...',
            success: () => {
               queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.TENANTS, SEARCH_TENANT_KEY] })
               if (cb) {
                  setTimeout(() => {
                     cb();
                  }, 500);
               }
               return 'Tenant created successfully';
            },
            error: (error: any) => {
               return parseError(error?.response?.data)?.message;
            },
         })
      },

      delete: (id: string, cb: any) => {
         toast.promise(deleteTenantMutation(id), {
            loading: "Deleting...",
            success: () => {
               setTimeout(() => {
                 queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.TENANTS, SEARCH_TENANT_KEY] })
               cb && cb()
               }, 2000);
               return 'Tenant deleted successfully';
            },
            error: (error: any) => {
               return parseError(error?.response?.data)?.message;
            },

         })
      },
      createSubscription: (id: string, payload: Record<string, any>, cb: any) => {
         toast.promise(createSubscriptionMutation({ payload, id }), {
            loading: 'Creating...',
            success: () => {
               queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.TENANTS, GET_ORG_SUBS_KEY] })
               cb && cb()
               return 'Created successfully';
            },
            error: (error: any) => {
               return parseError(error?.response?.data)?.message;
            },
         })
      },

      inviteUsers: (payload: Record<string, any>, id: string, cb: any) => {
         toast.promise(inviteBulkUsers({ payload, id }), {
            loading: 'Inviting...',
            success: () => {
               queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.USERS] })
               cb && cb()
               return <b>Invited successfully</b>;
            },
            error: (error: any) => {
               return <b>{parseError(error?.response?.data)?.message}</b>;
            },
         })
      },
      isSubscriptionCreating: creatingStatus === 'pending'
   }
};
