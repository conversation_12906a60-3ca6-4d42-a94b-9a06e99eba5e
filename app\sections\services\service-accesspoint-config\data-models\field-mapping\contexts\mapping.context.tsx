import { createContext, ReactNode, useContext } from "react";
import { MappingErrorTypes, MappingValues, ModeTypes } from "../type";
import { ServiceProfile } from "types/service-profile";
import { ServiceProfileSpecifications } from "types/service-profile-specification";
import { AdditionalFields } from "types/additional-attributes";
import { FieldMappings } from "types/field-mappings";

const MappingContext = createContext({});

interface MappingContextValue {
   onAddMapping: (parent?: MappingValues) => void
   onRemoveMapping: (id: string, parentId?: string) => void
   onUpdateMappings: (mapping: MappingValues, type: 'source' | 'target', value: string) => void
   onToggleExpand: (mapping: string) => void
   mappings: MappingValues[]
   viewMode: ModeTypes
   serviceProfile: ServiceProfile
   serviceProfileSpecification: ServiceProfileSpecifications.Root
   sourceFields: ServiceProfileSpecifications.Fields[]
   flattanedSourceFields: ServiceProfileSpecifications.Fields[]
   flattanedTargetFields: AdditionalFields.Root[]
   targetFields: AdditionalFields.Root[]
   fieldMapping: FieldMappings.Root | null
   mappingErrors: MappingErrorTypes[]
}

interface MappingContextProviderProps {
   children: ReactNode
   value: MappingContextValue
}

const MappingContextProvider = ({ value, children }: MappingContextProviderProps) => {
   return (
      <MappingContext.Provider value={value}>
         {children}
      </MappingContext.Provider>
   )
}

export const useMappingContext = () => {
   try {
      return useContext(MappingContext) as MappingContextValue
   } catch {
      throw new Error('useMappingContext hook should be use within <MappingContextProvider />')
   }
}

export default MappingContextProvider;