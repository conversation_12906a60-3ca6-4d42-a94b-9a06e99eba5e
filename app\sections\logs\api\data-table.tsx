import { useEffect, useMemo, useState } from "react";
import { Tooltip } from "@mui/material";
import { ColumnDef, ColumnFiltersState } from "@tanstack/react-table";
import _ from "lodash";
import Paper from "@mui/material/Paper";
import { TableSkeleton } from "components/skeletons";

import FilterTable from "components/@extended/Table/filter-table";
import { ProgressiveFilterBar } from "components/filters/ProgressiveFilterBar";

import { useGetLog } from "hooks/api/log/useGetLog";
import { useTable } from "hooks/table/useTable";
import { useDate } from "hooks/useDate";
import { useServiceProfile } from "hooks/useServiceProfile";
import { State, useStatus } from "hooks/useStatus";

import useUserDetails from "store/user";
import { minDate, maxDate } from "utils/date-field";

import { APILogsDetails } from "./api-logs.activity";
import { FormItemType } from "constants/form";
import useLogFilter from "../useFilter";

const COLUMN_ID = {
  type: "type",
  provider: "target.accessPoint.serviceProfile.id",
};

const LogTable = ({ integrationId }: any) => {
  const { resolveIcon, resolveStatus } = useStatus();
  const { loadImage } = useServiceProfile();
  const { loadDate } = useDate();
  const { search } = useGetLog({});
  const { user } = useUserDetails();

  const [paginationState, setPaginationState] = useState<any>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [selected, setSelected] = useState<Record<string, any> | null>();

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [filters, setFilters] = useState<any[]>([]);
  const [searchValue, setSearchValue] = useState("");

  const {
    paginationModal: { pagination, setTotal },
  } = useTable();

const selectedProvider: string = useMemo(() => {
    const typeFilter = columnFilters?.find((i) => i.id === COLUMN_ID.type);
    const value = typeFilter?.value;
    if (Array.isArray(value)) {
      return value[0] ?? ""; // Use first value or empty string
    }
    return value ?? "";
}, [columnFilters]);

  const { providerOptions, categoriesOptions } = useLogFilter({
    selectedProvider,
    columnFilters,
  });
  console.log(providerOptions, "providerOptions");
  const { data: resp, refetch, isLoading } = search({
    orgId: user?.organization?.id,
    integrationId: integrationId ?? "all",
    filter: columnFilters,
    ...paginationState,
  });

  const data = resp?.data?.data;
  const pagResp = resp?.data?.pagination;

  const columns = useMemo<ColumnDef<unknown, any>[]>(
    () => [
      {
        accessorKey: "state",
        header: "Status",
        cell({
          row: {
            original: { state },
          },
        }: any) {
          return resolveIcon(state);
        },
        meta: {
          filter: {
            filterType: FormItemType.Select,
            options: [
              { label: resolveStatus(State.SUCCESS), value: State.COMPLETED },
              { label: resolveStatus(State.FAILED), value: State.FAILED },
            ],
          },
        },
      },
      {
        accessorKey: "resource.action.apiDetails.url",
        header: "API end point",
        cell({
          row: {
            original: { resource },
          },
        }: any) {
          return (
            <Tooltip title={resource.action.apiDetails.url} placement="left">
              <span>{resource.action.apiDetails.url}</span>
            </Tooltip>
          );
        },
        meta: {
          className: "max-w-[16rem] truncate",
        },
      },
      {
        accessorKey: "name",
        header: "Activity type",
        cell({
          row: {
            original: { name },
          },
        }: any) {
          return (
            <Tooltip title={name} placement="left">
              <span>{name}</span>
            </Tooltip>
          );
        },
      },
      {
        accessorKey: "direction",
        header: "Direction",
        cell: ({ row: { original } }: any) => _.capitalize(original?.direction),
      },
      {
        accessorKey: "endUser.subOrganization.name",
        header: "Customer",
        meta: {
          className: "max-w-[8rem] truncate",
        },
      },
      {
        accessorKey: "endUser.integration.name",
        header: "Integration Name",
        meta: {
          filter: {
            filterType: FormItemType.Text,
          },
        },
      },
      {
        accessorKey: COLUMN_ID.type,
        header: "Category",
        meta: {
          filter: {
            filterType: FormItemType.Select,
            options: categoriesOptions,
          },
        },
      },
      {
        accessorKey: COLUMN_ID.provider,
        header: "Target",
        cell: ({ row: { original } }: any) => {
          return loadImage(original?.target?.accessPoint?.serviceProfile, {
            size: "xSmall",
          });
        },
        meta: {
          filter: {
            filterType: FormItemType.Select,
            options: providerOptions,
          },
        },
      },
      {
        accessorKey: "changeLog.startDateTime",
        header: "Start Date",
        cell: ({ row: { original } }: any) => {
          const changeLog = original?.changeLog;
          return loadDate(changeLog?.endDateTime ?? changeLog?.startDateTime);
        },
        meta: {
          filter: {
            filterType: FormItemType.DateTime,
            maxDateTime: maxDate({ filters: columnFilters }),
          },
          hidden: true,
        },
      },
      {
        accessorKey: "changeLog.endDateTime",
        header: "Date",
        cell: ({ row: { original } }: any) => {
          const changeLog = original?.changeLog;
          return loadDate(changeLog?.endDateTime ?? changeLog?.startDateTime);
        },
        meta: {
          filter: {
            filterType: FormItemType.DateTime,
            label: "End Date",
            minDateTime: minDate({ filters: columnFilters }),
          },
          className: "max-w-[12rem] truncate",
        },
      },
    ],
    [categoriesOptions, providerOptions, columnFilters]
  );
  useEffect(() => {
    setTotal(pagResp?.total ?? 0);
  }, [pagResp]);

  // Map ProgressiveFilterBar filters to columnFilters
  useEffect(() => {
    const mapped = filters.map((f) => {
      let value =  f.value;

      if(value === State.SUCCESS) value = State.COMPLETED;

    if (f.key === "type") {
  const matched = categoriesOptions.filter((opt) => 
    Array.isArray(value) ? value.includes(opt.label) : opt.label === value
  );
  const matchedProvider = providerOptions.filter((opt) => 
    Array.isArray(value) ? value.includes(opt.label) : opt.label === value
  );
  
  if (matched?.length || matchedProvider?.length) {
    value = matched?.length 
      ? matched.map((i) => i.value) 
      : matchedProvider.map((i) => i.value);
    
  }
}

      return {
        id: f.key,
        value,
      };
    });

    setColumnFilters(mapped);
  }, [filters, categoriesOptions]);

  // Global search binding (optional)
  useEffect(() => {
    setColumnFilters((prev) => {
      const otherFilters = prev.filter((f) => f.id !== "global");
      if (searchValue) {
        return [...otherFilters, { id: "global", value: searchValue }];
      }
      return otherFilters;
    });
  }, [searchValue]);

  const onOpen = (selected: any) => {
    setSelected(selected);
    setIsOpen(true);
  };

  const onClose = () => {
    setSelected(null);
    setIsOpen(false);
  };

  if (isLoading) {
    return <TableSkeleton rows={10} columns={7} title="API Logs" />;
  }

  return (
    <>
      {/* Unified Filter Bar + Table Block */}
      <Paper elevation={2} sx={{ 
        p: 3, 
        mb: 3, 
        borderRadius: 2,
        backgroundColor: theme => theme.palette.mode === 'dark' ? theme.palette.background.paper : theme.palette.background.paper,
        backgroundImage: 'none'
      }}>
        <ProgressiveFilterBar
          fields={[
            {
              key: "state",
              label: "Status",
              type: "enum",
              options: [State.SUCCESS, State.FAILED],
              useOnce: true,
            },
            {
              key: "endUser/integration/name",
              label: "Integration Name",
              type: "text",
              useOnce: true,
            },
            {
              key: COLUMN_ID.type,
              label: "Category",
              type: "enum",
              options: categoriesOptions.map((opt) => opt.label),
              multiSelect: true,
              useOnce: true,
            },
            {
              key: "target/accessPoint/serviceProfile/name",
              label: "Target",
              type: "enum",
              options: providerOptions.map((opt) => opt.label),
              useOnce: true,
            },
            {
              key: "changeLog/startDateTime",
              label: "Start Date",
              type: "date",
              useOnce: true,
            },
            {
              key: "changeLog/endDateTime",
              label: "End Date",
              type: "date",
              useOnce: true,
            },
          ]}
          filters={filters}
          onFiltersChange={setFilters}
          searchValue={searchValue}
          onSearchChange={setSearchValue}
           resultCount={data?.length || 0}
          placeholder="Search API logs..."
        />

        <FilterTable
          data={data}
          columns={columns}
          totalData={pagination?.total}
          onPaginationChange={setPaginationState}
          state={{
            pagination: {
              pageIndex: paginationState.pageIndex,
              pageSize: paginationState.pageSize,
            },
            columnFilters,
          } as any}
          onRowClick={({ original }) => onOpen(original)}
          manualFiltering={true}
          rowClickable={true}
          onColumnFiltersChange={() => {}}
          onReset={() => {
            setFilters([]);
            setSearchValue("");
          }}
          onReload={refetch}
        />
      </Paper>

      <APILogsDetails
        selected={selected as any}
        onClose={onClose}
        open={isOpen}
      />
    </>
  );
};

export default LogTable;
