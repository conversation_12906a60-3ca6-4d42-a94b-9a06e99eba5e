import React, { HTMLProps, useEffect, useMemo, useState } from 'react';
import './style.css';
import { extractProperties } from 'lib/utils';
import MainCard from 'components/MainCard';
import { Box, BoxProps, Stack, Typography, useTheme } from '@mui/material';

export type OptionTypes = {
    title: string
    description: string
    value: string
    extra?: React.ReactNode
    disabled?: boolean
    meta: any
}

export type CardSelectProps = {
    options: OptionTypes[]
    onValueSelect?: (selected: OptionTypes | OptionTypes[]) => void;
    selectedItem?: OptionTypes | OptionTypes[]
    multiple?: boolean
    cardSize?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
    render?: (t: OptionTypes) => React.ReactElement
}

export const CardSelect = React.forwardRef<HTMLDivElement, HTMLProps<HTMLDivElement> & CardSelectProps>((props, ref) => {

    const {
        options = [],
        onValueSelect: onValueSelectProp,
        selectedItem: selectedItemProp,
        multiple = false,
        cardSize = 'sm',
        disabled,
        render,
        ...rest
    } = props;

    const [selectedLocal, setSelectedLocal] = useState<OptionTypes | OptionTypes[]>(() => {
        return (multiple ? [] : null) as any
    });

    const selectedItem: OptionTypes | OptionTypes[] = selectedItemProp ?? selectedLocal;

    const onValueSelect = (arg: OptionTypes) => {

        if (multiple && Array.isArray(selectedItem)) {
            let updated: OptionTypes[];

            if (extractProperties(selectedItem, 'value').includes(arg?.value)) {

                updated = selectedItem?.filter((i) => (
                    i.value !== arg?.value
                ));

            } else {
                updated = [...selectedItem, arg]
            }

            if (typeof onValueSelectProp === 'function') {
                onValueSelectProp(updated)
            }
            setSelectedLocal(updated)
        } else {
            if (typeof onValueSelectProp === 'function') {
                onValueSelectProp(arg)
            }
            setSelectedLocal(arg)
        }

    }

    return (
        <Stack direction={'row'} className='card-select' gap={2} {...rest} ref={ref}>
            {options.map((option, index) => {

                let selected: boolean = false;

                if (!Array.isArray(selectedItem)) {
                    selected = selectedItem?.value === option?.value;
                } else {
                    selected = extractProperties(selectedItem, 'value').includes(option?.value);
                }

                return (
                    <CardInput
                        item={option}
                        key={index}
                        onClick={() => !option.disabled && onValueSelect(option)}
                        selected={selected}
                        cardSize={cardSize}
                        render={render}
                        disabled={option?.disabled ?? disabled}
                    />
                )
            })}
        </Stack>
    )
})

type CardInputProps = {
    item: OptionTypes
    selected?: boolean
} & Pick<CardSelectProps, 'cardSize' | 'render'>;

const CardInput = React.forwardRef<HTMLDivElement, HTMLProps<HTMLDivElement> & CardInputProps>((props, ref) => {
    const [, setIsHovered] = useState(false);
    const { item, selected, cardSize, render, disabled, ...rest } = props;

    const { title, description, meta } = item;

    const { palette, ...theme } = useTheme();

    const sx: BoxProps['sx'] = useMemo(() => {
        let localObj: Record<string, any> = { ...meta?.sx };

        if (disabled) {
            localObj = { ...localObj, opacity: .6, userSelect: 'none' }
        }

        return localObj
    }, [disabled, palette, theme, meta?.sx])

    return (
        <Box
            className='relative'
            component={'div'}
        >
            <MainCard
                onMouseEnter={() => !item?.disabled && setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
                aria-selected={selected}
                {...rest as any}
                ref={ref}
                sx={sx}
            >
                {typeof render === 'function' ? (
                    render(item)
                ) : (
                    <>
                        <Typography >{title}</Typography>
                        <p style={{ margin: "auto", display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "center" }}>{description}</p>
                    </>
                )}
            </MainCard>
        </Box>
    )
})

function SelectIcon() {
    return (
        <svg viewBox="0 0 24 24" fill="none" aria-label="check-circle">
            <path d="M21.3 12C21.3 6.9 17.1 2.7 12 2.7C6.86245 2.7 2.69995 6.9 2.69995 12C2.69995 17.1375 6.86245 21.3 12 21.3C17.1 21.3 21.3 17.1375 21.3 12ZM10.9125 16.95C10.6875 17.175 10.275 17.175 10.05 16.95L6.14995 13.05C5.92495 12.825 5.92495 12.4125 6.14995 12.1875L7.01245 11.3625C7.23745 11.1 7.61245 11.1 7.83745 11.3625L10.5 13.9875L16.125 8.3625C16.35 8.1 16.725 8.1 16.95 8.3625L17.8125 9.1875C18.0375 9.4125 18.0375 9.825 17.8125 10.05L10.9125 16.95Z" />
        </svg>
    )
}