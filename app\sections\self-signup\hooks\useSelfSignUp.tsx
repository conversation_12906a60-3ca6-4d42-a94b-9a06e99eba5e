import { useQuery } from "@tanstack/react-query";
import { tenantsClient } from "services/tenants.service";
import { API_ENDPOINTS } from "utils/api/api-endpoints";
import { TenantMetadata } from "types/tenant-metadata";
import { getAuthUserId } from "utils/auth";

const TENANT_METADATA_KEY = 'TENANT_METADATA';
const ALL_METADATA_KEY = 'ALL_METADATA';

const useSelfSignUp = () => {
   const userId = getAuthUserId();
   
   // First query to get all metadata
   const allMetadataQuery = useQuery({
      queryKey: [API_ENDPOINTS.TENANTS, ALL_METADATA_KEY, userId],
      queryFn: async () => {
         const response = await tenantsClient.getAllTenantMetadata({
            headers: {
               'externalkey': userId
            }
         });
         return response.data;
      },
      enabled: !!userId // Only run when userId is available
   });

   // console.log('UserId:', userId);
   // console.log('API Response:', allMetadataQuery.data);

   const tenantId = allMetadataQuery.data?.data[0]?.id;

   // Query for specific tenant metadata using the ID from first query
   const tenantMetaDataClient = useQuery<TenantMetadata>({
      queryKey: [API_ENDPOINTS.TENANTS, TENANT_METADATA_KEY, tenantId],
      queryFn: async () => {
         const response = await tenantsClient.getTenantMetadata(tenantId);
         return response.data;
      },
      enabled: !!tenantId // Only run this query when we have a tenantId
   });

   return {
      tenantMetaDataClient,
      isLoading: allMetadataQuery.isLoading || tenantMetaDataClient.isLoading,
      error: allMetadataQuery.error || tenantMetaDataClient.error
   }
}

export default useSelfSignUp;