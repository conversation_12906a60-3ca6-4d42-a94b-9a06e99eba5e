import { Grid } from '@mui/material'


import { useEffect, useState } from 'react';

import ProfileTabs from 'sections/account-settings/ProfileTabs';
import useCustomBreadcrumbs from 'store/custom-breadcrumbs';

import Personal from 'sections/account-settings/contact';
import Password from 'sections/account-settings/password';
import MainCard from 'components/MainCard';


const CONTACT_INFO = '/console/account-settings/user/contact';
const PASSWORD = '/console/account-settings/user/password';

export default () => {

   const { update, reset } = useCustomBreadcrumbs();

   const [selected, setSelectedTab] = useState('personal_info')


   useEffect(() => {
      update({
         title: 'Profile Settings',
         links: [
            { title: 'Profile Settings', to: '/console/profile-settings' },
         ]
      });

      return () => {
         reset()
      }

   }, [])

   return (
      <>
         <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
               <ProfileTabs
                  setSelectedTab={setSelectedTab}
                  selectedTab={selected}
               />
            </Grid>
            <Grid item xs={12} md={9}>
               <MainCard
                  title={selected === 'personal_info' ? 'Personal Information' : 'Change Password'}
               >
                  {selected === 'personal_info' ? (
                     <Personal />
                  ) : (
                     <Password />
                  )}
               </MainCard>
            </Grid>
         </Grid>
      </>

   )
}