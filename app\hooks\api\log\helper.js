import _ from 'lodash';

const DATES = {
   'changeLog/startDateTime': {
      operator: '>'
   },
   'changeLog/endDateTime': {
      operator: '<'
   }
}

export function parseLogFilterPayload(filter) {
   const criteria = [];

   filter.forEach(({ id, value }) => {
      const property = _.replace(id, /_/g, '/');

      // selecting operator
      let operator = 'LIKE';

      if (Object.keys(DATES).includes(property)) {
         operator = DATES[property]['operator']
      }
      // console.log({ property: `/${property}`, values: [value], operator })
      criteria.push({ property: `/${property}`, values: Array.isArray(value) ? value : [value], operator })
   });

   return criteria;
}