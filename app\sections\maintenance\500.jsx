import { Link } from '@remix-run/react';

import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project import
import { APP_DEFAULT_PATH } from 'config';

// assets
import error500 from '/images/maintenance/Error500.png';
import Image from 'remix-image';

// ==============================|| ERROR 500 - MAIN ||============================== //

const IMAGE_STYLE = { height: '100%', width: '100%' };
const ROOT_STYLE = { minHeight: '100vh' }

export default function Error500({ error }) {
  const downSM = useMediaQuery((theme) => theme.breakpoints.down('sm'));

  const onReset = () => {
    window?.location?.reload();
  }

  return (
    <Grid container direction="column" alignItems="center" justifyContent="center" sx={ROOT_STYLE}>
      <Grid item xs={12}>
        <Box sx={{ width: { xs: 350, sm: 396 } }}>
          <Image src={error500} alt="unizo" style={IMAGE_STYLE} />
        </Box>
      </Grid>
      <Grid item xs={12}>
        <Stack justifyContent="center" alignItems="center">
          <Typography align="center" variant={downSM ? 'h2' : 'h1'}>
            Oops! Something went wrong.
          </Typography>
          <Typography color="text.secondary" variant="body2" align="center" sx={{ width: { xs: '73%', sm: '70%' }, mt: 1 }}>
            {error.message}
          </Typography>

          <Button
            onClick={onReset}
            variant="contained"
            sx={{ textTransform: 'none', mt: 4 }}
          >
            Back To Home
          </Button>

        </Stack>
      </Grid>
    </Grid>
  );
}
