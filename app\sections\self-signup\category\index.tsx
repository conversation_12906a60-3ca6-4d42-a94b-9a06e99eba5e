/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo } from 'react';
import { Box, Stack, Typography, Chip, Skeleton, useTheme, alpha } from '@mui/material';

import useSelfRegistration from 'store/self-signUp/self-signup';
import { useGetSuperAdmin } from "hooks/api/super-admin/useGetSuperAdmin"

import Header from '../header';
import { StepComponentProps } from '../component-mapper';

import { getIsSelectionEnd } from '../helper';


const CategorySelection = ({ currentStep }: StepComponentProps) => {
    const theme = useTheme();
    const { selectedCategories, setSelectedCategories, setCurrentCategory } = useSelfRegistration();

    const { searchProducts } = useGetSuperAdmin();
    const { data: products = [], isPending } = searchProducts();



    // Filter out KMS from products
    const filteredProducts = useMemo(() => {
        return products?.filter((domain: any) => 
            !['KMS', 'COMPLIANCE',"SIEM"].includes(domain.code)
        ) || [];
    }, [products]);


    /**
     * Looking for max categories selected
    */
    const maxCategories = currentStep?.validations?.maxCategories;

    const isSelectionEnd = getIsSelectionEnd(maxCategories, selectedCategories?.length);

    const onSelectCategory = (categoryKey: string) => {

        if (selectedCategories.includes(categoryKey)) {
            setSelectedCategories(selectedCategories.filter(key => key !== categoryKey));
        } else {
            /**
            * Gets select within 3
            */
            if (!isSelectionEnd) {
                setSelectedCategories([...selectedCategories, categoryKey]);
            }
        }
        setCurrentCategory(null);
    };

    return (

        <Stack spacing={4} alignItems="center" sx={{ width: '100%', pt: 2 }} marginY='5rem'>

            {
                isPending ? (
                    <Stack spacing={2} alignItems="center" sx={{ width: '100%', maxWidth: 760 }}>
                        <Skeleton variant="text" width="60%" height={40} />
                        <Skeleton variant="text" width="40%" height={24} />
                        <Box
                            sx={{
                                display: 'flex',
                                flexWrap: 'wrap',
                                gap: 2,
                                justifyContent: 'center',
                                width: '100%',
                                mt: 2,
                            }}
                        >
                            {[...Array(6)].map((_, idx) => (
                                <Skeleton
                                    key={idx}
                                    variant="rounded"
                                    width={100}
                                    height={36}
                                    sx={{ borderRadius: '16px' }}
                                />
                            ))}
                        </Box>
                        <Skeleton variant="text" width="30%" height={18} sx={{ mt: 2 }} />
                    </Stack>
                ) : <>

                    <Header subTitle={currentStep?.subTitle} title={currentStep?.title} />

                    <Box
                        sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: 2,
                            justifyContent: 'center',
                            maxWidth: '760px'
                        }}
                    >
                        {filteredProducts?.map((domain: any) => {
                            const isSelected = selectedCategories.includes(domain.code);

                            const disabled = isSelectionEnd && !isSelected;

                            return (
                                <Chip
                                    key={domain.code}
                                    label={domain.name}
                                    onClick={() => void onSelectCategory(domain.code)}
                                    variant={isSelected ? "filled" : "outlined"}
                                    color={isSelected ? "primary" : "default"}
                                    sx={{
                                        px: 2,
                                        py: 2.5,
                                        borderRadius: '16px',
                                        fontSize: '0.875rem',
                                        fontWeight: 500,
                                        transition: 'all 0.2s ease',
                                        // Selected state
                                        ...(isSelected && {
                                            backgroundColor: theme.palette.primary.main,
                                            color: theme.palette.primary.contrastText,
                                            borderColor: theme.palette.primary.main,
                                            '&:hover': {
                                                backgroundColor: theme.palette.primary.dark,
                                                borderColor: theme.palette.primary.dark,
                                            },
                                        }),
                                        // Unselected state
                                        ...(!isSelected && !disabled && {
                                            backgroundColor: theme.palette.background.paper,
                                            color: theme.palette.text.primary,
                                            borderColor: theme.palette.divider,
                                            '&:hover': {
                                                backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                                borderColor: theme.palette.primary.main,
                                                color: theme.palette.primary.main,
                                            },
                                        }),
                                        // Disabled state
                                        ...(disabled && {
                                            opacity: 0.5,
                                            backgroundColor: theme.palette.action.disabledBackground,
                                            color: theme.palette.text.disabled,
                                            borderColor: theme.palette.action.disabled,
                                            cursor: 'not-allowed',
                                            '&:hover': {
                                                backgroundColor: theme.palette.action.disabledBackground,
                                                borderColor: theme.palette.action.disabled,
                                                color: theme.palette.text.disabled,
                                            },
                                        }),
                                    }}
                                    disabled={disabled}
                                />
                            );
                        })}
                    </Box>

                    <Typography
                        variant="body1"
                        align="center"
                        color="text.secondary"
                    >
                        {currentStep?.assistanceInfo}
                    </Typography>
                </>
            }

        </Stack>
    );
};

export default CategorySelection;
