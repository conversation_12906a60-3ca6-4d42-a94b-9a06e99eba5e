import { Stack, Typography } from "@mui/material";


interface LayoutProps {
   title: string
   subTitle?: string
}

const Header = ({ title, subTitle }: LayoutProps) => {
   return (
      <Stack spacing={1} alignItems="center" sx={{ width: '100%', pt: 2 }} marginTop='4rem'>
         <Typography variant="h2" align="center" sx={{ fontWeight: 600, color: 'text.primary' }}>
            {title}
         </Typography>

         {subTitle && (
            <Typography variant="h5" align="center" color="text.secondary" fontWeight='normal'>
               {subTitle}
            </Typography>
         )}
      </Stack>
   )
}

export default Header;