import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Box,
  Typography,
  Button,
  Stack,
  IconButton,
  useTheme,
  alpha,
  Card,
  Chip,
  Grid,
} from '@mui/material';
import {
  X,
  Clock,
  Sparkles,
  ArrowRight,
  Mail,
  CreditCard,
} from 'lucide-react';
import { useNavigate } from '@remix-run/react';

interface TrialExpiredDialogProps {
  open: boolean;
  onClose?: () => void;
  daysUsed?: number;
  onSignOut?: () => void;
}

export default function TrialExpiredDialog({
  open,
  onClose,
  daysUsed = 15,
  onSignOut,
}: TrialExpiredDialogProps) {
  const theme = useTheme();
  const navigate = useNavigate();

  const handleExtendTrial = () => {
    window.open('https://unizo.ai/contact-us/', '_blank');
    onClose?.();
  };

  const handleViewPricing = () => {
    window.open('https://unizo.ai/pricing/', '_blank');
    onClose?.();
  };

  const handleContactSales = () => {
    window.location.href = 'mailto:<EMAIL>?subject=Trial Extension Request';
  };

  return (
    <Dialog
      open={open}
      onClose={() => {}} // Prevent closing by clicking backdrop
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          overflow: 'hidden',
          backgroundColor: '#ffffff',
        },
      }}
    >
      {/* Clean Header */}
      <Box sx={{ p: 3, backgroundColor: '#ffffff', borderBottom: `1px solid ${theme.palette.divider}` }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Stack direction="row" alignItems="center" spacing={2}>
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: 1,
                backgroundColor: alpha(theme.palette.warning.main, 0.1),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Clock size={20} color={theme.palette.warning.main} />
            </Box>
            <Box>
              <Typography variant="h5" fontWeight={600}>
                Your trial has ended
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Continue your journey with Unizo by extending your trial or exploring our pricing plans
              </Typography>
            </Box>
          </Stack>
          {onClose && (
            <IconButton
              onClick={onClose}
              size="small"
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  backgroundColor: alpha(theme.palette.action.active, 0.1),
                },
              }}
            >
              <X size={20} />
            </IconButton>
          )}
        </Stack>
      </Box>

      <DialogContent sx={{ p: 0 }}>
        <Grid container>
          {/* Left Column - Main Content */}
          <Grid item xs={12} md={6} sx={{ p: 4, backgroundColor: '#ffffff' }}>
            <Stack spacing={3}>
              {/* Primary Actions */}
              <Stack spacing={2}>
                {/* Extend Trial Card */}
                <Card
              elevation={0}
              sx={{
                p: 2.5,
                cursor: 'pointer',
                transition: 'all 0.2s',
                border: `1px solid ${theme.palette.divider}`,
                backgroundColor: '#ffffff',
                '&:hover': {
                  borderColor: theme.palette.primary.main,
                  boxShadow: theme.shadows[2],
                },
              }}
              onClick={handleExtendTrial}
            >
              <Stack direction="row" alignItems="center" justifyContent="space-between">
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Box
                    sx={{
                      width: 44,
                      height: 44,
                      borderRadius: 1,
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Sparkles size={20} color={theme.palette.primary.main} />
                  </Box>
                  <Box>
                    <Typography variant="body1" fontWeight={600}>
                      Request Trial Extension
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Need more time to evaluate? Get up to 14 more days
                    </Typography>
                  </Box>
                </Stack>
                <ArrowRight size={20} color={theme.palette.primary.main} />
              </Stack>
            </Card>

                {/* View Pricing Card */}
                <Card
              elevation={0}
              sx={{
                p: 2.5,
                cursor: 'pointer',
                transition: 'all 0.2s',
                border: `1px solid ${theme.palette.divider}`,
                backgroundColor: '#ffffff',
                '&:hover': {
                  borderColor: theme.palette.primary.main,
                  boxShadow: theme.shadows[2],
                },
              }}
              onClick={handleViewPricing}
            >
              <Stack direction="row" alignItems="center" justifyContent="space-between">
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Box
                    sx={{
                      width: 44,
                      height: 44,
                      borderRadius: 1,
                      backgroundColor: theme.palette.action.hover,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <CreditCard size={20} />
                  </Box>
                  <Box>
                    <Typography variant="body1" fontWeight={600}>
                      View Pricing Plans
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Find the perfect plan for your team
                    </Typography>
                  </Box>
                </Stack>
                <ArrowRight size={20} />
              </Stack>
            </Card>
              </Stack>
            </Stack>
          </Grid>

          {/* Divider */}
          <Grid item xs={12} md="auto">
            <Box
              sx={{
                width: { xs: '100%', md: 1 },
                height: { xs: 1, md: '100%' },
                backgroundColor: theme.palette.divider,
              }}
            />
          </Grid>

          {/* Right Column - Contact Sales */}
          <Grid item xs={12} md={6} sx={{ p: 4, backgroundColor: theme.palette.grey[50] }}>
            <Stack spacing={3} justifyContent="center" height="100%">
              <Box>
                <Typography variant="h6" fontWeight={600} gutterBottom>
                  Need help deciding?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Our sales team can help you find the perfect plan for your needs
                </Typography>
              </Box>

              <Stack spacing={2}>
                <Stack direction="row" alignItems="center" spacing={1.5}>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: 1,
                      backgroundColor: alpha(theme.palette.primary.main, 0.1),
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Mail size={16} color={theme.palette.primary.main} />
                  </Box>
                  <Box>
                    <Typography variant="body2" fontWeight={500}>
                      Email us directly
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      <EMAIL>
                    </Typography>
                  </Box>
                </Stack>
              </Stack>

              <Button
                variant="contained"
                fullWidth
                onClick={handleContactSales}
                size="large"
                sx={{
                  backgroundColor: theme.palette.primary.main,
                  '&:hover': {
                    backgroundColor: theme.palette.primary.dark,
                  },
                }}
              >
                Contact Sales Team
              </Button>

              <Box sx={{ textAlign: 'center', mt: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Response within 24 hours
                </Typography>
              </Box>

              {/* Sign Out Option */}
              {onSignOut && (
                <Box sx={{ textAlign: 'center', pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
                  <Button
                    variant="text"
                    color="inherit"
                    size="small"
                    onClick={onSignOut}
                    sx={{
                      color: 'text.secondary',
                      '&:hover': {
                        color: 'text.primary',
                      },
                    }}
                  >
                    Sign out for now
                  </Button>
                </Box>
              )}
            </Stack>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  );
}