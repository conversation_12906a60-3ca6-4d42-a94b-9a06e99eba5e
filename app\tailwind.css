@tailwind base;
@tailwind components;
@tailwind utilities;

/* Unizo Brand Colors CSS Variables */
:root {
  --unizo-brand-primary: #213350;
  --unizo-primary-50: #f8fafc;
  --unizo-primary-100: #edf2f7;
  --unizo-primary-200: #d4e0ed;
  --unizo-primary-300: #a9c2db;
  --unizo-primary-400: #6f99c3;
  --unizo-primary-500: #4473a2;
  --unizo-primary-600: #35597e;
  --unizo-primary-700: #2a4765;
  --unizo-primary-800: #21384f;
  --unizo-primary-900: #1a2b3d;
  --unizo-primary-950: #0f1924;
  
  --unizo-secondary-50: #fafafa;
  --unizo-secondary-100: #f5f5f5;
  --unizo-secondary-200: #e5e5e5;
  --unizo-secondary-300: #d4d4d4;
  --unizo-secondary-400: #a3a3a3;
  --unizo-secondary-500: #000000;
  --unizo-secondary-600: #525252;
  --unizo-secondary-700: #404040;
  --unizo-secondary-800: #262626;
  --unizo-secondary-900: #171717;
  --unizo-secondary-950: #0a0a0a;
  
  --unizo-nav-bg: #21384f;
  --unizo-nav-hover: #2a4765;
  --unizo-nav-active: #1a2b3d;
  --unizo-nav-highlight: #000000;
}

/* Dark mode variables */
@media (prefers-color-scheme: dark) {
  :root {
    /* Inverted primary palette for dark mode */
    --unizo-primary-50: #0f1924;
    --unizo-primary-100: #1a2b3d;
    --unizo-primary-200: #21384f;
    --unizo-primary-300: #2a4765;
    --unizo-primary-400: #35597e;
    --unizo-primary-500: #4473a2;
    --unizo-primary-600: #6f99c3;
    --unizo-primary-700: #a9c2db;
    --unizo-primary-800: #d4e0ed;
    --unizo-primary-900: #edf2f7;
    --unizo-primary-950: #f8fafc;
    
    /* White secondary for dark mode */
    --unizo-secondary-50: #0a0a0a;
    --unizo-secondary-100: #171717;
    --unizo-secondary-200: #262626;
    --unizo-secondary-300: #404040;
    --unizo-secondary-400: #525252;
    --unizo-secondary-500: #ffffff;
    --unizo-secondary-600: #f5f5f5;
    --unizo-secondary-700: #e5e5e5;
    --unizo-secondary-800: #d4d4d4;
    --unizo-secondary-900: #fafafa;
    --unizo-secondary-950: #ffffff;
    
    --unizo-nav-highlight: #ffffff;
  }
}

/* html {
  zoom: 90%
} */

html,
body {
  .rate_limit_success {
    @apply bg-green-100 !border  border-green-200 text-green-700 font-semibold;
  }

  .rate_limit_warning {
    @apply bg-orange-100 !border border-orange-200 text-orange-700 font-semibold;
  }

  .rate_limit_danger {
    @apply bg-red-100 !border border-red-200 text-red-700 font-semibold;
  }

  .css-uhsl36-MuiTabs-root {
    @apply !min-h-[31px];
  }

  /*
  .css-1sx7tm3-MuiTypography-root, .css-piptxp-MuiTypography-root {
    @apply font-semibold
  } */

  .link {
    @apply text-blue-600 hover:underline cursor-pointer select-none;
  }

  .hover:link {
    @apply hover:text-blue-600 hover:underline cursor-pointer select-none;
  }

  .MuiAlert-message {
    @apply text-[14px];
  }

  .css-vtbemp {
    @apply border-0;
  }

  .modal {
    @apply absolute top-[50%] left-[50%] -translate-x-2/4 -translate-y-2/4 bg-white shadow-lg p-8 rounded-md;
  }

  .css-1o19h8o-MuiPaper-root {
    @apply shadow-none;
  }

  .css-1x6bjyf-MuiAutocomplete-root .MuiOutlinedInput-root {
    padding: 0px !important;
  }

  .css-1x6bjyf-MuiAutocomplete-root
    .MuiOutlinedInput-root
    .MuiAutocomplete-input {
    padding: 10.5px 14px 10.5px 12px !important;
  }

  th,
  td {
    padding-left: 24px !important;
  }

  /* accordian */
  .css-5wks7h-MuiPaper-root-MuiAccordion-root {
    @apply shadow-none;
  }

  .css-1f773le-MuiButtonBase-root-MuiAccordionSummary-root {
    @apply px-0;
  }

  .card-input {
    @apply drop-shadow-sm;
  }

  .card-select {
    position: relative;
  }

  .card-select [aria-selected="true"] {
    border: 1px solid rgb(240, 134, 64);
    cursor: pointer;
  }

}
