import { ServiceProfileSpecificationFieldType } from "constants/service-profile"

declare namespace ServiceProfileSpecifications {

   interface DataModal {
      type: string,
      api: {
         endpoint: string,
         method: string
      }
   }

   interface Fields {
      id: string
      name: string
      description: string
      type: ServiceProfileSpecificationFieldType
      properties: Fields[]
      items?: {
         type: ServiceProfileSpecificationFieldType,
         properties: Array<Fields>
      }
      path: string
      children: Fields[]
      parent?: Fields
   }

   interface Root {
      id: string
      name: string
      description: string
      dataModel: DataModal,
      fields: Array<Fields>
   }
}