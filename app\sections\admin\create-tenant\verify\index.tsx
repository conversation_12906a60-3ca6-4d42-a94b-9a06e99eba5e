import { But<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Typography } from "@mui/material"
import MainCard from "components/MainCard"
import name<PERSON><PERSON><PERSON> from "constants/categoryMapper"
import { useDate } from "hooks/useDate"
import useCreateTenant from "store/create-tenants"

const SPANS = {
   xs: 12,
   lg: 6
}

const keyProperties: any = {
   name: {
      label: 'Organization Name',
   },
   companyUrl: {
      label: 'Website',
   },
   industry: {
      label: 'Industry',
   },
   line1: {
      label: 'Address Line 1',
   },
   line2: {
      label: 'Address Line 2',
   },
   city: {
      label: 'City',
   },
   state: {
      label: "State"
   },
   zip: {
      label: 'Zip Code',
   },
   country: {
      label: "Country"
   }
}

const orgKeyProperties: any = {
   firstName: {
      label: 'First Name',
   },
   lastName: {
      label: 'Last Name',
   },
   email: {
      label: 'Email',
   },
   phone: {
      label: 'Phone Number',
   },
   role: {
      label: 'Role',
   },
   emails: {
      label: 'Additional Org Admins',
   }
}

const CardTitle = ({ title, index }: any) => {
   const { jump } = useCreateTenant();

   return (
      <Stack justifyContent={'space-between'} direction={'row'} alignItems={'center'}>
         {title}
         <Button onClick={() => jump(index)} size="small">Edit</Button>
      </Stack>
   )
}

export const Verify = () => {

   const { formData } = useCreateTenant();

   const { loadDate } = useDate()

   const customer = formData.customer,
      org = formData.orgDetails,
      subscriptions = formData.subscriptions

   return (
      <>
         <Grid container rowSpacing={2} columnSpacing={2}>
            <Grid item {...SPANS}>
               <MainCard
                  title={
                     <CardTitle title={'Customer Details'} index={0} />
                  }
               >
                  <Divider />
                  <Stack gap={2} mt={3}>
                     {Object.entries(customer).map(([key, value]: any) => {
                        return (
                           <Grid container key={key}>
                              <Grid item xs={6}>
                                 <Typography variant='h6'>{keyProperties?.[key]?.label}</Typography>
                              </Grid>
                              <Grid item xs={6}>
                                 <Typography variant='body1' className="font-medium">{value}</Typography>
                              </Grid>
                           </Grid>
                        )
                     })}
                  </Stack>
               </MainCard>
            </Grid >
            <Grid item {...SPANS}>
               <MainCard
                  title={
                     <CardTitle title={'Org Admin Details'} index={1} />
                  }
               >
                  <Divider />
                  <Stack gap={2} mt={3}>
                     {Object.entries(org).map(([key, value]: any) => {
                        if (key === 'emails') {
                           value = value?.join(', ')
                        }
                        return (
                           <Grid container key={key}>
                              <Grid item xs={6}>
                                 <Typography variant='h6'>{orgKeyProperties?.[key]?.label}</Typography>
                              </Grid>
                              <Grid item xs={6}>
                                 <Typography variant='body1' className="font-medium">{value}</Typography>
                              </Grid>
                           </Grid>
                        )
                     })}
                  </Stack>
               </MainCard>
            </Grid>
            <Grid item {...SPANS} lg={8} >
               <MainCard
                  title={
                     <CardTitle title={'Subscription Details'} index={2} />
                  }
               >
                  <Divider />
                  <Stack gap={2} mt={3}>
                     {subscriptions.map((value: any, index: number) => {
                        return (
                           <Grid container key={index} className="border-b border-gray-200 pb-2">
                              <Grid item xs={3}>
                                 <Typography variant='h6'>{nameChecker(value?.category?.name)}</Typography>
                              </Grid>
                              <Grid item xs={3}>
                                 <Typography variant='h6'>{value?.tier?.name}</Typography>
                              </Grid>
                              <Grid item xs={3}>
                                 <Typography variant='h6'>{loadDate(value?.startDate)}</Typography>
                              </Grid>
                              <Grid item xs={3}>
                                 <Typography variant='h6'>{loadDate(value?.endDate)}</Typography>
                              </Grid>
                           </Grid>
                        )
                     })}
                  </Stack>
               </MainCard>
            </Grid>
         </Grid>
      </>
   )
}