# Component Pattern Guidelines

## Core Principles

1. **No Inline Styles**: All styling should be done through styled components or theme-based sx props
2. **Component-Driven**: Every UI element should be a reusable component
3. **Consistent Naming**: Follow PascalCase for components, camelCase for props
4. **Type Safety**: All components must have TypeScript interfaces
5. **Theme Integration**: Use MUI theme for all styling decisions

## File Structure

```
components/
├── ComponentName/
│   ├── index.tsx           # Main component export
│   ├── ComponentName.tsx   # Component implementation
│   ├── ComponentName.styles.ts  # Styled components
│   ├── ComponentName.types.ts   # TypeScript interfaces
│   └── ComponentName.test.tsx   # Component tests
```

## Styling Patterns

### 1. Styled Components with MUI
```typescript
import { styled } from '@mui/material/styles';
import { Card, CardProps } from '@mui/material';

export const StyledCard = styled(Card)<CardProps>(({ theme }) => ({
  padding: theme.spacing(2),
  borderRadius: theme.shape.borderRadius,
  transition: theme.transitions.create(['box-shadow', 'transform']),
  '&:hover': {
    boxShadow: theme.shadows[4],
    transform: 'translateY(-2px)',
  },
}));
```

### 2. Component Variants
```typescript
interface StyledComponentProps {
  variant?: 'primary' | 'secondary';
  size?: 'small' | 'medium' | 'large';
}

export const StyledButton = styled(Button)<StyledComponentProps>(({ theme, variant, size }) => ({
  ...(variant === 'primary' && {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
  }),
  ...(size === 'small' && {
    padding: theme.spacing(1, 2),
  }),
}));
```

### 3. Theme-Aware Components
```typescript
export const useComponentStyles = () => {
  const theme = useTheme();
  
  return {
    root: {
      backgroundColor: theme.palette.background.paper,
      borderRadius: theme.shape.borderRadius,
      padding: theme.spacing(2),
    },
    header: {
      color: theme.palette.text.primary,
      marginBottom: theme.spacing(2),
    },
  };
};
```

## Component Template

```typescript
// ComponentName.types.ts
export interface ComponentNameProps {
  title: string;
  variant?: 'default' | 'outlined';
  onAction?: () => void;
  children?: React.ReactNode;
}

// ComponentName.styles.ts
import { styled } from '@mui/material/styles';
import { Box } from '@mui/material';

export const ComponentWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

// ComponentName.tsx
import React from 'react';
import { ComponentNameProps } from './ComponentName.types';
import { ComponentWrapper } from './ComponentName.styles';

export const ComponentName: React.FC<ComponentNameProps> = ({
  title,
  variant = 'default',
  onAction,
  children,
}) => {
  return (
    <ComponentWrapper>
      {/* Component implementation */}
    </ComponentWrapper>
  );
};

// index.tsx
export { ComponentName } from './ComponentName';
export type { ComponentNameProps } from './ComponentName.types';
```

## Dos and Don'ts

### ✅ DO:
- Use theme values for spacing, colors, and typography
- Create reusable styled components
- Use TypeScript interfaces for all props
- Follow consistent naming conventions
- Export types alongside components

### ❌ DON'T:
- Use inline style attributes
- Hard-code colors or spacing values
- Mix styling approaches within a component
- Create components without proper types
- Use sx prop with complex logic (move to styled components)

## Common Patterns

### Card Component
```typescript
export const StyledCard = styled(Card)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius * 2,
  padding: theme.spacing(3),
  boxShadow: theme.shadows[1],
  transition: theme.transitions.create('box-shadow'),
  '&:hover': {
    boxShadow: theme.shadows[4],
  },
}));
```

### Icon Button
```typescript
export const StyledIconButton = styled(IconButton)(({ theme }) => ({
  color: theme.palette.text.secondary,
  padding: theme.spacing(1),
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
    color: theme.palette.primary.main,
  },
}));
```

### Form Input
```typescript
export const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.shape.borderRadius,
    backgroundColor: theme.palette.background.paper,
    '& fieldset': {
      borderColor: theme.palette.divider,
    },
    '&:hover fieldset': {
      borderColor: theme.palette.primary.main,
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.primary.main,
    },
  },
}));
```