import { Changelog, Organization } from "./common"
import Environment from "./environment"

declare namespace AdditionalFields {
   interface Root {
      id: string
      name: string
      key: string
      description: string
      category: {
         type: string
      }
      dataModel: {
         type: string
      }
      dataType: {
         type: string
      }
      organization: Organization
      environment: Environment.Root
      changeLog: Changelog
      children: AdditionalFields.Root[]
   }

   type CreatePayload = {
      type: string
      name: string
      key: string
      description: string
      state: string
      category: { type: string }
      dataModel: { type: string }
      dataType: { type: string }
      children: CreateAdditionalField[]
      parent?: {
         id: Root['id']
      }
   }

}

export { AdditionalFields }