import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { parseError } from "lib/utils";
import { useEffect } from "react";
import { IntegrationClient } from "services/integration.service";
import { keyProtectionClient } from "services/security.service";
import { serviceProfileClient } from "services/service-profile.service";
import { toast } from "sonner";
import useUserDetails from "store/user";
import { API_ENDPOINTS } from "utils/api/api-endpoints";

const KEY_PROTECTION_API = 'KEY_PROTECTION_API';

const KEY_PROTECTION_ACCESS_POINT_S = 'KEY_PROTECTION_ACCESS_POINT_S';

const KEY_PROTECTION_S = 'KEY_PROTECTION_S';

const KEY_PROTECTION_S_ALL_INTEGRATION_S = 'KEY_PROTECTION_S_ALL_INTEGRATION_S';


type Props = {
   hasEnterpriseTier: boolean
}

export const useGetSecurity = ({ hasEnterpriseTier }: Props) => {

   const { user } = useUserDetails();
   const orgId = user?.organization?.id;

   const queryClient = useQueryClient();

   const {
      mutateAsync: createKeyProtectionMutation,
   } = useMutation({
      mutationFn: ({ payload, orgId }: { orgId: string, payload: Record<string, any> }) => {
         return keyProtectionClient.createKeyProtection(orgId, payload)
      },
   })

   const {
      mutateAsync: updateKeyProtectionMutation,
   } = useMutation({
      mutationFn: ({ payload, orgId, logId }: { orgId: string, payload: Record<string, any>, logId: string }) => {
         return keyProtectionClient.updateKeyProtection(orgId, logId, payload)
      },
   })

   // create integration
   const {
      mutateAsync: createIntegrationMutation,
   } = useMutation({
      mutationFn: ({ payload, orgId, logProtectionId }: { payload: Record<string, any>, orgId: string, logProtectionId: string }) => {
         return keyProtectionClient.createIntegration(orgId, logProtectionId, payload)
      },
   })

   // update integration
   const {
      mutateAsync: updateIntegrationMutation,
   } = useMutation({
      mutationFn: ({ payload, id, logProtectionId }: { logProtectionId: string, id: string, payload: Record<string, any> }) => {
         return keyProtectionClient.updateIntegration(orgId as string, logProtectionId, id, payload)
      },
   })

   // delete integration
   const {
      mutateAsync: deleteIntegrationMutation,
   } = useMutation({
      mutationFn: ({ payload, id, orgId }: { orgId: string, id: string, payload: Record<string, any> }) => {
         return keyProtectionClient.updateKeyProtection(orgId, id, payload)
      },
   })

   // test integration
   const {
      mutateAsync: testIntegrationMutation,
      isPending: isTestingIntegration,
   } = useMutation({
      mutationFn: ({ payload }: { payload: Record<string, any>[] }) => {
         return keyProtectionClient.testIntegration(payload)
      },
   })


   return {

      searchMonitoringProfiles: ({ logProtectionId }: { logProtectionId: string }) => {
         return useQuery({
            queryKey: [API_ENDPOINTS.SERVICE_PROFILE, KEY_PROTECTION_API],
            queryFn: async () => {
               const payload = {
                  filter: {
                     and: [
                        {
                           property: "/type",
                           operator: "=",
                           values: ["KMS"]
                        }
                     ]
                  },
                  pagination: {
                     limit: 10,
                     offset: 0
                  }
               }
               return await keyProtectionClient.searchMonitoringServices(orgId as string, logProtectionId, payload)
            },
            select: (resp) => {
               return resp?.data?.data ?? []
            },
            enabled: !!orgId && !!logProtectionId
         });
      },

      searchAccessPoint: ({ serviceId }: { serviceId?: string }) => {
         return useQuery({
            queryKey: [API_ENDPOINTS.SERVICE_PROFILE, KEY_PROTECTION_API, KEY_PROTECTION_ACCESS_POINT_S, serviceId],
            queryFn: async () => {
               return await serviceProfileClient.getServiceAccessPoints(serviceId as string)
            },
            select: (resp) => {
               return resp?.data?.data ?? []
            },
            enabled: !!serviceId
         });
      },

      searchKeyProtections: () => {
         return useQuery({
            queryKey: [API_ENDPOINTS.SERVICE_PROFILE, KEY_PROTECTION_API, KEY_PROTECTION_S],
            queryFn: async () => {
               return await keyProtectionClient.searchKeyProtections(orgId as string)
            },
            select: (resp) => {
               return resp?.data?.data?.[0]
            },
            enabled: !!orgId
         });
      },

      attemptCreateKeyProtection: (payload: Record<string, any>, cb?: any) => {
         return toast.promise(createKeyProtectionMutation({ payload, orgId } as any), {
            loading: 'Creating...',
            success: () => {

               queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.SERVICE_PROFILE, KEY_PROTECTION_API, KEY_PROTECTION_S] });
               cb && cb()
               return 'Updated'
            },
            error: (err: any) => {
               return parseError(err?.response?.data)?.message
            }
         })
      },

      attemptUpdateKeyProtection: (payload: Record<string, any>, logId: any, cb?: any) => {
         return toast.promise(updateKeyProtectionMutation({ payload, orgId, logId } as any), {
            loading: 'Updating Key Protection...',
            success: () => {
               queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.SERVICE_PROFILE, KEY_PROTECTION_API, KEY_PROTECTION_S] });
               cb && cb();
               return 'Updated Key Protection'
            },
            error: (err) => {
               return parseError(err?.response?.data)?.message
            }
         })
      },

      getIntegration: ({ id, logProtectionId }: { id: string, logProtectionId: string }) => {
         return useQuery({
            queryKey: [API_ENDPOINTS.SERVICE_PROFILE, KEY_PROTECTION_API, KEY_PROTECTION_S_ALL_INTEGRATION_S, id, logProtectionId],
            queryFn: async () => {
               return await keyProtectionClient.getIntegration(orgId as string, logProtectionId, id)
            },
            select: (resp) => {
               return resp?.data
            },
            enabled: !!id && !!orgId
         });
      },

      attemptCreateIntegration: (logProtectionId: string, payload: Record<string, any>, cb?: any) => {
         return toast.promise(createIntegrationMutation({ payload, logProtectionId, orgId } as any), {
            loading: 'Creating Integration ...',
            success: (resp) => {
               // integration query
               queryClient.invalidateQueries({
                  queryKey: [
                     API_ENDPOINTS.SERVICE_PROFILE, KEY_PROTECTION_API, KEY_PROTECTION_S_ALL_INTEGRATION_S
                  ]
               });
               cb && cb(resp);
               return 'Integration Created'
            },
            error: (err) => {
               return parseError(err?.response?.data)?.message
            },
         })
      },

      attemptUpdateIntegration: (logProtectionId: string, id: string, payload: Record<string, any>, cb?: any) => {
         return toast.promise(updateIntegrationMutation({ logProtectionId, payload, id } as any), {
            loading: 'updating Integration ...',
            success: (resp) => {
               // integration query
               queryClient.invalidateQueries({
                  queryKey: [
                     API_ENDPOINTS.SERVICE_PROFILE, KEY_PROTECTION_API, KEY_PROTECTION_S_ALL_INTEGRATION_S
                  ]
               });
               cb && cb(resp);
               return 'Integration Updated'
            },
            error: (err) => {
               return parseError(err?.response?.data)?.message
            }
         })
      },

      attemptDeleteIntegration: (id: string, payload: Record<string, any>, cb?: any) => {
         return toast.promise(deleteIntegrationMutation({ payload, id, orgId } as any), {
            loading: 'deleting Integration ...',
            success: (resp) => {
               // integration query
               queryClient.invalidateQueries({
                  queryKey: [
                     API_ENDPOINTS.SERVICE_PROFILE, KEY_PROTECTION_API
                  ]
               });
               // integration query
               queryClient.invalidateQueries({
                  queryKey: [
                     API_ENDPOINTS.SERVICE_PROFILE, KEY_PROTECTION_API, KEY_PROTECTION_S_ALL_INTEGRATION_S
                  ]
               });
               cb && cb(resp);
               return 'Deleted Integration'
            },
            error: (err) => {
               return parseError(err?.response?.data)?.message
            }
         })
      },

      attemptTestIntegration: (payload: Record<string, any>) => {
         return testIntegrationMutation({ payload } as any)
      },
      isTestingIntegration
   }
}