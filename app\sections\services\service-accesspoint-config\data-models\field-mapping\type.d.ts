import { AdditionalFields } from "types/additional-attributes";
import { FieldMappings } from "types/field-mappings";
import { ServiceProfileSpecifications } from "types/service-profile-specification";

export type ModeTypes = 'visual' | 'table';
export interface MappingValues {
   id: string
   source: string
   sourceDetails?: ServiceProfileSpecifications.Fields
   targetDetails?: AdditionalFields.Root
   target: string
   expanded: boolean
   parentSource?: MappingValues | null
   children: Array<MappingValues>
}

export interface HierarchyMappingValues {
   id: string
   source: string
   sourceDetails?: ServiceProfileSpecifications.Fields
   targetDetails?: AdditionalFields.Root
   target: string
   expanded: boolean
   parentSource?: MappingValues | null
   children: Array<HierarchyMappingValues>
}

export interface MappingsProps {
   serviceProfile: ServiceProfile
   viewMode: ModeTypes
}

export type DropdownOptionTypes = {
   label: string
   value: string
}

export interface FieldMappingRowProps {
   meta: {
      sourceValue: string;
      targetValue: string;
      hasError: boolean
      excludeSourceValues: string[]
      mapping: MappingValues
      canRemove: boolean
      showExpand?: boolean
   }
   sourceOptions: Array<DropdownOptionTypes>
   targetOptions: Array<DropdownOptionTypes>
   isChild?: boolean
}

export type AddMappingRecursivelyOptions = {
   mappings: MappingValues[],
   parentId: string,
   newMapping: MappingValues,
   parentValue?: string
   canAddChild?: boolean
}

export type DeleteMappingRecursivelyOptions = {
   parentId?: string,
   id: string
   mappings: MappingValues[]
}

export interface FieldMappingProps {
   dataModel: any
   serviceProfile: ServiceProfile
   mappings: Array<MappingValues>
   setMappings: React.Dispatch<React.SetStateAction<MappingValues[]>>
   fieldMapping: FieldMappings.Root | null
   mappingErrors: MappingErrorTypes[]
   setMappingErrors:  React.Dispatch<React.SetStateAction<MappingErrorTypes[]>>
}


export type MappingErrorTypes = {
   mapping: MappingValues
   errors: string[]
}