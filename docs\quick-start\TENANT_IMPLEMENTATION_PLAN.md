# Tenant-Based Quick Start Implementation Plan

## Overview

This document outlines the plan to integrate tenant/organization data into the quick-start flow, replacing the current test implementation with production-ready logic that determines tenant type based on actual subscription data.

## Current State Analysis

### Existing Infrastructure

1. **User Store** (`store/user.tsx`):
   - Fetches user data including organization
   - Fetches subscriptions for the organization
   - Maps subscriptions to available categories (DOMAIN_S)

2. **Organization Services**:
   - `organizationClient` - API client for organization operations
   - `tenantsClient` - API client for tenant operations
   - Platform fetch instance for authenticated requests

3. **Subscription Data Structure**:
   - Contains `productKey` which maps to domain categories
   - Contains `pricingTier` information
   - Has entitlements that define features

## Implementation Plan

### Phase 1: Update Service Layer

#### 1.1 Create Tenant Type Detection Service

```typescript
// services/tenant-configuration.ts - Update existing file

import useUserDetails from 'store/user';

/**
 * Determine tenant type from subscription data
 */
export const useTenantType = () => {
  const { subscriptions, isSubscriptionLoading } = useUserDetails();
  
  return useQuery({
    queryKey: ['tenant-type', subscriptions],
    queryFn: () => {
      if (!subscriptions?.length) return 'trial';
      
      // Analyze subscriptions to determine tenant type
      const productKeys = subscriptions.map(s => s.productKey);
      const pricingTiers = subscriptions.map(s => s.charge?.pricingTier?.name);
      
      // Business logic to map subscriptions to tenant types
      if (pricingTiers.includes('Enterprise')) return 'enterprise';
      if (pricingTiers.includes('Professional')) return 'enterprise';
      if (pricingTiers.includes('Security')) return 'security';
      if (pricingTiers.includes('Infrastructure')) return 'infra';
      if (pricingTiers.includes('Starter')) return 'basic';
      
      return 'trial';
    },
    enabled: !isSubscriptionLoading
  });
};
```

#### 1.2 Update Quick Start Status Check

```typescript
/**
 * Get quick-start status based on user's onboarding state
 */
export const useQuickStartStatus = () => {
  const { user } = useUserDetails();
  const organizationId = user?.organization?.id;
  
  return useQuery({
    queryKey: ['quick-start-status', organizationId],
    queryFn: async () => {
      if (!organizationId) return null;
      
      // Check organization onboarding state
      const { data } = await organizationClient.getOrganizationById(organizationId);
      
      return {
        needsQuickStart: !data.onboardingCompleted,
        isCompleted: data.onboardingCompleted,
        currentStep: data.onboardingStep || 0
      };
    },
    enabled: !!organizationId
  });
};
```

### Phase 2: Update Quick Start Page

#### 2.1 Integrate Real Tenant Data

```typescript
// pages/quick-start.tsx - Update existing file

export default function QuickStart() {
  const { user, subscriptions, categories } = useUserDetails();
  const { data: tenantType } = useTenantType();
  const { data: quickStartStatus } = useQuickStartStatus();
  
  // Get predefined categories based on subscriptions
  const predefinedCategories = useMemo(() => {
    if (!subscriptions?.length) return undefined;
    
    // Get enabled categories from subscriptions
    return categories
      .filter(cat => !cat.disabled)
      .map(cat => cat.value);
  }, [subscriptions, categories]);
  
  // Determine if this is a predefined tenant
  const isPredefinedTenant = subscriptions?.length > 0;
  
  // ... rest of component
}
```

### Phase 3: Update Context Provider

#### 3.1 Remove Test Mode Logic

```typescript
// Remove TestModeInitializer and URL parameter logic
// Update GettingStartedContext to use real data

const GettingStartedProvider = ({ children }) => {
  const { subscriptions, categories } = useUserDetails();
  
  // Set predefined configuration based on actual subscriptions
  useEffect(() => {
    if (subscriptions?.length > 0) {
      const enabledCategories = categories
        .filter(cat => !cat.disabled)
        .map(cat => cat.value);
      
      setPredefinedCategories(enabledCategories);
      setIsPredefinedTenant(true);
      setSelectedCategories(enabledCategories);
    }
  }, [subscriptions, categories]);
  
  // ... rest of provider
};
```

### Phase 4: API Integration

#### 4.1 Save Progress to Organization

```typescript
export const useSaveQuickStartProgress = () => {
  const { user } = useUserDetails();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: QuickStartSaveRequest) => {
      const orgId = user?.organization?.id;
      if (!orgId) throw new Error('No organization found');
      
      // Save to organization metadata
      return await organizationClient.updateOrganizationMetadata(orgId, {
        onboardingStep: data.currentStep,
        onboardingData: data
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['organization']);
    }
  });
};
```

#### 4.2 Complete Quick Start

```typescript
export const useCompleteQuickStart = () => {
  const { user } = useUserDetails();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: QuickStartCompletionData) => {
      const orgId = user?.organization?.id;
      if (!orgId) throw new Error('No organization found');
      
      // Mark onboarding as complete
      return await organizationClient.updateOrganizationState(orgId, {
        type: 'ONBOARDING_COMPLETED_REQUEST',
        onboardingData: data
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['organization']);
      // Redirect to dashboard
      window.location.href = '/console/dashboard';
    }
  });
};
```

### Phase 5: Route Protection

#### 5.1 Update Console Route

```typescript
// routes/console/index.tsx
export default function Console() {
  const { user } = useUserDetails();
  const { data: quickStartStatus } = useQuickStartStatus();
  
  // Redirect to quick-start if needed
  if (quickStartStatus?.needsQuickStart && !quickStartStatus?.isCompleted) {
    return <Navigate to="/console/quick-start" />;
  }
  
  return <DashboardLayout />;
}
```

### Phase 6: Clean Up

1. Remove test files and test-specific code
2. Remove URL parameter handling
3. Update documentation
4. Add proper error handling

## Migration Steps

1. **Update Service Layer**: Implement tenant type detection based on subscriptions
2. **Update Components**: Use real subscription data instead of test modes
3. **API Integration**: Connect to organization metadata endpoints
4. **Test**: Verify with different subscription types
5. **Deploy**: Roll out with feature flag if needed

## Key Considerations

1. **Backward Compatibility**: Ensure existing users aren't forced through quick-start
2. **Error Handling**: Gracefully handle missing subscription data
3. **Performance**: Cache subscription data to avoid repeated API calls
4. **Security**: Verify tenant configuration on backend, not just frontend

## API Endpoints Needed

1. `GET /organization/{id}/metadata` - Get onboarding status
2. `PUT /organization/{id}/metadata` - Update onboarding progress
3. `POST /organization/{id}/complete-onboarding` - Mark as complete

## Testing Strategy

1. Test with new organizations (no subscriptions)
2. Test with existing organizations (various subscription types)
3. Test error scenarios (API failures, missing data)
4. Test resume functionality
5. Performance testing with multiple API calls