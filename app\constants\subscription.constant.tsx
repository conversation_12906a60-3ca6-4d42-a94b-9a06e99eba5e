import { CheckOutlined } from '@ant-design/icons';

export enum EntitlementTypeEnum {
   ApiRateLimit = 'API_RATELIMIT',
   WebhookSupport = 'WEBHOOK_SUPPORT',
   RemoteDate = 'REMOTE_DATA',
   LogRetention = 'LOG_RETENTION',
   CustomerCount = 'CUSTOMER_COUNT',
   FeatureSupport = 'FEATURE_SUPPORT',
   ServiceAgreement = 'SERVICE_AGREEMENT',
   SSO = 'SSO',
}

export enum ChargeRecurringTypeEnum {
   Monthly = 'MONTHLY_RECURRING',
   Yearly = 'YEARLY_RECURRING',
}

const initSupportRecords = () => {
   SUPPORTABLE_RECORDS.set(EntitlementTypeEnum.ApiRateLimit, { key: 'callsPerMonth' });
}

const initChargeType = () => {
   CHARGE_TYPE.set(ChargeRecurringTypeEnum.Monthly, { period: 'months' });
   CHARGE_TYPE.set(ChargeRecurringTypeEnum.Yearly, { period: 'years' });
}


export const SUPPORTABLE_RECORDS = new Map();
initSupportRecords();

export const SUPPORTABLE_RECORDS_KEYS = Array.from(SUPPORTABLE_RECORDS.keys());

export const CHARGE_TYPE = new Map();
initChargeType();

export enum EntitlementLevel {
   topLevel = 'TOP_LEVEL',
   Collapsed = 'COLLAPSED'
};

const ICON_STYLE = {
   color: '#4c83ee', fontSize: '1.1rem'
}

export enum SubscriptionTiersType {
   Grow = 'Grow',
   Build = 'Build',
   Launch = 'Launch',
   Scale = 'Scale',
   Enterprise = 'Enterprise',
}

export const BILLING_CATALOG = {
   [EntitlementTypeEnum.ApiRateLimit]: {
      level: EntitlementLevel.topLevel,
      type: EntitlementTypeEnum.ApiRateLimit,
      label: 'API Calls',
      icon: <CheckOutlined style={ICON_STYLE} />,
      parseMessage: (data: any) => {
         if (data?.apiRateLimit?.type === "UNLIMITED_PKG") {
            return "Unlimited"
         } else {
            return data?.apiRateLimit?.callsPerMonth ?? '-'
         }
      },
      planIncludes: [],
      description: null,
      key: 'apiRateLimit',
   },

   [EntitlementTypeEnum.WebhookSupport]: {
      level: EntitlementLevel.Collapsed,
      type: EntitlementTypeEnum.WebhookSupport,
      label: 'Webhook Support',
      planIncludes: [SubscriptionTiersType.Build, SubscriptionTiersType.Launch, SubscriptionTiersType.Grow, SubscriptionTiersType.Scale],
      icon: <CheckOutlined style={ICON_STYLE} />,
      parseMessage: () => {
         return ''
      },
      description: 'Enables support for webhooks, allowing real-time data integration and notifications.',
   },

   [EntitlementTypeEnum.RemoteDate]: {
      level: EntitlementLevel.Collapsed,
      type: EntitlementTypeEnum.RemoteDate,
      label: 'Remote Data',
      planIncludes: [SubscriptionTiersType.Grow, SubscriptionTiersType.Scale],
      icon: <CheckOutlined style={ICON_STYLE} />,
      parseMessage: () => {
         return ''
      },
      description: 'Provides access to provider native response data for specific use cases.',
   },

   [EntitlementTypeEnum.LogRetention]: {
      level: EntitlementLevel.Collapsed,
      type: EntitlementTypeEnum.LogRetention,
      label: 'Log Retention',
      icon: <CheckOutlined style={ICON_STYLE} />,
      planIncludes: [SubscriptionTiersType.Build, SubscriptionTiersType.Launch, SubscriptionTiersType.Grow, SubscriptionTiersType.Scale],
      parseMessage: (data: Record<string, any>) => {
         if (data?.logRetention?.type?.toLowerCase() === 'custom') {
            return 'Custom'
         }
         return `${data?.logRetention?.countInDays} days` ?? '0 days'
      },
      key: 'logRetention',
      description: 'Duration for which system logs are retained, measured in days.',
   },

   [EntitlementTypeEnum.CustomerCount]: {
      level: EntitlementLevel.topLevel,
      type: EntitlementTypeEnum.CustomerCount,
      label: 'Customer Count',
      icon: <CheckOutlined style={ICON_STYLE} />,
      planIncludes: [],
      parseMessage: (data: Record<string, any>) => {
         if (data.customerCount.type === "UNLIMITED_PKG") {
            return "Unlimited"
         } else {
            return data?.customerCount?.value ?? '-'
         }
      },
      description: 'The number of customers you can manage under your current plan.',
   },

   [EntitlementTypeEnum.FeatureSupport]: {
      level: EntitlementLevel.Collapsed,
      type: EntitlementTypeEnum.FeatureSupport,
      label: 'Request New Integration',
      planIncludes: [SubscriptionTiersType.Grow, SubscriptionTiersType.Scale],
      icon: <CheckOutlined style={ICON_STYLE} />,
      parseMessage: () => {
         return ''
      },
      description: 'Ability to request new feature integrations and customizations.',
   },

   [EntitlementTypeEnum.ServiceAgreement]: {
      level: EntitlementLevel.Collapsed,
      type: EntitlementTypeEnum.ServiceAgreement,
      label: 'SLA',
      planIncludes: [SubscriptionTiersType.Build, SubscriptionTiersType.Launch, SubscriptionTiersType.Grow, SubscriptionTiersType.Scale],
      icon: <CheckOutlined style={ICON_STYLE} />,
      parseMessage: () => {
         return ''
      },
      description: 'Service Level Agreement provided on a best efforts basis.',
   },
   [EntitlementTypeEnum.SSO]: {
      level: EntitlementLevel.Collapsed,
      type: EntitlementTypeEnum.SSO,
      label: "Single Sign-On (SSO)",
      planIncludes: [SubscriptionTiersType.Scale],
      icon: <CheckOutlined style={ICON_STYLE} />,
      isEnabled: true,
      parseMessage: () => {
         return ''
      },
      description: 'Supports Single Sign-On for enhanced security and ease of access.',
   }
}
