import React from 'react';
import { Grid, Card, CardContent, Skeleton, Box, Stack } from '@mui/material';

interface ServiceCardSkeletonProps {
  count?: number;
}

const ServiceCardSkeleton: React.FC<ServiceCardSkeletonProps> = ({ count = 6 }) => {
  return (
    <Grid container spacing={2}>
      {Array.from({ length: count }).map((_, index) => (
        <Grid item xs={12} sm={6} md={4} key={index}>
          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <CardContent sx={{ flex: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Skeleton variant="circular" width={40} height={40} sx={{ mr: 2 }} />
                <Box sx={{ flex: 1 }}>
                  <Skeleton width="60%" height={24} />
                  <Skeleton width="40%" height={16} sx={{ mt: 0.5 }} />
                </Box>
              </Box>
              
              <Stack spacing={1} sx={{ mt: 2 }}>
                <Skeleton width="100%" height={16} />
                <Skeleton width="80%" height={16} />
              </Stack>
            </CardContent>
            
            <Box sx={{ px: 2, pb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Skeleton width={60} height={20} />
              <Skeleton variant="rectangular" width={48} height={32} />
            </Box>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default ServiceCardSkeleton;