import { useEffect, useState } from 'react';
import { useLocation } from '@remix-run/react';

// material-ui
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';

// assets
import LockOutlined from '@ant-design/icons/LockOutlined';
import UserOutlined from '@ant-design/icons/UserOutlined';

function getPathIndex(pathname: any) {
  let selectedTab = 0;
  switch (pathname) {
    case 'personal_info':
      selectedTab = 0;
      break;
    case 'change_password':
      selectedTab = 1;
      break;
    default:
      selectedTab = 0;
  }
  return selectedTab;
}

// ==============================|| USER PROFILE - TAB ||============================== //

const TAB_MENUS = [
  {
    title: 'Contact Information',
    id: 'personal_info',
    icon: <UserOutlined />,
    url: '/console/account-settings/user/contact'
  },
  {
    title: 'Change Password',
    id: 'change_password',
    icon: <LockOutlined />,
    url: '/console/account-settings/user/password'
  }
]

export default function ProfileTab({ setSelectedTab, selectedTab }: Record<string, any>) {

  const [selectedIndex, setSelectedIndex] = useState(getPathIndex(selectedTab));

  const onSelectTab = (key: string) => {
    setSelectedTab(key)
  }

  useEffect(() => {
    setSelectedIndex(
      getPathIndex(selectedTab)
    );
  }, [selectedTab]);

  return (
    <List component="nav" sx={{ p: 0, '& .MuiListItemIcon-root': { minWidth: 32, color: 'grey.500' } }}>

      {TAB_MENUS.map(({ title, icon, id }, index) => {
        return (
          <ListItemButton
            selected={selectedIndex === index}
            onClick={() => (
              void onSelectTab(id)
            )}
            key={index}
          >
            <ListItemIcon>
              {icon}
            </ListItemIcon>
            <ListItemText primary={title} />
          </ListItemButton>
        )
      })}

    </List>
  );
}
