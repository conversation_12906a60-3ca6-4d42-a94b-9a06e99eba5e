{"name": "ipass", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "dotenv -e .env.local -- remix vite:dev", "build": "remix vite:build --mode build", "start:development": "dotenv -e .env.development -- remix-serve  ./build/server/index.js", "start:production": "dotenv -e .env.production -- remix-serve ./build/server/index.js", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "typecheck": "tsc"}, "dependencies": {"@ant-design/colors": "^7.0.2", "@ant-design/form": "^1.0.0-beta.3", "@ant-design/icons": "^5.3.1", "@ant-design/pro-descriptions": "^2.6.1", "@dmitryrechkin/json-schema-to-zod": "^1.0.0", "@dnd-kit/core": "^6.1.0", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@fontsource/inter": "^5.0.17", "@fontsource/poppins": "^5.0.12", "@fontsource/public-sans": "^5.0.17", "@fontsource/roboto": "^5.0.12", "@hookform/resolvers": "^3.9.0", "@jazasoft/mui-table": "^1.0.32", "@loadable/component": "^5.16.4", "@mui/base": "^5.0.0-beta.38", "@mui/icons-material": "^6.1.10", "@mui/lab": "^5.0.0-alpha.167", "@mui/material": "^5.16.8", "@mui/system": "^5.15.12", "@mui/x-charts": "^7.23.0", "@mui/x-data-grid": "^7.21.0", "@mui/x-date-pickers-pro": "^7.22.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@react-keycloak/web": "^3.4.0", "@remix-run/node": "^2.13.1", "@remix-run/react": "^2.13.1", "@remix-run/serve": "^2.13.1", "@rollup/plugin-commonjs": "^28.0.1", "@svgr/webpack": "^8.1.0", "@tanstack/query-sync-storage-persister": "^5.59.13", "@tanstack/react-query": "^5.59.15", "@tanstack/react-query-persist-client": "^5.59.15", "@tanstack/react-table": "^8.20.5", "@vitejs/plugin-react": "^4.2.1", "@xyflow/react": "^12.3.4", "axios": "^1.7.7", "axios-error": "^1.0.4", "better-sqlite3": "^9.6.0", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.5", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-annotation": "^3.1.0", "chartjs-plugin-datalabels": "^2.2.0", "chartjs-plugin-zoom": "^2.0.1", "class-variance-authority": "^0.7.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "framer-motion": "^11.0.8", "isbot": "^4.1.0", "js-cookie": "^3.0.5", "jsdom": "^25.0.1", "json-refs": "^3.0.15", "json-schema-to-zod": "^2.4.1", "jsonpath": "^1.1.1", "keycloak-js": "^26.0.6", "lodash": "^4.17.21", "lodash.capitalize": "^4.2.1", "lucide-react": "^0.453.0", "materialui-daterange-picker": "^1.1.92", "moment": "^2.30.1", "mui-daterange-picker": "^1.0.5", "prettier": "^3.3.3", "prism-react-renderer": "^1.1.1", "prismjs": "^1.29.0", "process": "^0.11.10", "prop-types": "^15.8.1", "rc-upload": "^4.8.1", "react": "^19.0.0", "react-chartjs-2": "^5.2.0", "react-copy-to-clipboard": "^5.1.0", "react-csv": "^2.2.2", "react-date-range": "^2.0.1", "react-device-detect": "^2.2.3", "react-dom": "^19.0.0", "react-error-boundary": "^4.1.2", "react-flow-renderer": "^10.3.17", "react-grid-layout": "^1.5.0", "react-hook-form": "^7.45.4", "react-idle-timer": "^5.7.2", "react-intl": "^7.0.1", "react-number-format": "^5.3.3", "react-phone-input-2": "^2.15.1", "react-router": "^6.22.3", "react-router-dom": "^6.22.3", "react-text-mask-hoc": "^0.11.0", "reactflow": "^11.11.4", "remix": "^2.13.1", "remix-image": "^1.4.0", "remix-utils": "^7.7.0", "sass-embedded": "^1.80.6", "simplebar-react": "^3.2.4", "slick-carousel": "^1.8.1", "sonner": "^1.5.0", "swr": "^2.2.5", "tailwind-merge": "^2.5.4", "util": "^0.12.5", "vite": "^5.2.10", "vite-plugin-commonjs": "^0.10.3", "vite-plugin-environment": "^1.1.3", "web-vitals": "^3.5.2", "yup": "^1.4.0", "zod": "^3.23.8", "zustand": "^5.0.0"}, "devDependencies": {"@remix-run/dev": "^2.13.1", "@types/canvas-confetti": "^1.9.0", "@types/js-cookie": "^3.0.6", "@types/react": "^18.2.20", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.2.7", "@types/react-grid-layout": "^1.3.5", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.19", "dotenv": "^16.4.7", "dotenv-cli": "^7.4.4", "esbuild-plugin-less": "^1.3.10", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "less": "^4.2.0", "less-loader": "^12.2.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.1.6", "vite": "^5.1.0", "vite-plugin-style-import": "^2.0.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}}