import {
  Button,
  <PERSON>alogA<PERSON>,
  DialogContent,
  DialogTitle,
  <PERSON>ack,
  Typography,
} from "@mui/material";
import { Dialog, DialogClose } from "components/@extended/dialog";
import Form from "./form";
import { useRef } from "react";
import useEnvironment from "hooks/api/useEnvironment";
import { useMutation } from "@tanstack/react-query";
import Environment from "types/environment";
import useUserDetails from "store/user";
import { getSubscriptionFlags } from "utils/subscription";

interface CreateEnvProps {
  isOpen: boolean;
  mode: "create" | "edit";
  selected?: Environment.Root | null;
  onClose: () => void;
  slelectedKey?: string | null;
  selectedType?: string | null;
  refetchEnvironments: () => void;
  existingEnvironments?: Environment.Root[]; // Add this prop
}

const CreateEnv = ({
  isOpen,
  selected,
  mode,
  onClose,
  refetchEnvironments,
  slelectedKey,
  selectedType,
  existingEnvironments = [], // Default to empty array
}: CreateEnvProps) => {
  const formRef = useRef<HTMLFormElement>();
  const { subscriptions } = useUserDetails();

  const { createEnvironmentQueryConfig, patchEnvironmentQueryConfig } =
    useEnvironment();

  const title = `${mode === "edit" ? "Edit" : "Create"} Environment`;

  const description = `${mode === "edit" ? `Edit ${selected?.name}` : "Add a new"} for your applications.`;

  // Get subscription flags
  const subscriptionFlags = getSubscriptionFlags(subscriptions);

  const { mutateAsync: attemptCreateEnvironment, isPending: isSubmitting } =
    useMutation({
      ...createEnvironmentQueryConfig(),
      onSuccess: () => {
        refetchEnvironments();
        onClose();
      },
    });

  const { mutateAsync: attemptEditEnvironment, isPending: isUpdating } =
    useMutation({
      ...patchEnvironmentQueryConfig(),
      onSuccess: () => {
        refetchEnvironments();
        onClose();
      },
    });

  const onRequestSubmit = () => {
    formRef?.current?.requestSubmit();
  };

  return (
    <Dialog open={isOpen} maxWidth={"xs"}>
      <DialogTitle component={Stack} gap={0.7}>
        <Typography variant="h5">{title}</Typography>
        <Typography color={"secondary.main"} variant="body1">
          {description}
        </Typography>
      </DialogTitle>
      <DialogClose onClose={onClose} />
      <DialogContent>
        <Form
          selectedKey={slelectedKey}
          selectedType={selectedType}
          formRef={formRef as React.RefObject<HTMLFormElement>}
          selected={selected}
          attemptCreateEnvironment={attemptCreateEnvironment}
          attemptEditEnvironment={attemptEditEnvironment}
          mode={mode}
          onClose={onClose}
          subscriptionFlags={subscriptionFlags}
          existingEnvironments={existingEnvironments}
        />
      </DialogContent>
      <DialogActions>
        <></>
        <Stack direction={"row"} gap={2}>
          <Button
            disabled={isSubmitting || isUpdating}
            onClick={onClose}
            variant="outlined"
          >
            Cancel
          </Button>
          <Button
            color="primary"
            variant="contained"
            onClick={onRequestSubmit}
            disabled={isSubmitting || isUpdating}
          >
            {title}
          </Button>
        </Stack>
      </DialogActions>
    </Dialog>
  );
};

export default CreateEnv;
