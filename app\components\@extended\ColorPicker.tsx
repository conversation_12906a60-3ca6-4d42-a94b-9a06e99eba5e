import { HTMLAttributes, useState } from "react";

interface ColorPickerProps extends HTMLAttributes<HTMLInputElement> {
   value?: string
   onQuickSelect?: (selectedColor: string) => void
}

const ColorPicker = ({ value: valueProp, onChange: onChangeProp, onQuickSelect : onQuickSelectProp}: ColorPickerProps) => {

   const [valueLocal, setValueLocal] = useState<string>('');

   const value = valueProp ?? valueLocal;

   const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setValueLocal(e.target.value)
      onChangeProp && onChangeProp?.(e)
   }

   const onQuickSelect = (selectedColor: string) => {
      setValueLocal(selectedColor)
      onQuickSelectProp?.(selectedColor)
   }

   return (
      <>
         <div className="flex items-center space-x-3 mt-2">
            <input
               type="color"
               id="color"
               value={value}
               onChange={onChange}
               className="w-10 h-10 rounded-lg border border-slate-200 cursor-pointer"
            />
            <div className="flex items-center space-x-2">
               <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: value }}
               />
               <span className="text-sm text-slate-600">{value}</span>
            </div>
         </div>
         <div className="flex items-center space-x-2 mt-3">
            <span className="text-xs text-slate-500">Quick colors:</span>
            {[
               { name: "Blue", color: "#3b82f6" },
               { name: "Green", color: "#10b981" },
               { name: "Orange", color: "#f59e0b" },
               { name: "Red", color: "#ef4444" },
               { name: "Purple", color: "#8b5cf6" },
               { name: "Pink", color: "#ec4899" },
            ].map((preset) => (
               <button
                  key={preset.name}
                  type="button"
                  onClick={() => onQuickSelect(preset.color)}
                  className="w-6 h-6 rounded-full border-2 border-white shadow-sm hover:scale-110 transition-transform"
                  style={{ backgroundColor: preset.color }}
                  title={preset.name}
               />
            ))}
         </div>
      </>
   )
}

export default ColorPicker;