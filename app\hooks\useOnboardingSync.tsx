import { useEffect } from 'react';
import useOnboardingStore from 'store/onboarding';
import useOnboardingStatus from './useOnboardingStatus';
import { OnboardingStepType } from 'types/tenant-metadata';

/**
 * Hook that syncs backend onboarding status with the frontend store
 * This ensures that the onboarding dialog shows accurate progress based on
 * what's actually been completed in the backend
 */
export const useOnboardingSync = (orgId?: string) => {
  const { 
    hasCategories, 
    hasServices, 
    hasWebhooks, 
    hasTeamMembers,
    isLoading 
  } = useOnboardingStatus(orgId);
  
  const { 
    completeStep, 
    steps,
    resetOnboarding,
    completedSteps 
  } = useOnboardingStore();

  useEffect(() => {
    if (!isLoading) {
      // Sync backend status with frontend store
      
      // Categories step
      if (hasCategories && !steps.find(s => s.id === 'categories')?.completed) {
        completeStep('categories');
      }
      
      // Services/Connectors step
      if (hasServices && !steps.find(s => s.id === 'connectors')?.completed) {
        completeStep('connectors');
      }
      
      // Webhooks step
      if (hasWebhooks && !steps.find(s => s.id === 'webhooks')?.completed) {
        completeStep('webhooks');
      }
      
      // Team members step (optional)
      if (hasTeamMembers && !steps.find(s => s.id === 'team')?.completed) {
        completeStep('team');
      }
    }
  }, [
    isLoading, 
    hasCategories, 
    hasServices, 
    hasWebhooks, 
    hasTeamMembers, 
    completeStep, 
    steps
  ]);

  return {
    isBackendSynced: !isLoading,
    backendCompletedSteps: completedSteps,
  };
};

export default useOnboardingSync;