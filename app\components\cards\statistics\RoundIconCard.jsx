import PropTypes from 'prop-types';
// material-ui
import Avatar from '@mui/material/Avatar';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project imports
import MainCard from 'components/MainCard';
import { useMediaQuery } from '@mui/system';
import { useEffect } from 'react';

export default function RoundIconCard({ primary, secondary, content, iconPrimary, color, bgcolor }) {
  const IconPrimary = iconPrimary;
  const primaryIcon = iconPrimary ? <IconPrimary fontSize="large" /> : null;

  const downXl = useMediaQuery((theme) => theme.breakpoints.down('xl'))

  return (
    <MainCard contentSX={{ height: '100%' }} sx={{ height: '100%' }}>
      <Grid
        style={{ height: '100%' }}
        container alignItems="center" spacing={0} justifyContent="space-between">
        <Grid item>
          <Stack spacing={1}>
            <Typography variant="h5" color='secondary.main'>
              {primary}
            </Typography>
            <Typography variant={downXl ? "h2" : "h1"}>{secondary}</Typography>
            <Typography variant="subtitle2" color="inherit" mt={.5}>
              {content}
            </Typography>
          </Stack>
        </Grid>
        <Grid item>
          <Avatar
            variant="rounded"
            sx={{
              bgcolor,
              color,
              '& .MuiSvgIcon-root': { fontSize: '1.5rem' }
            }}
          >
            {primaryIcon}
          </Avatar>
        </Grid>
      </Grid>
    </MainCard>
  );
}

RoundIconCard.propTypes = {
  primary: PropTypes.string,
  secondary: PropTypes.string,
  content: PropTypes.string,
  iconPrimary: PropTypes.any,
  color: PropTypes.string,
  bgcolor: PropTypes.string
};
