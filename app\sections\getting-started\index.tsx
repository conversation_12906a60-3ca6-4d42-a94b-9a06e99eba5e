import { useState } from 'react';
import { Box, Container, useTheme } from '@mui/material';
import { useLocation, useNavigate, useSearchParams } from '@remix-run/react';
import StepperNavigation from './components/StepperNavigation';
import CategorySelection from './steps/CategorySelection';
import ServiceConfiguration from './steps/ServiceConfiguration';
import GetStartedFinal from './steps/GetStartedFinal';
import { GettingStartedProvider } from './context/GettingStartedContext';
import { TenantOnboardType } from 'constants/organization';
import { Routes } from 'constants/route';

// Step configuration
const STEPS = [
  {
    id: 'unified-apis',
    title: 'Select Unified APIs',
    helpText: 'Select and enable API categories',
    component: CategorySelection,
  },
  {
    id: 'connectors',
    title: 'Select Connectors',
    helpText: 'Select and enable connectors',
    component: ServiceConfiguration,
  },
  {
    id: 'get-started',
    title: 'Get Started',
    helpText: 'Get started with Unizo',
    component: GetStartedFinal,
  },
];

interface GettingStartedStepperProps {
  initialStep?: number;
  onComplete?: () => void;
  onSkip?: () => void;
  tenantConfig?: any;
}

function GettingStartedStepper({
  initialStep = 0,
  onComplete,
  onSkip,
  tenantConfig
}: GettingStartedStepperProps) {
  const theme = useTheme();

  const [params] = useSearchParams();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(initialStep);

  const CurrentStepComponent = STEPS[currentStep].component;

  const handleNext = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Call onComplete callback if provided, otherwise navigate
      if (onComplete) {
        onComplete();
      } else {
        navigate('/console/getting-started');
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    // Only allow navigation to completed or current steps
    if (stepIndex <= currentStep) {
      setCurrentStep(stepIndex);
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        minHeight: '100vh',
        backgroundColor: theme.palette.background.default,
      }}
    >
      {/* Left Sidebar - Stepper */}
      <Box
        sx={{
          width: 280,
          backgroundColor: '#2D3748',
          position: 'fixed',
          height: '100vh',
          left: 0,
          top: 0,
        }}
      >
        <StepperNavigation
          steps={STEPS}
          currentStep={currentStep}
          onStepClick={handleStepClick}
        />
      </Box>

      {/* Main Content Area */}
      <Box
        sx={{
          flex: 1,
          marginLeft: '280px',
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: theme.palette.background.default,
          minHeight: '100vh',
        }}
      >
        <Container
          maxWidth="xl"
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            py: 6,
            px: 4,
          }}
        >
          <CurrentStepComponent
            onNext={handleNext}
            onBack={handleBack}
            isFirstStep={currentStep === 0}
            isLastStep={currentStep === STEPS.length - 1}
          />
        </Container>
      </Box>
    </Box>
  );
}

export default function GettingStartedPage() {
  return (
    <GettingStartedStepper />
  );
}

// Export the stepper component for use in test page
export { GettingStartedStepper };