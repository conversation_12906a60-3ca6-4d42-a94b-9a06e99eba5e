import React from 'react';
import { Box, Skeleton, Stack, Tabs, Tab } from '@mui/material';

interface TabContentSkeletonProps {
  tabCount?: number;
  contentHeight?: number;
  showActions?: boolean;
}

const TabContentSkeleton: React.FC<TabContentSkeletonProps> = ({
  tabCount = 3,
  contentHeight = 400,
  showActions = false
}) => {
  return (
    <Box>
      {/* Tab Header */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Tabs value={0}>
          {Array.from({ length: tabCount }).map((_, index) => (
            <Tab 
              key={index} 
              label={<Skeleton width={80} height={20} />} 
              disabled 
            />
          ))}
        </Tabs>
        {showActions && (
          <Box sx={{ pr: 2 }}>
            <Skeleton variant="rectangular" width={100} height={36} />
          </Box>
        )}
      </Box>

      {/* Tab Content */}
      <Box sx={{ p: 3 }}>
        <Stack spacing={3}>
          <Box>
            <Skeleton width={200} height={28} />
            <Skeleton width={400} height={20} sx={{ mt: 1 }} />
          </Box>
          
          <Skeleton variant="rectangular" width="100%" height={contentHeight} />
        </Stack>
      </Box>
    </Box>
  );
};

export default TabContentSkeleton;