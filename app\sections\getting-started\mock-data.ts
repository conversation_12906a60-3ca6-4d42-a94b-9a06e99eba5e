// Mock data for testing the Getting Started flow
export const MOCK_SERVICE_PROFILES = {
  SCM: [
    { id: 'github', name: 'GitH<PERSON>', vendor: 'GitHub Inc.', logoUrl: 'https://cdn.simpleicons.org/github/181717', description: 'Version control and collaboration platform' },
    { id: 'gitlab', name: 'GitLab', vendor: 'GitLab Inc.', logoUrl: 'https://cdn.simpleicons.org/gitlab/FC6D26', description: 'Complete DevOps platform' },
    { id: 'bitbucket', name: 'Bitbucket', vendor: 'Atlassian', logoUrl: 'https://cdn.simpleicons.org/bitbucket/0052CC', description: 'Git repository management' },
    { id: 'azure-devops', name: 'Azure DevOps', vendor: 'Microsoft', logoUrl: 'https://cdn.simpleicons.org/azuredevops/0078D7', description: 'Development collaboration tools' },
  ],
  TICKETING: [
    { id: 'jira', name: '<PERSON><PERSON>', vendor: 'Atlassian', logoUrl: 'https://cdn.simpleicons.org/jira/0052CC', description: 'Issue tracking and project management' },
    { id: 'linear', name: 'Linear', vendor: 'Linear', logoUrl: 'https://cdn.simpleicons.org/linear/5E6AD2', description: 'Modern issue tracking' },
    { id: 'asana', name: 'Asana', vendor: 'Asana', logoUrl: 'https://cdn.simpleicons.org/asana/F06A6A', description: 'Work management platform' },
    { id: 'monday', name: 'Monday.com', vendor: 'Monday', logoUrl: 'https://cdn.simpleicons.org/monday/FF0E00', description: 'Work operating system' },
  ],
  MONITORING: [
    { id: 'datadog', name: 'Datadog', vendor: 'Datadog', logoUrl: 'https://cdn.simpleicons.org/datadog/632CA6', description: 'Monitoring and analytics platform' },
    { id: 'newrelic', name: 'New Relic', vendor: 'New Relic', logoUrl: 'https://cdn.simpleicons.org/newrelic/1CE783', description: 'Application performance monitoring' },
    { id: 'grafana', name: 'Grafana', vendor: 'Grafana Labs', logoUrl: 'https://cdn.simpleicons.org/grafana/F46800', description: 'Observability platform' },
    { id: 'prometheus', name: 'Prometheus', vendor: 'CNCF', logoUrl: 'https://cdn.simpleicons.org/prometheus/E6522C', description: 'Monitoring system and time series database' },
  ],
  INCIDENT: [
    { id: 'pagerduty', name: 'PagerDuty', vendor: 'PagerDuty', logoUrl: 'https://cdn.simpleicons.org/pagerduty/06AC38', description: 'Incident response platform' },
    { id: 'opsgenie', name: 'Opsgenie', vendor: 'Atlassian', logoUrl: 'https://cdn.simpleicons.org/opsgenie/0052CC', description: 'Alert and on-call management' },
    { id: 'victorops', name: 'VictorOps', vendor: 'Splunk', logoUrl: 'https://cdn.simpleicons.org/splunk/000000', description: 'Incident management platform' },
  ],
  COMMS: [
    { id: 'slack', name: 'Slack', vendor: 'Salesforce', logoUrl: 'https://cdn.simpleicons.org/slack/4A154B', description: 'Team communication platform' },
    { id: 'teams', name: 'Microsoft Teams', vendor: 'Microsoft', logoUrl: 'https://cdn.simpleicons.org/microsoftteams/6264A7', description: 'Collaboration platform' },
    { id: 'discord', name: 'Discord', vendor: 'Discord Inc.', logoUrl: 'https://cdn.simpleicons.org/discord/5865F2', description: 'Voice, video, and text communication' },
  ],
  CI_CD: [
    { id: 'jenkins', name: 'Jenkins', vendor: 'Jenkins', logoUrl: 'https://cdn.simpleicons.org/jenkins/D24939', description: 'Open source automation server' },
    { id: 'circleci', name: 'CircleCI', vendor: 'CircleCI', logoUrl: 'https://cdn.simpleicons.org/circleci/343434', description: 'Continuous integration and delivery' },
    { id: 'github-actions', name: 'GitHub Actions', vendor: 'GitHub', logoUrl: 'https://cdn.simpleicons.org/githubactions/2088FF', description: 'Workflow automation' },
    { id: 'gitlab-ci', name: 'GitLab CI', vendor: 'GitLab', logoUrl: 'https://cdn.simpleicons.org/gitlab/FC6D26', description: 'Built-in CI/CD' },
  ],
  PCR: [
    { id: 'docker-hub', name: 'Docker Hub', vendor: 'Docker', logoUrl: 'https://cdn.simpleicons.org/docker/2496ED', description: 'Container image registry' },
    { id: 'npm', name: 'npm', vendor: 'npm Inc.', logoUrl: 'https://cdn.simpleicons.org/npm/CB3837', description: 'JavaScript package registry' },
    { id: 'artifactory', name: 'JFrog Artifactory', vendor: 'JFrog', logoUrl: 'https://cdn.simpleicons.org/jfrog/40BE46', description: 'Universal artifact repository' },
  ],
  VMS: [
    { id: 'snyk', name: 'Snyk', vendor: 'Snyk', logoUrl: 'https://cdn.simpleicons.org/snyk/4C4A73', description: 'Developer security platform' },
    { id: 'sonarqube', name: 'SonarQube', vendor: 'SonarSource', logoUrl: 'https://cdn.simpleicons.org/sonarqube/4E9BCD', description: 'Code quality and security' },
    { id: 'whitesource', name: 'WhiteSource', vendor: 'WhiteSource', logoUrl: 'https://cdn.simpleicons.org/mend/00B4A6', description: 'Open source security and compliance' },
  ],
};

// Mock API response generator
export const getMockServiceProfiles = (category: string, limit: number = 4) => {
  const services = MOCK_SERVICE_PROFILES[category as keyof typeof MOCK_SERVICE_PROFILES] || [];
  return {
    data: {
      data: services.slice(0, limit),
      pagination: {
        total: services.length,
        limit,
        offset: 0,
      },
    },
  };
};