import { CheckCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { Button, Typography, DialogActions, DialogContent, DialogTitle, FormControlLabel, Checkbox, Stack, useTheme } from "@mui/material";
import { Dialog, DialogClose } from "components/@extended/dialog";
import { useGetServiceProfile } from "hooks/api/service-profile/useGetServiceProfile";
import { useEffect, useMemo, useState } from "react";
import useServiceConfig from "store/setup-service";


export default () => {
   const { palette } = useTheme()

   const { selectedService } = useServiceConfig();
   const { isEnabledSandboxConfig, isUpdateSandboxConfig } = useGetServiceProfile();
   const { data: checkConfigData } = isEnabledSandboxConfig({ id: selectedService?.id });
   const [isSandboxEnabled, setIsSandboxEnabled] = useState(selectedService?.service?.sandbox?.enabled || false);
   const [sandboxConfigId, setSandboxConfigId] =useState("")

   const [isModalVisible, setIsModalVisible] = useState<boolean>(false);

   const payload =[
      {
         op: 'replace',
         path: '/sandbox',
         value: {
            enabled: isSandboxEnabled,
            ...(isSandboxEnabled && { id: sandboxConfigId }),
         },
      },
   ];

   const handleSandboxUpdate = () => {
      isUpdateSandboxConfig(selectedService?.service?.id, payload, () => {
         if (selectedService?.service?.sandbox) {
            selectedService.service.sandbox.enabled = isSandboxEnabled;
         }
      });
   };

   const onEdit = () => {
      setIsModalVisible(true);
   };

   const onSave = () => {
      handleSandboxUpdate()
      setIsModalVisible(false);
   };

   const onCancel = () => {
      setIsModalVisible(false);
   };

   const onCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      setIsSandboxEnabled(event.target.checked);
   };


   const isEnabled = useMemo(() => checkConfigData?.data?.length > 0, [checkConfigData]);

   useEffect(() => {
      const ids = checkConfigData?.data.map((i:any) => i?.id);
      if (ids && ids?.length > 0) {
         setSandboxConfigId(ids[0]);
      }
    }, [checkConfigData]);

   return (
      <>
         {
            isEnabled && (
               <Stack direction={'row'} gap={1} alignItems={'center'} >
                  <Typography variant='subtitle1'>Sandbox Enabled:</Typography>
                  {selectedService?.service?.sandbox?.enabled ? (
                     <Stack direction={'row'} gap={1} alignItems={'center'}>
                        <CheckCircleOutlined style={{ color: palette?.success?.main }} />
                        <span>Yes</span>
                     </Stack>
                  ) : (
                     <Stack direction={'row'} gap={1} alignItems={'center'}>
                        <CloseCircleOutlined style={{ color: palette?.error?.main }} />
                        <span>No</span>
                     </Stack>
                  )}
                  <Button
                     onClick={onEdit}
                  >Edit</Button>
               </Stack>
            )}

         <Dialog open={isModalVisible} onClose={onCancel}>
            <DialogTitle>Sandbox Configuration</DialogTitle>
            <DialogClose onClose={onCancel} />
            <DialogContent dividers>
               <Typography variant="body1" paragraph>
                  Sandbox mode enables you to test integrations using synthetic data. Switch off Sandbox mode when transitioning to production.
               </Typography>
               <FormControlLabel
                  control={<Checkbox checked={isSandboxEnabled} onChange={onCheckboxChange} />}
                  label="Use Sandbox"
               />
            </DialogContent>
            <DialogActions>
               <Button onClick={onCancel}>Cancel</Button>
               <Button variant="contained" color="primary" onClick={onSave}>
                  Save
               </Button>
            </DialogActions>
         </Dialog>
      </>
   )
}