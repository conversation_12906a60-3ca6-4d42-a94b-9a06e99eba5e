import { Grid, Skeleton, Stack } from "@mui/material"
import { GRID } from "./selection/constants"
import MainCard from "components/MainCard"

const GridWrapper = ({ children }: any) => {
   return (
      <Grid container columnSpacing={2} rowSpacing={2}>
         {children}
      </Grid>
   )
}

const GridItem = ({ children }: any) => {
   return (
      <Grid item {...GRID}>
         {children}
      </Grid>
   )
}

const Placeholder = () => {
   return (
      <GridWrapper>
         {new Array(2).fill({}).map((_, key) => (
            <GridItem key={key}>
               <MainCard>
                  <Stack gap={1}>
                     <Skeleton sx={{ maxWidth: '50%' }} />
                     <Skeleton sx={{ maxWidth: '80%' }} />
                     <Skeleton sx={{ maxWidth: '20%' }} />
                  </Stack>
               </MainCard>
            </GridItem>
         ))}
      </GridWrapper>
   )
}

export default Placeholder;