import React from 'react';

import { RightOutlined } from '@ant-design/icons';

import { ConnectorCardProps } from './ConnectorList.types';
import {
  ConnectorCard as StyledCard,
  ConnectorContent,
  ConnectorRow,
  ConnectorInfo,
  ConnectorIcon,
  ConnectorName,
  ConnectorActions,
  StyledSwitch,
  ArrowIcon,
} from './ConnectorList.styles';

export const ConnectorCard: React.FC<ConnectorCardProps> = ({
  connector,
  onToggle,
  onClick,
}) => {
  const handleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation();
    onToggle(event.target.checked);
  };

  const handleCardClick = () => {
    if (onClick) {
      onClick();
    }
  };

  return (
    <StyledCard onClick={handleCardClick}>
      <ConnectorContent>
        <ConnectorRow>
          <ConnectorInfo>
            <ConnectorIcon
              src={connector.icon}
              alt={connector.displayName}
              loading="lazy"
            />
            <ConnectorName variant="body1">{connector.displayName}</ConnectorName>
          </ConnectorInfo>
          <ConnectorActions>
            <StyledSwitch
              checked={connector.enabled}
              onChange={handleToggle}
              onClick={(e) => e.stopPropagation()}
              size="medium"
            />
            <ArrowIcon>
              <RightOutlined />
            </ArrowIcon>
          </ConnectorActions>
        </ConnectorRow>
      </ConnectorContent>
    </StyledCard>
  );
};