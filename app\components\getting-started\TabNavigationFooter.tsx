import React from 'react';
import { Box, Button, Grid, useTheme } from '@mui/material';
import { ArrowLeft, ArrowRight } from 'lucide-react';

interface TabNavigationFooterProps {
  onNext?: () => void;
  onPrevious?: () => void;
  isFirstTab?: boolean;
  isLastTab?: boolean;
  nextLabel?: string;
  previousLabel?: string;
  isNextDisabled?: boolean;
  isPreviousDisabled?: boolean;
  showDoneButton?: boolean;
  onDone?: () => void;
  isDoneDisabled?: boolean;
  showSkipButton?: boolean;
  onSkip?: () => void;
  skipLabel?: string;
}

export default function TabNavigationFooter({
  onNext,
  onPrevious,
  isFirstTab = false,
  isLastTab = false,
  nextLabel = 'Continue',
  previousLabel = 'Back',
  isNextDisabled = false,
  isPreviousDisabled = false,
  showDoneButton = false,
  onDone,
  isDoneDisabled = false,
  showSkipButton = false,
  onSkip,
  skipLabel = 'Skip for now',
}: TabNavigationFooterProps) {
  const theme = useTheme();

  const handleNext = () => {
    if (onNext) {
      onNext();
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handlePrevious = () => {
    if (onPrevious) {
      onPrevious();
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  return (
    <Box
      sx={{
        mt: 6,
        pt: 4,
        pb: 2,
        backgroundColor: theme.palette.background.paper,
        mx: -3,
        px: 3,
        borderTop: `1px solid ${theme.palette.divider}`,
      }}
    >
      <Grid container justifyContent="space-between" alignItems="center">
        <Grid item>
          {!isFirstTab && (
            <Button
              variant="text"
              onClick={handlePrevious}
              disabled={isPreviousDisabled}
              startIcon={<ArrowLeft size={18} />}
              sx={{
                textTransform: 'none',
                color: theme.palette.text.primary,
                fontWeight: 500,
                fontSize: '0.875rem',
                '&:hover': {
                  backgroundColor: 'transparent',
                  textDecoration: 'underline',
                },
              }}
            >
              {previousLabel}
            </Button>
          )}
        </Grid>
        <Grid item>
          {showDoneButton && isLastTab ? (
            <Button
              variant="contained"
              onClick={onDone}
              disabled={isDoneDisabled}
              sx={{
                textTransform: 'none',
                backgroundColor: theme.palette.grey[900],
                color: theme.palette.common.white,
                fontWeight: 500,
                fontSize: '0.875rem',
                px: 3,
                py: 1,
                '&:hover': {
                  backgroundColor: theme.palette.grey[800],
                },
                '&:disabled': {
                  backgroundColor: theme.palette.action.disabledBackground,
                },
              }}
            >
              Done
            </Button>
          ) : (
            !isLastTab && (
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                {showSkipButton && (
                  <Button
                    variant="text"
                    onClick={onSkip}
                    sx={{
                      textTransform: 'none',
                      color: theme.palette.text.secondary,
                      fontWeight: 500,
                      fontSize: '0.875rem',
                      '&:hover': {
                        backgroundColor: 'transparent',
                        textDecoration: 'underline',
                      },
                    }}
                  >
                    {skipLabel}
                  </Button>
                )}
                <Button
                  variant="contained"
                  onClick={handleNext}
                  disabled={isNextDisabled}
                  endIcon={<ArrowRight size={18} />}
                  sx={{
                    textTransform: 'none',
                    backgroundColor:
                      theme.palette.mode === 'dark'
                     ? theme.palette.primary.dark
                     : theme.palette.grey[900],
                    color: theme.palette.common.white,
                    fontWeight: 500,
                    fontSize: '0.875rem',
                    px: 3,
                    py: 1,
                    '&:hover': {
                     backgroundColor:
                     theme.palette.mode === 'dark'
                ? theme.palette.primary.dark
                : theme.palette.grey[800],
                    },
                  }}
                >
                  {nextLabel}
                </Button>
              </Box>
            )
          )}
        </Grid>
      </Grid>
    </Box>
  );
}