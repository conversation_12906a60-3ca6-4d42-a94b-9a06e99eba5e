import React from 'react';
import {
  Box,
  Typography,
  useTheme,
  alpha,
} from '@mui/material';
import TabContentHeader from 'components/getting-started/TabContentHeader';

interface PendingStepTabProps {
  stepNumber: string;
  title: string;
  message?: string;
}

export default function PendingStepTab({ 
  stepNumber, 
  title, 
  message = 'Complete the previous steps to unlock this section.' 
}: PendingStepTabProps) {
  const theme = useTheme();

  return (
    <Box>
      <TabContentHeader
        icon={<Typography variant="body2" fontWeight={600}>{stepNumber}</Typography>}
        title={title}
        description={message}
        iconBgColor={theme.palette.grey[100]}
        iconColor={theme.palette.mode === 'light' ? theme.palette.grey[600] : theme.palette.text.secondary}
      />
      
      <Box sx={{ textAlign: 'center', py: 6 }}>
        <Typography 
          variant="body1" 
          sx={{
            color: theme.palette.mode === 'light' 
              ? theme.palette.grey[600] 
              : theme.palette.text.secondary
          }}
        >
          This section will be available once you complete the previous steps.
        </Typography>
      </Box>
    </Box>
  );
}