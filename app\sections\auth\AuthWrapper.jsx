import PropTypes from 'prop-types';
import { useSearchParams } from '@remix-run/react';

// material-ui
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Alert from '@mui/material/Alert';
import Typography from '@mui/material/Typography';
import Link from '@mui/material/Link';
import Divider from '@mui/material/Divider';

// project import
import AuthFooter from './AuthFooter';
import Logo from 'components/logo';
import AuthCard from './AuthCard';

// assets
import AuthBackground from './AuthBackground';
import ExclamationCircleOutlined from '@ant-design/icons/ExclamationCircleOutlined';
import Image from 'remix-image';

// ==============================|| AUTHENTICATION - WRAPPER ||============================== //

export default function AuthWrapper({ children }) {
  const isLoggedIn = false;

  const [searchParams] = useSearchParams();
  const authParam = searchParams.get('auth') || '';

  let documentationLink = 'https://codedthemes.gitbook.io/mantis/authentication';

  return (
    <Box sx={{ minHeight: '100vh' }}>
      <AuthBackground />
      <Grid container direction="column" justifyContent="flex-end" sx={{ minHeight: '100vh' }}>
        {/* <Grid item xs={12} sx={{ ml: 3, mt: 3 }}>
          <Image src="https://unizo.ai/images/unz_dark_logo.svg" alt="logo" class="mx-auto block" style={{ height: 50, width: 50 }} />
        </Grid> */}
        <Grid item xs={12}>
          <Grid
            item
            xs={12}
            container
            justifyContent="center"
            alignItems="center"
            sx={{ minHeight: { xs: 'calc(100vh - 210px)', sm: 'calc(100vh - 134px)', md: 'calc(100vh - 112px)' } }}
          >
            <Grid item>
              {!isLoggedIn && authParam && (
                <Box sx={{ maxWidth: { xs: 400, lg: 475 }, margin: { xs: 2.5, md: 3 }, '& > *': { flexGrow: 1, flexBasis: '50%' } }}>
                  <Alert variant="border" color="primary" icon={<ExclamationCircleOutlined />}>
                    <Typography variant="h5">View Only</Typography>
                    <Typography variant="h6">
                      This page is view-only. To make it fully functional, please read the documentation provided{' '}
                      <Link href={documentationLink} target="_blank">
                        here
                      </Link>{' '}
                      after purchasing the theme.
                    </Typography>
                  </Alert>
                </Box>
              )}
              <AuthCard>{children}</AuthCard>
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} sx={{ m: 3, mt: 1 }}>
          <AuthFooter />
        </Grid>
      </Grid>
    </Box>
  );
}

AuthWrapper.propTypes = { children: PropTypes.node };
