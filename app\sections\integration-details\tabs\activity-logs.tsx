import { Box, Button, Stack } from "@mui/material"
import LogTable from "sections/logs/api/data-table"
import { useLocation, useNavigate } from "@remix-run/react";
import { useGetIntegrationDetails } from "hooks/api/integration/useIntegration-details"
export const Logs = () => {
   const location = useLocation(),
      navigate = useNavigate();

   const { integration } = useGetIntegrationDetails();

   return (
      <Box>
         <LogTable
            integrationId={integration?.id}
         />
         <Stack direction="row" textAlign='center' margin='auto' justifyContent='center' alignItems='center'>
            <Button onClick={() => {
               navigate("/console/logs")
            }}>
               View All
            </Button>
         </Stack>

      </Box>
   )
}