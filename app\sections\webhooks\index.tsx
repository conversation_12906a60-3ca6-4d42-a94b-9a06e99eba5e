import { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { 
  Typo<PERSON>, 
  Button, 
  Box,
  Grid,
  Stack,
  IconButton,
  Menu,
  MenuItem,
  alpha,
  useTheme,
  TextField,
  InputAdornment,
  Paper,
  Divider,
  Chip,
  Link,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  useMediaQuery,
  Slide
} from '@mui/material';
import { 
  MoreVertical, 
  Webhook as WebhookIcon, 
  Plus, 
  Search,
  Copy,
  Eye,
  EyeOff,
  ExternalLink,
  Trash2,
  ArrowLeft,
  Edit2,
  Play,
  MoreHorizontal,
  SearchX
} from 'lucide-react';
import EditIcon from '@mui/icons-material/Edit';
import VisibilityIcon from '@mui/icons-material/Visibility';
import DeleteIcon from '@mui/icons-material/Delete';
import useUserDetails from 'store/user';
import WebhookConfigurationDrawer from 'components/webhooks/WebhookConfigurationDrawer';
import PageCard from 'components/cards/PageCard';
import LearnMoreLink from 'components/@extended/LearnMoreLink';
import { toast } from 'sonner';
import WebhookStatusChip from 'components/webhooks/WebhookStatusChip';
import WebhookDetailsPanel from 'components/webhooks/WebhookDetailsPanel';
import { useWebhooks } from 'hooks/api/webhooks/useWebhooks';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { webhookClient } from 'services/webhook.service';
import { API_ENDPOINTS } from 'utils/api/api-endpoints';
import useDebounce from 'hooks/use-debounce';
import { State } from 'hooks/useStatus';

const WebhooksPage = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'));
  const { user, subscriptions: subscriptionsData } = useUserDetails();
  const organizationId = user?.organization?.id;
  
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  const [selected, setSelected] = useState<any>(null);
  const [selectedWebhook, setSelectedWebhook] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const debouncedSearch = useDebounce(searchQuery, 300);
  const [showSecret, setShowSecret] = useState(false);
  const [showAllEvents, setShowAllEvents] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [webhookToDelete, setWebhookToDelete] = useState<any>(null);
  const [mobileDetailsOpen, setMobileDetailsOpen] = useState(false);
  const [showDetailsPanel, setShowDetailsPanel] = useState(false);
  const detailsPanelRef = useRef<HTMLDivElement>(null);
  
  // Server-backed search
  const { data: searchResp, isLoading: isSearchLoading } = useQuery({
    queryKey: [API_ENDPOINTS.WEBHOOKS, 'search', organizationId, debouncedSearch],
    enabled: !!organizationId,
    queryFn: async () => {
      // Base organization filter
      const orgFilter = {
        property: '/organization/id',
        operator: '=',
        values: [organizationId]
      };
       const statusFilter = {
      property: '/state',
      operator: '=',
      values: [State.ACTIVE]
    };
      let filter;

      if (debouncedSearch && debouncedSearch.trim().length > 0) {
        const q = debouncedSearch.trim();
        filter = {
          and: [
            orgFilter,
            statusFilter,
            {
              or: [
                { property: '/name', operator: 'LIKE', values: [q] },
                { property: '/description', operator: 'LIKE', values: [q] },
                { property: '/destination/url', operator: 'LIKE', values: [q] },
                { property: '/eventType', operator: 'LIKE', values: [q] }
              ]
            }
          ]
        };
      } else {
        filter = {
        and: [orgFilter, statusFilter]
      };
      }

      const payload = {
        filter,
        pagination: {
          limit: 50,
          offset: 0
        },
        sort: [
          {
            property: '/changeLog/lastUpdatedDateTime',
            direction: 'DESC'
          }
        ]
      };
      return await webhookClient.searchWebhooks(payload);
    },
    retry: (failureCount, error: any) => {
      // Don't retry on 400 errors (client errors)
      if (error?.response?.status === 400) {
        console.error('400 error - not retrying:', error?.response?.data);
        return false;
      }
      // Retry up to 2 times for other errors
      return failureCount < 2;
    },
    staleTime: 1000 * 60 * 5, 
    gcTime: 1000 * 60 * 10, 
  });

  const data = searchResp?.data ?? [];
  const queryClient = useQueryClient();
  
  // Check if we have any webhooks at all (for empty state)
  const hasAnyWebhooks = data.length > 0 || debouncedSearch.trim().length > 0;
  
  // Don't auto-select - wait for user click
  useEffect(() => {
    // Reset when data changes
    if (!data.find((w: any) => w.id === selectedWebhook?.id)) {
      setSelectedWebhook(null);
      setShowDetailsPanel(false);
    }
  }, [data]);

  // Filter on client as a fallback (server already filters by debouncedSearch)
  const filteredWebhooks = useMemo(() => {
    if (!debouncedSearch) return data;
    const searchLower = debouncedSearch.toLowerCase();
    return data.filter((webhook: any) => {
      const url = (
        webhook.destination?.webhookConfig?.url ||
        webhook.destination?.url ||
        webhook.data?.url ||
        ''
      ).toLowerCase();
      const events = (webhook.eventSubscriptions?.eventTypes || webhook.data?.events || []).join(' ').toLowerCase();
      const description = (webhook.description || '').toLowerCase();
      return (
        url.includes(searchLower) ||
        description.includes(searchLower) ||
        webhook.name.toLowerCase().includes(searchLower) ||
        events.includes(searchLower)
      );
    });
  }, [data, debouncedSearch]);

  const onOpen = (
    arg: boolean,
    mode: boolean = false,
    selected: Record<string, any> | null = null
  ) => {
    setIsOpen(arg);
    setIsEditMode(mode);
    mode && setSelected(selected);
  };

  const onClose = () => {
    setIsOpen(false);
    setSelected(null);
    setIsEditMode(false);
  };

  const handleCopy = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const handleDeleteClick = () => {
    setWebhookToDelete(selectedWebhook);
    setDeleteDialogOpen(true);
  };

  const handleDeleteWebhook = (webhook: any) => {
    setWebhookToDelete(webhook);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!webhookToDelete?.id) return;
    try {
      await webhookClient.deleteWebhook(webhookToDelete.id);
      toast.success('Webhook deleted successfully');
      setDeleteDialogOpen(false);
      setWebhookToDelete(null);
      await queryClient.invalidateQueries({ predicate: (q) => Array.isArray(q.queryKey) && q.queryKey[0] === API_ENDPOINTS.WEBHOOKS });
    } catch (err: any) {
      toast.error(err?.response?.data?.message || 'Failed to delete webhook');
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setWebhookToDelete(null);
  };

  const handleWebhookSelect = (webhook: any) => {
    setSelectedWebhook(webhook);
    if (isMobile) {
      setMobileDetailsOpen(true);
    } else {
      setShowDetailsPanel(true);
      // Scroll to top of details panel after a brief delay to ensure it's rendered
      setTimeout(() => {
        if (detailsPanelRef.current) {
          detailsPanelRef.current.scrollTop = 0;
        }
      }, 50);
    }
  };

  const handleMobileBack = () => {
    setMobileDetailsOpen(false);
  };

  const handleCloseDetails = () => {
    setShowDetailsPanel(false);
    // Don't clear selection to allow reopening
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    const now = new Date();
    
    const dateOptions: Intl.DateTimeFormatOptions = {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
    };
    
    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    };
    
    const formattedDate = new Intl.DateTimeFormat(navigator.language || 'en-US', dateOptions).format(date);
    const formattedTime = new Intl.DateTimeFormat(navigator.language || 'en-US', timeOptions).format(date);
    
    const isToday = date.toDateString() === now.toDateString();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isToday) {
      return `Today, ${formattedTime}`;
    } else if (isYesterday) {
      return `Yesterday, ${formattedTime}`;
    } else {
      return `${formattedDate}, ${formattedTime}`;
    }
  };

  const formatSecret = (secret: string) => {
    if (!secret) return '';
    if (showSecret) return secret;
    return `${secret.substring(0, 10)}${'•'.repeat(20)}`;
  };

  // Show loader while data is being fetched
  if (isSearchLoading) {
    return (
      <PageCard>
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          minHeight: 400,
          flexDirection: 'column',
          gap: 2
        }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              border: `3px solid ${theme.palette.divider}`,
              borderTopColor: theme.palette.primary.main,
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              }
            }}
          />
          <Typography variant="body2" color="text.secondary">
            Loading webhooks...
          </Typography>
        </Box>
      </PageCard>
    );
  }

  // Show empty state only when there are no webhooks at all AND no search query
  if (!hasAnyWebhooks && !debouncedSearch.trim()) {
    return (
      <>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <PageCard>
              <Box 
                sx={{ 
                  textAlign: 'center', 
                  py: { xs: 6, sm: 8, md: 10 },
                  px: { xs: 2, sm: 4 },
                  maxWidth: 600,
                  mx: 'auto'
                }}
              >
                <Box
                  sx={{
                    width: { xs: 60, sm: 80 },
                    height: { xs: 60, sm: 80 },
                    borderRadius: '50%',
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 3
                  }}
                >
                  <WebhookIcon 
                    size={40} 
                    style={{ 
                      color: theme.palette.primary.main 
                    }} 
                  />
                </Box>
                
                <Typography variant="h5" fontWeight={600} gutterBottom>
                  Configure your first webhook
                </Typography>
                
                <Typography 
                  variant="body1" 
                  color="text.secondary" 
                  sx={{ mb: 4, maxWidth: 450, mx: 'auto' }}
                >
                  Set up webhooks to receive real-time notifications about events in your integrations. Get instant updates when your users connect services or when data changes.
                </Typography>
                
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<Plus />}
                  onClick={() => onOpen(true, false, null)}
                  sx={{ 
                    px: 4,
                    textTransform: 'none',
                    fontWeight: 500
                  }}
                >
                  Add Webhook
                </Button>
                
                <Box sx={{ mt: 4 }}>
                  <Typography variant="body2" color="text.secondary">
                    Learn more about webhook events{' '}
                    <LearnMoreLink
                      href="https://docs.unizo.ai/docs/unizo-console/webhooks"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      View documentation
                    </LearnMoreLink>
                  </Typography>
                </Box>
              </Box>
            </PageCard>
          </Grid>
        </Grid>

        <WebhookConfigurationDrawer
          open={isOpen}
          onClose={onClose}
          onSave={async (payload) => {
            try {
              await webhookClient.createWebhook(payload as any);
              toast.success('Webhook created successfully');
              onClose();
              await queryClient.invalidateQueries({ predicate: (q) => Array.isArray(q.queryKey) && q.queryKey[0] === API_ENDPOINTS.WEBHOOKS });
            } catch (err: any) {
              toast.error(err?.response?.data?.message || 'Failed to create webhook');
              throw err;
            }
          }}
          webhook={selected}
          isEditMode={isEditMode}
          userSubscriptions={subscriptionsData?.map(sub => sub.product?.code || sub.type) || []}
        />
      </>
    );
  }

  // Normal view when webhooks exist or there's a search query
  return (
    <>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box>
            {/* Search bar and Add button */}
            <Box sx={{ 
              display: 'flex', 
              gap: 2, 
              alignItems: 'center', 
              mb: 3,
              flexDirection: { xs: 'column', sm: 'row' },
              width: '100%'
            }}>
              <TextField
                placeholder="Search webhooks..."
                size={isMobile ? "small" : "medium"}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                sx={{ 
                  flex: 1,
                  width: { xs: '100%', sm: 'auto' },
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: theme.palette.background.paper,
                    '&:hover': {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      }
                    },
                    '&.Mui-focused': {
                      '& .MuiOutlinedInput-notchedOutline': {
                        borderColor: theme.palette.primary.main,
                      }
                    }
                  }
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search size={20} style={{ color: theme.palette.text.secondary }} />
                    </InputAdornment>
                  ),
                }}
              />
              <Button
                variant="contained"
                onClick={() => onOpen(true, false, null)}
                size={isMobile ? "small" : "medium"}
                sx={{
                  textTransform: 'none',
                  fontWeight: 500,
                  px: isMobile ? 2 : 3,
                  whiteSpace: 'nowrap',
                  minWidth: { xs: '100%', sm: 'auto' }
                }}
              >
                Add Webhook
              </Button>
            </Box>

            {/* Two column layout for desktop, single column for mobile */}
            <Paper sx={{ 
              height: isMobile ? 'auto' : 'calc(100vh - 300px)',
              minHeight: isMobile ? 400 : 500,
              display: 'flex',
              overflow: 'hidden',
              flexDirection: isMobile ? 'column' : 'row',
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 0
            }}>
              {/* Left Column - Webhook List */}
              <Box sx={{ 
                flex: isMobile ? '1 1 auto' : showDetailsPanel ? '0 0 60%' : '1 1 auto',
                borderRight: isMobile || !showDetailsPanel ? 'none' : `1px solid ${theme.palette.divider}`,
                borderBottom: isMobile ? `1px solid ${theme.palette.divider}` : 'none',
                overflow: 'auto',
                display: isMobile && mobileDetailsOpen ? 'none' : 'block',
                transition: 'flex 0.3s ease',
                // Hide scrollbar for all browsers
                scrollbarWidth: 'none', // Firefox
                msOverflowStyle: 'none', // IE and Edge
                '&::-webkit-scrollbar': {
                  display: 'none', // Chrome, Safari, Opera
                },
              }}>
                {/* Table Header */}
                <Box sx={{ 
                  display: 'grid',
                  gridTemplateColumns: isMobile ? '1fr 80px 80px' : isTablet ? '1fr 1fr 80px 80px 120px' : showDetailsPanel ? '1fr 80px 80px 120px 120px' : '1fr 1fr 80px 80px 120px 120px 16px 120px',
                  gap: isMobile ? 1 : 2,
                  px: isMobile ? 2 : 3,
                  py: 2,
                  borderBottom: `1px solid ${theme.palette.divider}`,
                  backgroundColor: theme.palette.background.default,
                  position: 'sticky',
                  top: 0,
                  zIndex: 1
                }}>
                  <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary', fontSize: isMobile ? '0.7rem' : '0.75rem' }}>NAME</Typography>
                  {!isMobile && !showDetailsPanel && (
                    <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary', fontSize: isMobile ? '0.7rem' : '0.75rem' }}>ENDPOINT</Typography>
                  )}
                  <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary', textAlign: 'center', fontSize: isMobile ? '0.7rem' : '0.75rem' }}>EVENTS</Typography>
                  <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary', textAlign: 'center', fontSize: isMobile ? '0.7rem' : '0.75rem' }}>STATUS</Typography>
                  {!isMobile && (
                    <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary', textAlign: 'center', fontSize: isTablet ? '0.7rem' : '0.75rem' }}>CREATED</Typography>
                  )}
                  {!isMobile && !isTablet && (
                    <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary', textAlign: 'center', fontSize: '0.75rem' }}>MODIFIED</Typography>
                  )}
                  {!isMobile && !isTablet && !showDetailsPanel && (
                    <Box />
                  )}
                  {!isMobile && !isTablet && !showDetailsPanel && (
                    <Typography variant="caption" sx={{ fontWeight: 600, color: 'text.secondary', textAlign: 'center', fontSize: '0.75rem' }}>ACTIONS</Typography>
                  )}
                </Box>

                {/* Table Body - Show "No results" message if search returns empty */}
                {filteredWebhooks.length === 0 ? (
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    py: 8,
                    px: 3,
                    textAlign: 'center'
                  }}>
                    <Box
                      sx={{
                        width: 64,
                        height: 64,
                        borderRadius: '50%',
                        backgroundColor: alpha(theme.palette.text.secondary, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mb: 3
                      }}
                    >
                      <SearchX 
                        size={32} 
                        style={{ color: theme.palette.text.secondary }} 
                      />
                    </Box>
                    <Typography variant="h6" sx={{ mb: 1, fontWeight: 500 }}>
                      No webhooks found
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      {debouncedSearch.trim() 
                        ? `No webhooks match "${debouncedSearch}"`
                        : "No webhooks have been configured yet"
                      }
                    </Typography>
                    {debouncedSearch.trim() ? (
                      <Button
                        variant="outlined"
                        onClick={() => setSearchQuery('')}
                        sx={{ textTransform: 'none' }}
                      >
                        Clear search
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        startIcon={<Plus />}
                        onClick={() => onOpen(true, false, null)}
                        sx={{ textTransform: 'none' }}
                      >
                        Add Webhook
                      </Button>
                    )}
                  </Box>
                ) : (
                  // Show webhook rows when there are results
                  filteredWebhooks.map((webhook: any, index: number) => {
                    const isSelected = selectedWebhook?.id === webhook.id;
                    const showActions = !isMobile && !isTablet && !showDetailsPanel;
                    
                    return (
                      <Box
                        key={webhook.id || `webhook-${index}`}
                        onClick={(e) => {
                          // Prevent row click if clicking on action buttons
                          const target = e.target as HTMLElement;
                          if (target.closest('.webhook-actions') || target.closest('button')) {
                            return;
                          }
                          handleWebhookSelect(webhook);
                        }}
                        sx={{
                          display: 'grid',
                          gridTemplateColumns: isMobile ? '1fr 80px 80px' : isTablet ? '1fr 1fr 80px 80px 120px' : showDetailsPanel ? '1fr 80px 80px 120px 120px' : '1fr 1fr 80px 80px 120px 120px 16px 120px',
                          gap: isMobile ? 1 : 2,
                          px: isMobile ? 2 : 3,
                          py: isMobile ? 1.5 : 2,
                          cursor: 'pointer',
                          backgroundColor: isSelected ? alpha(theme.palette.primary.main, 0.08) : 'transparent',
                          '&:hover': {
                            backgroundColor: isSelected 
                              ? alpha(theme.palette.primary.main, 0.12) 
                              : alpha(theme.palette.action.hover, 0.04)
                          },
                          borderBottom: `1px solid ${theme.palette.divider}`,
                          transition: 'background-color 0.2s',
                          position: 'relative'
                        }}
                      >
                        {/* Name and Description Column */}
                        <Box>
                          <Typography variant="body2" sx={{ 
                            fontWeight: 600,
                            fontSize: isMobile ? '0.75rem' : '0.875rem',
                            mb: 0.25,
                            color: 'text.primary'
                          }}>
                            {webhook.name || 'Unnamed Webhook'}
                          </Typography>
                          <Typography variant="caption" sx={{ 
                            color: webhook.description ? 'text.secondary' : 'text.disabled',
                            fontStyle: webhook.description ? 'normal' : 'italic',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            lineHeight: 1.4,
                            fontSize: isMobile ? '0.65rem' : '0.75rem'
                          }}>
                            {webhook.description || 'No description'}
                          </Typography>
                        </Box>
                        
                        {/* Endpoint Column - Hidden on mobile and when details panel is shown */}
                        {!isMobile && !showDetailsPanel && (
                          <Tooltip title={webhook.destination?.webhookConfig?.url || webhook.destination?.url || webhook.data?.url} placement="top">
                            <Typography variant="body2" sx={{ 
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                              fontFamily: 'monospace',
                              fontSize: '0.813rem',
                              color: 'text.secondary'
                            }}>
                              {webhook.destination?.webhookConfig?.url || webhook.destination?.url || webhook.data?.url}
                            </Typography>
                          </Tooltip>
                        )}
                        <Typography variant="body2" sx={{ textAlign: 'center', fontSize: isMobile ? '0.75rem' : '0.875rem' }}>
                          {(webhook.eventSubscriptions?.eventTypes || webhook.data?.events || []).length}
                        </Typography>
                        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                          <WebhookStatusChip status={webhook.operation?.status || webhook.state} size="small" />
                        </Box>
                        {!isMobile && (
                          <Typography variant="caption" sx={{ textAlign: 'center', color: 'text.secondary', fontSize: isTablet ? '0.7rem' : '0.75rem' }}>
                            {formatDate(webhook.changeLog?.createdDateTime)}
                          </Typography>
                        )}
                        {!isMobile && !isTablet && (
                          <Typography variant="caption" sx={{ textAlign: 'center', color: 'text.secondary', fontSize: '0.75rem' }}>
                            {formatDate(webhook.changeLog?.lastUpdatedDateTime || webhook.changeLog?.updatedDateTime || webhook.changeLog?.createdDateTime)}
                          </Typography>
                        )}
                        {showActions && <Box />}
                        {showActions ? (
                          <Box 
                            className="webhook-actions"
                            sx={{ 
                              display: 'flex', 
                              justifyContent: 'center', 
                              alignItems: 'center',
                              gap: 0.5
                            }}
                          >
                            <Tooltip title="View details" arrow>
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  handleWebhookSelect(webhook);
                                  setShowDetailsPanel(true);
                                }}
                                sx={{
                                  color: theme.palette.text.secondary,
                                  padding: { xs: '4px', sm: '8px' },
                                  '&:hover': {
                                    backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                    color: theme.palette.primary.main
                                  }
                                }}
                              >
                                <VisibilityIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Edit" arrow>
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  try {
                                    setSelected(webhook);
                                    setIsEditMode(true);
                                    setIsOpen(true);
                                  } catch (error) {
                                    console.error('Error opening edit dialog:', error);
                                    toast.error('Failed to open edit dialog');
                                  }
                                }}
                                sx={{
                                  color: theme.palette.text.secondary,
                                  padding: { xs: '4px', sm: '8px' },
                                  '&:hover': {
                                    backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                    color: theme.palette.primary.main
                                  }
                                }}
                              >
                                <EditIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete" arrow>
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  e.preventDefault();
                                  try {
                                    handleDeleteWebhook(webhook);
                                  } catch (error) {
                                    console.error('Error initiating delete:', error);
                                    toast.error('Failed to delete webhook');
                                  }
                                }}
                                sx={{
                                  color: theme.palette.text.secondary,
                                  padding: { xs: '4px', sm: '8px' },
                                  '&:hover': {
                                    backgroundColor: alpha(theme.palette.error.main, 0.08),
                                    color: theme.palette.error.main
                                  }
                                }}
                              >
                                <DeleteIcon sx={{ fontSize: { xs: 18, sm: 20 } }} />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        ) : null}
                      </Box>
                    );
                  })
                )}
              </Box>

              {/* Right Column - Webhook Details (Desktop) */}
              {!isMobile && showDetailsPanel && selectedWebhook && (
                <Box 
                  ref={detailsPanelRef}
                  sx={{ 
                    flex: '0 0 40%',
                    overflow: 'auto',
                    backgroundColor: alpha(theme.palette.background.default, 0.5),
                    borderLeft: `1px solid ${theme.palette.divider}`,
                    animation: 'slideIn 0.3s ease',
                    '@keyframes slideIn': {
                      from: { transform: 'translateX(100%)' },
                      to: { transform: 'translateX(0)' }
                    },
                    // Hide scrollbar until needed
                    scrollbarWidth: 'thin', // Firefox
                    scrollbarColor: `${alpha(theme.palette.text.primary, 0.3)} transparent`, // Firefox
                    '&::-webkit-scrollbar': {
                      width: '6px',
                      height: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                      background: 'transparent',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      background: alpha(theme.palette.text.primary, 0.2),
                      borderRadius: '3px',
                      transition: 'background 0.2s',
                    },
                    '&::-webkit-scrollbar-thumb:hover': {
                      background: alpha(theme.palette.text.primary, 0.3),
                    },
                    // Hide scrollbar by default, show on hover
                    '&:not(:hover)': {
                      scrollbarWidth: 'none', // Firefox
                      '&::-webkit-scrollbar': {
                        display: 'none', // Chrome, Safari, Edge
                      },
                    },
                    // IE 10+
                    '-ms-overflow-style': '-ms-autohiding-scrollbar',
                  }}>
                  <WebhookDetailsPanel
                    webhook={selectedWebhook}
                    showSecret={showSecret}
                    showAllEvents={showAllEvents}
                    onToggleSecret={() => setShowSecret(!showSecret)}
                    onToggleEvents={() => setShowAllEvents(!showAllEvents)}
                    onEdit={() => {
                      onOpen(true, true, selectedWebhook);
                    }}
                    onEditEvents={() => {
                      onOpen(true, true, selectedWebhook);
                    }}
                    onDelete={handleDeleteClick}
                    onClose={handleCloseDetails}
                    showCloseButton={true}
                  />
                </Box>
              )}
            </Paper>

            {/* Mobile Details Slide */}
            {isMobile && (
              <Slide direction="left" in={mobileDetailsOpen} mountOnEnter unmountOnExit>
                <Paper
                  sx={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 1300,
                    overflow: 'auto',
                    backgroundColor: theme.palette.background.paper
                  }}
                >
                  {/* Mobile Header */}
                  <Box
                    sx={{
                      position: 'sticky',
                      top: 0,
                      backgroundColor: theme.palette.background.paper,
                      borderBottom: `1px solid ${theme.palette.divider}`,
                      p: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 2,
                      zIndex: 1
                    }}
                  >
                    <IconButton onClick={handleMobileBack} edge="start">
                      <ArrowLeft />
                    </IconButton>
                    <Typography variant="h6" sx={{ flex: 1, fontWeight: 600 }}>
                      {selectedWebhook?.name || 'Webhook Details'}
                    </Typography>
                  </Box>

                  {/* Mobile Details Content */}
                  {selectedWebhook && (
                    <WebhookDetailsPanel
                      webhook={selectedWebhook}
                      showSecret={showSecret}
                      showAllEvents={showAllEvents}
                      onToggleSecret={() => setShowSecret(!showSecret)}
                      onToggleEvents={() => setShowAllEvents(!showAllEvents)}
                      onEdit={() => {
                        onOpen(true, true, selectedWebhook);
                      }}
                      onEditEvents={() => {
                        onOpen(true, true, selectedWebhook);
                      }}
                      onDelete={handleDeleteClick}
                    />
                  )}
                </Paper>
              </Slide>
            )}
          </Box>
        </Grid>
      </Grid>

      <WebhookConfigurationDrawer
        open={isOpen}
        onClose={onClose}
        onSave={async (payload) => {
          try {
            await webhookClient.createWebhook(payload as any);
            onClose();
            await queryClient.invalidateQueries({ predicate: (q) => Array.isArray(q.queryKey) && q.queryKey[0] === API_ENDPOINTS.WEBHOOKS });
          } catch (err: any) {
            toast.error(err?.response?.data?.message || 'Failed to create webhook');
            throw err;
          }
        }}
        webhook={selected}
        isEditMode={isEditMode}
        userSubscriptions={subscriptionsData?.map(sub => sub.product?.code || sub.type) || []}
      />
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Delete webhook endpoint</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this webhook endpoint? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} sx={{ textTransform: 'none' }}>
            Cancel
          </Button>
          <Button 
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            sx={{ textTransform: 'none' }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default WebhooksPage;