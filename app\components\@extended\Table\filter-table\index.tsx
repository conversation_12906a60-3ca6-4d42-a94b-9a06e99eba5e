// material-ui
import Paper from '@mui/material/Paper';
import TablePrimitive from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { Box, Button, Divider, Grid, GridProps, Stack,useTheme } from '@mui/material';

// third-party
import {
   flexRender,
   useReactTable,
   getCoreRowModel,
   getPaginationRowModel,
   getFilteredRowModel
} from '@tanstack/react-table';

import SimpleBarScroll from "components/third-party/SimpleBar"
import { Filter, TablePagination } from 'components/third-party/react-table';

// project import
import ScrollX from 'components/ScrollX';
import MainCard from 'components/MainCard';

import { extractColumnMeta, extractHeaderColumnMeta, getPaginationTotal } from '../table.helper'
import { TableProps } from '../type.js';
import Empty from '../Empty';
import { useState, useMemo } from 'react';
// import { apiLogFilterFields } from 'components/filters/ProgressivefilterOptions';
import { ProgressiveFilterBar } from 'components/filters/ProgressiveFilterBar';
import { FilterField } from 'components/filters/type';

const FILTER_GRID_VALUE: GridProps = {
   xs: 12,
   md: 6,
   lg: 4,
   xl: 3,
}
const apiLogFilterFields: FilterField[] = [
  { 
    key: 'state', 
    label: 'Status', 
    type: 'enum', 
    options: ['SUCCESS', 'FAILED'], 
    useOnce: true
  },
    { 
    key: 'integration', 
    label: 'Integration Name', 
    type: 'text',
    validation: {
      minLength: 2,
      maxLength: 50,
    }
  }, 
  { 
    key: 'date', 
    label: 'Date', 
    type: 'date',
    useOnce: true,
    validation: {
      required: true
    }
  },  { 
    key: 'category', 
    label: 'Category', 
    type: 'enum', 
    options: ['Source Code', 'Ticketing', 'Package & Container Registry', 'Communication', 'Incident', 'Vulnerability Management', 'Compliance', 'Monitoring','Key Management','Identity'], 
    multiSelect: true,
    validation: {
      custom: (value) => {
        if (Array.isArray(value) && value.length > 5) {
          return 'Please select at most 5 methods';
        }
        return null;
      }
    }
  },
];
export default function FilterTable<TData>(props: TableProps<TData>) {
   const [selectedRowId, setSelectedRowId] = useState<string | null>(null);
   const {palette} =useTheme()
   const [filters, setFilters] = useState<any[]>([]);
   const [searchValue, setSearchValue] = useState('');

   const {
      striped = false,
      data = [],
      columns = [],
      title = null,

      // action-bar
      actionBar = null,

      // filter
      onReset,
      onReload = () => { },

      // pagination
      totalData = 0,
      paginationPosition = 'bottom',
      manualPagination = true,
      disablePagination = false,

      // row-props
      rowClickable = false,
      onRowClick,
      ...rest
   } = props;

   const table = useReactTable({
      data,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      getFilteredRowModel: getFilteredRowModel(),

      manualPagination,
      pageCount: getPaginationTotal(totalData, rest?.state?.pagination?.pageSize),
      ...rest,
   });

   const resetFilter = () => {
      typeof onReset === 'function' && (
         onReset()
      )
      table.setColumnFilters([])
   }

   return (
      <MainCard
         content={false}
         title={title}
      >
         {/* <ProgressiveFilterBar
            fields={apiLogFilterFields}
            filters={filters}
            onFiltersChange={setFilters}
            searchValue={searchValue}
            onSearchChange={setSearchValue}
            resultCount={data.length}
            placeholder="Search API logs..."
         /> */}
         {/* filters */}
         {/* <Stack direction={'row'} sx={{ m: 2 }}>
            {table.getHeaderGroups().map((headerGroup) => (
               <Grid spacing={2} container key={headerGroup.id}>
                  {headerGroup.headers.map((header: any) => {
                     const { filterType = null } = header?.column?.columnDef?.meta?.filter ?? {};

                     if (filterType) {
                        return (
                           header.column.getCanFilter() && (
                              <Grid item {...FILTER_GRID_VALUE} key={header.id}>
                                 <Filter column={header.column} table={table} />
                              </Grid>
                           )
                        )
                     }
                  })}
               </Grid>
            ))}
         </Stack> */}
         {/* filters */}
         {/* <Stack direction={'row'} justifyContent={'flex-end'} gap={2} sx={{ m: 2 }}>
            <IconButton
               onClick={onReload}
            >
               <RefreshIcon />
            </IconButton>
            <Button onClick={resetFilter} variant='contained' size='small' >Reset</Button>
         </Stack> */}
         {/* scroll container */}
         < ScrollX >
            <TableContainer component={Paper}>
               <SimpleBarScroll
                  sx={{
                     '& .simplebar-content':
                        { display: 'flex', flexDirection: 'column' },
                  }}
               >
                  <TablePrimitive>
                     <TableHead>
                        {table.getHeaderGroups().map((headerGroup) => (
                           <TableRow key={headerGroup.id}>
                              {headerGroup?.headers.map((header) => (
                                 <TableCell
                                    key={header.id}
                                    {...extractHeaderColumnMeta(header)}
                                 >
                                    {header?.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                                 </TableCell>
                              ))}
                           </TableRow>
                        ))}
                     </TableHead>
                     <TableBody {...(striped && { className: 'striped' })}>
                        {data?.length ? (
                           table.getRowModel().rows.map((row) => (
                              <TableRow
                                 onClick={() => {
                                    setSelectedRowId(row.id); // Set the clicked row ID
                                    onRowClick && onRowClick(row);
                                 }}
                                 className={`${rowClickable ? 'cursor-pointer' : ''}`}
                                 key={row.id}
                                 sx={{
                                    border: selectedRowId === row.id ? `2px solid ${palette.grey[400]}` : 'none',
                                 }}
                              >
                                 {row.getVisibleCells().map((cell: any) => {
                                    return (
                                       <TableCell
                                          key={cell.id}
                                          {...extractColumnMeta(
                                             cell,
                                          )}
                                       >
                                          {flexRender(cell?.column?.columnDef?.cell, cell?.getContext())}
                                       </TableCell>
                                    )
                                 })}
                              </TableRow>
                           ))
                        ) : (
                           <Empty colSpan={columns?.length} />
                        )}
                     </TableBody>

                  </TablePrimitive>
               </SimpleBarScroll>
            </TableContainer>

            {/* pagination block */}
            {!disablePagination ? (
               paginationPosition !== 'top' && (
                  <>
                     <Divider />
                     <Box sx={{ p: 2 }}>
                        <TablePagination
                           {...{
                              setPageSize: table.setPageSize,
                              setPageIndex: table.setPageIndex,
                              getState: table.getState,
                              getPageCount: table.getPageCount
                           }}
                        />
                     </Box>
                  </>
               )
            ) : null}

         </ScrollX>
         {/* scroll container */}
      </MainCard>
   )
}