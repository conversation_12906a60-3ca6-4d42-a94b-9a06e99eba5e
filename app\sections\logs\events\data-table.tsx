import { useEffect, useMemo, useState } from "react";
import { Chip, Paper, Tooltip } from "@mui/material";
import { ColumnDef, ColumnFiltersState } from "@tanstack/react-table";
import _ from "lodash";
import { TableSkeleton } from "components/skeletons";

import FilterTable from "components/@extended/Table/filter-table";

import { useGetLog } from "hooks/api/log/useGetLog";
import { useTable } from "hooks/table/useTable";
import { useDate } from "hooks/useDate";
import { useServiceProfile } from "hooks/useServiceProfile";
import { State, useStatus } from "hooks/useStatus";
import { EventLogsActivity } from "./event-logs-activity";
import useUserDetails from "store/user";
import { FormItemType } from "constants/form";
import useLogFilter from "../useFilter";
import { maxDate, minDate } from "utils/date-field";
import { string } from "prop-types";
import { ProgressiveFilterBar } from "components/filters/ProgressiveFilterBar";
import eventConfiguration from "../constant";

const COLUMN_ID = {
  type: "serviceProfile.type",
  provider: "target.accessPoint.serviceProfile.name",
};

const LogTable = ({ integrationId }: any) => {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [filters, setFilters] = useState<any[]>([]);
  const [searchValue, setSearchValue] = useState("");

  // First, get the raw selected provider value without processing
  const rawSelectedProvider = useMemo(() => {
    const progressiveFilter = filters?.find((f) => f.key === "serviceProfile/type");
    const columnFilter = columnFilters?.find((f) => f.id === "serviceProfile_type");

    return progressiveFilter?.value || columnFilter?.value;
  }, [columnFilters, filters]);

  // Get the options first
  const { providerOptions, categoriesOptions } = useLogFilter({
    selectedProvider: rawSelectedProvider, // Pass raw value first
    columnFilters,
  });


  const matchedEvents = useMemo(() => {
    const typeFilter = filters.find(f => f.key === 'serviceProfile/type');

    if (!typeFilter || !Array.isArray(typeFilter.value)) return [];

    return typeFilter.value.flatMap(type => eventConfiguration[type as keyof typeof eventConfiguration] || []);
  }, [filters]);

  const { resolveStatusColor, resolveStatus } = useStatus(),
    { loadImage } = useServiceProfile(),
    { loadDate } = useDate();

  const columns: any = useMemo(
    () => [
      {
        accessorKey: "operation.type", //access nested data with dot notation
        header: "Status",
        cell({
          row: {
            original: { operation = {} },
          },
        }: any) {
          return (
            <Chip
              size="small"
              variant="outlined"
              color={
                resolveStatusColor(operation?.type?.toUpperCase()) ?? "default"
              }
              label={`${operation?.statusCode} ${operation?.statusMessage}`}
            />
          );
        },
        meta: {
          filter: {
            filterType: FormItemType.Select,
            options: [
              {
                label: resolveStatus(State.SUCCESS),
                value: State.SUCCESS,
              },
              {
                label: resolveStatus(State.FAILED),
                value: State.FAILED,
              },
            ],
          },
        },
      },
      {
        accessorKey: "integration.name", //access nested data with dot notation
        header: "Integration Name",
        grow: 1,
        meta: {
          filter: {
            filterType: FormItemType.Text,
          },
        },
      },
      {
        accessorKey: "name", //access nested data with dot notation
        header: "Event",
        grow: 1,
        meta: {
          filter: {
            filterType: FormItemType.Text,
          },
        },
      },
      {
        accessorKey: "endUser.subOrganization.name", //access nested data with dot notation
        header: "Organization",
        grow: 1,
      },
      {
        accessorKey: COLUMN_ID.type, //access nested data with dot notation
        header: "Category",
        meta: {
          filter: {
            filterType: FormItemType.Select,
            options: categoriesOptions,
          },
        },
      },
      {
        accessorKey: "serviceProfile.id", //access nested data with dot notation
        header: "Target",
        cell({ row: { original } }: any) {
          return loadImage(original?.serviceProfile);
        },
        meta: {
          filter: {
            filterType: FormItemType.Select,
            options: providerOptions,
          },
        },
      },
      {
        accessorKey: "changeLog.startDateTime",
        header: "Start Date",
        cell: ({ row: { original } }: any) => {
          const changeLog = original?.changeLog;
          return loadDate(changeLog?.endDateTime ?? changeLog?.startDateTime);
        },
        meta: {
          filter: {
            filterType: FormItemType.DateTime,
            options: providerOptions,
            maxDateTime: maxDate({ filters: columnFilters }),
          },
          hidden: true,
        },
      },
      {
        accessorKey: "changeLog.endDateTime", //access nested data with dot notation
        header: "Date",
        cell({ row: { original } }: any) {
          return loadDate(original?.changeLog?.endDateTime);
        },
        meta: {
          filter: {
            filterType: FormItemType.DateTime,
            options: providerOptions,
            minDateTime: minDate({ filters: columnFilters }),
            label: "End Date",
          },
        },
      },
    ],
    [categoriesOptions, providerOptions, columnFilters]
  );

  const [paginationState, setPaginationState] = useState<any>({
    pageIndex: 0,
    pageSize: 10,
    total: 0,
  });

  const { searchEvent } = useGetLog({}),
    { user }: any = useUserDetails();

  const [isOpen, setIsOpen] = useState<boolean>(false),
    [selected, setSelected] = useState<any>();

  const onOpen = (selected: any) => {
      setSelected(selected);
      setIsOpen(true);
    },
    onClose = () => {
      setSelected(null);
      setIsOpen(false);
    };

  const { data: resp, refetch, isLoading } = searchEvent({
    orgId: user?.organization?.id,
    ...paginationState,
    filter: columnFilters,
  });

  const onColumnFiltersChange: any = (filters: ColumnFiltersState) => {
    setColumnFilters(filters.filter((f) => f.key !== "entityType")); // Update the state if necessary
  };
  
  const selectedEventType: string[] = useMemo(() => {
    return filters?.find((i) => i.key === "entityType")?.value as string[] || [];
  }, [filters]);

  const valuesFromEvent: string[] = useMemo(() => {
    return selectedEventType.flatMap((type) => {
      const matched = matchedEvents.find((event : any) => event.name === type);
      return matched ? [matched.value] : [];
    });
  }, [selectedEventType, matchedEvents]);

  const data = valuesFromEvent.length ?  resp?.data?.data.filter((item: any) => valuesFromEvent.includes(item.name)) : resp?.data?.data,
    pagResp = resp?.data?.pagination;

  useEffect(() => {
    setPaginationState((prev: Record<string, any>) => ({
      ...prev,
      total: pagResp?.total ?? 0,
    }));
  }, [pagResp]);

  useEffect(() => {
    const mapped = filters.filter((f) => f.key !== "entityType").map((f) => {
      let value = f.value;


      // For serviceProfile/type (Category), map labels to values
      if (f.key === "serviceProfile/type") {
        const matched = categoriesOptions.filter((opt) =>
          Array.isArray(value) ? value.includes(opt.label) : opt.label === value
        );

        if (matched?.length) {
          value = matched.map((i) => i.value);
        }
      }

      // For serviceProfile/name (Target), map labels to values
      if (f.key === "serviceProfile/id") {
        const matched = providerOptions.filter((opt) =>
          Array.isArray(value) ? value.includes(opt.label) : opt.label === value
        );

        if (matched?.length) {
          value = matched.map((i) => i.value);

        }
      }


      return {
        id: f.key,
        value: value,
      };
    });
    setColumnFilters(mapped);
  }, [filters, categoriesOptions, providerOptions]);

  if (isLoading) {
    return <TableSkeleton rows={10} columns={6} title="Webhook Events" />;
  }

  return (
    <>
      <Paper elevation={2} sx={{ p: 3, mb: 3, borderRadius: 2 }}>
        <ProgressiveFilterBar
          fields={[
            {
              key: "operation/type",
              label: "Status",
              type: "enum",
              options: [State.SUCCESS, State.FAILED],
              useOnce: true,
            },
            {
              key: "entityType",
              label: "Event",
              type: "enum",
              options: matchedEvents.map((event :any) => event.name),
              multiSelect: true,
              useOnce: true,
            },
            {
              key: "integration/name",
              label: "Integration Name",
              type: "text",
              useOnce: true,
            },
            {
              key: "serviceProfile/type",
              label: "Category",
              type: "enum",
              options: categoriesOptions.map((opt) => opt.label),
              multiSelect: true,
              useOnce: true,
            },
            {
              key: "serviceProfile/id",
              label: "Target",
              type: "enum",
              options: providerOptions.map((opt) => opt.label),
              useOnce: true,
            },
            {
              key: "changeLog/startDateTime",
              label: "Start Date",
              type: "date",
              useOnce: true,
            },
            {
              key: "changeLog/endDateTime",
              label: "End Date",
              type: "date",
              useOnce: true,
            },
          ]}
          filters={filters}
          onFiltersChange={setFilters}
          searchValue={searchValue}
          onSearchChange={setSearchValue}
          resultCount={data?.length || 0}
          placeholder="Search Webhook logs..."
        />
        <FilterTable
          data={data}
          columns={columns}
          totalData={paginationState?.total}
          {...{
            onPaginationChange: setPaginationState,
            state: {
              pagination: {
                pageIndex: paginationState?.pageIndex,
                pageSize: paginationState?.pageSize,
              },
              columnFilters,
            } as any,
            onRowClick({ original }) {
              onOpen(original);
            },
            rowClickable: true,
            manualFiltering: true,
            onColumnFiltersChange: onColumnFiltersChange,
            onReload() {
              refetch();
            },
          }}
        />
      </Paper>
      <EventLogsActivity
        open={isOpen}
        selected={selected as any}
        onClose={onClose}
      />
    </>
  );
};

export default LogTable;