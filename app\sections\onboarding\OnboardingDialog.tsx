import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  Icon<PERSON>utton,
  Typo<PERSON>,
  Box,
  Stepper,
  Step,
  StepL<PERSON>l,
  Button,
  Stack,
  useTheme,
  alpha,
  Divider,
} from '@mui/material';
import { X, ChevronLeft, ChevronRight, Check } from 'lucide-react';
import confetti from 'canvas-confetti';
import useOnboardingStore from 'store/onboarding';
import CategorySelection from './steps/CategorySelection';
import ConnectorSelection from './steps/ConnectorSelection';
import WebhookConfiguration from './steps/WebhookConfiguration';
import TeamInvitation from './steps/TeamInvitation';
import { useMutation } from '@tanstack/react-query';
import { tenantsClient } from 'services/tenants.service';
import { serviceProfileClient } from 'services/service-profile.service';
import { organizationClient } from 'services/organization.service';
import { userClient } from 'services/user.service';
import { toast } from 'sonner';
import { parseError } from 'lib/utils';
import { useNavigate } from '@remix-run/react';
import { useServiceProfile } from 'hooks/useServiceProfile';
import useUserDetails from 'store/user';

const stepComponents: Record<number, React.ComponentType> = {
  0: CategorySelection,
  1: ConnectorSelection,
  2: WebhookConfiguration,
  3: TeamInvitation,
};

enum OnboardingType {
  SELF_SIGNUP = 'SELF_SIGNUP'
}

enum ProviderType {
  SERVICE_PROFILE = 'SERVICE_PROFILE'
}

export default function OnboardingDialog() {
  const theme = useTheme();
  const navigate = useNavigate();
  const [showExitConfirmation, setShowExitConfirmation] = React.useState(false);
  const { getAllDomains } = useServiceProfile();
  const userDetails = useUserDetails();
  
  const {
    isOpen,
    currentStep,
    steps,
    closeOnboarding,
    nextStep,
    previousStep,
    skipStep,
    calculateProgress,
    selectedCategories,
    selectedServices,
    webhookConfigs,
    invitedMembers,
    resetOnboarding,
  } = useOnboardingStore();

  const StepComponent = stepComponents[currentStep] || null;
  const currentStepData = steps[currentStep];
  const progress = calculateProgress();

  // Create/update integrations mutation - MOCKED
  const { mutate: createIntegrations, isPending: isCreating } = useMutation({
    mutationFn: async () => {
      // MOCK: Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // MOCK: Return success
      return { success: true };
    },
    onSuccess: async () => {
      toast.success('Integrations setup completed successfully!');
      
      // Wait for 1 second before closing modal
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      resetOnboarding();
      closeOnboarding();
      // Optionally navigate to integrations page
      // navigate('/console/integrations');
    },
    onError: (error: any) => {
      const errorMessage = parseError(error?.response?.data)?.message || 'Failed to setup integrations';
      toast.error(errorMessage);
    },
  });

  const handleClose = () => {
    // Show confirmation dialog if user has made progress
    if (currentStep > 0 || selectedCategories.length > 0 || selectedServices.length > 0) {
      setShowExitConfirmation(true);
    } else {
      closeOnboarding();
    }
  };

  const handleConfirmExit = () => {
    resetOnboarding();
    closeOnboarding();
    setShowExitConfirmation(false);
  };

  const handleCancelExit = () => {
    setShowExitConfirmation(false);
  };

  const isAllStepsCompleted = () => {
    // Check if all required steps are completed
    const hasCategories = selectedCategories.length > 0;
    const hasServices = selectedServices.length > 0;
    const hasPlatformWebhook = webhookConfigs.some(
      wc => wc.category === 'PLATFORM' && wc.url && wc.isValid
    );
    
    // Team invitation is optional, so we don't check it
    return hasCategories && hasServices && hasPlatformWebhook;
  };

  const handleComplete = (event: React.MouseEvent<HTMLButtonElement>) => {
    // Only show confetti if all steps are completed
    if (isAllStepsCompleted()) {
      // Create a canvas element inside the modal
      const canvas = document.createElement('canvas');
      canvas.style.position = 'fixed';
      canvas.style.top = '0';
      canvas.style.left = '0';
      canvas.style.width = '100%';
      canvas.style.height = '100%';
      canvas.style.pointerEvents = 'none';
      canvas.style.zIndex = '99999';
      
      // Find the dialog paper element and append canvas to it
      const dialogPaper = document.querySelector('.MuiDialog-paper');
      if (dialogPaper) {
        dialogPaper.appendChild(canvas);
      } else {
        document.body.appendChild(canvas);
      }
      
      // Create confetti instance with our canvas
      const myConfetti = confetti.create(canvas, {
        resize: true,
        useWorker: true,
      });
      
      // Get button position for confetti origin
      const rect = event.currentTarget.getBoundingClientRect();
      const x = (rect.left + rect.width / 2) / window.innerWidth;
      const y = (rect.top + rect.height / 2) / window.innerHeight;
      
      // Fire confetti from button
      myConfetti({
        particleCount: 80,
        spread: 60,
        origin: { x, y },
        colors: ['#ea580c', '#f97316', '#fb923c', '#fbbf24', '#f59e0b'],
        gravity: 0.8,
        scalar: 1,
        ticks: 200,
        startVelocity: 25,
      });
      
      // Clean up canvas after animation
      setTimeout(() => {
        canvas.remove();
      }, 3000);
    }
    
    createIntegrations();
  };

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Categories
        return selectedCategories.length > 0;
      case 1: // Connectors
        return selectedServices.length > 0;
      case 2: // Webhooks
        return true; // Optional step
      case 3: // Team
        return true; // Optional step
      default:
        return false;
    }
  };

  return (
    <>
      <Dialog
        open={isOpen}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        disableEscapeKeyDown
        sx={{
          '& .MuiDialog-paper': {
            borderRadius: 2,
            minHeight: '600px',
          },
          '& .MuiBackdrop-root': {
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
          },
        }}
      >
      <DialogTitle sx={{ m: 0, p: 2 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="h5" fontWeight={600}>
              Get Started with Unizo
            </Typography>
            <Typography variant="body2" color="text.secondary" mt={0.5}>
              {currentStepData?.description}
            </Typography>
          </Box>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              color: theme.palette.grey[500],
              '&:hover': {
                backgroundColor: alpha(theme.palette.grey[500], 0.1),
              },
            }}
          >
            <X size={20} />
          </IconButton>
        </Stack>
      </DialogTitle>
      
      {/* Divider line */}
      <Box sx={{ px: 3, pb: 2 }}>
        <Box sx={{ height: '1px', backgroundColor: '#ea580c' }} />
      </Box>

      <Box sx={{ px: 3, pt: 1 }}>
        <Stepper activeStep={currentStep} sx={{ mb: 3 }}>
          {steps.map((step, index) => (
            <Step key={step.id} completed={step.completed}>
              <StepLabel
                StepIconComponent={({ active, completed }) => (
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: completed
                        ? theme.palette.primary.main
                        : active
                        ? theme.palette.primary.main
                        : theme.palette.grey[300],
                      color: completed || active ? '#fff' : theme.palette.text.secondary,
                      fontWeight: 600,
                      fontSize: '0.875rem',
                    }}
                  >
                    {completed ? <Check size={18} /> : index + 1}
                  </Box>
                )}
              >
                {step.title}
              </StepLabel>
            </Step>
          ))}
        </Stepper>
      </Box>

      <DialogContent sx={{ px: 3, pb: 0, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ minHeight: 350, flex: 1, overflow: 'auto', pb: 3 }}>
          {StepComponent && <StepComponent />}
        </Box>

        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          sx={{ 
            position: 'sticky',
            bottom: 0,
            left: 0,
            right: 0,
            backgroundColor: theme.palette.background.paper,
            borderTop: 1,
            borderColor: 'divider',
            p: 3,
            mx: -3,
            mb: -3,
            zIndex: 1,
          }}
        >
          <Box>
            {currentStep > 0 && (
              <Button
                variant="outlined"
                onClick={previousStep}
                startIcon={<ChevronLeft size={18} />}
                sx={{ 
                  borderRadius: 1.5,
                  transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                  '&:hover': {
                    transform: 'translateX(-2px)',
                    borderColor: theme.palette.primary.main,
                  },
                }}
              >
                Back
              </Button>
            )}
          </Box>

          <Stack direction="row" spacing={2}>
            {currentStep < steps.length - 1 && currentStep >= 2 && (
              <Button
                variant="text"
                onClick={skipStep}
                sx={{ color: theme.palette.text.secondary }}
              >
                Skip
              </Button>
            )}
            <Button
              variant="contained"
              onClick={(e) => currentStep === steps.length - 1 ? handleComplete(e) : nextStep()}
              disabled={!canProceed() || isCreating}
              endIcon={currentStep < steps.length - 1 && <ChevronRight size={18} />}
              sx={{
                borderRadius: 1.5,
                textTransform: 'none',
                fontWeight: 500,
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                ...(currentStep === steps.length - 1 ? {
                  background: `linear-gradient(135deg, #ea580c 0%, #f97316 100%)`,
                  boxShadow: '0 2px 8px rgba(234, 88, 12, 0.2)',
                  '&:hover': {
                    transform: 'translateY(-1px)',
                    boxShadow: '0 4px 12px rgba(234, 88, 12, 0.3)',
                    background: `linear-gradient(135deg, #dc2626 0%, #ea580c 100%)`,
                  },
                } : {
                  boxShadow: 'none',
                  '&:hover': {
                    boxShadow: 'none',
                    transform: 'translateY(-1px)',
                  },
                }),
                '&:active': {
                  transform: 'translateY(0)',
                },
              }}
            >
              {isCreating ? 'Setting up...' : currentStep === steps.length - 1 ? 'All Set!' : 'Next'}
            </Button>
          </Stack>
        </Stack>

      </DialogContent>
      </Dialog>

      {/* Exit Confirmation Dialog */}
      <Dialog
        open={showExitConfirmation}
        onClose={handleCancelExit}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Exit Onboarding?</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary">
            You have unsaved progress. Are you sure you want to exit the onboarding process? Your selections will be lost.
          </Typography>
        </DialogContent>
        <Stack
          direction="row"
          spacing={2}
          sx={{ p: 2, pt: 0 }}
          justifyContent="flex-end"
        >
          <Button
            variant="outlined"
            onClick={handleCancelExit}
            sx={{ borderRadius: 1 }}
          >
            Continue Setup
          </Button>
          <Button
            variant="contained"
            onClick={handleConfirmExit}
            color="error"
            sx={{ borderRadius: 1 }}
          >
            Exit
          </Button>
        </Stack>
      </Dialog>
    </>
  );
}