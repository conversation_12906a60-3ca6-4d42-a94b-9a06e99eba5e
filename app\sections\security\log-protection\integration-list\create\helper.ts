import _ from 'lodash';
import mapper, { ComponentMap } from './component-mapper';

/**
 * Get the text for the primary button based on the current step.
 * @param {number} step - The current step number (0-indexed).
 * @param {number} total - The total number of steps.
 * @returns {string} - The text for the primary button ('Next' or 'Submit').
 */
function getPrimaryButtonText(step: number, total: number): string {
   if (step < total - 1) return 'Next';
   return 'Submit';
}

/**
 * Retrieve the details of the component mapped to the current step.
 * @param {number} step - The current step number (0-indexed).
 * @returns {any} - The details of the component for the given step.
 */
function getCurrentStepDetails(step: number): ComponentMap | null {
   try {
      return mapper[step];
   } catch (error) {
      console.error(`There is no component mapped for step ${step}`);
      return null;
   }
}

/**
 * Determine if the "Previous" button should be disabled.
 * @param {number} step - The current step number (0-indexed).
 * @returns {boolean} - True if the step is 0, false otherwise.
 */
const getPrevBtnDisabled = (step: number): boolean => {
   return step === 0;
};


export {
   getPrimaryButtonText,
   getCurrentStepDetails,
   getPrevBtnDisabled,
};
