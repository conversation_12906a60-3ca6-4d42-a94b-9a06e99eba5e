import { useEffect, useMemo, useState } from 'react';

// material-ui
import Grid from '@mui/material/Grid';

import Typography from '@mui/material/Typography';

import { PieChart } from '@mui/x-charts';

import Dot from 'components/@extended/Dot';
import { useDashboard } from 'hooks/api/dashboard/useDashboard';
import { useServiceProfile } from 'hooks/useServiceProfile';
import { Stack } from '@mui/material';


// ==============================|| INVOICE - PIE CHART ||============================== //

type AdoptionTypes = {
   value: number, label: string, color: string
}

export default () => {

   const { getColor, getLabel } = useServiceProfile();

   const { integrationStats } = useDashboard({ subsIds: [] })

   const data: Array<AdoptionTypes> = useMemo(() => {
      return integrationStats.reduce((acc: Array<AdoptionTypes>, cur: Record<string, any>) => {
         const tempObj = {} as AdoptionTypes;
         const { type, stats: { totalCount } } = cur;

         tempObj.label = getLabel(type) as string;
         tempObj.value = totalCount;
         tempObj.color = getColor(type) as string;
         acc.push(tempObj);

         return acc
      }, []) ?? [];
   }, [integrationStats])

   //sx style
   const DotSize = { display: 'flex', alignItems: 'center', gap: 1 };
   const ExpenseSize = { fontSize: '1rem', lineHeight: '1.5rem', fontWeight: 500 };

   return (
      <Stack gap={2}>
         <Grid
            container
            alignItems="center"
            spacing={1}
            justifyContent={'space-between'}
            height={'100%'}
         >
            <Grid item xs={12}>
               <PieChart
                  height={247}
                  margin={{ top: 0, right: 0, left: 0, bottom: 0 }}
                  series={[
                     {
                        data,
                        arcLabelMinAngle: 35,
                        arcLabelRadius: '60%',
                        innerRadius: 50,
                        outerRadius: 110,
                        type: 'pie',
                        highlightScope: { highlighted: 'item' },
                        valueFormatter: (value) => `${value.value}`
                     },
                  ]}
                  slotProps={{ legend: { hidden: true } }}
               />
            </Grid>
         </Grid>
         <Grid container spacing={1}>
            {data.map((item, key: number) => {
               return (
                  <Grid item xs={12} key={key}>
                     <Grid container>
                        <Grid item></Grid>
                        <Grid item xs sx={DotSize}>
                           <Dot
                              size={12}
                              sx={{
                                 bgcolor: item?.color
                              }}
                           />
                           <Typography color="secondary.600">
                              {item?.label}
                           </Typography>
                        </Grid>
                        <Grid item sx={ExpenseSize}>
                           <Typography>
                              {item?.value}
                           </Typography>
                        </Grid>
                     </Grid>
                  </Grid>
               )
            })}
         </Grid>
      </Stack>
   );
}
