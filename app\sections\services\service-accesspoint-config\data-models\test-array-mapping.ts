/**
 * Test Array-Object Mapping Functionality
 * Demonstrates creative array handling in field mapping
 */

import { generateOpenApiFieldMapping } from './openapi-field-mapping';

// Test function to demonstrate array-object mapping capabilities
function testArrayObjectMapping() {
  console.log('='.repeat(60));
  console.log('🧪 Array-Object Mapping Test');
  console.log('='.repeat(60));

  try {
    const result = generateOpenApiFieldMapping('github', 'repository');
    
    console.log('\n📋 Array Fields with Objects:');
    console.log('-'.repeat(40));
    
    // Find array fields in the source data
    const arrayFields = findArrayFields(result.sourceFields);
    
    arrayFields.forEach(field => {
      console.log(`\n🔍 Array: ${field.name} (${field.type})`);
      console.log(`   Description: ${field.description || 'N/A'}`);
      
      if (field.children) {
        field.children.forEach((child: any) => {
          const badge = getArrayItemBadge(child);
          const type = getArrayItemType(child);
          const icon = getArrayIcon(type);
          
          console.log(`   ${icon} ${child.name}${badge} - ${child.description || child.type}`);
          
          if (child.children && child.children.length > 0) {
            console.log(`      └─ ${child.children.length} nested fields available`);
            child.children.slice(0, 3).forEach((nested: any) => {
              console.log(`         • ${nested.name} (${nested.type})`);
            });
            if (child.children.length > 3) {
              console.log(`         • ... and ${child.children.length - 3} more`);
            }
          }
        });
      }
    });

    console.log('\n🎯 Creative Array Mapping Scenarios:');
    console.log('-'.repeat(40));
    
    console.log('1. 📊 Generic Array Item Mapping:');
    console.log('   contributors.item.login → team_members[].username');
    console.log('   Use this for: Mapping all array items uniformly');
    
    console.log('\n2. 🎯 Specific Index Mapping:');
    console.log('   contributors[0].login → primary_maintainer.username');
    console.log('   contributors[1].login → secondary_maintainer.username');
    console.log('   Use this for: Known positions with special meaning');
    
    console.log('\n3. ⚡ Dynamic Index Mapping:');
    console.log('   contributors[*].profile.bio → team_members[*].biography');
    console.log('   Use this for: Runtime-determined array access');
    
    console.log('\n4. 🏗️ Nested Array-Object Mapping:');
    console.log('   webhooks.item.config.events[0] → integrations[].primary_event');
    console.log('   Use this for: Complex nested array structures');

    console.log('\n💡 Benefits of Creative Array Mapping:');
    console.log('-'.repeat(40));
    console.log('• 🎨 Flexible field selection for different use cases');
    console.log('• 🔢 Index-aware mapping for position-specific data');
    console.log('• ⚡ Dynamic mapping for runtime flexibility');
    console.log('• 🏗️ Deep nesting support for complex structures');
    console.log('• 👁️ Visual indicators help users understand array context');

    return result;
    
  } catch (error) {
    console.error('❌ Array mapping test failed:', error);
    return null;
  }
}

// Helper functions
function findArrayFields(schema: any): any[] {
  const arrays: any[] = [];
  
  function traverse(field: any) {
    if (field.type === 'array') {
      arrays.push(field);
    }
    
    if (field.children) {
      field.children.forEach(traverse);
    }
  }
  
  traverse(schema);
  return arrays;
}

function getArrayItemBadge(field: any): string {
  if (!field.isArrayItem) return '';
  if (field.isDynamicIndex) return '[*]';
  if (field.arrayIndex !== undefined) return `[${field.arrayIndex}]`;
  return '[item]';
}

function getArrayItemType(field: any): string {
  if (!field.isArrayItem) return 'normal';
  if (field.isDynamicIndex) return 'dynamic';
  if (field.arrayIndex !== undefined) return 'indexed';
  return 'item';
}

function getArrayIcon(type: string): string {
  const icons = {
    item: '📦',
    indexed: '🔢',
    dynamic: '⚡',
    normal: '📄'
  };
  return icons[type as keyof typeof icons] || '📄';
}

// Export for console testing
if (typeof window !== 'undefined') {
  (window as any).testArrayObjectMapping = testArrayObjectMapping;
}

export { testArrayObjectMapping };