import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Box, Typography, Button, Paper, Stack } from '@mui/material';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class FieldMappingsErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Field Mappings Error:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null });
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return <>{this.props.fallback}</>;
      }

      return (
        <Paper sx={{ p: 4, textAlign: 'center', maxWidth: 500, mx: 'auto', my: 4 }}>
          <Box
            sx={{
              width: 64,
              height: 64,
              borderRadius: '50%',
              backgroundColor: (theme) => theme.palette.error.light + '20',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mx: 'auto',
              mb: 3
            }}
          >
            <AlertTriangle size={32} color="#ef4444" />
          </Box>
          
          <Typography variant="h6" fontWeight={600} gutterBottom>
            Something went wrong
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            We encountered an error while loading the field mappings. Please try refreshing the page.
          </Typography>

          {process.env.NODE_ENV === 'development' && this.state.error && (
            <Box
              sx={{
                mt: 2,
                p: 2,
                backgroundColor: (theme) => theme.palette.grey[100],
                borderRadius: 1,
                textAlign: 'left',
                fontSize: '0.75rem',
                fontFamily: 'monospace',
                maxHeight: 200,
                overflow: 'auto'
              }}
            >
              <Typography variant="caption" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
                {this.state.error.toString()}
                {this.state.errorInfo?.componentStack}
              </Typography>
            </Box>
          )}
          
          <Stack direction="row" spacing={2} justifyContent="center" sx={{ mt: 3 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshCw size={16} />}
              onClick={this.handleReset}
              sx={{ textTransform: 'none' }}
            >
              Refresh Page
            </Button>
          </Stack>
        </Paper>
      );
    }

    return this.props.children;
  }
}

export default FieldMappingsErrorBoundary;