import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, CardHeader, Skeleton, Box, Stack, Typography, alpha, useTheme } from '@mui/material';

const DashboardSkeleton: React.FC = () => {
  const theme = useTheme();

  // Stat Card Skeleton Component
  const StatCardSkeleton = () => (
    <Card sx={{ 
      height: '100%',
      border: `1px solid ${theme.palette.divider}`,
      borderRadius: 0,
      boxShadow: theme.palette.mode === 'dark'
        ? '0 1px 3px rgba(0, 0, 0, 0.2)'
        : '0 1px 3px rgba(0, 0, 0, 0.05)',
    }}>
      <CardContent sx={{ p: 3 }}>
        <Box display="flex" alignItems="flex-start" justifyContent="space-between">
          <Box flex={1}>
            <Skeleton width={100} height={12} sx={{ mb: 1 }} />
            <Skeleton width={40} height={36} sx={{ mb: 0.5 }} />
            <Skeleton width={80} height={14} />
          </Box>
          <Skeleton variant="rectangular" width={48} height={48} />
        </Box>
      </CardContent>
    </Card>
  );

  // Chart Card Skeleton Component
  const ChartCardSkeleton = ({ width = 150 }: { width?: number }) => (
    <Card sx={{
      height: '100%',
      border: `1px solid ${theme.palette.divider}`,
      borderRadius: 0,
      boxShadow: theme.palette.mode === 'dark'
        ? '0 1px 3px rgba(0, 0, 0, 0.2)'
        : '0 1px 3px rgba(0, 0, 0, 0.05)',
    }}>
      <CardHeader
        sx={{
          borderBottom: `1px solid ${theme.palette.divider}`,
          background: theme.palette.mode === 'dark'
            ? theme.palette.background.paper
            : theme.palette.grey[50],
          py: 2,
        }}
        title={
          <Box>
            <Skeleton width={width} height={20} />
            <Skeleton width={width + 50} height={14} sx={{ mt: 0.5 }} />
          </Box>
        }
        action={<Skeleton variant="circular" width={24} height={24} />}
      />
      <CardContent sx={{ p: 3, pt: 2 }}>
        <Box sx={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Skeleton width={120} height={16} />
        </Box>
      </CardContent>
    </Card>
  );

  // Bottom Card Skeleton Component
  const BottomCardSkeleton = ({ hasChart = false }: { hasChart?: boolean }) => (
    <Card sx={{
      height: '100%',
      border: `1px solid ${theme.palette.divider}`,
      borderRadius: 0,
      boxShadow: theme.palette.mode === 'dark'
        ? '0 1px 3px rgba(0, 0, 0, 0.2)'
        : '0 1px 3px rgba(0, 0, 0, 0.05)',
    }}>
      <CardHeader
        sx={{
          borderBottom: `1px solid ${theme.palette.divider}`,
          background: theme.palette.mode === 'dark'
            ? theme.palette.background.paper
            : theme.palette.grey[50],
          py: 2,
        }}
        title={<Skeleton width={180} height={20} />}
        subheader={<Skeleton width={200} height={14} sx={{ mt: 0.5 }} />}
        action={<Skeleton variant="circular" width={24} height={24} />}
      />
      <CardContent sx={{ p: 3 }}>
        {hasChart ? (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
              <Skeleton variant="circular" width={150} height={150} />
            </Box>
            <Stack spacing={1}>
              {Array.from({ length: 2 }).map((_, i) => (
                <Box key={i} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Skeleton variant="circular" width={8} height={8} />
                    <Skeleton width={80} height={14} />
                  </Box>
                  <Skeleton width={40} height={14} />
                </Box>
              ))}
            </Stack>
          </Box>
        ) : (
          <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Skeleton width={120} height={16} />
          </Box>
        )}
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Welcome Banner */}
      <Box sx={{ mb: 3 }}>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Skeleton width={120} height={28} />
          <Skeleton variant="circular" width={24} height={24} />
        </Stack>
        <Skeleton width={200} height={16} sx={{ mt: 1 }} />
      </Box>

      {/* Top Stats Cards */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        {Array.from({ length: 4 }).map((_, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatCardSkeleton />
          </Grid>
        ))}
      </Grid>

      {/* Charts Row */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <ChartCardSkeleton width={150} />
        </Grid>
        <Grid item xs={12} md={6}>
          <ChartCardSkeleton width={170} />
        </Grid>
      </Grid>

      {/* Bottom Cards */}
      <Grid container spacing={2}>
        <Grid item xs={12} md={4}>
          <BottomCardSkeleton />
        </Grid>
        <Grid item xs={12} md={4}>
          <BottomCardSkeleton hasChart />
        </Grid>
        <Grid item xs={12} md={4}>
          <Card sx={{
            height: '100%',
            border: `1px solid ${theme.palette.divider}`,
            borderRadius: 0,
            boxShadow: theme.palette.mode === 'dark'
              ? '0 1px 3px rgba(0, 0, 0, 0.2)'
              : '0 1px 3px rgba(0, 0, 0, 0.05)',
          }}>
            <CardHeader
              sx={{
                borderBottom: `1px solid ${theme.palette.divider}`,
                background: theme.palette.mode === 'dark'
                  ? theme.palette.background.paper
                  : theme.palette.grey[50],
                py: 2,
              }}
              title={<Skeleton width={60} height={20} />}
              subheader={<Skeleton width={150} height={14} sx={{ mt: 0.5 }} />}
              action={<Skeleton variant="circular" width={24} height={24} />}
            />
            <CardContent sx={{ p: 3 }}>
              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', pb: 2, mb: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
                  <Skeleton width={80} height={16} />
                  <Skeleton width={60} height={16} />
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: 150 }}>
                  <Skeleton width={120} height={16} />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardSkeleton;