import { API_ENDPOINTS } from "utils/api/api-endpoints";
import platformfetchInstance from "utils/api/fetchinstance/platform-fetch-Instance";
import fetchInstance from "utils/api/fetchinstance";

export const organizationClient = {
  getOrgConfig: (id: string) => {
    return fetchInstance.get(`${API_ENDPOINTS.ORGANIZATION}s/${id}/configurations`);
  },
  createOrgWatchConfig: (id: string, payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.ORGANIZATION}s/${id}/configurations`, payload);
  },
  updateOrgWatchConfig: (id: string, watchId: string, payload: Record<string, any>) => {
    return fetchInstance.patch(`${API_ENDPOINTS.ORGANIZATION}s/${id}/configurations/${watchId}`, payload);
  },
  searchOrgServices: (payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.SERVICE}s/search`, payload);
  },
  getOrganizationById: (id: string) => {
    return platformfetchInstance.get(`${API_ENDPOINTS.ORGANIZATION}s/${id}`);
  },
  updateOrganizationState: (id: string, payload: Record<string, any>) => {
    return platformfetchInstance.post(`${API_ENDPOINTS.ORGANIZATION}s/${id}/actions`, payload);
  },
  searchOrganizationWatches: (payload: Record<string, any>) => {
    return fetchInstance.post(`${API_ENDPOINTS.WATCHES}/search`, payload);
  },
  searchCategoryProfileEvents:(payload: Record<string, any>) =>{
    return fetchInstance.post(`${API_ENDPOINTS.EVENT_PROFILES}/search`, payload)
  }
};
