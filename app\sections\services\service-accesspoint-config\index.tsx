import { Suspense, lazy, useMemo, useState } from "react";
import { Stack, } from "@mui/material";
import useManageProfile from "../service-context/use-manage-profile";
import { State } from "hooks/useStatus";
import Placeholder from "./skeleton";

const ConfigForm = lazy(() => import('./config-modal'));
const EditAccessTypeSelection = lazy(() => import('./edit-accessType-selection'));
const Selection = lazy(() => import('./selection'));


const ServiceConfig = () => {

   const {
      serviceProfileAccessPoints: data,
      selectedService
   } = useManageProfile()

   const [open, setOpen] = useState<boolean>(false);

   const onEdit = () => {
      setOpen(true)
   }

   const onClose = () => {
      setOpen(false)
   }

   const selected = useMemo(() => {
      return data?.reduce((acc: string[], i: Record<string, any>) => {
         if (!!i?.accessPoint?.isEnabled) {
            acc.push(i?.accessPoint?.id)
         };
         return acc;
      }, []) ?? []
   }, [data, open]);

   if (selectedService?.service?.state === State.IN_PROGRESS) {
      return (
         <Stack gap={1}>
            <Placeholder />
         </Stack>
      )
   }

   return (
      <Stack
         gap={1}
      >
         {/* selection box */}
         <Suspense fallback={null}>
            <Selection selected={selected} onEdit={onEdit} />
         </Suspense>

         {/* config form */}
         <Suspense fallback={null}>
            <ConfigForm />
         </Suspense>

         {/* selection Modal */}
         <Suspense fallback={null}>
            <EditAccessTypeSelection
               open={open}
               onClose={onClose}
               selected={selected}
            />
         </Suspense>
      </Stack>
   )
}


export default ServiceConfig;