import { jsonSchemaToZod } from "json-schema-to-zod";

import { z } from "zod";
import { resolveRefs } from "json-refs";
import { format } from "prettier";

export async function example(jsonSchema: Record<string, unknown>): Promise<string> {
   const { resolved } = await resolveRefs(jsonSchema);
   const code = jsonSchemaToZod(resolved);
   const formatted = await format(code, { parser: "typescript" });

   return formatted;
}


export const getZODSchema = (schema: any): any => {

   const workerBlob = new Blob([jsonSchemaToZod(schema)], { type: 'application/javascript' });
   return  (window.URL || window.webkitURL).createObjectURL(workerBlob);
}
