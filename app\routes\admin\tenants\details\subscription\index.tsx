import { Divider, Grid, Stack, Typography } from "@mui/material"
import MainCard from "components/MainCard";
import { useParams } from "@remix-run/react";
import { useGetSuperAdmin } from "hooks/api/super-admin/useGetSuperAdmin";
import { useStatus } from "hooks/useStatus";
import { useDate } from "hooks/useDate";
import { useMemo } from "react";
import { ENTITLE_MAPPER } from "constants/entitlements";
import { useTable } from "hooks/table/useTable";
import Table from "components/@extended/Table";
import nameChecker from "constants/categoryMapper";

export default () => {

   const { id: orgId, subscriptionId: id } = useParams()
   const { getSubscriptionById } = useGetSuperAdmin();

   const { data } = getSubscriptionById({ id, orgId });

   const { renderStatus } = useStatus(),
      { loadDate } = useDate()

   const OVERVIEW = {
      NAME: {
         label: 'Name',
         getData: (resp: any) => {
            return <Typography >{nameChecker(resp?.name ?? '')}</Typography>
         }
      },
      TIER: {
         label: 'Tier',
         getData: (resp: any) => {
            return (
               <Typography >{resp?.charge?.pricingTier?.name ?? ''}</Typography>
            )
         }
      },
      STATUS: {
         label: 'Status',
         getData: (resp: any) => {
            return resp?.state ? renderStatus(resp?.state) : ''
         }
      },
      START_DATE: {
         label: 'Start Date',
         getData: (resp: any) => {
            return (
               loadDate(resp?.startDate)
            )
         }
      },
      END_DATE: {
         label: 'End Date',
         getData: (resp: any) => {
            return loadDate(resp?.cancelledDate)
         }
      },
   }

   const entitlements = useMemo(() => {
      return data?.entitlements ?? []
   }, [data?.entitlements]);

   const { extendedProps } = useTable()

   const columns: any = useMemo(
      () => [
         {
            accessorKey: '1', //access nested data with dot notation
            header: 'Feature',
            accessorFn(row: any) {
               const label = ENTITLE_MAPPER?.[row?.type]?.properties?.[0]?.label
               return label ?? ''
            },
         },
         {
            accessorKey: '2',
            header: 'Entitlement',
            accessorFn(row: any) {
               const value = ENTITLE_MAPPER?.[row?.type]?.properties?.[0]?.getMessage(row)
               return value
            },
         },
      ],
      [],
   );

   return (
      <Stack gap={4}>
         <Stack gap={2}>
            <Typography variant="h5">Subscription</Typography>
            <MainCard>
               <Grid container rowSpacing={3}>
                  {Object.entries(OVERVIEW).map(([key, { label, getData }]) => {
                     return (
                        <Grid item xs={4} key={key}>

                           <Stack gap={1} direction={'row'} alignItems={'center'}>
                              <Typography variant="overline" className="font-semibold">
                                 {`${label} :`}
                              </Typography>
                              {getData(data)}
                           </Stack>
                        </Grid>
                     )
                  })}
               </Grid>
            </MainCard>
         </Stack>
         <Stack gap={2}>
            <Typography variant="h5">Entitlements</Typography>
            <Table
               data={entitlements}
               columns={columns}
               disablePagination
            />
         </Stack>
      </Stack>
   )
}