import { useEffect, useState } from "react"
import { Box, Button, Grid, MenuItem, Select, Stack, Typography } from "@mui/material"

import MainCard from "components/MainCard"
import { MonitorEnum, useGetIntegration } from "hooks/api/integration/useGetIntegration"

import useUserDetails from "store/user"

export default () => {

   const { user } = useUserDetails(),
      { getHealthCheckOption,
         monitorConfig,
         attemptToCreateMonitorConfig,
         attemptToUpdateMonitorConfig
      } = useGetIntegration({ orgId: user?.organization?.id });

   const options = getHealthCheckOption()

   const [selected, setSelected] = useState<MonitorEnum>(() => MonitorEnum.DAILY)

   const onSubmit = () => {
      !monitorConfig?.id ? attemptToCreateMonitorConfig(selected) :
         attemptToUpdateMonitorConfig(selected)
   }

   useEffect(() => {
      if (monitorConfig?.schedulerTypeConfig) {
         setSelected(monitorConfig?.schedulerTypeConfig?.type)
      }
   }, [monitorConfig?.schedulerTypeConfig])

   return (
      <Grid container columnSpacing={2}>
         <Grid item xs={12} lg={12}>
            <Stack direction={'column'} gap={2}>
               <Typography variant='button' color='secondary.600'>
                  You can set your integration health check schedule here. This frequency determines how often the system will check and report the integration's health status. If you leave this field empty, the default setting will be daily. Note that provider rate limits apply to monitoring and health check connectors.
               </Typography>
               <MainCard >
                  <Stack gap={2} >
                     <Stack direction={'column'} gap={1}>
                        <Typography variant="h6" className="font-semibold" >{'Select Frequency'}</Typography>
                        <Select
                           className="w-[30%]"
                           value={selected}
                           onChange={(e) => setSelected(e.target.value as MonitorEnum)}
                        >
                           {options.map((item) => {
                              return (
                                 <MenuItem
                                    value={item.value}
                                    key={item.value}
                                 >
                                    {item.label}
                                 </MenuItem>
                              )
                           })}
                        </Select>
                        <Box>
                           <Button
                              onClick={onSubmit}
                              variant='contained'
                              color='primary'
                              sx={{ mt: .5 }}
                           >
                              Submit
                           </Button>
                        </Box>
                     </Stack>
                  </Stack>
               </MainCard >
            </Stack>

         </Grid>
      </Grid >
   )
}