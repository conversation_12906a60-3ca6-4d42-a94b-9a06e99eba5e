// ==============================|| OVERRIDES - TABLE ROW ||============================== //

export default function TableRow(theme) {
  return {
    MuiTableRow: {
      styleOverrides: {
        root: ({ theme }) => ({
          '&:last-of-type': {
            '& .MuiTableCell-root': {
              borderBottom: 'none'
            }
          },
          '& .MuiTableCell-root': {
            '&:last-of-type': {
              paddingRight: 24
            },
            '&:first-of-type': {
              paddingLeft: 24
            }
          },
          '&:hover': {
            backgroundColor: theme.palette.mode === 'dark' 
              ? theme.palette.action.hover
              : theme.palette.action.hover
          }
        })
      }
    }
  };
}
