import type { Service } from './service';


export interface ServiceProfile {
  href: string
  type: string
  id: string
  name: string
  description: string
  state: string
  visibility: string
  deploymentModel: DeploymentModel
  image: Image
  assistanceInfo: AssistanceInfo
  notifications: any[]
  tags: any[]
  attributes: any[]
  organization: Organization
  changeLog: ChangeLog
  links: any[]

  service?: Service
}

export interface DeploymentModel {
  type: string
}

export interface Image {
  original: string
  xSmall: string
  small: string
  medium: string
  large: string
}

export interface AssistanceInfo { }

export interface Organization {
  href: string
  type: string
  id: string
  name: string
}

export interface ChangeLog {
  createdDateTime: string
  lastUpdatedDateTime: string
}
