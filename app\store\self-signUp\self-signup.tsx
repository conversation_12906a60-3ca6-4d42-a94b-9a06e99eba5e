/* eslint-disable @typescript-eslint/no-explicit-any */
import { ServiceProfile } from "types/service-profile";
import { getAuthUserId } from "utils/auth";
import { create } from "zustand";
import { persist } from 'zustand/middleware';

interface Member {
    id: string;
    email: string;
    role: string;
}

interface SelfRegistration {
    isReady: boolean
    setReady: (isReady: boolean) => void
    move: (type: 'next' | 'prev') => void
    jump: (step: number) => void
    step: number
    selectedCategories: string[]
    setSelectedCategories: (categories: string[]) => void
    selectedServices: Partial<ServiceProfile>[]
    setSelectedServices: (services: Partial<ServiceProfile>[]) => void
    addSelectedService: (service: ServiceProfile) => void
    currentCategory: string | null
    setCurrentCategory: (category: string | null) => void
    webhookData: WebhookData[]
    setWebhookData: (data: WebhookData[]) => void
    selectedMembers: Member[];
    setSelectedMembers: (members: Member[]) => void;
    addSelectedMember: (member: Member) => void;
    removeSelectedMember: (memberId: string) => void;

    isSetupDone: boolean,
    setIsSetupDone: (isSetupDone: boolean) => void,

    reset: () => void;

}

export interface WebhookData {
    category: string;
    url: string;
    isValid: boolean;
}

const SELF_REGISTRATION: string = `${getAuthUserId()}-self-registration`;

const DEFAULT_VALUES: any = {
    step: 0,
    selectedCategories: [],
    selectedServices: [],
    currentCategory: null,
    webhookData: [],
    selectedMembers: [],
    isReady: false,
    isSetupDone: false
}

const useSelfRegistration = create(
    persist<SelfRegistration>(
        (set, get) => ({
            reset: () => {
                set(DEFAULT_VALUES)
            },
            isReady: false,
            setReady: (isReady) => {
                set((state) => ({
                    ...state,
                    isReady
                }))
            },
            isSetupDone: false,
            setIsSetupDone: (isSetupDone) => {
                set((state) => ({
                    ...state,
                    isSetupDone
                }))
            },
            step: DEFAULT_VALUES.step,
            selectedCategories: DEFAULT_VALUES.selectedCategories,
            selectedServices: DEFAULT_VALUES.selectedServices,
            currentCategory: DEFAULT_VALUES.currentCategory,
            setSelectedCategories: (categories) => {
                // Filter out null values and ensure unique entries
                const validCategories = categories.filter(category => category !== null);
                set((state) => ({
                    ...state,
                    selectedCategories: [...new Set(validCategories)]
                }))
            },
            setSelectedServices: (services) => {
                set((state) => ({
                    ...state,
                    selectedServices: services
                }))
            },
            addSelectedService: (service) => {
                const { selectedServices: prevSelectedServices } = get();

                let updated = prevSelectedServices;

                if (prevSelectedServices?.map((i) => i?.id).includes(service?.id)) {
                    updated = updated?.filter(({ id }) => id !== service.id);
                } else {
                    updated = prevSelectedServices.concat(service)
                }

                set((state) => ({
                    ...state,
                    selectedServices: updated
                }))
            },
            setCurrentCategory: (currentCategory) => {
                set((state) => ({
                    ...state,
                    currentCategory
                }))
            },
            move: (type) => {
                set(({ step }) => {
                    return ({ step: type === 'next' ? step + 1 : step - 1 })
                })
            },
            jump: (step) => {
                set((prevState) => (
                    { ...prevState, step }
                ))
            },
            webhookData: DEFAULT_VALUES.webhookData,
            setWebhookData: (data) => {
                set((state) => ({
                    ...state,
                    webhookData: data
                }))
            },
            selectedMembers: DEFAULT_VALUES.selectedMembers,
            setSelectedMembers: (members) => {
                set((state) => ({
                    ...state,
                    selectedMembers: members
                }))
            },
            addSelectedMember: (member) => {
                set((state) => ({
                    ...state,
                    selectedMembers: [...state.selectedMembers, member]
                }))
            },
            removeSelectedMember: (memberId) => {
                set((state) => ({
                    ...state,
                    selectedMembers: state.selectedMembers.filter(member => member.id !== memberId)
                }))
            },
        }),
        {
            name: SELF_REGISTRATION
        }
    )
)

export default useSelfRegistration;
