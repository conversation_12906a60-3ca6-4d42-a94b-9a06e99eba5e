import React from 'react';
import { EnvironmentCard } from '../EnvironmentCard';
import { ConnectorList, Connector } from '../ConnectorList';

// Example usage of EnvironmentCard
export const EnvironmentCardExample = () => {
  return (
    <EnvironmentCard
      name="Production"
      description="Live environment"
      isProduction={true}
      onEdit={() => console.log('Edit clicked')}
    />
  );
};

// Example usage of ConnectorList
export const ConnectorListExample = () => {
  const connectors: Connector[] = [
    {
      id: 'ado',
      name: 'ado',
      displayName: 'ADO',
      icon: 'https://unizopublicpaas.blob.core.windows.net/imgs/azure_devops/azure_devops_64.png',
      enabled: true,
    },
    {
      id: 'bitbucket',
      name: 'bitbucket',
      displayName: 'Bitbucket',
      icon: 'https://unizopublicpaas.blob.core.windows.net/imgs/bitbucket/bitbucket_64.png',
      enabled: true,
    },
    {
      id: 'github',
      name: 'github',
      displayName: 'GitHub',
      icon: 'https://unizopublicpaas.blob.core.windows.net/imgs/github/github_64.png',
      enabled: false,
    },
    {
      id: 'github-enterprise',
      name: 'github-enterprise',
      displayName: 'GitHub Enterprise',
      icon: 'https://unizopublicpaas.blob.core.windows.net/imgs/github/github_64.png',
      enabled: true,
    },
    {
      id: 'gitlab',
      name: 'gitlab',
      displayName: 'GitLab',
      icon: 'https://unizopublicpaas.blob.core.windows.net/imgs/gitlab/gitlab_64.png',
      enabled: true,
    },
    {
      id: 'gitlab-self-managed',
      name: 'gitlab-self-managed',
      displayName: 'GitLab Self-Managed',
      icon: 'https://unizopublicpaas.blob.core.windows.net/imgs/gitlab/gitlab_64.png',
      enabled: false,
    },
  ];

  return (
    <ConnectorList
      title="Configure Source Code integrations"
      subtitle="In our {docs}, view supported fields and compare integration coverage."
      docsLink="https://docs.unizo.ai/docs/unified-apis/scm/coverage"
      connectors={connectors}
      onToggle={(id, enabled) => console.log(`Toggle ${id}: ${enabled}`)}
      onRefresh={() => console.log('Refresh clicked')}
      onConnectorClick={(connector) => console.log('Clicked:', connector)}
    />
  );
};