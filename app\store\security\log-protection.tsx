import { State } from "hooks/useStatus";
import React from "react";
import { Service } from "types/service";
import { ServiceProfile } from "types/service-profile";
import { create } from "zustand";
import { persist } from 'zustand/middleware';

type SegmentValues = {
   service: Service | null
   integrationDetails: any
   selectAccount: Record<string, any>
}

type ValueType = 'service' | 'integrationDetails' | 'selectAccount'
type Step = number;

interface LogProtectionStore {
   // modal
   openCreate: false,
   setOpenCreate: (openCreate: boolean) => void,

   values: SegmentValues
   setValues: (type: ValueType, value: Record<string, any>[] | Record<string, any>) => void

   windowStates: {
      integrationDetails: State,
   }
   setWindowStates: (type: 'integrationDetails', value: State) => void
   resetWindowStates: () => void

   mode?: Mode
   setMode: (mode: Mode) => void

   step: Step,
   move: (type: 'next' | 'prev') => void
   jump: (step: Step) => void

   done: boolean,
   setDone: (isDone: boolean) => void,

   reset: () => void;
}

const STORE_KEY: string = 'log-protection';

const DEFAULT_VALUES: any = {
   openCreate: false,
   values: {
      service: null,
      integrationDetails: null,
      selectAccount: null
   },
   mode: 'create',
   step: 0,
   acceptedWelcome: false,
   done: false,
   windowStates: {
      integrationDetails: false,
   }
}

const useLogProtection = create(
   persist<LogProtectionStore>(
      (set, get) => ({
         // open create
         openCreate: DEFAULT_VALUES.openCreate,
         setOpenCreate: (openCreate: any) => {
            set((prevState) => ({ ...prevState, openCreate }))
         },

         // segment values
         values: DEFAULT_VALUES.values,
         setValues: (type, value) => {
            const { values, ...rest } = get();
            set({
               ...rest,
               values: {
                  ...values,
                  [type]: value
               }
            })
         },

         windowStates: DEFAULT_VALUES?.windowStates,
         setWindowStates: (type, value) => {
            set((prevState) => ({ ...prevState, windowStates: { [type]: value } }))
         },

         resetWindowStates: () => {
            set({
               ...get(),
               windowStates: DEFAULT_VALUES?.windowStates
            })
         },

         mode: DEFAULT_VALUES.mode,
         setMode: (mode: Mode) => {
            set((prevState) => ({ ...prevState, mode }))
         },

         // step values
         step: DEFAULT_VALUES.step as Step,
         move: (type) => {
            const { step, ...rest } = get();
            set({
               ...rest,
               step: (() => {
                  return (type === 'next' ? step + 1 : step - 1)
               })()
            })
         },
         jump: (step) => {
            set((prevState) => (
               // hard move, setting the index what you are passing
               { ...prevState, step }
            ))
         },

         // conclusion message, container for agree the conclusion
         done: DEFAULT_VALUES.done,
         setDone: (done) => {

            set((prev) => {
               return { ...prev, done }
            });

            // rest the state
            get().reset();
         },

         reset: () => {
            set(DEFAULT_VALUES as Partial<LogProtectionStore>)
         }
      }),
      {
         name: `${STORE_KEY}_${(window as any).authUserOrgId}`,
      }
   )
);

export default useLogProtection;