/**
 * Get the text for the primary button based on the current step.
 * @param {number} step - The current step number (0-indexed).
 * @param {number} total - The total number of steps.
 * @returns {string} - The text for the primary button ('Next' or 'Submit').
 */
function getPrimaryButtonText(step: number, total: number): string {
   if (step < total - 1) return 'Next';
   return 'Done';
}

/**
 * Determine if the "Previous" button should be disabled.
 * @param {number} step - The current step number (0-indexed).
 * @returns {boolean} - True if the step is 0, false otherwise.
 */
const getPrevBtnDisabled = (step: number): boolean => {
   return step === 0;
};

const getIsSelectionEnd = (maxCategoriesLength: number | undefined, selectionLength: number) => {
   if (!maxCategoriesLength) return false;

   return selectionLength >= maxCategoriesLength;
}

export {
   getPrimaryButtonText,
   getPrevBtnDisabled,
   getIsSelectionEnd
};
