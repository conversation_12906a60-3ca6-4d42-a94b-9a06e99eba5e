import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Stack,
  Button,
  Paper,
  Grid,
  useTheme,
  alpha,
  IconButton,
  Link,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  TextField,
  Checkbox,
  DialogTitle,
  DialogContent,
} from '@mui/material';
import { Code2, Spark<PERSON>, Key, Copy, Check, AlertCircle, Play, ExternalLink } from 'lucide-react';
import { Dialog, DialogClose } from 'components/@extended/dialog';
import TabNavigationFooter from 'components/getting-started/TabNavigationFooter';
import ConnectUIModal from 'sections/advanced/connect-ui/ConnectUIModal';
import { useAccesskey } from 'hooks/api/useAccesskey';
import platformfetchInstance from 'utils/api/fetchinstance/platform-fetch-Instance';
import fetchInstance from 'utils/api/fetchinstance';
import { DocksClient } from 'services/dock.service';
import { State } from 'hooks/useStatus';
import { useGetDockProfile } from 'hooks/api/dockProfiles/useDockProfile';
import useUserDetails from 'store/user';
import { generateUUID } from 'utils/uuid';
import CopyableText from 'components/@extended/Copyable-text';

const sdkLanguages = [
  {
    name: 'JavaScript/TypeScript',
    shortName: 'TypeScript',
    icon: 'https://unizopublicpaas.blob.core.windows.net/svg/javascript-original.svg',
    description: 'Full TypeScript support, automatic retries, built-in rate limiting',
    docLink: 'https://docs.unizo.ai/docs/sdks/javascript/',
  },
  {
    name: 'Python',
    shortName: 'Python',
    icon: 'https://unizopublicpaas.blob.core.windows.net/svg/python-original.svg',
    description: 'Async/await support, Pydantic models, comprehensive error handling',
    docLink: 'https://docs.unizo.ai/docs/sdks/python',
  },
  {
    name: 'Go',
    shortName: 'Go',
    icon: 'https://unizopublicpaas.blob.core.windows.net/svg/go-original-wordmark.svg',
    description: 'Context support, structured errors, concurrent safe',
    docLink: 'https://docs.unizo.ai/docs/sdks/go',
  },
  {
    name: 'Java',
    shortName: 'Java',
    icon: 'https://unizopublicpaas.blob.core.windows.net/svg/java-original.svg',
    description: 'OkHttp client, Jackson serialization, Spring Boot integration',
    docLink: 'https://docs.unizo.ai/docs/sdks/java',
  },
];

interface DockProfile {
  href: string;
  type: string;
  id: string;
  state: string;
  pageLayout: string;
  name: string;
  displayId: string;
  frontendUrl: string;
  customConfigs: any[];
  tags: any[];
  organization: {
    id: string;
  };
  environment: {
    id: string;
  };
  userFlow: {
    type: string;
  };
  notifications: any[];
  changeLog: {
    createdDateTime: string;
    lastUpdatedDateTime: string;
  };
}

interface DockProfilesResponse {
  pagination: {
    total: number;
    offset: number;
    previous: number;
    next: number;
  };
  data: DockProfile[];
}

interface MakeApiCallTabProps {
  onConnectUIStatusChange?: (enabled: boolean) => void;
  onApiKeyGenerated?: (generated: boolean) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  isFirstTab?: boolean;
  isLastTab?: boolean;
  nextLabel?: string;
  previousLabel?: string;
}

export default function MakeApiCallTab({ 
  onConnectUIStatusChange,
  onApiKeyGenerated,
  onNext,
  onPrevious,
  isFirstTab,
  isLastTab,
  nextLabel,
  previousLabel,
}: MakeApiCallTabProps) {
  const theme = useTheme();
  const [copied, setCopied] = useState(false);
  const { accessKey, generate, createdAt, expiresAt, isPending } = useAccesskey();
  const [apiKeyGenerated, setApiKeyGenerated] = useState(true);
  const [apiKey, setApiKey] = useState('********************************');
  const [dockProfiles, setDockProfiles] = useState<DockProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectUIEnabled, setConnectUIEnabled] = useState(false);
  const [enableError, setEnableError] = useState<string | null>(null);
  const [apiKeyError, setApiKeyError] = useState<string | null>(null);
  
  // New state for integration path configuration
  const [selectedApproach, setSelectedApproach] = useState<'connect-ui' | 'apis' | null>(null);
  const [customDomainEnabled, setCustomDomainEnabled] = useState(false);
  const [customDomain, setCustomDomain] = useState('https://connect.yourapp.com');
  const [isSavingConfiguration, setIsSavingConfiguration] = useState(false);
  const [configurationSaved, setConfigurationSaved] = useState(false);
  const [isSavingOnContinue, setIsSavingOnContinue] = useState(false);
  const [isGeneratingTestKey, setIsGeneratingTestKey] = useState(false);
  const [generatedServiceKey, setGeneratedServiceKey] = useState<string | null>(null);
  const [testRunError, setTestRunError] = useState<string | null>(null);
  const [testModalOpen, setTestModalOpen] = useState(false);

  const handleGenerateApiKey = async () => {
    try {
      setApiKeyError(null);
      await generate();
      if (onApiKeyGenerated) {
        onApiKeyGenerated(true);
      }
    } catch (error: any) {
      // Handle different error scenarios
      if (error.response?.status === 504) {
        setApiKeyError('Gateway timeout. Please try again in a few moments.');
      } else if (error.response?.status >= 500) {
        setApiKeyError('Server error. Please try again later.');
      } else if (error.response?.status >= 400) {
        setApiKeyError(error.response?.data?.message || 'Failed to generate API key. Please try again.');
      } else if (error.message === 'Network Error') {
        setApiKeyError('Network error. Please check your connection and try again.');
      } else {
        setApiKeyError('An unexpected error occurred. Please try again.');
      }
      console.error('API Key generation error:', error);
    }
  };

  const handleCopyApiKey = async () => {
    try {
      if (!accessKey) {
        console.error('No API key to copy');
        return;
      }
      
      // Use modern clipboard API with fallback
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(accessKey);
      } else {
        // Fallback for older browsers or non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = accessKey;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      }
      
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy API key:', error);
      setApiKeyError('Failed to copy API key. Please try selecting and copying manually.');
    }
  };

  // Notify parent when API key is available
  useEffect(() => {
    if (accessKey && onApiKeyGenerated) {
      onApiKeyGenerated(true);
      setApiKeyError(null); // Clear error on success
    }
  }, [accessKey, onApiKeyGenerated]);

  // Recover previously generated service key from session storage
  useEffect(() => {
    try {
      const storedKey = sessionStorage.getItem('unizo_demo_service_key');
      const storedUrl = sessionStorage.getItem('unizo_demo_service_url');
      
      if (storedKey) {
        const parsed = JSON.parse(storedKey);
        // Check if key is less than 1 hour old
        if (parsed.timestamp && Date.now() - parsed.timestamp < 3600000) {
          setGeneratedServiceKey(parsed.key);
        } else {
          sessionStorage.removeItem('unizo_demo_service_key');
        }
      } else if (storedUrl) {
        const parsed = JSON.parse(storedUrl);
        if (parsed.timestamp && Date.now() - parsed.timestamp < 3600000) {
          setGeneratedServiceKey(parsed.url);
        } else {
          sessionStorage.removeItem('unizo_demo_service_url');
        }
      }
    } catch (error) {
      console.error('Error recovering stored service key:', error);
      // Clean up corrupted data
      sessionStorage.removeItem('unizo_demo_service_key');
      sessionStorage.removeItem('unizo_demo_service_url');
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear any pending timeouts
      if (isGeneratingTestKey) {
        setIsGeneratingTestKey(false);
      }
      if (isSavingConfiguration) {
        setIsSavingConfiguration(false);
      }
    };
  }, []);

  // Fetch dock profiles on component mount
  useEffect(() => {
    const fetchDockProfiles = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await DocksClient.getDockProfiles();

        // if (!response.ok) {
        //   if (response.status === 404) {
        //     // No dock profiles exist yet
        //     setDockProfiles([]);
        //     setConnectUIEnabled(false);
        //     return;
        //   }
        //   throw new Error(`Failed to fetch dock profiles: ${response.status}`);
        // }

        const data: DockProfilesResponse = response?.data;

        const dockProfiles: DockProfilesResponse['data'] = data.data?.filter((i) => i?.state === State.ACTIVE) || [];

        setDockProfiles(dockProfiles);

        // Set toggle state based on whether profiles exist
        const hasProfiles = dockProfiles.length > 0;
        setConnectUIEnabled(hasProfiles);
        if (hasProfiles) {
          setSelectedApproach('connect-ui');
          setConfigurationSaved(true);
        }
        onConnectUIStatusChange?.(hasProfiles);
      } catch (err) {
        console.error('Error fetching dock profiles:', err);
        // Don't show error for 404 as it means no profiles exist yet
        if (err instanceof Error && !err.message.includes('404')) {
          setError(err.message);
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchDockProfiles();
  }, [onConnectUIStatusChange]);

  const handleSaveConfiguration = async () => {
    if (!selectedApproach || selectedApproach !== 'connect-ui') {
      console.warn('Invalid state for saving configuration');
      return;
    }
    
    setEnableError(null);
    setIsSavingConfiguration(true);

    try {
      // Validate custom domain if enabled
      if (customDomainEnabled && !isValidCustomDomain(customDomain)) {
        throw new Error('Invalid custom domain URL. Please ensure it starts with https://');
      }

      const frontendUrl = customDomainEnabled && customDomain ? customDomain : 'https://dock.unizo.ai';

      // Only create if no dock profiles exist
      if (!dockProfiles || dockProfiles.length === 0) {
        const requestPayload = {
          type: 'CUSTOM',
          pageLayout: 'EMBEDDED',
          name: 'Quick Start',
          userFlow: {
            type: 'CATEGORY'
          },
          frontendUrl: frontendUrl,
          enableScopedAccess: false,
          metadata: {
            source: 'quickstart',
            createdAt: new Date().toISOString(),
            customDomainEnabled
          }
        };

        try {
          const response = await DocksClient.createDockProfiles(requestPayload);

          if (!response?.data) {
            throw new Error('Invalid response from server');
          }

          const newDockProfile: DockProfile = response.data;
          setDockProfiles([newDockProfile]);
          
          // Store successful configuration
          localStorage.setItem('unizo_connect_ui_configured', 'true');
          localStorage.setItem('unizo_connect_ui_timestamp', Date.now().toString());
        } catch (fetchError: any) {
          console.error('Dock profile creation error:', fetchError);
          throw fetchError;
        }
      }

      setConnectUIEnabled(true);
      setConfigurationSaved(true);
      onConnectUIStatusChange?.(true);
      setEnableError(null);
      
      // Reset test key state when configuration changes
      setGeneratedServiceKey(null);
      sessionStorage.removeItem('unizo_demo_service_key');
      sessionStorage.removeItem('unizo_demo_service_url');
    } catch (err: any) {
      console.error('Error saving configuration:', err);
      
      let errorMessage = 'Unable to save configuration. ';
      
      if (err?.response?.status === 401) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (err?.response?.status === 403) {
        errorMessage = 'You don\'t have permission to enable Connect UI. Please contact your administrator.';
      } else if (err?.response?.status === 409) {
        errorMessage = 'A configuration already exists. Please refresh the page.';
      } else if (err?.response?.status === 422) {
        errorMessage = 'Invalid configuration data. Please check your settings.';
      } else if (err?.response?.status >= 500) {
        errorMessage = 'Server error. Please try again in a few moments.';
      } else if (err?.message?.toLowerCase().includes('network')) {
        errorMessage = 'Network error. Please check your connection.';
      } else if (err?.message) {
        errorMessage = err.message;
      } else {
        errorMessage += 'Please try again or contact support.';
      }
      
      setEnableError(errorMessage);
      setConfigurationSaved(false);
    } finally {
      setIsSavingConfiguration(false);
    }
  };

  // Validate custom domain
  const isValidCustomDomain = (domain: string) => {
    try {
      const url = new URL(domain);
      return url.protocol === 'https:';
    } catch {
      return false;
    }
  };

  const { attemptCreateServiceKey } = useGetDockProfile();
  const { categories } = useUserDetails();

  const onLaunchDemo = async () => {
    // Prevent multiple simultaneous requests
    if (isGeneratingTestKey) {
      console.warn('Test key generation already in progress');
      return;
    }

    // Reset state
    setIsGeneratingTestKey(true);
    setTestRunError(null);
    setGeneratedServiceKey(null);
    
    try {
      // Validate prerequisites
      if (!categories || categories.length === 0) {
        throw new Error('No service categories available. Please ensure your subscription includes at least one service category.');
      }

      const enabledCategories = categories.filter((cat) => !cat?.disabled);
      if (enabledCategories.length === 0) {
        throw new Error('All service categories are disabled. Please enable at least one category to proceed.');
      }

      // Validate organization context
      const { user } = useUserDetails.getState();
      if (!user?.organization?.id) {
        throw new Error('Organization context not found. Please refresh the page and try again.');
      }

      // Generate unique identifiers
      const customerKey = generateUUID();
      const customerName = 'Connect UI Test Run';
      
      // Get the dock profile ID from the first dock profile
      const dockProfileId = dockProfiles && dockProfiles.length > 0 ? dockProfiles[0].id : null;
      if (!dockProfileId) {
        throw new Error('No Connect UI profile found. Please configure Connect UI first.');
      }
      
      // Build payload with validation
      const payload = {
        type: "INTEGRATION_TOKEN",
        name: customerName,
        subOrganization: {
          name: customerName,
          externalKey: customerKey
        },
        integration: {
          type: "GENERIC",
          target: {
            type: "Category",
            categorySelectors: enabledCategories.map((cat) => ({
              type: cat.value || cat.type || cat.name // Use the category type value
            }))
          }
        },
        dockProfile: {
          id: dockProfileId
        }
      };

      // Add timeout handling
      const timeoutId = setTimeout(() => {
        setTestRunError('Request timed out. Please check your connection and try again.');
        setIsGeneratingTestKey(false);
      }, 30000); // 30 second timeout

      attemptCreateServiceKey(
        payload, 
        (data: any) => {
          clearTimeout(timeoutId);
          
          try {
            // Validate response structure
            if (!data || typeof data !== 'object') {
              throw new Error('Invalid response format');
            }

            // Extract token/URL with multiple fallback options
            const token = data?.data?.integrationToken || 
                         data?.integrationToken ||
                         data?.data?.token ||
                         data?.token;

            const url = data?.data?.formDescriptorUrl || 
                       data?.formDescriptorUrl ||
                       data?.data?.url ||
                       data?.url;

            if (token) {
              setGeneratedServiceKey(token);
              // Store in session storage for recovery
              sessionStorage.setItem('unizo_demo_service_key', JSON.stringify({
                key: token,
                timestamp: Date.now(),
                customerKey
              }));
            } else if (url) {
              setGeneratedServiceKey(url);
              sessionStorage.setItem('unizo_demo_service_url', JSON.stringify({
                url,
                timestamp: Date.now(),
                customerKey
              }));
            } else {
              throw new Error('No service key or URL received in response');
            }

            setIsGeneratingTestKey(false);
            setTestRunError(null);
          } catch (parseError) {
            console.error('Error parsing response:', parseError);
            setTestRunError('Received invalid response format. Please try again.');
            setIsGeneratingTestKey(false);
          }
        }, 
        (error: any) => {
          clearTimeout(timeoutId);
          console.error('Service key generation failed:', error);
          
          // Enhanced error handling with specific messages
          let errorMessage = 'Failed to generate test key. ';
          
          if (error?.response?.status === 401) {
            errorMessage = 'Authentication failed. Please log in again.';
          } else if (error?.response?.status === 403) {
            errorMessage = 'You don\'t have permission to generate test keys. Please contact your administrator.';
          } else if (error?.response?.status === 429) {
            errorMessage = 'Too many requests. Please wait a moment and try again.';
          } else if (error?.response?.status >= 500) {
            errorMessage = 'Server error. Please try again in a few moments.';
          } else if (error?.message?.toLowerCase().includes('network')) {
            errorMessage = 'Network error. Please check your internet connection.';
          } else if (error?.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error?.message) {
            errorMessage += error.message;
          } else {
            errorMessage += 'Please try again or contact support if the issue persists.';
          }
          
          setTestRunError(errorMessage);
          setIsGeneratingTestKey(false);
        }
      );
    } catch (error: any) {
      console.error('Error in onLaunchDemo:', error);
      setTestRunError(error.message || 'An unexpected error occurred. Please refresh the page and try again.');
      setIsGeneratingTestKey(false);
    }
  };

  return (
    <Box>
      <Stack spacing={3}>
        {/* Section 1: API Key */}
        <Box
          sx={{
            backgroundColor: theme.palette.background.default,
            borderRadius: 1,
            p: 4,
          }}
        >
          <Stack spacing={4}>
            {/* Header */}
            <Box>
              <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 2 }}>
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: theme.palette.common.white,
                  }}
                >
                  <Key size={18} strokeWidth={3} />
                </Box>
                <Typography variant="h5" fontWeight={600}>
                  API Key
                </Typography>
              </Stack>
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.mode === 'light'
                    ? theme.palette.grey[600]
                    : theme.palette.text.secondary,
                  maxWidth: 800,
                }}
              >
                Authenticate your product with Unizo's APIs. You'll include this key in server side requests.
              </Typography>
            </Box>

            {/* API Key Card */}
            <Box sx={{ maxWidth: 800 }}>
              <Box
                sx={{
                  backgroundColor: theme.palette.background.paper,
                  borderRadius: 1,
                  p: 4,
                }}
              >
                {/* API Key Generation */}
                {!accessKey ? (
                  <Box>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 1.5,
                        color: theme.palette.mode === 'light'
                          ? theme.palette.grey[700]
                          : theme.palette.text.primary,
                        fontWeight: 500,
                      }}
                    >
                      You need an API key to call REST APIs or use our SDKs. If you haven't generated one yet, generate it here:
                    </Typography>
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<Key size={16} />}
                      onClick={handleGenerateApiKey}
                      disabled={isPending}
                      sx={{
                        textTransform: 'none',
                        fontWeight: 500,
                        px: 2,
                        py: 1,
                      }}
                    >
                      {isPending ? 'Generating...' : 'Generate API Key'}
                    </Button>
                    {apiKeyError && (
                      <Alert 
                        severity="error" 
                        sx={{ 
                          mt: 2,
                          '& .MuiAlert-icon': {
                            fontSize: '20px',
                          },
                        }}
                        onClose={() => setApiKeyError(null)}
                      >
                        {apiKeyError}
                      </Alert>
                    )}
                  </Box>
                ) : (
                  <Box>
                    <Box
                      sx={{
                        p: 2,
                        backgroundColor: alpha(theme.palette.warning.main, 0.08),
                        borderRadius: 1,
                        border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`,
                        mb: 2,
                      }}
                    >
                      <Typography
                        variant="caption"
                        sx={{
                          color: theme.palette.warning.dark,
                          fontWeight: 500,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 0.5,
                        }}
                      >
                        <Sparkles size={14} />
                        Keep this API key safe. It will not be shown again.
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        gap: 1,
                        p: 1.5,
                        backgroundColor: theme.palette.mode === 'light'
                          ? theme.palette.grey[50]
                          : alpha(theme.palette.grey[800], 0.5),
                        borderRadius: 1,
                                    mb: 2,
                      }}
                    >                       
                        <Typography
                        variant="body2"
                        sx={{
                          fontFamily: 'monospace',
                          flex: 1,
                          color: theme.palette.text.primary,
                        }}
                      >
                        {accessKey}
                      </Typography>
                      <CopyableText text={accessKey as string}/>
                    </Box>
                    <Stack direction="row" spacing={4}>
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Created
                        </Typography>
                        <Typography variant="body2">{createdAt ?? '-'}</Typography>
                      </Box>
                      <Box>
                        <Typography variant="caption" color="text.secondary">
                          Expires
                        </Typography>
                        <Typography variant="body2">{expiresAt ?? '-'}</Typography>
                      </Box>
                    </Stack>
                  </Box>
                )}
              </Box>
            </Box>
          </Stack>
        </Box>

        {/* Section 2: Integration Path */}
        <Box
          sx={{
            backgroundColor: theme.palette.background.default,
            borderRadius: 1,
            p: 4,
          }}
        >
          <Stack spacing={4}>
            {/* Header */}
            <Box>
              <Stack direction="row" alignItems="center" spacing={1.5} sx={{ mb: 2 }}>
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.primary.main,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: theme.palette.common.white,
                  }}
                >
                  <Code2 size={18} strokeWidth={3} />
                </Box>
                <Typography variant="h5" fontWeight={600}>
                  How Users Link and Authorize Their Tools
                </Typography>
              </Stack>
              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.mode === 'light'
                    ? theme.palette.grey[600]
                    : theme.palette.text.secondary,
                  maxWidth: 800,
                }}
              >
                Guide how your app handles authentication when users connect tools.
              </Typography>

            </Box>

            {/* Integration Configuration Card */}
            <Box sx={{ maxWidth: 800 }}>
              <Box
                sx={{
                  backgroundColor: theme.palette.background.paper,
                  borderRadius: 1,
                  p: 3,
                }}
              >
                {/* Step 1 - Choose your approach */}
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant="body1"
                    sx={{
                      fontWeight: 600,
                      mb: 1,
                      color: theme.palette.text.primary,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                    }}
                  >
                    Choose the link & auth experience
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: theme.palette.text.secondary,
                      mb: 2,
                      mt: 0.5,
                    }}
                  >
                    Use Connect UI (embedded, fully managed) for the fastest launch, or build a custom UI using Unizo's APIs for maximum flexibility.
                  </Typography>
                  
                  {/* Option Cards */}
                  <Stack spacing={2}>
                    {/* Enable Connect UI Option */}
                    <Box
                      sx={{
                        p: 2,
                        border: `2px solid ${selectedApproach === 'connect-ui' ? theme.palette.primary.main : theme.palette.divider}`,
                        borderRadius: 1.5,
                        backgroundColor: selectedApproach === 'connect-ui'
                          ? alpha(theme.palette.primary.main, 0.04)
                          : theme.palette.background.default,
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          borderColor: theme.palette.primary.main,
                          backgroundColor: alpha(theme.palette.primary.main, 0.04),
                        },
                      }}
                      onClick={() => {
                        setSelectedApproach('connect-ui');
                        setConfigurationSaved(false);
                      }}
                    >
                      <Stack direction="row" spacing={1.5} alignItems="flex-start">
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            border: `2px solid ${selectedApproach === 'connect-ui' ? theme.palette.primary.main : theme.palette.divider}`,
                            backgroundColor: selectedApproach === 'connect-ui' ? theme.palette.primary.main : 'transparent',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mt: 0.5,
                            flexShrink: 0,
                          }}
                        >
                          {selectedApproach === 'connect-ui' && (
                            <Box
                              sx={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                backgroundColor: theme.palette.common.white,
                              }}
                            />
                          )}
                        </Box>
                        <Box sx={{ flex: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.25 }}>
                            <Typography variant="body2" fontWeight={600}>
                              Connect UI - Managed Auth
                            </Typography>
                            <Box
                              sx={{
                                px: 1,
                                py: 0.25,
                                backgroundColor: alpha(theme.palette.success.main, 0.1),
                                color: theme.palette.success.main,
                                borderRadius: 1,
                                fontSize: '0.75rem',
                                fontWeight: 600,
                              }}
                            >
                              Recommended
                            </Box>
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            Embed a Unizo widget to seamlessly manage OAuth and PAT flows, token management, and
                            error handling. This option provides fully managed authentication across providers,
                            eliminating the need to handle credentials within your app. It is the fastest way to launch,
                            ensures a consistent user experience, and leverages Unizo’s secure token storage.
                          </Typography>
                        </Box>
                      </Stack>
                    </Box>

                    {/* Use APIs Option */}
                    <Box
                      component="button"
                      type="button"
                      sx={{
                        width: '100%',
                        p: 2.5,
                        border: `1px solid`,
                        borderColor: selectedApproach === 'apis' 
                          ? theme.palette.primary.main 
                          : theme.palette.divider,
                        borderRadius: 1,
                        backgroundColor: selectedApproach === 'apis'
                          ? alpha(theme.palette.primary.main, 0.08)
                          : theme.palette.background.paper,
                        cursor: 'pointer',
                        transition: theme.transitions.create(['border-color', 'background-color', 'box-shadow'], {
                          duration: theme.transitions.duration.short,
                        }),
                        display: 'block',
                        textAlign: 'left',
                        position: 'relative',
                        overflow: 'hidden',
                        '&:hover': {
                          borderColor: selectedApproach === 'apis' 
                            ? theme.palette.primary.main 
                            : theme.palette.action.hover,
                          backgroundColor: selectedApproach === 'apis'
                            ? alpha(theme.palette.primary.main, 0.08)
                            : theme.palette.action.hover,
                        },
                        '&:focus': {
                          outline: 'none',
                        },
                        '&:focus-visible': {
                          outline: `2px solid ${theme.palette.primary.main}`,
                          outlineOffset: 2,
                        },
                      }}
                      onClick={() => {
                        setSelectedApproach('apis');
                        setConfigurationSaved(false);
                      }}
                      aria-pressed={selectedApproach === 'apis'}
                      aria-label="Use APIs to build your own UI"
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                        {/* Radio Button Indicator */}
                        <Box
                          sx={{
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            border: `2px solid`,
                            borderColor: selectedApproach === 'apis' 
                              ? theme.palette.primary.main 
                              : theme.palette.action.disabled,
                            backgroundColor: selectedApproach === 'apis' 
                              ? theme.palette.primary.main 
                              : 'transparent',
                            mt: 0.25,
                            flexShrink: 0,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            transition: theme.transitions.create(['border-color', 'background-color']),
                          }}
                        >
                          {selectedApproach === 'apis' && (
                            <Box
                              sx={{
                                width: 8,
                                height: 8,
                                borderRadius: '50%',
                                backgroundColor: theme.palette.primary.contrastText,
                              }}
                            />
                          )}
                        </Box>
                        
                        {/* Text Content */}
                        <Box sx={{ flex: 1, minWidth: 0 }}>
                          <Typography 
                            variant="body2" 
                            component="div"
                            sx={{ 
                              fontWeight: 600,
                              color: theme.palette.text.primary,
                              mb: 0.25,
                            }}
                          >
                            APIs Only - Build your Own UI
                          </Typography>
                          <Typography 
                            variant="caption" 
                            component="div"
                            sx={{
                              color: theme.palette.text.secondary,
                              lineHeight: 1.5,
                            }}
                          >
                            Implement your own screens by calling Unizo APIs and managing authentication details
                            directly. This approach gives you full control of the user experience and credential intake,
                            while also making you responsible for token handling and error management. It offers maximum
                            flexibility but requires more engineering effort and ongoing maintenance.
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Stack>
                </Box>

                {/* Conditional Steps 2 & 3 - Only show when Connect UI is selected */}
                {selectedApproach === 'connect-ui' && (
                  <>
                    {/* Step 2 - Change Connect UI's URL using Custom Domains */}
                    <Box sx={{ mb: 4 }}>
                      <Typography
                        variant="body1"
                        sx={{
                          fontWeight: 600,
                          mb: 2,
                          color: theme.palette.text.primary,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                        }}
                      >
                        Change Connect UI's URL using Custom Domain
                      </Typography>
                      <Typography
                        variant="body2"
                        sx={{
                          color: theme.palette.text.secondary,
                          mb: 2,
                        }}
                      >
                        By default, Connect UI loads from <code style={{ 
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          padding: '2px 6px',
                          borderRadius: '4px',
                          fontSize: '0.875rem',
                        }}>https://dock.unizo.ai</code> in your product's web UI (rendered in your user's browser).
                        If preferred, you can configure a <strong>custom domain</strong> so the URL matches your application. This requires a small network/DNS configuration on your web UI's domain.
                      </Typography>

                      {/* Custom domain checkbox */}
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={customDomainEnabled}
                            onChange={(e) => {
                              setCustomDomainEnabled(e.target.checked);
                              setConfigurationSaved(false);
                            }}
                            sx={{
                              '&.Mui-checked': {
                                color: theme.palette.primary.main,
                              },
                            }}
                          />
                        }
                        label={
                          <Typography variant="body2" fontWeight={500}>
                            Enable custom domain
                          </Typography>
                        }
                      />
                      
                      {/* Custom domain input field */}
                      {customDomainEnabled && (
                        <TextField
                          fullWidth
                          size="small"
                          value={customDomain}
                          onChange={(e) => {
                            setCustomDomain(e.target.value);
                            setConfigurationSaved(false);
                          }}
                          error={customDomainEnabled && !isValidCustomDomain(customDomain)}
                          helperText={
                            customDomainEnabled && !isValidCustomDomain(customDomain)
                              ? 'Please enter a valid HTTPS URL'
                              : ''
                          }
                          sx={{
                            mt: 1.5,
                            '& .MuiOutlinedInput-root': {
                              fontFamily: 'monospace',
                            },
                          }}
                        />
                      )}
                    </Box>

                    {/* Configuration Error */}
                    {enableError && (
                      <Alert 
                        severity="error" 
                        sx={{ 
                          mt: 3,
                          '& .MuiAlert-icon': {
                            fontSize: '20px',
                          },
                        }}
                        onClose={() => setEnableError(null)}
                      >
                        <Typography variant="body2">
                          {enableError}
                        </Typography>
                      </Alert>
                    )}

                    {/* Success Message and Test Run */}
                    {configurationSaved && (
                      <Box sx={{ mt: 3 }}>
                        {/* Test Run Section */}
                        <Box>
                          <Typography 
                            variant="body2" 
                            fontWeight={600} 
                            sx={{ mb: 2 }}
                          >
                            Test your Connect UI
                          </Typography>
                          
                          <Button
                            variant="contained"
                            size="medium"
                            startIcon={<Play size={18} />}
                            onClick={() => {
                              if (!generatedServiceKey) {
                                onLaunchDemo();
                              }
                              setTestModalOpen(true);
                            }}
                            disabled={isGeneratingTestKey}
                            sx={{
                              textTransform: 'none',
                              fontWeight: 500,
                            }}
                          >
                            {isGeneratingTestKey ? 'Generating...' : 'Test Run'}
                          </Button>
                        </Box>
                      </Box>
                    )}
                  </>
                )}
              </Box>
            </Box>
          </Stack>
        </Box>
      </Stack>
      
      {/* Test Modal - Using ConnectUIModal when URL is ready, otherwise custom Dialog for loading state */}
      {testModalOpen && generatedServiceKey && !isGeneratingTestKey ? (
        <ConnectUIModal
          opened={testModalOpen}
          onClosed={() => {
            setTestModalOpen(false);
            setTestRunError(null);
          }}
          magicLink={generatedServiceKey}
        />
      ) : (
        <Dialog
          open={testModalOpen && (isGeneratingTestKey || !generatedServiceKey)}
          onClose={(event, reason) => {
            if (reason === "backdropClick") return; // Prevent closing on backdrop click
            setTestModalOpen(false);
          }}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle variant="h5">Test your Connect UI</DialogTitle>
          <DialogClose onClose={() => setTestModalOpen(false)} />
          <DialogContent dividers>
            {isGeneratingTestKey ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, py: 3 }}>
                <CircularProgress size={20} />
                <Typography variant="body1" color="text.secondary">
                  Generating test session...
                </Typography>
              </Box>
            ) : (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="body1" color="text.secondary">
                  Click "Test Run" to generate a test URL
                </Typography>
              </Box>
            )}
            
            {testRunError && (
              <Alert 
                severity="error" 
                sx={{ 
                  mt: 2,
                  '& .MuiAlert-icon': {
                    fontSize: '20px',
                  },
                }}
                onClose={() => setTestRunError(null)}
              >
                {testRunError}
              </Alert>
            )}
          </DialogContent>
        </Dialog>
      )}
      
      <TabNavigationFooter
        onNext={async () => {
          // If Connect UI is selected and not saved, save before continuing
          if (selectedApproach === 'connect-ui' && !configurationSaved) {
            setIsSavingOnContinue(true);
            
            try {
              await handleSaveConfiguration();
              
              // Only proceed to next step if save was successful
              if (onNext) {
                onNext();
              }
            } catch (error) {
              console.error('Failed to save configuration:', error);
              // Don't proceed to next step if save failed
            } finally {
              setIsSavingOnContinue(false);
            }
          } else {
            // Already saved or using APIs, just continue
            if (onNext) {
              onNext();
            }
          }
        }}
        onPrevious={onPrevious}
        isFirstTab={isFirstTab}
        isLastTab={isLastTab}
        nextLabel={
          selectedApproach === 'connect-ui' && !configurationSaved
            ? (isSavingOnContinue ? 'Saving...' : 'Save & Continue')
            : (nextLabel || 'Continue')
        }
        previousLabel={previousLabel}
        isNextDisabled={
          // Disable next if Connect UI is selected and has validation errors or is saving
          isSavingOnContinue ||
          (selectedApproach === 'connect-ui' && 
           customDomainEnabled && 
           !isValidCustomDomain(customDomain))
        }
        showSkipButton={selectedApproach === 'connect-ui' && !configurationSaved && !isSavingOnContinue}
        onSkip={() => {
          if (onNext) {
            onNext();
          }
        }}
        skipLabel="Skip for now"
      />
    </Box>
  );
}