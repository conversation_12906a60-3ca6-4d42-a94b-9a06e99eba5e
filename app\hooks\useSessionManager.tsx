import { useEffect, useCallback, useRef } from 'react';
import { useNavigate } from '@remix-run/react';
import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { toast } from 'sonner';

// Configuration constants
const SESSION_CHECK_INTERVAL = 60000; // Check every 60 seconds
const SESSION_WARNING_TIME = 300000; // Show warning 5 minutes before expiry
const TOKEN_REFRESH_BUFFER = 60000; // Refresh token 1 minute before expiry

interface SessionConfig {
  checkInterval?: number;
  warningTime?: number;
  refreshBuffer?: number;
  onSessionExpired?: () => void;
  onSessionWarning?: (timeRemaining: number) => void;
  enabled?: boolean;
}

interface SessionStatus {
  isValid: boolean;
  expiresAt?: number;
  refreshToken?: string;
}

export const useSessionManager = (config: SessionConfig = {}) => {
  const navigate = useNavigate();
  const checkIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const warningTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastActivityRef = useRef<number>(Date.now());
  
  const {
    checkInterval = SESSION_CHECK_INTERVAL,
    warningTime = SESSION_WARNING_TIME,
    refreshBuffer = TOKEN_REFRESH_BUFFER,
    onSessionExpired,
    onSessionWarning,
    enabled = true
  } = config;

  // Check session status with backend
  const checkSessionMutation = useMutation({
    mutationFn: async (): Promise<SessionStatus> => {
      try {
        const response = await axios.get('/api/auth/session/check', {
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache'
          },
          withCredentials: true
        });
        return response.data;
      } catch (error) {
        if (axios.isAxiosError(error) && error.response?.status === 401) {
          return { isValid: false };
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      if (!data.isValid) {
        handleSessionExpired();
      } else if (data.expiresAt) {
        scheduleSessionWarning(data.expiresAt);
        scheduleTokenRefresh(data.expiresAt);
      }
    },
    onError: (error) => {
      console.error('Session check failed:', error);
      // Don't logout on network errors, only on explicit 401s
    }
  });

  // Refresh session token
  const refreshSessionMutation = useMutation({
    mutationFn: async (): Promise<SessionStatus> => {
      const response = await axios.post('/api/auth/session/refresh', {}, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true
      });
      return response.data;
    },
    onSuccess: (data) => {
      if (data.isValid && data.expiresAt) {
        scheduleSessionWarning(data.expiresAt);
        scheduleTokenRefresh(data.expiresAt);
      }
    },
    onError: (error) => {
      console.error('Token refresh failed:', error);
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        handleSessionExpired();
      }
    }
  });

  // Handle session expiration
  const handleSessionExpired = useCallback(() => {
    // Clear all intervals and timeouts
    if (checkIntervalRef.current) {
      clearInterval(checkIntervalRef.current);
    }
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }

    // Clear local storage
    localStorage.removeItem('authToken');
    sessionStorage.clear();

    // Call custom handler if provided
    if (onSessionExpired) {
      onSessionExpired();
    } else {
      toast.error('Your session has expired. Please login again.');
      
      // Redirect to login with return URL
      const currentPath = window.location.pathname + window.location.search;
      navigate(`/login?returnUrl=${encodeURIComponent(currentPath)}`, { 
        replace: true 
      });
    }
  }, [navigate, onSessionExpired]);

  // Schedule session warning
  const scheduleSessionWarning = useCallback((expiresAt: number) => {
    if (warningTimeoutRef.current) {
      clearTimeout(warningTimeoutRef.current);
    }

    const timeUntilExpiry = expiresAt - Date.now();
    const timeUntilWarning = timeUntilExpiry - warningTime;

    if (timeUntilWarning > 0) {
      warningTimeoutRef.current = setTimeout(() => {
        if (onSessionWarning) {
          onSessionWarning(warningTime);
        } else {
          toast.warning('Your session will expire soon. Please save your work.');
        }
      }, timeUntilWarning);
    }
  }, [warningTime, onSessionWarning]);

  // Schedule token refresh
  const scheduleTokenRefresh = useCallback((expiresAt: number) => {
    const timeUntilExpiry = expiresAt - Date.now();
    const timeUntilRefresh = timeUntilExpiry - refreshBuffer;

    if (timeUntilRefresh > 0) {
      setTimeout(() => {
        refreshSessionMutation.mutate();
      }, timeUntilRefresh);
    }
  }, [refreshBuffer, refreshSessionMutation]);

  // Track user activity
  const trackActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
  }, []);

  // Setup activity tracking
  useEffect(() => {
    if (!enabled) return;

    const events = ['mousedown', 'keydown', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      window.addEventListener(event, trackActivity);
    });

    return () => {
      events.forEach(event => {
        window.removeEventListener(event, trackActivity);
      });
    };
  }, [enabled, trackActivity]);

  // Setup session checking interval
  useEffect(() => {
    if (!enabled) return;

    // Initial check
    checkSessionMutation.mutate();

    // Setup periodic checks
    checkIntervalRef.current = setInterval(() => {
      checkSessionMutation.mutate();
    }, checkInterval);

    return () => {
      if (checkIntervalRef.current) {
        clearInterval(checkIntervalRef.current);
      }
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
      }
    };
  }, [enabled, checkInterval, checkSessionMutation]);

  // Handle visibility change (tab focus/blur)
  useEffect(() => {
    if (!enabled) return;

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Check session when tab becomes visible
        checkSessionMutation.mutate();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [enabled, checkSessionMutation]);

  return {
    checkSession: () => checkSessionMutation.mutate(),
    refreshSession: () => refreshSessionMutation.mutate(),
    isChecking: checkSessionMutation.isPending,
    isRefreshing: refreshSessionMutation.isPending,
    lastActivity: lastActivityRef.current
  };
};