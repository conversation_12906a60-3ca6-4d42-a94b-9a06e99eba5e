import { API_ENDPOINTS } from "utils/api/api-endpoints";
import fetchInstance from "utils/api/fetchinstance";

export const fieldMappingsClient = {
   createFieldMappings: (serviceId: string, payload: object) => {
      return fetchInstance.post(`${API_ENDPOINTS.SERVICE}s/${serviceId}/fieldMappings`, payload)
   },
   updateFieldMappings: (serviceId: string, fieldMappingId: string, payload: object) => {
      return fetchInstance.patch(`${API_ENDPOINTS.SERVICE}s/${serviceId}/fieldMappings/${fieldMappingId}`, payload)
   },
   searchFieldMappings: (serviceId: string, payload: object) => {
      return fetchInstance.post(`${API_ENDPOINTS.SERVICE}s/${serviceId}/fieldMappings/search`, payload)
   },
   getFieldMappings: (serviceId: string, params: object) => {
      return fetchInstance.get(`${API_ENDPOINTS.SERVICE}s/${serviceId}/fieldMappings`, { params })
   }
};
