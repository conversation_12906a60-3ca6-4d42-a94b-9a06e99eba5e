/* eslint-disable @typescript-eslint/no-explicit-any */
import { Stack, Chip, Typography, Theme, useTheme, alpha } from "@mui/material";
import { ROLE_ICONS } from '../constants';
import { ROLE_MAPPER } from "hooks/api/permission/usePermission";

interface Member {
   id: string;
   email: string;
   role: string;
}

interface ChipListProps {
   members: Member[];
   onRemove: (id: string) => void;
}

const ChipList = ({ members, onRemove }: ChipListProps) => {
   const theme = useTheme();

   return (
      <Stack
         direction="row"
         flexWrap="wrap"
         sx={{
            gap: 1,
            minHeight: 32,
            pt: 0.5,
            '& > :not(style) ~ :not(style)': {
               marginLeft: '0 !important'
            }
         }}
      >
         {members.map((member) => (
            <Chip
               key={member.id}
               icon={ROLE_ICONS[member.role]}
               label={
                  <Stack direction="row" spacing={1} alignItems="center">
                     <span>{member.email}</span>
                     <Typography
                        component="span"
                        sx={{
                           fontSize: '0.75rem',
                           opacity: 0.7,
                           fontWeight: 400
                        }}
                     >
                        ({(ROLE_MAPPER as any)[member.role]})
                     </Typography>
                  </Stack>
               }
               onDelete={() => onRemove(member.id)}
               sx={style.chip({ theme })}
            />
         ))}
      </Stack>
   );
};

const style = {
   chip: ({ theme }: { theme: Theme }) => ({
      height: 32,
      backgroundColor: alpha(theme.palette.common.black, 0.08),
      border: `1px solid ${alpha(theme.palette.common.black, 0.12)}`,
      borderRadius: '16px',
      transition: 'all 0.2s ease-in-out',
      '& .MuiChip-icon': {
         color: alpha(theme.palette.common.black, 0.75),
         marginLeft: '8px',
         marginRight: '-4px'
      },
      '& .MuiChip-label': {
         fontSize: '0.815rem',
         color: alpha(theme.palette.common.black, 0.75),
         px: 1.5,
         fontWeight: 500,
      },
      '& .MuiChip-deleteIcon': {
         fontSize: '18px',
         color: alpha(theme.palette.common.black, 0.75),
         opacity: 0.7,
         transition: 'all 0.2s ease-in-out',
         '&:hover': {
            opacity: 1,
            color: alpha(theme.palette.common.black, 0.95),
         },
      },
      '&:hover': {
         backgroundColor: alpha(theme.palette.common.black, 0.12),
         border: `1px solid ${alpha(theme.palette.common.black, 0.2)}`,
      }
   })
};

export default ChipList;