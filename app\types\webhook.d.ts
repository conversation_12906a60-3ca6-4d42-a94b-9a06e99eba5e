export type WebhookCategory = 
  | 'Communications'
  | 'Incident management'
  | 'Identity'
  | 'Platform'
  | 'Source Code';

export type WebhookType = 
  | 'PLATFORM_WATCH_HOOK'
  | 'SCM_WATCH_HOOK'
  | 'TICKETING_WATCH_HOOK'
  | 'ORG_ACTIVITY_HOOK'
  | 'CONSUMER_ACTIVITY_HOOK'
  | 'INTEGRATION_ACTIVITY_HOOK'
  | 'RESOURCE_ADDED_HOOK'
  | 'RESOURCE_UPDATED_HOOK'
  | 'RESOURCE_DELETED_HOOK'
  | 'CUSTOM_ACTION_TRIGGERED_HOOK';

export type WebhookStatus = 'active' | 'inactive' | 'error';

export interface Webhook {
  id: string;
  name: string;
  url: string;
  type: WebhookType;
  category: WebhookCategory;
  status: WebhookStatus;
  isActive: boolean;
  organizationId: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateWebhookPayload {
  name: string;
  url: string;
  type: WebhookType;
  category: WebhookCategory;
  isActive: boolean;
}

export interface UpdateWebhookPayload {
  name?: string;
  url?: string;
  type?: WebhookType;
  category?: WebhookCategory;
  isActive?: boolean;
}