import { API_ENDPOINTS } from 'utils/api/api-endpoints';
import fetchInstance from 'utils/api/fetchinstance';

export const serviceClient = {
  searchServices: (payload: any) => {
    return fetchInstance.post(`${API_ENDPOINTS.SERVICE}s/search`, payload);
  },
  
  createService: (payload: any) => {
    return fetchInstance.post(`${API_ENDPOINTS.SERVICE}s`, payload);
  },
  
  updateService: (id: string, payload: any) => {
    return fetchInstance.post(`${API_ENDPOINTS.SERVICE}s/${id}/actions`, payload);
  },
  
  getService: (id: string) => {
    return fetchInstance.get(`${API_ENDPOINTS.SERVICE}s/${id}`);
  }
};

