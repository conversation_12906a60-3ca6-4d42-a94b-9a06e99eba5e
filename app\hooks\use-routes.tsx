import ITEMS from "menu-items"

import { useEffect, useMemo, useState } from "react"
import { useLocation, useNavigate } from "@remix-run/react";
import { UserRoleEnum } from "hooks/api/permission/usePermission";
import { SUPER_ADMIN_EXCLUDES } from 'menu-items/integration';
import { ListItemButton, ListItemIcon, ListItemText } from "@mui/material";
import { UserSwitchOutlined, UsergroupAddOutlined } from "@ant-design/icons";

import useUserDetails from "store/user";
import { ADMIN_BASE_URL, APP_DEFAULT_PATH } from "config";
import menuItem from 'menu-items';
import ADMIN_ITEMS from "menu-items/admin"


const ADMIN_CONSOLE = '/admin'

export const useRoutes = () => {
   const location = useLocation(),
      navigate = useNavigate();


   const { isSupeAdmin,user } = useUserDetails();

   const isAdmin = location.pathname.startsWith(ADMIN_CONSOLE);

   const getFilteredMenu = (items: Array<Record<string, any>>) => {
      return items
        .map((item) => {

          if (item.children) {
            item.children = item.children.filter(
              (child:any) =>
                !child.hideFor?.includes(user?.role?.type) &&
                !(child.hideForSuperAdmin && isSupeAdmin)
            );
          }

          const shouldHideParent =
            (item.hideForSuperAdmin && isSupeAdmin) ||
            item.hideFor?.includes(user?.role?.type);

          return !shouldHideParent ? item : null;
        })
        .filter(Boolean);
    };

   const filtered = useMemo(() => {
      return {
         items: getFilteredMenu(menuItem.items) ?? []
      }
   }, [menuItem, isSupeAdmin])

   return {
      menuItem: (() => {
         if (!isAdmin) return filtered;
         return ADMIN_ITEMS;
      })(),
      getPermissionLinks(items: Record<string, any>[], parentId: string) {
         if (!isSupeAdmin && parentId === 'configuration') {
            return items.concat(...SUPER_ADMIN_EXCLUDES)
         }
         return items;
      },
      isAdmin,
      switchLink: () => {
         return (
            <ListItemButton
               onClick={() => {
                  navigate(isAdmin ? APP_DEFAULT_PATH : ADMIN_BASE_URL)
               }}>
               <ListItemIcon>
                  {!isAdmin ? <UsergroupAddOutlined /> : <UserSwitchOutlined />}
               </ListItemIcon>
               <ListItemText
                  primary={(
                     !isAdmin ? "Admin Console" : "Customer portal"
                  )}
               />
            </ListItemButton>
         )
      }
   }
}