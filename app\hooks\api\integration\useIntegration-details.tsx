import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { parseError } from "lib/utils";
import moment from "moment";
import { useParams } from "@remix-run/react";
import { IntegrationClient } from "services/integration.service";
import { organizationClient } from "services/organization.service";
import { serviceProfileClient } from "services/service-profile.service";
import { toast } from "sonner";

import { API_ENDPOINTS } from "utils/api/api-endpoints";

export type SPQueryOptionsType = {
   offset?: number;
   limit?: number;
   orderBy?: string;
};

enum APIIntegrationType {
   healthCheck = 'HEALTH_CHECK'
}

export const useGetIntegrationDetails = () => {
   const { id, spId: serviceProfileId }: any = useParams();

   const queryClient = useQueryClient();

   const { data: serviceProfile, isLoading: serviceProfileLoading } = useQuery({
      queryKey: [API_ENDPOINTS.SERVICE_PROFILE, serviceProfileId],
      queryFn: async () => {
         return await serviceProfileClient.searchProfiles({
            "filter": {
               "and": [
                  {
                     "property": "/id",
                     "operator": "=",
                     "values": [serviceProfileId]
                  }
               ]
            },
            "pagination": { "limit": 10, "offset": 0 }
         })
      },
      select: (resp) => {
         const data = resp?.data?.data?.[0] || {};
         return data
      },
      enabled: !!serviceProfileId
   });

   const { data: integration, isLoading: integrationLoading } = useQuery({
      queryKey: [API_ENDPOINTS.INTEGRATIONS, id],
      queryFn: async () => {
         return await IntegrationClient.getIntegration(id)
      },
      enabled: !!id,
      select: (resp) => {
         const data = resp?.data || {};
         return data
      }
   });

   const { data: integrationHealth, isLoading: integrationHealthLoading } = useQuery({
      queryKey: [API_ENDPOINTS.INTEGRATIONS, id, APIIntegrationType.healthCheck],
      queryFn: async () => {
         return await IntegrationClient.getIntegrationHealth(id, {
            type: APIIntegrationType.healthCheck,
            startDateTime: moment().subtract('day', 7).startOf('day').utc().startOf('day').format(),
            endDateTime: moment().endOf('day').utc().endOf('day').format()
         })
      },
      enabled: !!id,
      select: (resp) => {
         const data = resp?.data?.metrics || [];
         return data
      }
   });

   const {
      mutateAsync: updateIntegrationMutation,
      isPending: isUpdating,
   } = useMutation({
      mutationFn: ({ id, payload }: any) => {
         return IntegrationClient.updateIntegration(id as string, payload)
      },
      mutationKey: [API_ENDPOINTS.INTEGRATIONS],
   });

   const {
      mutateAsync: deleteIntegrationMutation,
      isPending: isDeleting,
   } = useMutation({
      mutationFn: (id: any) => {
         return IntegrationClient.deleteIntegration(id as string)
      },
      mutationKey: [API_ENDPOINTS.INTEGRATIONS],
   });

   const attemptDeleteIntegration = async (id: string, cb: any) => {
      toast.promise(deleteIntegrationMutation(id), {
         loading: 'Deleting...',
         success: () => {
            cb && cb();
            queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.INTEGRATIONS, id] })
            return 'Deleted successfully';
         },
         error: (error: any) => {
            return parseError(error?.response?.data)?.message;
         },
      })
   }

   const attemptUpdateIntegration = async (id: string, payload: Record<string, any>, cb: any) => {
      toast.promise(updateIntegrationMutation({ id, payload }), {
         loading: 'Updating...',
         success: () => {
            cb && cb();
            queryClient.invalidateQueries({ queryKey: [API_ENDPOINTS.INTEGRATIONS, id] })
            return 'Updated successfully';
         },
         error: (error: any) => {
            return parseError(error?.response?.data)?.message;
         },
      })
   }


   return {
      serviceProfile,
      serviceProfileLoading,
      integration,
      integrationLoading,
      integrationHealth: integrationHealth ?? [],
      integrationHealthLoading,

      // update
      attemptUpdateIntegration,
      attemptDeleteIntegration
   }
};
