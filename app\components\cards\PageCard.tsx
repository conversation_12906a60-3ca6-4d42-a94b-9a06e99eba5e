import React from 'react';
import { Paper, useTheme } from '@mui/material';

interface PageCardProps {
  children: React.ReactNode;
  sx?: object;
  elevation?: number;
}

export default function PageCard({ children, sx = {}, elevation = 2 }: PageCardProps) {
  const theme = useTheme();

  return (
    <Paper
      elevation={elevation}
      sx={{
        backgroundColor: theme.palette.background.paper,
        borderRadius: 2,
        p: 4,
        overflow: 'hidden',
        ...sx,
      }}
    >
      {children}
    </Paper>
  );
}