import { Changelog } from "./common"
import { Service } from "./service"


declare namespace FieldMappings {

   type CreateFieldMappingsPayload = {
      mappings: FieldMappings.Mappings
      dataModel: FieldMappings.Root['dataModel']
   }

   type UpdateFieldMappingsPayload = {
      op: string
      path: string
      value: FieldMappings.Mappings
   }

   type MappingFieldObject = { field: string, type: string, path: string }

   type MappingValue = {
      key: string
      type: 'root' | 'child',
      isPredefined: boolean,
      source: MappingFieldObject,
      target: MappingFieldObject
      parentKey?: string
   }

   interface Mappings {
      [key: string]: MappingValue
   }

   interface Root {
      id: string
      href: string
      type: string
      mappings: Mappings
      changeLog: Changelog
      service: Partial<Service>
      dataModel: {
         type: string
      }
   }
}

export type { FieldMappings }