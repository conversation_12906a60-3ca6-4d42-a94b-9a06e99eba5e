import { InputAdornment, TextFieldProps } from "@mui/material";
import { State, useStatus } from "hooks/useStatus";
import { useCallback, useEffect, useMemo, useState } from "react";
import { UseFormReturn } from "react-hook-form";

import customFields from "./custom-fields";
import { useIntegrationCreateProvider } from "sections/security/log-protection/integration-create-provider";
import useLogProtection from "store/security/log-protection";

type Props = {
   form: UseFormReturn<any, any, undefined>
   accessPoint: Record<string, any>
   targetFields: string[]
};

const BASE_PATH: string = '/target/accessPoint';
const CUSTOM_FIELDS: string[] = Object.values(customFields).map((i: any) => i.property)

export default ({ form, accessPoint: accessPointProp }: Props) => {

   const { setWindowStates } = useLogProtection()

   const [formState, setFormState] = useState({});
   const [debounceValues, setDebounceValues] = useState({});

   const [status, setStatus] = useState<boolean | State>(false)

   const { attemptTestIntegration, isTestingIntegration } = useIntegrationCreateProvider();
   const { resolveOutlinedIcon } = useStatus();

   const { watch } = form;

   const updateErrorStatus = () => {
      setStatus(State.INACTIVE);
      setWindowStates('integrationDetails', State.INACTIVE)
   };

   const updateSuccessStatus = () => {
      setStatus(State.ACTIVE);
      setWindowStates('integrationDetails', State.ACTIVE)
   }

   const validateAllFields = useCallback((fields: Record<string, any>, accessPoint: Record<string, any>) => {
      const serviceId: string = accessPoint?.service?.id;
      const type = accessPoint?.type;

      if (form.formState.isValid && serviceId && type) {

         const buildCriteria = Object.keys(fields)?.reduce((
            acc: Array<{ property: string, operator: '=', values: Array<string> }>,
            cur: string
         ) => {

            const isCustom = CUSTOM_FIELDS.includes(cur);

            if (!isCustom) {

               acc.push({
                  property: `${BASE_PATH}${cur}`,
                  operator: "=",
                  values: [fields?.[cur]]
               })
            }

            return acc;
         }, [
            {
               "property": `${BASE_PATH}/service/id`,
               "operator": "=",
               "values": [
                  serviceId
               ]
            },
            {
               "property": `${BASE_PATH}/accessPointTypeConfig/type`,
               "operator": "=",
               "values": [
                  type
               ]
            }
         ]);

         attemptTestIntegration({
            criteria: {
               and: buildCriteria
            }
         })
            .then((resp) => {
               const { data } = resp?.data;
               const values = data?.[0];
               values?.type === State.FAILURE ? updateErrorStatus() : updateSuccessStatus()
            })
            .catch(() => {
               updateErrorStatus()
            })
      }
   }, []);

   const fieldProps: TextFieldProps['InputProps'] = useMemo(() => {

      if (isTestingIntegration) {
         return {
            endAdornment: (
               <InputAdornment position="end">
                  {resolveOutlinedIcon(State.IN_PROGRESS)}
               </InputAdornment>
            )
         }
      }

      return {
         endAdornment: (
            status ? (
               status === State.ACTIVE ? (
                  <InputAdornment position="end">
                     {resolveOutlinedIcon(State.ACTIVE)}
                  </InputAdornment>
               ) : (
                  <InputAdornment position="end">
                     {resolveOutlinedIcon(State.IN_ACTIVE)}
                  </InputAdornment>
               )
            ) : null
         ),
         color: status ? (
            status === State.ACTIVE ? 'success' : 'error'
         ) : undefined
      }
   }, [status, isTestingIntegration])

   useEffect(() => void validateAllFields(debounceValues, accessPointProp), [
      debounceValues,
      form.formState.isValid,
      accessPointProp
   ]);

   useEffect(() => {
      const timer = setTimeout(setDebounceValues, 500, formState);
      return () => clearTimeout(timer);
   }, [formState])

   useEffect(() => {
      let subscription: any;

      subscription = watch((values) => {
         setFormState(values);
      });

      return () => {
         subscription?.unsubscribe();
      };

   }, [watch])

   return {
      fieldProps
   }
}