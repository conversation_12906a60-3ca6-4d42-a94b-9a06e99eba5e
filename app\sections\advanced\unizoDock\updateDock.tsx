/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/display-name */
import { useEffect, useRef } from "react"
import { Button, Stack, TextField, DialogContent, DialogTitle, DialogActions, FormControl, InputLabel, Select, MenuItem } from "@mui/material"
import _ from 'lodash';

import { useForm } from 'react-hook-form';
import { Form, FormField, FormItem } from "components/@extended/Form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import SimpleBar from "components/third-party/SimpleBar"
import { DialogClose, Dialog } from "components/@extended/dialog"
import { useGetDockProfile } from "hooks/api/dockProfiles/useDockProfile";

const watchFormScheme = z.object({
   name: z.string().nonempty("Please enter name"),
   frontendUrl: z.string().optional(),
   apiKey: z.string().optional(),
   pageLayout: z.string().optional()
})

type WatchFormValues = z.infer<typeof watchFormScheme>

const defaultValues: Partial<WatchFormValues> = {
   name: '',
   frontendUrl: '',
   apiKey: '',
   pageLayout: ''
}

export default ({
   isOpen,
   onClose: onCloseProp,
   selected,
   isEditMode,
}: Record<string, any>) => {

   const { attemptEditDockProfile } = useGetDockProfile()

   const form = useForm<WatchFormValues>({
      resolver: zodResolver(watchFormScheme),
      defaultValues,
      mode: "onChange",
   })

   const formRef = useRef<HTMLFormElement>(null);

   const onClose = () => {
      onCloseProp && onCloseProp();
      form.reset({})
   }

   const layoutOptions = [
      {
         key: "POP_UP",
         label: "PopUp",
         value: "PopUp"
      },
      {
         key: "EMBEDDED",
         label: "Embedded",
         value: "Embedded"
      }
   ]

   const onSubmit = async (data: WatchFormValues) => {
      const patchPayload = Object.entries(form.formState.dirtyFields).reduce((acc: any, [key]) => {
         acc.push({
            op: "replace",
            path: `/${key}`,
            value: data[key as keyof WatchFormValues]
         });
         return acc;
      }, []);

      attemptEditDockProfile(selected?.id, patchPayload, () => {
         onClose();
      });
   };

   useEffect(() => {
      if (isEditMode && selected) {
         form.reset({
            name: selected?.name || '',
            frontendUrl: selected?.frontendUrl || '',
            pageLayout: selected?.pageLayout || ''
         })
      } else {
         form.reset(defaultValues)
      }

   }, [selected?.id, isEditMode, isOpen, form])

   return (
      <Dialog
         open={isOpen}
         onClose={onClose}
      >
         <DialogTitle variant="h5">
            Update Configuration
         </DialogTitle>
         <DialogClose onClose={onClose} />

         <DialogContent dividers tabIndex={-1}>
            <SimpleBar sx={{ '& .simplebar-content': { display: 'flex', flexDirection: 'column' } }}>
               <Stack gap={2}>
                  <Form {...form}>
                     <Stack
                        component="form"
                        onSubmit={(...args) => (
                           void form.handleSubmit(onSubmit)(...args)
                        )}
                        ref={formRef}
                        gap={3}
                     >
                        <FormField
                           control={form.control}
                           name="name"
                           render={({ field }) => (
                              <FormItem label="Name" description="Enter a name for this configuration.">
                                 <FormControl>
                                    <TextField placeholder="Configuration name" {...field} />
                                 </FormControl>
                              </FormItem>
                           )}
                        />
                        <FormField
                           control={form.control}
                           name="frontendUrl"
                           render={({ field }) => (
                              <FormItem label="Frontend URL" description="Enter the Frontend URL.">
                                 <FormControl>
                                    <TextField placeholder="https://example.com" {...field} />
                                 </FormControl>
                              </FormItem>
                           )}
                        />
                        <FormField
                           control={form.control}
                           name="pageLayout"
                           render={({ field }) => (
                              <FormItem label="Page Layout" description="Select the layout type that best suits your needs.">
                                 <FormControl>
                                    <InputLabel id="layout-select-label">Layout type</InputLabel>
                                    <Select
                                       labelId="layout-select-label"
                                       label="Layout type"
                                       disabled={true}
                                       {...field}
                                       value={field.value || selected?.pageLayout}
                                    >
                                       {layoutOptions?.map((option) => (
                                          <MenuItem value={option.key} key={option.key}>{option.label}</MenuItem>
                                       ))}
                                    </Select>
                                 </FormControl>
                              </FormItem>
                           )}
                        />
                     </Stack>
                  </Form>
               </Stack>
            </SimpleBar>
         </DialogContent>

         <DialogActions>
            <Stack direction="row" justifyContent="flex-end" gap={1}>
               <Button onClick={onClose}>Cancel</Button>
               <Button
                  variant="contained"
                  color="primary"
                  onClick={() => {
                     formRef.current?.requestSubmit();
                  }}
               >
                  Submit
               </Button>
            </Stack>
         </DialogActions>
      </Dialog>
   )
}